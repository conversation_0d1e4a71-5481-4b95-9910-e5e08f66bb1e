import asyncio
import copy
import json
from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
)

from agent.agent import Agent
from baml_client import b
from baml_client.types import ResponseAllPreferences, TripPlanningWorkType
from flight_agent.flights_helper import FlightBamlHelper
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.flight_and_hotel_executor import TripPlanExecutor
from front_of_house_agent.common_models import TravelContext
from front_of_house_agent.sub_agent.trip_planner_sub_agent import handle_flight_planner, handle_hotel_planner
from hotel_agent.hotels_helper import HotelsHelper
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.memory.trips.retriever import TripMemoryRetriever
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.helpers import (
    get_current_date_string,
)
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory


class TripPlanningAgent(Agent):
    def __init__(
        self,
        user: User,
        user_profile: UserProfile | None,
        thread: ChatThread,
        websocket_send_message: partial[Coroutine[Any, Any, None]],
        timezone: str | None,
        flight_helper: FlightBamlHelper,
        hotel_helper: HotelsHelper,
        user_preferences: ResponseAllPreferences,
        travel_context: TravelContext,
        messages: list[BaseMessage],
    ):
        super().__init__(
            user=user,
            history=PostgresChatMessageHistory(thread_id=thread.id),
            mem_loader=TripMemoryRetriever(user_id=str(user.id), thread_id=str(thread.id)),
            websocket_send_message=websocket_send_message,
        )
        self.user_profile = user_profile
        self.thread = thread
        self.timezone = timezone
        self.flight_helper = flight_helper
        self.hotel_helper = hotel_helper
        self.user_preferences = user_preferences
        self.travel_context = travel_context
        self.synchronous = True
        self.messages = messages

        self.trip_plan_executor = TripPlanExecutor(
            flight_helper=flight_helper,
            message_sender=websocket_send_message,
            thread=thread,
            hotel_helper=hotel_helper,
            user=user,
            user_profile=user_profile,
            timezone=timezone,
        )

    async def persist_messages(self, messages: list[BaseMessage]):
        """Persist messages to history - interface compatibility with FrontOfHouseAgent."""
        if self.history:
            self.messages.extend(messages)
            for message in messages:
                self.history.add_pending_message(message)
            await self.history.apersist()

    async def run(self, message=None, message_type="text", extra_payload=None) -> list[Any]:
        message_buffer_strs = get_message_buffer_as_strings(self.messages)
        works = await b.NewSupervisorDoConverse(
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            travel_context=self.travel_context.model_dump_json(exclude_none=True),
        )
        send = []
        send_websocket_messages = []
        for work in works.work_types or []:
            if work == TripPlanningWorkType.TripPlanning:
                res = await self.handle_trip_planner(
                    messages=self.messages,
                    message_buffer_strs=message_buffer_strs,
                    extra_user_input=extra_payload or {},
                    streaming=True,
                )
                send.extend(res["messages"])
            else:
                res = await self.handle_trip_booking(
                    messages=self.messages,
                    message_buffer_strs=message_buffer_strs,
                    extra_user_input=extra_payload or {},
                    flight_select_result_str=self.travel_context.flight_select_result.model_dump_json()
                    if self.travel_context.flight_select_result
                    else "{}",
                    hotel_select_result_str=self.travel_context.hotel_select_result.model_dump_json()
                    if self.travel_context.hotel_select_result
                    else "{}",
                    flight_validation_result_str=self.travel_context.flight_validation_result.model_dump_json()
                    if self.travel_context.flight_validation_result
                    else "{}",
                    hotel_validation_result_str=self.travel_context.hotel_validation_result.model_dump_json()
                    if self.travel_context.hotel_validation_result
                    else "{}",
                    flight_booking_status_str=self.get_flight_booking_status(self.travel_context),
                    hotel_booking_status_str=self.get_hotel_booking_status(self.travel_context),
                    streaming=True,
                )

                send.extend(res["messages"])
        for m in send:
            if m:
                self.messages.append(m)
                self.add_history_message(m)
                send_websocket_messages += await map_websocket_message(
                    m,
                    self.travel_context.hotel_search_core_criteria.check_in_date,
                    self.travel_context.hotel_search_core_criteria.check_out_date,
                )

        return send_websocket_messages

    async def handle_trip_planner(
        self,
        messages: list[BaseMessage],
        message_buffer_strs: list[str],
        extra_user_input: dict[str, Any],
        streaming: bool = True,
    ) -> dict[str, Any]:
        """Handle trip planning coordination for flight and hotel planning workflows."""

        trip_plan_response = await b.TripPlanner(
            travel_preference=self.user_preferences.model_dump_json(
                exclude_none=True, exclude={k for k, v in self.user_preferences.model_dump().items() if v == []}
            ),
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            user_name=self.user.name,
        )

        if trip_plan_response.tasks:
            flight_tasks = [task for task in trip_plan_response.tasks if task.task_type.value == "FLIGHT"]
            hotel_tasks = [task for task in trip_plan_response.tasks if task.task_type.value == "HOTEL"]

            if flight_tasks:
                if len(flight_tasks) > 1:
                    flight_search_tasks = []
                    for task in flight_tasks:
                        task_context = copy.deepcopy(self.travel_context) if self.travel_context else None
                        if task_context and task_context.flight_search_core_criteria:
                            task_context.flight_search_core_criteria.outbound_date = task.outbound_date
                            if task.return_date:
                                task_context.flight_search_core_criteria.return_date = task.return_date

                        flight_search_tasks.append(
                            handle_flight_planner(
                                self,
                                messages,
                                message_buffer_strs,
                                extra_user_input,
                                flight_search_core_criteria_str=task_context.flight_search_core_criteria.model_dump_json()
                                if task_context and task_context.flight_search_core_criteria
                                else "{}",
                                flight_search_additional_criteria_str="{}",
                                flight_select_result_str="{}",
                                prompt_user_preferences=None,
                                streaming=False,
                            )
                        )

                    flight_results = await asyncio.gather(*flight_search_tasks)

                    flight_result_strings = []
                    for result in flight_results:
                        if result.get("messages"):
                            for message in result["messages"]:
                                flight_result_strings.append(
                                    json.dumps(
                                        await map_websocket_message(
                                            message,
                                            self.travel_context.hotel_search_core_criteria.check_in_date,
                                            self.travel_context.hotel_search_core_criteria.check_out_date,
                                        )
                                    )
                                )

                    search_dates = [task.outbound_date for task in flight_tasks if task.outbound_date is not None]
                    summary_response = await b.FlightSummary(
                        flight_results=flight_result_strings,
                        search_dates=search_dates,
                        user_name=self.user.name,
                    )

                    aggregated_message = AIMessage(
                        content=f"{trip_plan_response.agent_response}\n\n{summary_response.summary}",
                        additional_kwargs={
                            "agent_classification": AgentTypes.FOH,
                            "task_type": "FLIGHT",
                            "multi_day_search": True,
                            "search_dates": search_dates,
                        },
                    )

                    return {
                        "messages": [aggregated_message],
                        "current_topic": "TripPlanning",
                        "model": trip_plan_response,
                    }
                else:
                    task = flight_tasks[0]
                    return await handle_flight_planner(
                        self,
                        messages,
                        message_buffer_strs,
                        extra_user_input,
                        flight_search_core_criteria_str="{}",
                        flight_search_additional_criteria_str="{}",
                        flight_select_result_str="{}",
                        prompt_user_preferences=None,
                        streaming=streaming,
                    )

            elif hotel_tasks:
                return await handle_hotel_planner(
                    self,
                    messages,
                    message_buffer_strs,
                    self.travel_context,
                    extra_user_input,
                    prompt_user_preferences=None,
                    streaming=streaming,
                )

        final_message = AIMessage(
            content=trip_plan_response.agent_response,
            additional_kwargs={
                "agent_classification": AgentTypes.FOH,
                "task_type": "NONE",
            },
        )
        return {
            "messages": [final_message],
            "current_topic": "TripPlanning",
            "model": trip_plan_response,
        }

    async def handle_trip_booking(
        self,
        messages: list[BaseMessage],
        message_buffer_strs: list[str],
        extra_user_input: dict[str, Any],
        flight_select_result_str: str | None,
        hotel_select_result_str: str | None,
        flight_validation_result_str: str | None,
        hotel_validation_result_str: str | None,
        flight_booking_status_str: str,
        hotel_booking_status_str: str,
        streaming: bool = True,
    ) -> dict[str, Any]:
        """Handle trip booking coordination for flight and hotel booking workflows."""

        trip_booking_response = await b.TripBooking(
            travel_preference=self.user_preferences.model_dump_json(
                exclude_none=True, exclude={k for k, v in self.user_preferences.model_dump().items() if v == []}
            ),
            flight_select_result=flight_select_result_str,
            hotel_select_result=hotel_select_result_str,
            flight_validation_result=flight_validation_result_str,
            hotel_validation_result=hotel_validation_result_str,
            flight_booking_status=flight_booking_status_str,
            hotel_booking_status=hotel_booking_status_str,
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            user_name=self.user.name,
        )

        if trip_booking_response.task_type.value == "FLIGHT_BOOKING":
            return await self.execute_flight_booking_workflow(
                messages, message_buffer_strs, extra_user_input, streaming
            )
        elif trip_booking_response.task_type.value == "HOTEL_BOOKING":
            return await self.execute_hotel_booking_workflow(messages, message_buffer_strs, extra_user_input, streaming)
        else:
            final_message = AIMessage(
                content=trip_booking_response.agent_response,
                additional_kwargs={
                    "agent_classification": AgentTypes.FOH,
                    "task_type": trip_booking_response.task_type.value,
                },
            )
            return {
                "messages": [final_message],
                "current_topic": "TripBooking",
                "model": trip_booking_response,
            }

    async def execute_flight_booking_workflow(
        self,
        messages: list[BaseMessage],
        message_buffer_strs: list[str],
        extra_user_input: dict[str, Any],
        streaming: bool = True,
    ) -> dict[str, Any]:
        """Execute flight booking workflow using existing TripPlanExecutor."""
        travel_context = self.travel_context
        if not travel_context or not travel_context.flight_select_result:
            error_message = AIMessage(
                content="No flight selection found. Please select a flight first through trip planning.",
                additional_kwargs={"agent_classification": AgentTypes.FOH},
            )
            return {
                "messages": [error_message],
                "current_topic": "TripBooking",
                "model": None,
            }

        return await handle_flight_planner(
            self,
            messages,
            message_buffer_strs,
            extra_user_input,
            travel_context.flight_search_core_criteria.model_dump_json()
            if travel_context.flight_search_core_criteria
            else "{}",
            travel_context.flight_search_additional_criteria.model_dump_json()
            if travel_context.flight_search_additional_criteria
            else "{}",
            travel_context.flight_select_result.model_dump_json() if travel_context.flight_select_result else "{}",
            None,
            streaming=streaming,
        )

    async def execute_hotel_booking_workflow(
        self,
        messages: list[BaseMessage],
        message_buffer_strs: list[str],
        extra_user_input: dict[str, Any],
        streaming: bool = True,
    ) -> dict[str, Any]:
        """Execute hotel booking workflow using existing TripPlanExecutor."""
        travel_context = self.travel_context
        if not travel_context or not travel_context.hotel_select_result:
            error_message = AIMessage(
                content="No hotel selection found. Please select a hotel first through trip planning.",
                additional_kwargs={"agent_classification": AgentTypes.FOH},
            )
            return {
                "messages": [error_message],
                "current_topic": "TripBooking",
                "model": None,
            }

        return await handle_hotel_planner(
            self,
            messages,
            message_buffer_strs,
            travel_context,
            extra_user_input,
            None,
            streaming=streaming,
        )

    def get_flight_booking_status(self, travel_context: TravelContext) -> str:
        """Get current flight booking status."""
        if not travel_context or not travel_context.flight_select_result:
            return "not_selected"
        if travel_context.flight_validation_result:
            return "validated"
        if travel_context.flight_select_result.selected_outbound_flight_id:
            return "selected"
        return "not_selected"

    def get_hotel_booking_status(self, travel_context: TravelContext) -> str:
        """Get current hotel booking status."""
        if not travel_context or not travel_context.hotel_select_result:
            return "not_selected"
        if travel_context.hotel_validation_result:
            return "validated"
        if travel_context.hotel_select_result.property_id:
            return "selected"
        return "not_selected"
