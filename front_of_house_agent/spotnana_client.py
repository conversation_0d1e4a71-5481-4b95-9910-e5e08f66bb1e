from dataclasses import dataclass
from datetime import datetime
from typing import Any, List, Optional, Tu<PERSON>, cast
from zoneinfo import ZoneInfo

from flight_agent.flights_tools import FlightSearchTools
from front_of_house_agent.common_models import FlightOption, FlightStop, SpotnanaFlightOption
from front_of_house_agent.flight_utils import get_timezone_from_iata_airport_code
from server.utils.logger import logger
from server.utils.settings import settings
from virtual_travel_agent.helpers import atime_function


@dataclass
class SpotnanaFlightSearchParam:
    is_outbound_search: bool
    search_id: Optional[str]
    selected_outbound_flight_id: Optional[str]
    departure_airport_code: str
    arrival_airport_code: str
    outbound_date: str
    return_date: Optional[str]
    flight_number: str
    flight_airline_code: str
    start_time: str
    is_one_way: bool
    spotnana_user_id: str


class SpotnanaClient:
    @staticmethod
    @atime_function
    async def get_fare_basis_codes(search_id: str, air_option_id: str) -> list[str]:
        url = f"{settings.SPOTNANA_HOST}/v2/air/fare-rules/fetch"
        payload = {
            "searchId": search_id,
            "rateOptionId": air_option_id,
            "userId": FlightSearchTools.user_guid,
        }
        try:
            data = await FlightSearchTools.acall_spotnana_api(
                url,
                payload,
                None,
                "flight_fare_rule_spotnana.json",
                "flight_fare_rule_result_spotnana.json",
            )
            fare_basis_codes = []
            for leg_rule in data.get("legRuleInfos") or []:
                fare_basis_rules = leg_rule.get("fareBasisRules") or []
                for fare_basis_rule in fare_basis_rules:
                    fare_basis_code = fare_basis_rule.get("fareBasisCode")
                    if fare_basis_code:
                        fare_basis_codes.append(fare_basis_code)

            return fare_basis_codes

        except Exception as e:
            logger.error(f"Error fetching fare rule: {e}")
            return []

    @staticmethod
    @atime_function
    async def get_trip_details(trip_id: str) -> Optional[dict[str, Any]]:
        trip_details = await FlightSearchTools.get_trip_details_spotnana(trip_id)
        pnrs = trip_details.get("pnrs", [])
        if not pnrs:
            return None

        # so far we only have one pnr which is flight
        first_pnr = pnrs[0]
        data = first_pnr.get("data")
        if not data:
            return None
        air_pnr = data.get("airPnr")
        return air_pnr

    @staticmethod
    @atime_function
    async def search_flights_spotnana_legs(
        candidate_flight_options: List[FlightOption],
        segment_index: int,
        previous_selected_flight_id: Optional[str],
        previous_search_id: Optional[str],
    ) -> Tuple[List[SpotnanaFlightOption], str]:
        url = f"{settings.SPOTNANA_HOST}/v2/air/search-flights"

        legs = []
        for candidate_flight_option in candidate_flight_options:
            origin = {"airport": candidate_flight_option.departure_airport_code}
            destination = {"airport": candidate_flight_option.arrival_airport_code}
            date = {"iso8601": candidate_flight_option.departure_time.split(" ")[0]}
            legs.append(
                {
                    "origin": origin,
                    "destination": destination,
                    "date": date,
                }
            )

        flight_number = candidate_flight_options[segment_index].stops[0].flight_number
        airline_code = candidate_flight_options[segment_index].stops[0].airline_code

        payload = {
            "travelers": [
                {
                    "travelerType": "ADULT",
                    "travelerInfo": {"userId": {"id": FlightSearchTools.user_guid}},
                }
            ],
            "legs": legs,
            "filters": [
                {
                    "flightNumber": {
                        "flightNumber": {"num": flight_number, "airlineCode": airline_code},
                    }
                },
                {"changeable": "CHANGEABLE_FLEXIBLE_REFUNDABLE"},
                {"multiTicketFilter": {"hideMultiTicket": True}},
            ],
            "legSearchParams": {"pageNumber": 1},
            # So that we always get non-stop flights if possible
            "sortOptions": [{"sortBy": "DURATION", "sortOrder": "ASCENDING"}],
        }

        if previous_search_id and previous_selected_flight_id:
            payload["legSearchParams"].update(
                {
                    "searchId": previous_search_id,
                    "selectedRateOptionId": previous_selected_flight_id,
                    "legIndex": segment_index,
                }
            )

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            payload,
            None,
            "flight_match_search_spotnana.json",
            "flight_match_result_spotnana.json",
        )

        search_id = data.get("searchId")

        flight_options: List[SpotnanaFlightOption] = []
        error_response = None
        if data.get("metadata") and data["metadata"].get("airlineInfo"):
            for airline in data["metadata"]["airlineInfo"]:
                FlightSearchTools.airline_dict[airline["airlineCode"]] = airline["airlineName"]

        if data.get("itineraryDetails"):
            flightData_dict = {}
            for flightData in data["itineraryDetails"]["flightData"]:
                flight_ref = flightData.get("flightRef")
                if flight_ref:
                    flightData_dict[flight_ref] = flightData

            logger.info(f"Found {len(flightData_dict.keys())} flights.")

            for itin in data["itineraryDetails"]["itineraries"]:
                flight_option_dicts = FlightSearchTools.flattenFlights_itin(itin, flightData_dict)
                for flight_option_dict in flight_option_dicts:
                    flight_options.append(SpotnanaFlightOption.from_spotnan_dict(flight_option_dict))
        else:
            error_response = data.get("message")
            raise Exception(f"Spotnana API error: {error_response}")

        return flight_options, search_id

    @staticmethod
    @atime_function
    async def search_flights_spotnana(param: SpotnanaFlightSearchParam) -> Tuple[List[SpotnanaFlightOption], str]:
        url = f"{settings.SPOTNANA_HOST}/v2/air/search-flights"

        origin = {"airport": param.departure_airport_code}

        destination = {"airport": param.arrival_airport_code}

        legs = [
            {
                "origin": origin,
                "destination": destination,
                "date": {"iso8601": param.outbound_date},
            },
        ]

        if not param.is_one_way and param.return_date:
            legs.append(
                {
                    "origin": destination,
                    "destination": origin,
                    "date": {"iso8601": param.return_date},
                }
            )
        payload = {
            "travelers": [
                {
                    "travelerType": "ADULT",
                    "travelerInfo": {"userId": {"id": FlightSearchTools.user_guid}},
                }
            ],
            "legs": legs,
            "filters": [
                {
                    "flightNumber": {
                        "flightNumber": {"num": param.flight_number, "airlineCode": param.flight_airline_code},
                    }
                },
                {"changeable": "CHANGEABLE_FLEXIBLE_REFUNDABLE"},
            ],
            "legSearchParams": {"pageNumber": 1},
            # So that we always get non-stop flights if possible
            "sortOptions": [{"sortBy": "DURATION", "sortOrder": "ASCENDING"}],
        }

        # ask for the return flight
        if not param.is_one_way and not param.is_outbound_search and param.search_id and param.return_date:
            payload["legSearchParams"].update(
                {
                    "searchId": param.search_id,
                    "selectedRateOptionId": param.selected_outbound_flight_id,
                    "legIndex": 1,
                }
            )

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            payload,
            None,
            "flight_match_search_spotnana.json",
            "flight_match_result_spotnana.json",
        )

        search_id = data.get("searchId")

        flight_options: List[SpotnanaFlightOption] = []
        error_response = None
        if data.get("metadata") and data["metadata"].get("airlineInfo"):
            for airline in data["metadata"]["airlineInfo"]:
                FlightSearchTools.airline_dict[airline["airlineCode"]] = airline["airlineName"]

        if data.get("itineraryDetails"):
            flightData_dict = {}
            for flightData in data["itineraryDetails"]["flightData"]:
                flight_ref = flightData.get("flightRef")
                if flight_ref:
                    flightData_dict[flight_ref] = flightData

            logger.info(f"Found {len(flightData_dict.keys())} flights.")

            for itin in data["itineraryDetails"]["itineraries"]:
                flight_option_dicts = FlightSearchTools.flattenFlights_itin(itin, flightData_dict)
                for flight_option_dict in flight_option_dicts:
                    flight_options.append(SpotnanaFlightOption.from_spotnan_dict(flight_option_dict))
        else:
            error_response = data.get("message")
            raise Exception(f"Spotnana API error: {error_response}")

        return flight_options, search_id

    @staticmethod
    def flight_options_from_spotnana_result(
        itin: dict[str, Any],
        flight_data_dict: dict[str, Any] | None = None,
    ) -> List[FlightOption]:
        legs = cast(List[dict[str, Any]], itin.get("legs"))
        flight_options: List[FlightOption] = []
        for leg in legs:
            total_fare = itin.get("fareInfo").get("totalFare")  # type: ignore

            # for now we are ignoring flight stop info (e.g. layovers), we are
            # just looking at the first and last flight in the leg
            segments = cast(List[dict[str, Any]], leg.get("flights"))
            if flight_data_dict:
                # need to merge the flightData_dict info with the current flight info, not just rely on the flightData_dict
                # the flightData_dict has the specific flight info but not
                # amenties or attributes like the flight has
                for segment in segments:
                    segment["flightData"]["flightData"] = flight_data_dict.get(
                        segment["flightData"]["flightRef"]["flightRef"], {}
                    )
            firstSegment = segments[0]
            lastSegment = segments[-1]

            firstSegmentData = firstSegment["flightData"]["flightData"]
            lastSegmentData = lastSegment["flightData"]["flightData"]

            # calculate the duration of the flight including all segments
            flight_departure_dt = datetime.fromisoformat(firstSegmentData["departureDateTime"]["iso8601"]).replace(
                tzinfo=ZoneInfo(get_timezone_from_iata_airport_code(firstSegmentData["origin"]["airportCode"]))  # type: ignore
            )
            flight_arrival_dt = datetime.fromisoformat(lastSegmentData["arrivalDateTime"]["iso8601"]).replace(
                tzinfo=ZoneInfo(get_timezone_from_iata_airport_code(lastSegmentData["destination"]["airportCode"]))  # type: ignore
            )
            flight_duration = flight_arrival_dt - flight_departure_dt

            flight_duration_in_seconds = flight_duration.total_seconds()

            total_distance_km = sum(
                [segment.get("carbonEmission", {}).get("flightDistanceKm", 0) for segment in segments]
            )
            total_distance_miles = round(total_distance_km * 0.621371, 2)  # Convert km to miles

            flight_stops: List[FlightStop] = []
            for segment in segments:
                segmentFlight = segment["flightData"]["flightData"]

                flight_stops.append(
                    FlightStop(
                        departure_time=segmentFlight["departureDateTime"]["iso8601"],
                        arrival_time=segmentFlight["arrivalDateTime"]["iso8601"],
                        departure_timezone=get_timezone_from_iata_airport_code(segmentFlight["origin"]["airportCode"]),
                        arrival_timezone=get_timezone_from_iata_airport_code(
                            segmentFlight["destination"]["airportCode"]
                        ),
                        airline_code=segmentFlight["marketing"]["airlineCode"],
                        flight_number=segmentFlight["marketing"]["num"],
                        departure_airport_code=segmentFlight["origin"]["airportCode"],
                        arrival_airport_code=segmentFlight["destination"]["airportCode"],
                        cabin=segment.get("cabin") or "",
                        airline_name=FlightSearchTools.airline_dict.get(segmentFlight["marketing"]["airlineCode"], ""),
                        departure_airport_name=segmentFlight["origin"]["airportName"],
                        arrival_airport_name=segmentFlight["destination"]["airportName"],
                    )
                )

            flight_option = FlightOption(
                stops=flight_stops,
                total_price=round(
                    round(total_fare.get("base").get("amount"), 2) + round(total_fare.get("tax").get("amount"), 2),  # type: ignore
                    2,
                ),
                total_distance_miles=total_distance_miles,
                total_duration=flight_duration_in_seconds,
            )

            flight_options.append(flight_option)

        return flight_options
