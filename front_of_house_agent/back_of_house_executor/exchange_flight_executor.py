import asyncio
from functools import partial
from typing import Any, Callable, Coroutine, Dict

from langchain_core.messages import AIMessage, BaseMessage

from baml_client.types import ExchangeFlightStep
from flight_agent.flights_helper import Flight<PERSON>aml<PERSON>elper
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.back_of_house_executor.executor import Agent<PERSON><PERSON>cutor
from front_of_house_agent.common_models import EnrichedExchangeFlightState
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User
from server.services.trips.bookings import get_bookings_from_airline_confirmation_code
from server.utils.message_constants import SEARCH_UPDATE_MESSAGES
from server.utils.settings import AgentTypes


class ExchangeFlightExecutor(AgentExecutor):
    def __init__(
        self,
        flight_helper: FlightBamlHelper,
        message_sender: partial[Coroutine[Any, Any, None]],
        thread: ChatThread,
        user: User,
    ):
        super().__init__(message_sender)
        self.message_sender = message_sender
        self.flightHelper = flight_helper
        self.thread = thread
        self.user = user

    async def execute(
        self,
        exchange_flight_state: EnrichedExchangeFlightState,
        message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
        # current we only need {"messag_strs": ["message1", "message2"], "travel_context": "travel_context"}
        extral_params: dict[str, Any],
        callback_handlers: Dict[ExchangeFlightStep, Callable],
    ):
        if exchange_flight_state.airline_confirmation_number:
            booking = await get_bookings_from_airline_confirmation_code(
                exchange_flight_state.airline_confirmation_number
            )
            exchange_flight_state.flight_trip_id = booking.get("flight", {}).get("trip_id") or ""
            exchange_flight_state.flight_confirmation_id = booking.get("flight", {}).get("confirmation_id") or ""
        if exchange_flight_state.current_step == ExchangeFlightStep.CHANGE_REQUIRMENT_NOT_COLLECTED:
            task_id = f"{self.thread.id}_{ExchangeFlightStep.CHANGE_REQUIRMENT_NOT_COLLECTED.name}"

            async def __result_handler():
                asyncio.create_task(
                    self.message_sender(
                        message={
                            "type": "search_update",
                            "text": SEARCH_UPDATE_MESSAGES["FLIGHT_EXCHANGE_CHECK"],
                            "isBotMessage": True,
                            "expectResponse": False,
                        }
                    )
                )
                result = await self.flightHelper.verify_flight_exchangeable(
                    exchange_flight_state.flight_confirmation_id,
                    exchange_flight_state.flight_trip_id,
                )

                await message_persistor([result])
                return_message = await map_websocket_message(
                    result,
                    None,
                    None,
                )
                await self.message_sender(message={**return_message[0]})

            if task_id in self.task_map and not self.task_map[task_id].done():
                self.task_map[task_id].cancel()
            self.wrap_task(
                asyncio.create_task(
                    __result_handler(),
                    name=task_id,
                )
            )
        elif exchange_flight_state.current_step == ExchangeFlightStep.CHANGE_OUTBOUND_FLIGHT_SEARCH:
            task_id = f"{self.thread.id}_{ExchangeFlightStep.CHANGE_OUTBOUND_FLIGHT_SEARCH.name}"
            if task_id in self.task_map and not self.task_map[task_id].done():
                pass
            else:

                async def __handle_outbound_search_id():
                    asyncio.create_task(
                        self.message_sender(
                            message={
                                "type": "search_update",
                                "text": SEARCH_UPDATE_MESSAGES["FLIGHT_EXCHANGE_OUTBOUND"],
                                "isBotMessage": True,
                                "expectResponse": False,
                            }
                        )
                    )
                    result = await self.flightHelper.exchange_flight_search(
                        exchange_flight_state.flight_confirmation_id,
                        exchange_flight_state.flight_trip_id,
                        {
                            "departure_flight_want_change_to": exchange_flight_state.departure_flight_want_change_to.model_dump()
                            if exchange_flight_state.departure_flight_want_change_to
                            else None,
                            "return_flight_want_change_to": exchange_flight_state.return_flight_want_change_to.model_dump()
                            if exchange_flight_state.return_flight_want_change_to
                            else None,
                            "do_change_outbound_flight_search": True,
                            "do_change_return_flight_search": False,
                            "change_flight_search_id": exchange_flight_state.change_flight_search_id,
                            "selected_change_outbound_flight_id": exchange_flight_state.selected_change_outbound_flight_id,
                            "message_buffer_strs": extral_params.get("message_strs") or [],
                            "travel_context": extral_params.get("travel_context") or "",
                        },
                        flight_search_additional_criteria=exchange_flight_state.change_flight_preference,
                    )
                    message, search_id = result
                    if search_id and callback_handlers.get(ExchangeFlightStep.CHANGE_OUTBOUND_FLIGHT_SEARCH):
                        await callback_handlers[ExchangeFlightStep.CHANGE_OUTBOUND_FLIGHT_SEARCH](search_id)

                    await message_persistor([message])
                    return_message = await map_websocket_message(
                        message,
                        None,
                        None,
                    )
                    await self.message_sender(message={**return_message[0]})

                self.wrap_task(
                    asyncio.create_task(
                        __handle_outbound_search_id(),
                        name=task_id,
                    )
                )
        elif exchange_flight_state.current_step == ExchangeFlightStep.CHANGE_RETURN_FLIGHT_SEARCH:
            task_id = f"{self.thread.id}_{ExchangeFlightStep.CHANGE_RETURN_FLIGHT_SEARCH.name}"
            if task_id in self.task_map and not self.task_map[task_id].done():
                pass
            else:

                async def __handle_return_search_id():
                    asyncio.create_task(
                        self.message_sender(
                            message={
                                "type": "search_update",
                                "text": SEARCH_UPDATE_MESSAGES["FLIGHT_EXCHANGE_RETURN"],
                                "isBotMessage": True,
                                "expectResponse": False,
                            }
                        )
                    )
                    result = await self.flightHelper.exchange_flight_search(
                        exchange_flight_state.flight_confirmation_id,
                        exchange_flight_state.flight_trip_id,
                        {
                            "step": ExchangeFlightStep.CHANGE_RETURN_FLIGHT_SEARCH.name,
                            "departure_flight_want_change_to": exchange_flight_state.departure_flight_want_change_to.model_dump()
                            if exchange_flight_state.departure_flight_want_change_to
                            else None,
                            "return_flight_want_change_to": exchange_flight_state.return_flight_want_change_to.model_dump()
                            if exchange_flight_state.return_flight_want_change_to
                            else None,
                            "do_change_outbound_flight_search": False,
                            "do_change_return_flight_search": True,
                            "change_flight_search_id": exchange_flight_state.change_flight_search_id,
                            "selected_change_outbound_flight_id": exchange_flight_state.selected_change_outbound_flight_id,
                            "message_buffer_strs": extral_params.get("message_strs") or [],
                            "travel_context": extral_params.get("travel_context") or "",
                        },
                        flight_search_additional_criteria=exchange_flight_state.change_flight_preference,
                    )
                    message, search_id = result
                    if search_id and callback_handlers.get(ExchangeFlightStep.CHANGE_RETURN_FLIGHT_SEARCH):
                        await callback_handlers[ExchangeFlightStep.CHANGE_RETURN_FLIGHT_SEARCH](search_id)
                    await message_persistor([message])
                    return_message = await map_websocket_message(
                        message,
                        None,
                        None,
                    )
                    await self.message_sender(message={**return_message[0]})

                self.wrap_task(
                    asyncio.create_task(
                        __handle_return_search_id(),
                        name=task_id,
                    )
                )
        elif exchange_flight_state.current_step == ExchangeFlightStep.CHANGE_FLIGHT_VALIDATION:
            task_id = f"{self.thread.id}_{ExchangeFlightStep.CHANGE_FLIGHT_VALIDATION.name}"
            if task_id in self.task_map and not self.task_map[task_id].done():
                pass
            else:

                async def __handle_validation():
                    asyncio.create_task(
                        self.message_sender(
                            message={
                                "type": "search_update",
                                "text": SEARCH_UPDATE_MESSAGES["FLIGHT_EXCHANGE_CONFIRM"],
                                "isBotMessage": True,
                                "expectResponse": False,
                            }
                        )
                    )

                    result = await self.flightHelper.exchange_flight_validate_itinerary(
                        exchange_flight_state.flight_confirmation_id,
                        exchange_flight_state.flight_trip_id,
                        {
                            "search_id": exchange_flight_state.change_flight_search_id,
                            "selected_change_outbound_flight_id": exchange_flight_state.selected_change_outbound_flight_id,
                            "selected_change_return_flight_id": exchange_flight_state.selected_change_return_flight_id,
                        },
                        exchange_flight_state.seat_selections,
                    )
                    message, change_flight_booking_id = result
                    if change_flight_booking_id and callback_handlers.get(ExchangeFlightStep.CHANGE_FLIGHT_VALIDATION):
                        await callback_handlers[ExchangeFlightStep.CHANGE_FLIGHT_VALIDATION](change_flight_booking_id)

                    await message_persistor([message])
                    return_message = await map_websocket_message(
                        message,
                        None,
                        None,
                    )
                    await self.message_sender(message={**return_message[0]})

                self.wrap_task(
                    asyncio.create_task(
                        __handle_validation(),
                        name=task_id,
                    )
                )
        elif exchange_flight_state.current_step == ExchangeFlightStep.CHANGE_FLIGHT_SEAT_SELECTION:
            task_id = f"{self.thread.id}_{ExchangeFlightStep.CHANGE_FLIGHT_SEAT_SELECTION.name}"
            if task_id in self.task_map and not self.task_map[task_id].done():
                pass
            else:

                async def __handle_seat_selection():
                    from front_of_house_agent.back_of_house_executor.flight_executor_functions import seat_selection

                    summary_response, _, _ = await seat_selection(
                        user=self.user,
                        spotnana_search_id=exchange_flight_state.change_flight_search_id,
                        spotnana_itinerary_id=exchange_flight_state.selected_change_return_flight_id
                        or exchange_flight_state.selected_change_outbound_flight_id,
                        preferred_seat_types=(exchange_flight_state.change_flight_preference.seat_types or [])
                        if exchange_flight_state.change_flight_preference
                        else [],
                        company_allow_paid_seat=None,
                        schedule_send_message_fn=self.schedule_send_message,
                    )

                    msg = AIMessage(
                        content=summary_response,
                        name="ChangeFlightSeatSelection",
                        additional_kwargs={
                            "agent_classification": AgentTypes.EXCHANGE_FLIGHTS,
                        },
                    )

                    await message_persistor([msg])
                    return_message = await map_websocket_message(
                        msg,
                        None,
                        None,
                    )
                    await self.message_sender(message={**return_message[0]})

                self.wrap_task(
                    asyncio.create_task(
                        __handle_seat_selection(),
                        name=task_id,
                    )
                )
        elif exchange_flight_state.current_step == ExchangeFlightStep.CHANGE_FLIGHT_BOOKING:
            task_id = f"{self.thread.id}_{ExchangeFlightStep.CHANGE_FLIGHT_BOOKING.name}"
            if task_id in self.task_map and not self.task_map[task_id].done():
                pass
            else:

                async def __booking_result_handler():
                    asyncio.create_task(
                        self.message_sender(
                            message={
                                "type": "search_update",
                                "text": SEARCH_UPDATE_MESSAGES["FLIGHT_EXCHANGE_BOOKING"],
                                "isBotMessage": True,
                                "expectResponse": False,
                            }
                        )
                    )
                    result = await self.flightHelper.exchange_flight_booking(
                        exchange_flight_state.flight_trip_id,
                        {
                            "valid_change_flight_booking_id": exchange_flight_state.change_flight_booking_id,
                        },
                    )
                    await message_persistor([result])
                    return_message = await map_websocket_message(
                        result,
                        None,
                        None,
                    )
                    await self.message_sender(message={**return_message[0]})
                    await self.message_sender(message={"type": "itinerary_update"})
                    await self.message_sender(message={"type": "trip_update"})

                self.wrap_task(
                    asyncio.create_task(
                        __booking_result_handler(),
                        name=task_id,
                    )
                )

    def schedule_send_message(self, message: dict[str, Any]):
        asyncio.create_task(self.message_sender(message=message))
