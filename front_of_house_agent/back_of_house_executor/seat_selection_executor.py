import json
from typing import Any, Callable, Coroutine

from langchain_core.messages import BaseMessage, FunctionMessage

import front_of_house_agent.back_of_house_executor.flight_and_hotel_executor as fhe
from baml_client import b
from baml_client.types import CheckSeatAvailability
from flight_agent.flights_tools import FlightSearchTools
from front_of_house_agent.adapter import map_websocket_message
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.timings import Timings


async def check_available_seats(
    fh_executor: "fhe.TripPlanExecutor",
    search_id: str | None,
    itinerary_id: str | None,
    seat_preferences: list[str],
    user_email: str,
    messages: list[str],
    seat_availability_request: CheckSeatAvailability | None,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
) -> BaseMessage | None:
    assert search_id is not None
    assert itinerary_id is not None
    assert seat_availability_request is not None
    itinerary = await FlightSearchTools.get_selected_itinerary_spotnana(search_id, itinerary_id)
    flights = FlightSearchTools.map_flights_in_itin(itinerary.get("itinerary", {}))
    flight_index = None
    for i, flight in enumerate(flights):
        if (
            flight["origin"].get("airportCode") == seat_availability_request.start_airport
            and flight["destination"].get("airportCode") == seat_availability_request.end_airport
        ):
            flight_index = i
            break
    user_in_company = await FlightSearchTools.query_user_spotnana(user_email)
    user_guid_in_spotnana: str | None = (user_in_company.get("elements") or [{}])[0].get("id")

    seat_maps = await FlightSearchTools.seat_map_spotnana(
        searchId=search_id,
        itineraryId=itinerary_id,
        loyaltyInfos=None,  # TODO: query user loyalty info
        user_guid=user_guid_in_spotnana,
    )
    flight_seat_map_ids = seat_maps.get("travelerSeatMaps", [])[0].get("flightSeatMapIds", [])
    if len(flight_seat_map_ids) == 1 and flight_index is None:
        logger.info("Only one flight seat map found, can set flight_index to 0")
        flight_index = 0
    flight_seat_map_id = flight_seat_map_ids[flight_index]

    seat_map_list = [seat for seat in seat_maps.get("seatMaps") if seat.get("seatMapId") == flight_seat_map_id]

    seat_map_csv = ""
    if len(seat_map_list) == 0 or len(seat_map_list[0].get("cabinSections", [])) == 0:
        seat_map_csv = ""
    else:
        seat_map_csv, _ = FlightSearchTools.convert_seat_map_to_csv(seat_map_list[0])

    t = Timings("BAML: DoAvaiableSeatSelection")
    flight = flights[flight_index or 0]
    flight.pop("flight_id")
    response = await b.DoAvaiableSeatSelection(
        seat_map=seat_map_csv or None,
        preferred_seat=",".join(seat_preferences),
        current_flight=json.dumps(flight),
        self_intro=settings.OTTO_SELF_INTRO,
        convo_style=settings.OTTO_CONVO_STYLE,
        messages=messages,
        baml_options={"collector": logger.collector},
    )
    t.print_timing("green")
    logger.log_baml()
    msg = FunctionMessage(
        content="",
        name="SeatSelection",
        additional_kwargs={
            "function_call": {
                "name": "SeatSelection",
                "arguments": json.dumps(response.model_dump()),
            },
            "agent_classification": AgentTypes.FLIGHTS,
        },
    )

    await message_persistor([msg])
    message = await map_websocket_message(
        msg,
        None,
        None,
    )

    await fh_executor.message_sender(message={**message[0]})
