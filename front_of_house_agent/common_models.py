from enum import StrEnum
from typing import Any, Dict, List, Optional, TypedDict

from pydantic import BaseModel, Field

from baml_client.types import (
    BamlFlightOption,
    ChangeSeatState,
    ExchangeFlightConversationStateSchemaWithStep,
    FlightSearchAdditionalCriteria,
    FlightSearchCoreCriteria,
    FlightSegment,
    FlightSelectResult,
    FlightType,
    FrequentFlierNumber,  # noqa this is needed to make pydantic happy:  pydantic.errors.PydanticUndefinedAnnotation: name 'FrequentFlierNumber' is not defined
    HotelSearchAdditionalCriteria,
    HotelSearchCoreCriteria,
    HotelSelectResult,
    SeatSelectionForFlight,
    SelectedFlightForSegment,  # noqa this is needed to make pydantic happy
    SelectedSeat,  # noqa this is needed to make pydantic happy
)
from server.services.trips.bookings import construct_accommodation_dict


class FlightSearchType(StrEnum):
    ROUND_TRIP = "ROUND_TRIP"
    ONE_WAY = "ONE_WAY"
    MULTI_CITY = "MULTI_CITY"


class FlightCheckoutMissField(StrEnum):
    PREFFERED_SEAT_TYPE = "PREFFERED_SEAT_TYPE"
    FFN = "FFN"


class FlightSearchSource(StrEnum):
    FALLBACK_SPOTNANA = "FALLBACK_SPOTNANA"
    SERP = "SERP"


class FlightSearchParams(TypedDict):
    current_step: str
    departure_airport_code: Optional[str]
    is_departure_iata_city_code: Optional[bool]
    arrival_airport_code: Optional[str]
    is_arrival_iata_city_code: Optional[bool]
    outbound_date: Optional[str]
    return_date: Optional[str]
    flight_type: Optional[FlightType]
    search_id: Optional[str]
    preferred_airline_codes: Optional[List[str]]
    default_airline_brands: Optional[List[str]]
    preferred_cabin: Optional[List[str]]
    travel_context: Optional[str]
    outbound_departure_time: Optional[str]
    outbound_arrival_time: Optional[str]
    return_departure_time: Optional[str]
    return_arrival_time: Optional[str]
    number_of_stops: Optional[int]
    selected_outbound_flight_id: Optional[str]

    # only for multi leg search
    search_segments: Optional[List[FlightSegment]]


class FlightStop(BaseModel):
    departure_time: str
    departure_timezone: Optional[str] = None
    arrival_time: str
    arrival_timezone: Optional[str] = None
    airline_code: str
    airline_name: Optional[str] = None
    flight_number: str
    operating_airline_code: Optional[str] = None
    operating_flight_number: Optional[str] = None
    departure_airport_code: str
    arrival_airport_code: str
    departure_airport_name: Optional[str] = None
    arrival_airport_name: Optional[str] = None
    cabin: Optional[str] = None
    airplane: Optional[str] = None
    legroom: Optional[str] = None
    booking_code: Optional[str] = None

    # user selected seat for this flight stop,
    # might be not very good to have this here.
    seat_number: Optional[str] = None


class FlightOption(BaseModel):
    stops: List[FlightStop]
    departure_airport_code: str
    arrival_airport_code: str
    departure_time: str
    arrival_time: str
    total_price: float
    total_distance_miles: Optional[float] = None
    total_duration: Optional[float] = None
    exchange_policy: Optional[str] = None
    cancellation_policy: Optional[str] = None
    seat_selection_policy: Optional[str] = None
    fare_option_name: Optional[str] = None  # main cabin, first class, etc
    airline_code: Optional[str] = None
    flight_number: Optional[str] = None
    iso_duration: Optional[str] = None
    boarding_policy: Optional[str] = None
    baggage_policy: Optional[str] = None
    selection_reason: Optional[str] = None
    keyword: Optional[str] = None
    is_red_eye: Optional[bool] = None

    def get_all_airline_codes(self) -> List[str]:
        """
        Get all airline codes from the flight stops.
        """
        return list(set(stop.airline_code for stop in self.stops if stop.airline_code))

    def get_flight_option_dedup_key(self) -> str:
        stop_name = []
        for stop in self.stops:
            stop_name.append(
                f"{(stop.operating_airline_code or stop.airline_code).lower()}{(stop.operating_flight_number or stop.flight_number).lower()}"
            )
        return "_".join(stop_name)

    def get_all_marketing_airline_codes(self) -> str:
        return " | ".join(set([stop.airline_code for stop in self.stops if stop.airline_code and stop.airline_code]))

    def to_flight_cabin_compare_candidate_dict(self, ignore_none=False):
        res_dict = {}
        if self.cancellation_policy or not ignore_none:
            res_dict["cancellation_policy"] = self.cancellation_policy
        if self.exchange_policy or not ignore_none:
            res_dict["exchange_policy"] = self.exchange_policy
        if self.fare_option_name or not ignore_none:
            res_dict["fare_option_name"] = self.fare_option_name

        return res_dict

    def to_raw_tool_output(self, selected_by_otto: bool, preminum_search: bool) -> str:
        """
        In the following format, construct the raw tool output:
        [
        "DL282\tSEA\tSFO... | DL942\tSFO\tLAX...",
        "DL282\tSEA\tSFO... | DL942\tSFO\tLAX...",
        ]
        """
        output = []
        # headers of this leg
        output.append(
            "\t".join(
                [
                    "selected_by_otto",
                    "preminum_search",
                    "flight",
                    "price",
                    "fare_option_name",
                    "duration",
                    "distance",
                    "boarding_policy",
                    "baggage_policy",
                ]
            )
        )
        output.append(
            "\t".join(
                [
                    str(selected_by_otto),
                    str(preminum_search),
                    str(f"{self.airline_code or ''} {self.flight_number or ''}"),
                    str(f"${self.total_price:.2f}"),
                    str(self.fare_option_name),
                    str(self.iso_duration),
                    str(f"{self.total_distance_miles:.2f} miles" if self.total_distance_miles else "Unknown"),
                    str(self.boarding_policy),
                    str(self.baggage_policy),
                ]
            )
        )
        # headers of segments
        output.append(
            "\t".join(
                [
                    "flight",
                    "origin",
                    "destination",
                    "departure_time",
                    "arrival_time",
                    "cabin",
                    "airplane",
                    "legroom",
                ]
            )
        )
        for stop in self.stops:
            # for each lay over...
            output.append(
                "\t".join(
                    [
                        str(f"{stop.airline_code or ''} {stop.flight_number or ''}"),
                        str(stop.departure_airport_code),
                        str(stop.arrival_airport_code),
                        str(stop.departure_time),
                        str(stop.arrival_time),
                        str(stop.cabin),
                        str(stop.airplane),
                        str(stop.legroom),
                    ]
                )
            )
        return " | ".join(output)

    def to_fe_display_dict(self):
        res_dict = {
            "airline_code": self.stops[0].airline_code,
            "id_token_key": "",  # override it in the derived class.
            "price": self.total_price,
            "currency": "USD",
            "cabin": self.stops[0].cabin,
            "departure_time": self.stops[0].departure_time,
            "total_distance_miles": self.total_distance_miles,
            "total_duration": self.total_duration,
            "exchange_policy": self.exchange_policy,
            "cancellation_policy": self.cancellation_policy,
            "seat_selection_policy": self.seat_selection_policy,
            "fare_option_name": self.fare_option_name,
            "selection_reason": self.selection_reason,
            "keyword": self.keyword,
            "is_red_eye": self.is_red_eye,
            "booking_code": self.stops[0].booking_code,
            "boarding_policy": self.boarding_policy,
            "baggage_policy": self.baggage_policy,
            "operating_airline_code": self.stops[0].operating_airline_code,
            "operating_flight_number": self.stops[0].operating_flight_number,
        }

        for idx, stop in enumerate(self.stops):
            res_dict[f"fs{idx}_departure_time"] = stop.departure_time
            res_dict[f"fs{idx}_arrival_time"] = stop.arrival_time
            res_dict[f"fs{idx}_departure_timezone"] = stop.departure_timezone
            res_dict[f"fs{idx}_arrival_timezone"] = stop.arrival_timezone
            res_dict[f"fs{idx}_airline_code"] = stop.airline_code
            res_dict[f"fs{idx}_airline_name"] = stop.airline_name
            res_dict[f"fs{idx}_flight_number"] = stop.flight_number
            res_dict[f"fs{idx}_origin"] = stop.departure_airport_code
            res_dict[f"fs{idx}_origin_name"] = stop.departure_airport_name
            res_dict[f"fs{idx}_destination"] = stop.arrival_airport_code
            res_dict[f"fs{idx}_destination_name"] = stop.arrival_airport_name
            res_dict[f"fs{idx}_cabin"] = stop.cabin
            res_dict[f"fs{idx}_seat_number"] = stop.seat_number
            res_dict[f"fs{idx}_booking_code"] = stop.booking_code

        return res_dict

    @classmethod
    def from_fe_display_dict(cls, fe_display_dict: Dict[str, Any]) -> "FlightOption":
        flight_stops = []
        total_fs_origin = len(
            [
                key
                for key in fe_display_dict
                if key.startswith("fs") and key.endswith("_origin") and fe_display_dict[key] is not None
            ]
        )

        for idx in range(total_fs_origin):
            # for each lay over...
            if fe_display_dict.get(f"fs{idx}_origin"):
                flight_stops.append(
                    FlightStop(
                        departure_time=fe_display_dict.get(f"fs{idx}_departure_time"),  # type: ignore
                        arrival_time=fe_display_dict.get(f"fs{idx}_arrival_time"),  # type: ignore
                        airline_code=fe_display_dict.get(f"fs{idx}_airline_code"),  # type: ignore
                        flight_number=fe_display_dict.get(f"fs{idx}_flight_number"),  # type: ignore
                        departure_airport_code=fe_display_dict.get(f"fs{idx}_origin"),  # type: ignore
                        arrival_airport_code=fe_display_dict.get(f"fs{idx}_destination"),  # type: ignore
                        cabin=fe_display_dict.get(f"fs{idx}_cabin"),  # type: ignore
                        airline_name=fe_display_dict.get(f"fs{idx}_airline_name"),  # type: ignore
                        departure_airport_name=fe_display_dict.get(f"fs{idx}_origin_name"),  # type: ignore
                        arrival_airport_name=fe_display_dict.get(f"fs{idx}_destination_name"),  # type: ignore
                        departure_timezone=fe_display_dict.get(f"fs{idx}_departure_timezone"),  # type: ignore
                        arrival_timezone=fe_display_dict.get(f"fs{idx}_arrival_timezone"),  # type: ignore
                    )
                )

        if len(flight_stops) == 0:
            # backward compatibility
            flight_stops.append(
                FlightStop(
                    departure_time=fe_display_dict.get("departure_time"),  # type: ignore
                    arrival_time=fe_display_dict.get("arrival_time"),  # type: ignore
                    airline_code=fe_display_dict.get("airline_code"),  # type: ignore
                    flight_number=fe_display_dict.get("flight_number"),  # type: ignore
                    departure_airport_code=fe_display_dict.get("departure_airport_code"),  # type: ignore
                    arrival_airport_code=fe_display_dict.get("arrival_airport_code"),  # type: ignore
                    cabin=fe_display_dict.get("cabin"),  # type: ignore
                    airline_name=fe_display_dict.get("fs0_airline_name"),  # type: ignore
                    departure_airport_name=fe_display_dict.get("fs0_origin_name"),  # type: ignore
                    arrival_airport_name=fe_display_dict.get("fs0_destination_name"),  # type: ignore
                    departure_timezone=fe_display_dict.get("fs0_departure_timezone"),  # type: ignore
                    arrival_timezone=fe_display_dict.get("fs0_arrival_timezone"),  # type: ignore
                )
            )

        return FlightOption(
            stops=flight_stops,
            departure_airport_code=flight_stops[0].departure_airport_code,
            arrival_airport_code=flight_stops[-1].arrival_airport_code,
            departure_time=flight_stops[0].departure_time,
            arrival_time=flight_stops[-1].arrival_time,
            total_price=fe_display_dict["price"],
            total_distance_miles=fe_display_dict.get("total_distance_miles"),
            total_duration=fe_display_dict.get("flight_duration_in_seconds"),
            cancellation_policy=fe_display_dict.get("cancellation_policy"),
            exchange_policy=fe_display_dict.get("exchange_policy"),
            seat_selection_policy=fe_display_dict.get("seat_selection_policy"),
            boarding_policy=fe_display_dict.get("boarding_policy"),
            fare_option_name=fe_display_dict.get("fare_option_name"),
            selection_reason=fe_display_dict.get("selection_reason"),
            keyword=fe_display_dict.get("keyword"),
            is_red_eye=fe_display_dict.get("is_red_eye"),
        )


class SerpFlightOption(FlightOption):
    serp_flight_id: str

    def to_fe_display_dict(self):
        res_dict = super().to_fe_display_dict()
        res_dict["id_token_key"] = self.serp_flight_id
        return res_dict

    def to_baml_flight_option(self):
        baml_field_dict = {
            "airline_code": self.stops[0].airline_code,
            "flight_number": self.stops[0].flight_number,
            "departure_time": self.stops[0].departure_time,
            "arrival_time": self.stops[-1].arrival_time,
            "cabin": self.stops[0].cabin,
            "fare_option_name": self.fare_option_name,
        }
        return BamlFlightOption(**baml_field_dict)


class SpotnanaFlightOption(FlightOption):
    spotnana_flight_id: str

    source: Optional[str] = None

    def to_fe_display_dict(self):
        res_dict = super().to_fe_display_dict()
        res_dict["id_token_key"] = self.spotnana_flight_id
        res_dict["source"] = self.source
        return res_dict

    @classmethod
    def from_spotnan_dict(cls, spotnana_dict: Dict[str, Any]) -> "SpotnanaFlightOption":
        flight_stops = []

        # if there is a connecting flight, let's assume it's less than 4
        # otherwise, we should have at least one here
        for idx in range(0, 3):
            # for each lay over...
            if spotnana_dict.get(f"fs{idx}_origin"):
                flight_stops.append(
                    FlightStop(
                        departure_time=spotnana_dict.get(f"fs{idx}_departure_time"),  # type: ignore
                        arrival_time=spotnana_dict.get(f"fs{idx}_arrival_time"),  # type: ignore
                        airline_code=spotnana_dict.get(f"fs{idx}_airline_code"),  # type: ignore
                        flight_number=spotnana_dict.get(f"fs{idx}_flight_number"),  # type: ignore
                        operating_airline_code=spotnana_dict.get(f"fs{idx}_operating_airline_code"),  # type: ignore
                        operating_flight_number=spotnana_dict.get(f"fs{idx}_operating_flight_number"),  # type: ignore
                        departure_airport_code=spotnana_dict.get(f"fs{idx}_origin"),  # type: ignore
                        arrival_airport_code=spotnana_dict.get(f"fs{idx}_destination"),  # type: ignore
                        cabin=spotnana_dict.get(f"fs{idx}_cabin"),  # type: ignore
                        airline_name=spotnana_dict.get(f"fs{idx}_airline_name"),  # type: ignore
                        departure_airport_name=spotnana_dict.get(f"fs{idx}_origin_name"),  # type: ignore
                        arrival_airport_name=spotnana_dict.get(f"fs{idx}_destination_name"),  # type: ignore
                        booking_code=spotnana_dict.get(f"fs{idx}_booking_code"),  # type: ignore
                        departure_timezone=spotnana_dict.get(f"fs{idx}_departure_timezone"),  # type: ignore
                        arrival_timezone=spotnana_dict.get(f"fs{idx}_arrival_timezone"),  # type: ignore
                    )
                )

        if len(flight_stops) == 0:
            # backward compatibility
            flight_stops.append(
                FlightStop(
                    departure_time=spotnana_dict.get("departure_time"),  # type: ignore
                    arrival_time=spotnana_dict.get("arrival_time"),  # type: ignore
                    airline_code=spotnana_dict.get("airline_code"),  # type: ignore
                    flight_number=spotnana_dict.get("flight_number"),  # type: ignore
                    operating_airline_code=spotnana_dict.get("operating_airline_code"),  # type: ignore
                    operating_flight_number=spotnana_dict.get("operating_flight_number"),  # type: ignore
                    departure_airport_code=spotnana_dict.get("departure_airport_code"),  # type: ignore
                    arrival_airport_code=spotnana_dict.get("arrival_airport_code"),  # type: ignore
                    cabin=spotnana_dict.get("cabin"),  # type: ignore
                    airline_name=spotnana_dict.get("fs0_airline_name"),  # type: ignore
                    departure_airport_name=spotnana_dict.get("fs0_origin_name"),  # type: ignore
                    arrival_airport_name=spotnana_dict.get("fs0_destination_name"),  # type: ignore
                    booking_code=spotnana_dict.get("fs0_booking_code"),  # type: ignore
                    departure_timezone=spotnana_dict.get("fs0_departure_timezone"),  # type: ignore
                    arrival_timezone=spotnana_dict.get("fs0_arrival_timezone"),  # type: ignore
                )
            )

        return SpotnanaFlightOption(
            spotnana_flight_id=spotnana_dict["id_token_key"],
            stops=flight_stops,
            departure_airport_code=flight_stops[0].departure_airport_code,
            arrival_airport_code=flight_stops[-1].arrival_airport_code,
            departure_time=flight_stops[0].departure_time,
            arrival_time=flight_stops[-1].arrival_time,
            total_price=spotnana_dict["price"],
            total_distance_miles=spotnana_dict["total_distance_miles"],
            total_duration=spotnana_dict["flight_duration_in_seconds"],
            exchange_policy=spotnana_dict.get("exchange_policy"),
            cancellation_policy=spotnana_dict.get("cancellation_policy"),
            seat_selection_policy=spotnana_dict.get("seat_selection_policy"),
            fare_option_name=spotnana_dict.get("fare_option_name"),
            source=spotnana_dict.get("source"),
            boarding_policy=spotnana_dict.get("boarding_policy"),
        )


class SpotnanaMatchedFlight(BaseModel):
    spotnana_outbound_flight: SpotnanaFlightOption
    spotnana_return_flight: Optional[SpotnanaFlightOption] = None
    spotnana_search_id: str

    # For multi leg search
    flight_option_per_segment: Optional[list[SpotnanaFlightOption]] = None


class SeatSelectionInfo(BaseModel):
    seat_number: str
    price: float = 0
    airline_code: str
    flight_number: str


class EnrichedFlightSelectResult(FlightSelectResult):
    search_id: Optional[str] = None
    seat_selection: Optional[Dict[str, SeatSelectionInfo]] = Field(
        None, deprecated=True, description="use top level flight_seat_selection field instead"
    )
    matched_flight_id: Optional[str] = None
    search_source: Optional[FlightSearchSource] = None


class EnrichedSeatSelectionForFlight(SeatSelectionForFlight):
    is_loyalty_upgraded: Optional[bool] = None


EnrichedFlightSelectResult.model_rebuild()


class FlightValidationResult(BaseModel):
    trip_id: Optional[str] = None
    booking_id: Optional[str] = None
    initial_booking_id: Optional[str] = None


class HotelValidationResult(BaseModel):
    order_token: Optional[str] = None
    validated_price: Optional[float] = None


class EnrichedExchangeFlightState(ExchangeFlightConversationStateSchemaWithStep):
    change_flight_booking_id: Optional[str] = None
    change_flight_search_id: Optional[str] = None

    flight_confirmation_id: str
    flight_trip_id: str


class EnrichedChangeSeatState(ChangeSeatState):
    flight_confirmation_id: str
    flight_trip_id: str


EnrichedChangeSeatState.model_rebuild()


class TravelContext(BaseModel):
    flight_search_core_criteria: FlightSearchCoreCriteria = FlightSearchCoreCriteria()
    flight_search_additional_criteria: FlightSearchAdditionalCriteria = FlightSearchAdditionalCriteria()
    flight_validation_result: FlightValidationResult = FlightValidationResult()
    flight_select_result: EnrichedFlightSelectResult = EnrichedFlightSelectResult(seat_selection=None)
    flight_seat_selection: Optional[List[SeatSelectionForFlight]] = None

    selected_outbound_flight: FlightOption | None = None
    selected_return_flight: FlightOption | None = None
    latest_flight_confirmation_id: str | None = None
    latest_flight_trip_id: str | None = None
    latest_flight_airline_confirmation_number: str | None = None
    selected_flight_for_segment: Optional[Dict[str, FlightOption]] = None
    special_service_requests: list[dict] = []

    hotel_search_core_criteria: HotelSearchCoreCriteria = HotelSearchCoreCriteria()
    hotel_search_additional_criteria: HotelSearchAdditionalCriteria = HotelSearchAdditionalCriteria()
    hotel_validation_result: HotelValidationResult = HotelValidationResult()
    hotel_select_result: HotelSelectResult = HotelSelectResult()

    selected_hotel_for_segment: Optional[Dict[str, HotelSelectResult]] = None
    selected_hotel_option_for_segment: Optional[Dict[str, Dict[str, Any]]] = None
    hotel_validation_for_segment: Optional[Dict[str, HotelValidationResult]] = None

    hotel_order_id: str | None = None

    user_responsed_entry_requirment: bool = False

    user_provided_citizenship: Optional[list[str]] = None

    # Indicates if this trip session is initiated from a mobile device, including browser and app.
    is_mobile: bool = False

    def to_dict(self):
        return self.model_dump(exclude_none=True, exclude_unset=True)

    def to_selected_flights(self) -> dict[str, Any] | None:
        from server.services.trips.flight_card import construct_flight_itinerary_dict

        if (
            not self.selected_flight_for_segment
            and not self.selected_outbound_flight
            and not self.selected_return_flight
        ):
            return None

        selected_flights = []
        if self.selected_flight_for_segment:
            selected_flights = [
                self.selected_flight_for_segment[key] for key in sorted(self.selected_flight_for_segment.keys())
            ]
        else:
            if self.selected_outbound_flight:
                selected_flights.append(self.selected_outbound_flight)
            if self.selected_return_flight:
                selected_flights.append(self.selected_return_flight)

        processed_flights = []
        for flight in selected_flights:
            for seat_selection in self.flight_seat_selection or []:
                for stop in flight.stops:
                    if (
                        stop.departure_airport_code == seat_selection.start_airport
                        and stop.arrival_airport_code == seat_selection.end_airport
                    ):
                        stop.seat_number = seat_selection.seat_number
                        break
            flight_dict = flight.to_fe_display_dict()

            processed_flights.append(flight_dict)

        return construct_flight_itinerary_dict(processed_flights, "")

    async def to_selected_accomodation(self) -> dict[str, Any] | None:
        if self.selected_hotel_option_for_segment is None:
            return None
        res = []
        for hotel_option_dict in self.selected_hotel_option_for_segment.values():
            res.append(
                await construct_accommodation_dict(
                    hotel_option_dict,
                    self.hotel_search_core_criteria.check_in_date,
                    self.hotel_search_core_criteria.check_out_date,
                )
            )
        return {
            "accommodations": res,
        }
