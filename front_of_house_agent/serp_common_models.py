from typing import Any, Dict, List, Optional

from pydantic import BaseModel, model_validator

from front_of_house_agent.flight_utils import SerpResponseParam


class Stop(BaseModel):
    departure_airport: Dict[str, str]
    arrival_airport: Dict[str, str]
    airplane: Optional[str] = None
    airline: str
    travel_class: Optional[str]
    flight_number: str
    legroom: Optional[str] = None


class BookingDetail(BaseModel):
    book_with: Optional[str] = None
    marketed_as: List[str] = []
    price: float
    option_title: Optional[str] = None
    extensions: List[str] = []
    baggage_prices: List[str] = []


class FlightDetail(BaseModel):
    total_duration: Optional[float] = None
    price: Optional[float] = None
    departure_token: Optional[str] = None
    booking_token: Optional[str] = None
    extensions: List[str] = []  # This is ususally for baggage info
    flights: List[Stop]


class Flight(BaseModel):
    flight_search_id: str
    total_time_taken: float
    flight_details: List[FlightDetail]


class Booking(BaseModel):
    booking_search_id: str
    flight_search_id: Optional[str] = None
    selected_flights: List[FlightDetail]  # Combination of depature + return flight
    booking_options: List[BookingDetail]  # Each booking option is for different cabin

    @model_validator(mode="before")
    def check_selected_flight_not_empty(cls, data):
        if not data.get(SerpResponseParam.SELECTED_FLIGHTS):
            raise ValueError("Missing flights info.")
        return data


class SerpFlightParser:
    @staticmethod
    def __parse_individual_flight_stop(flights: List[Dict[str, Any]]) -> List[Stop]:
        return [Stop(**flight) for flight in flights]

    @staticmethod
    def __parse_booking_flight_detail(booking_detail_data: Dict[str, Any]) -> BookingDetail:
        return BookingDetail(**booking_detail_data)

    @staticmethod
    def parse_flight_search_response(response: Dict[str, Any]) -> Flight:
        best_flights = response.get(SerpResponseParam.BEST_FLIGHTS, [])
        other_flights = response.get(SerpResponseParam.OTHER_FLIGHTS, [])
        search_id = response.get(SerpResponseParam.SEARCH_METADATA, {}).get(SerpResponseParam.ID, "")
        total_time_taken = response.get(SerpResponseParam.SEARCH_METADATA, {}).get(
            SerpResponseParam.SEARCH_LATENCY, 0.0
        )

        all_flight_details = []

        if best_flights:
            all_flight_details.extend([FlightDetail(**flight_data) for flight_data in best_flights])

        if other_flights:
            all_flight_details.extend([FlightDetail(**flight_data) for flight_data in other_flights])

        return Flight(flight_search_id=search_id, total_time_taken=total_time_taken, flight_details=all_flight_details)

    @staticmethod
    def parse_booking_option_response(response: Dict[str, Any]) -> Booking:
        selected_flight_details = [
            FlightDetail(**flight_option) for flight_option in response.get(SerpResponseParam.SELECTED_FLIGHTS, [])
        ]

        direct_booking_options: List[BookingDetail] = []
        other_booking_options: List[BookingDetail] = []
        for option in response.get(SerpResponseParam.BOOKING_OPTIONS, []):
            separate_tickets = option.get(SerpResponseParam.SEPARATE_TICKETS, False)
            # We don't support separate ticket
            if separate_tickets:
                continue
            if not option.get("together") or not option.get("together").get("price"):
                continue
            booking_detail = SerpFlightParser.__parse_booking_flight_detail(option.get("together", {}))
            if booking_detail.option_title:
                direct_booking_options.append(booking_detail)
            else:
                other_booking_options.append(booking_detail)

        # Serp also returns booking options from other travel agency without any title option info.
        # If all the options don't have option title, pick the first booking option.
        parsed_booking_options = direct_booking_options or ([other_booking_options[0]] if other_booking_options else [])
        return Booking(
            booking_search_id=response[SerpResponseParam.SEARCH_METADATA][SerpResponseParam.ID],
            selected_flights=selected_flight_details,
            booking_options=parsed_booking_options,
        )
