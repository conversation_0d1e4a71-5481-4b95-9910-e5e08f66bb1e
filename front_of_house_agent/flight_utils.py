import asyncio
import json
import uuid
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Any, List, Optional

import aiofiles
from prometheus_client import Counter
from pydantic import BaseModel, Field

from baml_client.types import FlightSegment
from front_of_house_agent.common_models import FlightSearchParams
from server.utils.firehose_utils import firehose
from server.utils.logger import logger
from server.utils.settings import airports_info, settings

# ===============================================
#    Constants for commonly used string keys.
# ==============================================


# SERP request parameters
@dataclass(frozen=True)
class SerpRequestParam:
    DEPARTURE_ID: str = "departure_id"
    ARRIVAL_ID: str = "arrival_id"
    OUTBOUND_DATE: str = "outbound_date"
    CURRENCY: str = "currency"
    LANGUAGE = "hl"
    COUNTRY = "gl"
    RETURN_DATE = "return_date"
    TYPE = "type"
    SORT_BY = "sort_by"
    ASYNC = "async"
    AIRLINES_ALLIANCES = "include_airlines"
    STOPS = "stops"
    OUTBOUND_TIMES = "outbound_times"
    RETURN_TIMES = "return_times"
    TRAVEL_CLASS = "travel_class"
    SHOW_HIDDEN = "show_hidden"
    NO_CACHE = "no_cache"
    DEPARTURE_TOKEN = "departure_token"
    BOOKING_TOKEN = "booking_token"

    # for multi-city
    MULTI_CITY_JSON = "multi_city_json"
    DATE = "date"


# SERP response parameters
@dataclass(frozen=True)
class SerpResponseParam:
    SEARCH_METADATA = "search_metadata"
    ID = "id"
    SEARCH_LATENCY = "total_time_taken"
    DEPARTURE_TOKEN = "departure_token"
    BOOKING_TOKEN = "booking_token"
    BEST_FLIGHTS = "best_flights"
    OTHER_FLIGHTS = "other_flights"
    BOOKING_OPTIONS = "booking_options"
    SELECTED_FLIGHTS = "selected_flights"
    SEPARATE_TICKETS = "separate_tickets"


class SerpAPIError(Exception):
    """
    Custom exception for errors originating from the SERP API.
    """

    def __init__(self, message, retryable=False):
        super().__init__(message)
        self.retryable = retryable

    def __str__(self):
        return f"SerpAPIError: {self.args[0]}"

    def is_retryable(self):
        return self.retryable


SEM_LIMIT = 500  # based on system's file descriptor limits
semaphore = asyncio.Semaphore(SEM_LIMIT)


class BaseFirehoseRecord(BaseModel):
    request_id: str
    timestamp: str = Field(default_factory=lambda: datetime.now(timezone.utc).isoformat())
    env: str = settings.OTTO_ENV
    trip_id: int
    user_id: int
    method: str
    request_param: str
    response: Optional[str] = None

    @classmethod
    def from_data(
        cls, request_id: str, trip_id: int, user_id: int, method: str, params: dict, response: Optional[dict] = None
    ):
        """Creates an instance from raw input data."""
        return cls(
            request_id=request_id,
            trip_id=trip_id,
            user_id=user_id,
            method=method,
            request_param=json.dumps(params),
            response=json.dumps(response) if response else None,
        )


class SerpFirehoseRecord(BaseFirehoseRecord):
    source: str = "SERP"

    @classmethod
    def build(cls, trip_id: int, user_id: int, method: str, params: dict, response: dict) -> "SerpFirehoseRecord":
        """Creates a SERP-specific firehose record."""
        search_id = response.get(SerpResponseParam.SEARCH_METADATA, {}).get(SerpResponseParam.ID)
        assert search_id is not None, "Search metadata ID is required"
        return super().from_data(
            request_id=str(search_id),
            trip_id=trip_id,
            user_id=user_id,
            method=method,
            params=params,
        )


class SpotnanaFirehoseRecord(BaseFirehoseRecord):
    source: str = "SPOTNANA"

    @classmethod
    def build(cls, trip_id: int, user_id: int, method: str, params: dict, response: dict) -> "SpotnanaFirehoseRecord":
        """Creates a Spotnana-specific firehose record."""
        return super().from_data(
            request_id=str(uuid.uuid4()),
            trip_id=trip_id,
            user_id=user_id,
            method=method,
            params=params,
            response=response,
        )


async def log_to_firehose(record: BaseFirehoseRecord):
    await firehose.put_record_to_firehose(record.model_dump())


def log_request_to_firehose(source: str, trip_id: int, user_id: int, method: str, params: dict, response: dict) -> None:
    # Stop logging to firehose for DEV, requests are available in json files
    if settings.RUN_CONTEXT != "server":
        return
    record_class = SerpFirehoseRecord if source == "SERP" else SpotnanaFirehoseRecord
    if source == "SERP":
        params.pop("api_key", None)
    record = record_class.build(trip_id, user_id, method, params, response)
    asyncio.create_task(log_to_firehose(record))


async def write_to_file(filename, content, write_mode):
    try:
        async with semaphore:
            async with aiofiles.open(filename, mode=write_mode) as file:
                await file.write(content + "\n")
    except Exception as e:
        logger.error(f"Error writing to file {filename}: {e}")


def write_to_file_async(filename, content, write_mode="w"):
    if settings.RUN_CONTEXT != "server":
        asyncio.create_task(write_to_file(filename, content, write_mode))


def minutes_to_iso_duration(minutes: int | float | None) -> str:
    if not minutes:
        return ""
    td = timedelta(minutes=minutes)
    hours, minutes = divmod(td.seconds // 60, 60)
    return f"PT{hours}H{minutes}M" if hours else f"PT{minutes}M"


def get_with_assert(params: FlightSearchParams | FlightSegment, field: str) -> Any:
    """Asserts that a required field exists in the given params and returns its value."""
    if isinstance(params, FlightSegment):
        params_dict = params.model_dump()
    else:
        params_dict = params
    value = params_dict.get(field)
    assert value is not None, f"{field.replace('_', ' ').capitalize()} is required."
    return value


def is_premium_cabin(cabins: List[str] | None) -> bool:
    preferred_cabin = cabins or []
    return any(cabin.lower() in ["first", "business"] for cabin in preferred_cabin)


# Define metrics for SERP no flights found search
SERP_NOT_FOUND_COUNT = Counter(
    "serp_no_result_search_total",
    "Total number of SERP search that returned no results",
    ["user_id", "trip_id", "environment"],
)

SPOTNANA_FALLBACK_COUNT = Counter(
    "spotnana_fallback_total",
    "Total number of times fallback to Spotnana was needed",
    ["user_id", "trip_id", "environment"],
)


async def track_serp_not_found_metrics(user_id: int, trip_id: int):
    environment = settings.OTTO_ENV.upper()

    # Increment not found count
    SERP_NOT_FOUND_COUNT.labels(user_id=str(user_id), trip_id=str(trip_id), environment=environment).inc()


async def track_fallback_spotnana_metrics(user_id: int, trip_id: int):
    environment = settings.OTTO_ENV.upper()

    # Increment fallback count
    SPOTNANA_FALLBACK_COUNT.labels(user_id=str(user_id), trip_id=str(trip_id), environment=environment).inc()


def get_flight_search_id_for_return_search_from_serp_flight_id(serp_flight_id: str | None):
    serp_flight_search_id = None
    if serp_flight_id:
        splitted = serp_flight_id.split("_")
        if len(splitted) == 3:
            # this is for backwards compatibility
            serp_flight_search_id = splitted[1]
        elif len(splitted) == 4:
            serp_flight_search_id = splitted[2]
    return serp_flight_search_id


def generate_serp_flight_option_id(*, is_returned: bool, booking_search_id: str, flight_search_id: str, id: int) -> str:
    return f"{'r' if is_returned else 'o'}_{booking_search_id}_{flight_search_id}_{id}"


def get_timezone_from_iata_airport_code(iata_code: str) -> str | None:
    airport = airports_info.get(iata_code)
    if not airport:
        return None
    return airport.get("tz")
