import json
import random
from datetime import datetime
from typing import Any, Optional
from zoneinfo import ZoneInfo

from bson import ObjectId
from pydantic import BaseModel

from server.services.google_maps_api.get_lat_long import get_lat_long_from_loc_str
from server.services.user.user_preferences import get_user_preferences
from server.utils.logger import logger
from server.utils.mongo_connector import sample_trips_collection
from server.utils.sample_trip_date_shifting import phase_shift_dates

FILTER_DISTANCE_IN_METERS = 50 * 1.60934 * 1000  # 50 miles
MAX_SAMPLE_TRIPS = 4
BASE_DATE = datetime(2025, 5, 6, tzinfo=ZoneInfo("America/Los_Angeles"))


class SampleTrip(BaseModel):
    id: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    prompt: Optional[str] = None


class SampleTrips(BaseModel):
    trips: list[SampleTrip]


_sample_trips_cache = None


def resolve_lat_long(location_latitude_longitude: str) -> dict[str, float]:
    lat_long_list = location_latitude_longitude.split(",", 1)
    latitude = float(lat_long_list[0])
    longitude = float(lat_long_list[1])
    return {"latitude": latitude, "longitude": longitude}


async def load_sample_trips(user_id: int) -> list[SampleTrip]:
    user_preferences = await get_user_preferences(user_id)

    results = []
    if user_preferences.preferred_home_airport:
        lat_long_str = await get_lat_long_from_loc_str(f"{user_preferences.preferred_home_airport} Airport")
        if lat_long_str:
            lat_long = resolve_lat_long(lat_long_str)
            # filter out locations closer than this distance
            results = await sample_trips_collection.find(
                {
                    "location": {
                        "$nearSphere": {
                            "$geometry": {
                                "type": "Point",
                                "coordinates": [lat_long["longitude"], lat_long["latitude"]],
                            },
                            "$minDistance": FILTER_DISTANCE_IN_METERS,
                        }
                    }
                }
            ).to_list(length=None)

    if not results:
        results = await sample_trips_collection.find({}).to_list(length=None)

    if results:
        sample_trips = []
        for trip in results:
            sample_trips.append(SampleTrip(**trip, id=str(trip["_id"])))
        return sample_trips

    # fallback to the sample trips from the file
    global _sample_trips_cache
    logger.warning("No sample trips found, using fallback")
    if _sample_trips_cache is None:
        with open("data/sample_trips.json", "r") as file:
            _sample_trips_cache = [SampleTrip(**trip) for trip in json.load(file)]
    return _sample_trips_cache


def _shift_trip_dates(trip: SampleTrip, base_date: datetime) -> SampleTrip | None:
    shifted_prompt = phase_shift_dates(base_date, trip.prompt or "")
    if not shifted_prompt:
        return None

    shifted_title = phase_shift_dates(base_date, trip.title or "")
    if not shifted_title:
        return None

    shifted_description = phase_shift_dates(base_date, trip.description or "")
    if not shifted_description:
        return None

    return SampleTrip(
        title=shifted_title,
        description=shifted_description,
        prompt=shifted_prompt,
        id=trip.id,
    )


async def get_sample_trips_by_ids(ids: list[str]) -> list[SampleTrip]:
    sample_trips = await sample_trips_collection.find({"_id": {"$in": [ObjectId(id) for id in ids]}}).to_list(
        length=None
    )
    # Convert _id to id in each trip document
    sample_trips = [SampleTrip(**trip, id=str(trip["_id"])) for trip in sample_trips]
    input_prompts = []

    for trip in sample_trips:
        shifted_trip = _shift_trip_dates(trip, BASE_DATE)
        if not shifted_trip:
            continue

        input_prompts.append(shifted_trip)

    return input_prompts


async def get_sample_trips(user_id: int) -> dict[str, Any]:
    sample_trips = await load_sample_trips(user_id)
    input_prompts = []

    for trip in random.sample(sample_trips, min(MAX_SAMPLE_TRIPS, len(sample_trips))):
        if trip.id is None:
            input_prompts.append(
                SampleTrip(
                    title=trip.title,
                    description=trip.description,
                    prompt=trip.prompt,
                )
            )
        else:
            input_prompts.append(
                SampleTrip(
                    id=trip.id,
                )
            )

    return SampleTrips(trips=input_prompts).model_dump(exclude_defaults=True)
