import json
from functools import partial
from typing import Any

from langchain_core.messages import FunctionMessage

import front_of_house_agent.front_of_house_agent as fha
from agent.agent import StopResponse
from baml_client import b
from baml_client.types import EntryRequirementAction
from international_checker.international_checker import remote_international_checker
from server.database.models.user import User
from server.utils.logger import logger
from virtual_travel_agent.timings import Timings


async def handle_entry_requirment(foh_agent: "fha.FrontOfHouseAgent", message_buffer_strs: list[str]) -> dict[str, Any]:
    t = Timings("BAML: EntryRequirements")

    response = await b.EntryRequirements(
        messages=message_buffer_strs,
        user_provided_citizenship=", ".join(foh_agent.user.citizenship) if foh_agent.user.citizenship else "",
        baml_options={"collector": logger.collector},
    )
    logger.log_baml()
    t.print_timing("purple")

    if (
        response.action == EntryRequirementAction.CheckEntryRequirements
        and response.destination_country
        and response.user_citizenship_country
    ):
        requirements = await remote_international_checker.get_entry_requirements(
            response.user_citizenship_country, response.destination_country, response.transfer_airports
        )

        if requirements:
            streaming_response: str | StopResponse = await foh_agent.process_streaming(
                partial(
                    b.stream.EntryRequirmentSummary,
                    entry_requirment_string=[requirement.to_string() for requirement in requirements],
                    show_flight_booking_message=response.book_flight_ticket or False,
                ),
                lambda x: x,  # Identity function since we're returning a string directly
                lambda x: x,  # Identity function for the same reason
            )

            if isinstance(streaming_response, StopResponse):
                response_data = {"agent_response": streaming_response.last_text or "", "is_stopped": True}
            else:
                response_data = {"agent_response": streaming_response}

        else:
            response_data = {"agent_response": "We don't have any specific entry requirements for your trip."}
    else:
        if not response.user_citizenship_country:
            response_data = {
                "agent_response": "To give you the most accurate information, I'll need to know your citizenship(s). Want to share that?"
            }
        else:
            response_data = None

    if response.user_citizenship_country:
        citizenship_to_update = foh_agent.user.citizenship or []
        for citizenship in response.user_citizenship_country:
            if citizenship not in (foh_agent.user.citizenship or []):
                citizenship_to_update.append(citizenship)
        foh_agent.user.citizenship = citizenship_to_update
        await User.update_citizenship(foh_agent.user.id, citizenship_to_update)

    if not response_data:
        return {
            "messages": [],
        }

    new_message = FunctionMessage(
        content="",
        name="EntryRequirementResponse",
        additional_kwargs={
            "is_stopped": response_data.get("is_stopped", False),
            "function_call": {
                "name": "EntryRequirementResponse",
                "arguments": json.dumps(response_data),
            },
        },
    )

    return {
        "messages": [new_message],
    }
