import asyncio
import json
from functools import partial

from langchain_core.messages import (
    AIMessage,
)

import front_of_house_agent.front_of_house_agent as fha
from agent.agent import StopResponse
from baml_client import b
from baml_client.types import ProfileAction, ProfileAgentResponse
from flight_agent.flights_tools import airline_loyalty_program_mapping
from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.services.user_profile.loyalty_programs import (
    get_user_profile_flights_loyalty_programs,
    update_user_profile_flights_loyalty_programs,
)
from server.services.user_profile.payment_information import get_user_profile_payment_information
from server.services.user_profile.personal_information import get_user_profile_personal_information
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings


async def handle_profile(foh_agent: "fha.FrontOfHouseAgent", message_buffer_strs: list[str]):
    """Handle profile-related requests in the front-of-house agent."""

    company_admin_user: UserDB | None = None
    if foh_agent.user.organization_id:
        company_admin_user = await UserDB.from_organization_id_and_role(
            foh_agent.user.organization_id, UserRole.company_admin
        )

    get_user_profile_tasks = [
        get_user_profile_personal_information(foh_agent.user.id),
        get_user_profile_payment_information(foh_agent.user.id),
        get_user_profile_flights_loyalty_programs(foh_agent.user.id),
    ]
    if company_admin_user:
        get_user_profile_tasks.append(get_user_profile_payment_information(company_admin_user.id))

    (
        user_profile_personal_information,
        user_profile_payment_information,
        user_profile_loyalty_programs,
        *rest,
    ) = await asyncio.gather(*get_user_profile_tasks)
    admin_payment_information = rest[0] if company_admin_user else None

    user_payment_profile = {
        "personal_profile": user_profile_personal_information,
        "personal_payment_profile": user_profile_payment_information,
        "company_payment_profile": admin_payment_information,
        "loyalty_programs": (user_profile_loyalty_programs or {}).get("loyaltyPrograms"),
    }

    response: ProfileAgentResponse | StopResponse = await foh_agent.process_streaming(
        partial(
            b.stream.ProfileAgent,
            messages=message_buffer_strs,
            convo_style=settings.OTTO_CONVO_STYLE,
            profile_info=str(user_payment_profile),
            airline_loyalty_program_mapping=str(airline_loyalty_program_mapping),
        ),
        lambda x: x.agent_response,
        lambda x: x.agent_response,
        pre_streaming_check=lambda x: x.profile_action != ProfileAction.NotProfileAction
        if x and x.profile_action
        else True,
    )

    if isinstance(response, ProfileAgentResponse) and response.profile_action != ProfileAction.NotProfileAction:
        presentation = AIMessage(
            content=response.agent_response or "",
            additional_kwargs={"agent_classification": AgentTypes.FOH},
        )
        if response.profile_action == ProfileAction.UpdateFrequentFlierNumber:
            if FFNs := response.FFNs:
                try:
                    # Initialize or get existing loyalty programs
                    current_programs = (user_profile_loyalty_programs or {}).get("loyaltyPrograms", [])
                    current_programs_dict = {prog["IATACode"]: prog for prog in current_programs}

                    # Update or add new FFNs
                    for ffn in FFNs:
                        if ffn.number and ffn.airline_code:
                            current_programs_dict[ffn.airline_code] = {
                                "IATACode": ffn.airline_code,
                                "number": ffn.number,
                            }

                    # Convert back to list format
                    updated_programs = {"loyaltyPrograms": list(current_programs_dict.values())}

                    # Save to database
                    await update_user_profile_flights_loyalty_programs(updated_programs, foh_agent.user.id)
                except Exception as e:
                    logger.warn(f"Error updating frequent flyer number for user {foh_agent.user.id}: {e}")
                    presentation.content = "Error updating profile. Please try again later."

            return {"messages": [presentation]}

        form_type = None
        if response.profile_action == ProfileAction.FlightsPaymentForm:
            form_type = "open_flights_payment_form"
        elif response.profile_action == ProfileAction.HotelsPaymentForm:
            form_type = "open_hotels_payment_form"
        elif response.profile_action == ProfileAction.GeneralPaymentForm:
            form_type = "open_payment_form"

        popup = AIMessage(
            content="",
            additional_kwargs={
                "agent_classification": AgentTypes.FOH,
                "message_type": form_type or "open_payment_form",
                "function_call": {
                    "name": "ProfileAgentResponse",
                    "arguments": json.dumps({"status": "show_main_profile_form"}),
                },
            },
        )

        return {"messages": [presentation, popup], "current_topic": "Profile", "model": response}
    elif isinstance(response, StopResponse):
        if response.last_text:
            return {"messages": [AIMessage(content=response.last_text or "", additional_kwargs={"is_stopped": True})]}
    else:
        logger.warn(
            f"Possible NotProfileAction detected. Last user message: {message_buffer_strs[-1] if message_buffer_strs else ''}"
        )
