import itertools
import json
from datetime import datetime, timezone
from functools import partial
from typing import Any, Coroutine, List, Optional

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.graph.graph import CompiledGraph

from baml_client import b
from baml_client.types import CompanyPolicy
from company_travel_policy_agent.models import AgentState
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.user import UserRole
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.schemas.authenticate.user import User
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings
from server.utils.websocket_no_op import partial_no_op
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import Timings

config: RunnableConfig = {"configurable": {"recursion_limit": 10}}


class CompanyTravelPolicyAgent:
    def __init__(
        self,
        user: User,
        thread_id: int,
        websocket_send_message: partial[Coroutine[Any, Any, None]] | None,
        base_company_travel_policy: CompanyPolicy | None = None,
    ):
        self.websocket_send_message = websocket_send_message if websocket_send_message is not None else partial_no_op

        self.user: User = user
        self.thread_id = thread_id

        # Add memory
        self.history = PostgresChatMessageHistory(thread_id=thread_id)

        self.company_travel_policy = base_company_travel_policy
        self.messages: list[BaseMessage] = []

        self.base_company_travel_policy = base_company_travel_policy

        # Define a new graph
        self.workflow: StateGraph = StateGraph(AgentState)

        self.workflow.add_node("travel_policy_convo_agent", self.travel_policy_convo_agent_model_function)

        self.workflow.add_conditional_edges(
            "travel_policy_convo_agent",
            self.handle_travel_policy_convo_completion,
            {
                "end": END,
            },
        )

        # This means that this node is the first one called
        self.workflow.set_entry_point("travel_policy_convo_agent")

        # This compiles it into a LangChain Runnable,
        # meaning you can use it as you would any other runnable
        self.graph: CompiledGraph = self.workflow.compile()

    async def travel_policy_convo_agent_model_function(self, state):
        messages = state["messages"]
        user_input = state["input"]
        messages.append(user_input)
        message_buffer_strs = get_message_buffer_as_strings(messages)

        t = Timings("BAML: ConverseTravelPolicy")
        response = await b.ConverseTravelPolicy(
            existing_travel_policy=self.base_company_travel_policy.model_dump_json()
            if self.base_company_travel_policy is not None
            else None,
            is_company_admin=True if self.user.role == UserRole.company_admin else False,
            messages=message_buffer_strs,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("purple")
        logger.log_baml()
        # Hack in cleaner line feeds that the webapp will
        response.agent_response = response.agent_response.replace("\n\n", "&NewLine; &NewLine;")
        new_message = AIMessage(content="")
        json_representation = response.model_dump_json()
        new_message.additional_kwargs = {"function_call": {"arguments": json_representation}}
        return {"messages": [new_message]}

    def handle_travel_policy_convo_completion(self, state):
        messages = state["messages"]
        # Last response from the model
        last_message = messages[-1]
        logger.info(last_message, mask="\033[93m {}\033[00m")

        arguments_string = last_message.additional_kwargs["function_call"]["arguments"]
        arguments_obj = json.loads(arguments_string)

        last_message.content = arguments_obj["agent_response"]  # Note: Creating duplicate content

        return "end"

    async def run(self, message=None, message_type="text", extra_payload=None):
        to_send: list[dict[str, str | bool | None]] = []

        if message is None:
            to_send = await self.get_history_messages()

            if len(self.messages) == 0:
                # The messages list is empty, send the opening message
                await self.add_timestamp_message(send_ws_message=True)

                agent_response = AIMessage(content="")

            elif isinstance(self.messages[-1], AIMessage):
                # In the previous session we were waiting for user input,
                # return the entire history from previous session
                to_send[-1]["expectResponse"] = True
                return to_send
            else:
                # In the previous session we were waiting for LLM, wait for LLM
                # new message then return it with the entire history from
                # previous session
                agent_response = self.messages.pop()

                history_message = {
                    "type": "history",
                    "messages": [{**message, "is_history": True} for message in to_send],
                }

                await self.websocket_send_message(message=history_message)
                to_send = []
        else:
            if message_type == "update":
                agent_response = HumanMessage(
                    content=", ".join(map(str, message)),
                    additional_kwargs={"is_card_update": True},
                )
            elif message_type == "silent_prompt":
                agent_response = HumanMessage(content=message)
                agent_response.additional_kwargs["message_type"] = message_type
            else:
                agent_response = HumanMessage(content=message)
            self.history.add_pending_message(agent_response)
            self.messages.append(agent_response)

        response = await self.graph.ainvoke({"messages": list(self.messages), "input": agent_response}, config=config)

        lastest_model_message = response["messages"][-1]
        self.history.add_pending_message(lastest_model_message)
        self.messages.append(lastest_model_message)

        to_send += self.map_websocket_message(lastest_model_message)

        arguments_obj = json.loads(lastest_model_message.additional_kwargs["function_call"]["arguments"])

        to_be_update = (
            None if arguments_obj.get("company_policy") is None else CompanyPolicy(**arguments_obj["company_policy"])
        )

        logger.info(f"FINAL COMPANY POLICY JSON: {to_be_update}")
        await UserCompanyTravelPolicy.update_company_travel_policy(self.user.id, to_be_update)
        if self.company_travel_policy != to_be_update:
            self.company_travel_policy = to_be_update
            # also send back a profile update message to let FE know to update the profile
            to_send.append(
                {
                    "type": "profile_update",
                    "expectResponse": True,
                }
            )

        return to_send

    def map_websocket_message(self, message: BaseMessage) -> list[dict[str, str | bool | None]]:
        is_bot_message: bool = isinstance(message, AIMessage)

        messages: list[dict[str, str | bool | None]] = []
        try:
            message_dict: dict[str, str] = json.loads(str(message.content))

            if "agent_response" in message_dict.keys():
                messages.append(
                    {
                        "type": message.additional_kwargs.get("message_type", "prompt"),
                        "text": message_dict["agent_response"],
                        "expectResponse": True,
                        "isBotMessage": True,
                        "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                            message.additional_kwargs.get("agent_classification", AgentTypes.OTHER),
                            None,
                        ),
                    }
                )

        except ValueError:
            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": str(message.content),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", AgentTypes.ERROR),
                        None,
                    ),
                }
            )

        except Exception as e:
            logger.error(f"Error: {e}")

            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": str(message.content),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", AgentTypes.ERROR),
                        None,
                    ),
                }
            )

        return messages

    async def add_timestamp_message(self, new_timestamp: Optional[str] = None, send_ws_message: bool = False):
        if new_timestamp is None:
            new_timestamp = datetime.now(timezone.utc).isoformat()

        if datetime.fromisoformat(new_timestamp) - self.history.max_created_date > settings.MIN_MESSAGE_TIMESTAMP_DELTA:
            timestamp_message = AIMessage(content="", additional_kwargs={"timestamp": new_timestamp})
            self.history.add_pending_message(timestamp_message)

            if send_ws_message:
                await self.websocket_send_message(
                    message={
                        "type": "prompt",
                        "timestamp": timestamp_message.additional_kwargs.get("timestamp"),
                    }
                )

    async def get_history_messages(self):
        self.messages: List[BaseMessage] = await self.history.persisted_messages

        history: list[list[dict[str, str | bool | None]]] = []
        for idx, message in reversed(list(enumerate(self.messages))):
            if message.additional_kwargs.get("is_card_update", False):
                continue
            elif message.additional_kwargs.get("timestamp", False):
                history.append(
                    [
                        {
                            "type": "prompt",
                            "timestamp": str(message.additional_kwargs.get("timestamp")),
                        }
                    ]
                )
                del self.messages[idx]
            else:
                history.append(self.map_websocket_message(message))
        history.reverse()
        to_send = list(itertools.chain.from_iterable(history))

        # This is history, do not wait for user input
        for msg in to_send:
            msg["expectResponse"] = False

        return to_send
