{
	"data": [
		{
			"id": 3,
			"name": "Louvre Hotels Group",
			"brands": [
				{
					"id": 1018,
					"name": "Campanile"
				},
				{
					"id": 1019,
					"name": "Kyriad Hotel"
				},
				{
					"id": 1020,
					"name": "Premiere Classe"
				},
				{
					"id": 1021,
					"name": "Kyriad Prestige Hotel"
				},
				{
					"id": 6535,
					"name": "Tulip Inn"
				},
				{
					"id": 6536,
					"name": "Golden Tulip"
				},
				{
					"id": 6537,
					"name": "Royal Tulip Luxury Hotels"
				},
				{
					"id": 12126,
					"name": "Kyriad Direct"
				},
				{
					"id": 13268,
					"name": "Tulip Residences"
				},
				{
					"id": 14500,
					"name": "<PERSON><PERSON><PERSON> (Louvre)"
				}
			]
		},
		{
			"id": 11,
			"name": "<PERSON>r<PERSON>th Hôtels",
			"brands": [
				{
					"id": 1026,
					"name": "Amrath Hotels"
				}
			]
		},
		{
			"id": 13,
			"name": "Best Western Hotels",
			"brands": [
				{
					"id": 1029,
					"name": "Best Western"
				},
				{
					"id": 4444,
					"name": "Best Western Plus"
				},
				{
					"id": 4445,
					"name": "Best Western Premier"
				},
				{
					"id": 6737,
					"name": "Executive Residency by Best Western "
				},
				{
					"id": 6738,
					"name": "Vib by Best Western"
				},
				{
					"id": 6740,
					"name": "BW Premier Collection"
				},
				{
					"id": 8033,
					"name": "SureStay Hotel by Best Western"
				},
				{
					"id": 8034,
					"name": "SureStay Plus Hotel by Best Western"
				},
				{
					"id": 8035,
					"name": "SureStay Collection by Best Western"
				},
				{
					"id": 9983,
					"name": "BW Signature Collection by Best Western"
				},
				{
					"id": 9984,
					"name": "Glo by Best Western"
				},
				{
					"id": 11485,
					"name": "Aiden by Best Western"
				},
				{
					"id": 11856,
					"name": "Sure Hotel by Best Western"
				},
				{
					"id": 11857,
					"name": "Sure Hotel Collection by Best Western"
				},
				{
					"id": 12917,
					"name": "WorldHotels Collection"
				},
				{
					"id": 12918,
					"name": "WorldHotels Distinctive"
				},
				{
					"id": 12919,
					"name": "WorldHotels Luxury"
				},
				{
					"id": 12920,
					"name": "WorldHotels Elite"
				},
				{
					"id": 13640,
					"name": "WorldHotels Crafted Collection"
				}
			]
		},
		{
			"id": 14,
			"name": "Fletcher Hotels",
			"brands": [
				{
					"id": 1030,
					"name": "Fletcher Hotels"
				}
			]
		},
		{
			"id": 15,
			"name": "Bastion Hotel Groep",
			"brands": [
				{
					"id": 1031,
					"name": "Bastion Hotels"
				}
			]
		},
		{
			"id": 16,
			"name": "Hampshire Hotel Group",
			"brands": [
				{
					"id": 1032,
					"name": "Hampshire Hotels"
				},
				{
					"id": 1038,
					"name": "Hampshire Inns"
				},
				{
					"id": 1039,
					"name": "Hampshire Classic Hotels"
				}
			]
		},
		{
			"id": 19,
			"name": "Radisson Hotel Group",
			"brands": [
				{
					"id": 1040,
					"name": "Park Plaza Hotels & Resorts"
				},
				{
					"id": 1041,
					"name": "art'otel"
				},
				{
					"id": 6175,
					"name": "Arena Hospitality Group"
				},
				{
					"id": 8644,
					"name": "Country Inn & Suites by Radisson"
				},
				{
					"id": 8645,
					"name": "Park Inn by Radisson"
				},
				{
					"id": 8647,
					"name": "Radisson"
				},
				{
					"id": 8648,
					"name": "Radisson Blu"
				},
				{
					"id": 8649,
					"name": "Radisson Blu Edwardian"
				},
				{
					"id": 8650,
					"name": "Radisson Red"
				},
				{
					"id": 10295,
					"name": "Radisson Collection"
				},
				{
					"id": 13289,
					"name": "Radisson Individuals"
				},
				{
					"id": 13895,
					"name": "Prize by Radisson"
				},
				{
					"id": 14062,
					"name": "Sokos Hotels (Radisson)"
				}
			]
		},
		{
			"id": 20,
			"name": "Van der Valk Hotels",
			"brands": [
				{
					"id": 1042,
					"name": "Van der Valk Hotels"
				}
			]
		},
		{
			"id": 21,
			"name": "NH Hotel Group",
			"brands": [
				{
					"id": 1043,
					"name": "NH Hotels"
				},
				{
					"id": 1077,
					"name": "nhow"
				},
				{
					"id": 1711,
					"name": "NH Collection"
				},
				{
					"id": 2766,
					"name": "Hesperia Resorts"
				},
				{
					"id": 14088,
					"name": "Anantara Hotels & Resorts"
				},
				{
					"id": 14089,
					"name": "Tivoli Hotels"
				},
				{
					"id": 14539,
					"name": "Avani"
				}
			]
		},
		{
			"id": 22,
			"name": "WestCord Hotels",
			"brands": [
				{
					"id": 1044,
					"name": "WestCord Hotels"
				}
			]
		},
		{
			"id": 23,
			"name": "Wyndham",
			"brands": [
				{
					"id": 1045,
					"name": "Ramada"
				},
				{
					"id": 1046,
					"name": "Days Inn"
				},
				{
					"id": 1047,
					"name": "Travelodge by Wyndham"
				},
				{
					"id": 1048,
					"name": "Wyndham Hotels & Resorts"
				},
				{
					"id": 1738,
					"name": "Howard Johnson"
				},
				{
					"id": 1750,
					"name": "Super 8"
				},
				{
					"id": 1783,
					"name": "Wingate by Wyndham"
				},
				{
					"id": 1993,
					"name": "Baymont Inn & Suites"
				},
				{
					"id": 2110,
					"name": "Hawthorn Suites"
				},
				{
					"id": 2111,
					"name": "Microtel Inns & Suites"
				},
				{
					"id": 3893,
					"name": "Tryp"
				},
				{
					"id": 5006,
					"name": "Extra Holidays"
				},
				{
					"id": 5889,
					"name": "Wyndham Garden"
				},
				{
					"id": 5890,
					"name": "Planet Hollywood"
				},
				{
					"id": 5892,
					"name": "Wyndham Grand"
				},
				{
					"id": 6920,
					"name": "Dolce Hotels & Resorts"
				},
				{
					"id": 8235,
					"name": "Fen Hoteles"
				},
				{
					"id": 9329,
					"name": "Trademark"
				},
				{
					"id": 9349,
					"name": "AmericInn"
				},
				{
					"id": 10689,
					"name": "Dazzler By Wyndham"
				},
				{
					"id": 10690,
					"name": "Esplendor by Wyndham"
				},
				{
					"id": 12220,
					"name": "La Quinta by Wyndham"
				},
				{
					"id": 12970,
					"name": "Ramada Encore"
				},
				{
					"id": 13527,
					"name": "Registry Collection Hotels"
				},
				{
					"id": 13765,
					"name": "Alltra"
				},
				{
					"id": 14334,
					"name": "Vienna House by Wyndham"
				},
				{
					"id": 14775,
					"name": "Wyndham"
				},
				{
					"id": 14776,
					"name": "Echo Suites Extended Stay by Wyndham"
				},
				{
					"id": 14834,
					"name": "WaterWalk Extended Stay by Wyndham"
				}
			]
		},
		{
			"id": 24,
			"name": "Accor",
			"brands": [
				{
					"id": 1049,
					"name": "Sofitel"
				},
				{
					"id": 1050,
					"name": "Novotel"
				},
				{
					"id": 1051,
					"name": "Mercure"
				},
				{
					"id": 1052,
					"name": "Novotel Suites"
				},
				{
					"id": 1053,
					"name": "ibis"
				},
				{
					"id": 1854,
					"name": "Pullman Hotels and Resorts"
				},
				{
					"id": 1889,
					"name": "all seasons (Accor)"
				},
				{
					"id": 1936,
					"name": "MGallery"
				},
				{
					"id": 2285,
					"name": "Accor"
				},
				{
					"id": 2408,
					"name": "hotelF1"
				},
				{
					"id": 2994,
					"name": "Adagio Access Aparthotels"
				},
				{
					"id": 3086,
					"name": "ibis Styles"
				},
				{
					"id": 3135,
					"name": "Grand Mercure"
				},
				{
					"id": 3186,
					"name": "ibis Budget"
				},
				{
					"id": 3707,
					"name": "Adagio Aparthotels"
				},
				{
					"id": 4382,
					"name": "The Sebel"
				},
				{
					"id": 7005,
					"name": "SO/"
				},
				{
					"id": 7006,
					"name": "Sofitel Legend"
				},
				{
					"id": 7425,
					"name": "Quay West"
				},
				{
					"id": 7536,
					"name": "Mama Shelter"
				},
				{
					"id": 8279,
					"name": "Fairmont Hotels & Resorts"
				},
				{
					"id": 8282,
					"name": "Raffles"
				},
				{
					"id": 8283,
					"name": "Swissôtel Hotels & Resorts"
				},
				{
					"id": 8673,
					"name": "JO & JOE"
				},
				{
					"id": 9446,
					"name": "Rixos"
				},
				{
					"id": 11518,
					"name": "Mantra"
				},
				{
					"id": 11520,
					"name": "BreakFree"
				},
				{
					"id": 11521,
					"name": "Peppers"
				},
				{
					"id": 11560,
					"name": "Mövenpick"
				},
				{
					"id": 12250,
					"name": "Greet Hotel"
				},
				{
					"id": 13180,
					"name": "SBE"
				},
				{
					"id": 13266,
					"name": "Tribe"
				},
				{
					"id": 13269,
					"name": "Mantis"
				},
				{
					"id": 13270,
					"name": "The House Of Originals"
				},
				{
					"id": 13271,
					"name": "Art Series"
				},
				{
					"id": 13272,
					"name": "Mondrian"
				},
				{
					"id": 13273,
					"name": "Delano"
				},
				{
					"id": 13274,
					"name": "SLS"
				},
				{
					"id": 13275,
					"name": "The Redbury"
				},
				{
					"id": 13276,
					"name": "25 hours"
				},
				{
					"id": 13277,
					"name": "Hyde"
				},
				{
					"id": 13653,
					"name": "Faena"
				},
				{
					"id": 13654,
					"name": "21c Museum Hotels"
				},
				{
					"id": 14322,
					"name": "Handwritten Collection"
				},
				{
					"id": 14635,
					"name": "the hoxton"
				},
				{
					"id": 14646,
					"name": "Adagio Premium"
				},
				{
					"id": 14647,
					"name": "Orient Express"
				},
				{
					"id": 14648,
					"name": "HIPARK"
				},
				{
					"id": 14654,
					"name": "Banyan Tree"
				}
			]
		},
		{
			"id": 28,
			"name": "Zenit Hoteles",
			"brands": [
				{
					"id": 1068,
					"name": "Zenit Hoteles"
				}
			]
		},
		{
			"id": 29,
			"name": "InterContinental Hotels Group",
			"brands": [
				{
					"id": 1070,
					"name": "InterContinental Hotels & Resorts"
				},
				{
					"id": 1071,
					"name": "Crowne Plaza Hotels & Resorts"
				},
				{
					"id": 1072,
					"name": "Holiday Inn Hotels & Resorts"
				},
				{
					"id": 1073,
					"name": "Even Hotels & Resorts"
				},
				{
					"id": 1074,
					"name": "Candlewood Suites"
				},
				{
					"id": 1075,
					"name": "Staybridge Suites"
				},
				{
					"id": 2207,
					"name": "Hotel Indigo"
				},
				{
					"id": 2298,
					"name": "Hualuxe Hotels & Resorts"
				},
				{
					"id": 2301,
					"name": "Holiday Inn Express"
				},
				{
					"id": 7076,
					"name": "Kimpton Hotels"
				},
				{
					"id": 10533,
					"name": "Avid"
				},
				{
					"id": 11575,
					"name": "Voco"
				},
				{
					"id": 12461,
					"name": "Regent"
				},
				{
					"id": 13683,
					"name": "Atwell Suites"
				},
				{
					"id": 13771,
					"name": "Vignette Collection"
				},
				{
					"id": 14650,
					"name": "Garner"
				}
			]
		},
		{
			"id": 33,
			"name": "Hilton Worldwide",
			"brands": [
				{
					"id": 1078,
					"name": "Hilton Hotels & Resorts"
				},
				{
					"id": 1851,
					"name": "Hampton Inn"
				},
				{
					"id": 1888,
					"name": "Conrad Hotels & Resorts"
				},
				{
					"id": 1982,
					"name": "Homewood Suites by Hilton"
				},
				{
					"id": 1983,
					"name": "Embassy Suites Hotels"
				},
				{
					"id": 1984,
					"name": "Doubletree by Hilton"
				},
				{
					"id": 2117,
					"name": "Hilton Garden Inn"
				},
				{
					"id": 2154,
					"name": "Waldorf Astoria Hotels & Resorts"
				},
				{
					"id": 2452,
					"name": "Hilton Grand Vacations"
				},
				{
					"id": 3203,
					"name": "Home2 Suites by Hilton"
				},
				{
					"id": 4381,
					"name": "Curio Collection by Hilton"
				},
				{
					"id": 6239,
					"name": "Canopy by Hilton"
				},
				{
					"id": 6532,
					"name": "Tru by Hilton"
				},
				{
					"id": 9817,
					"name": "Tapestry Collection"
				},
				{
					"id": 11694,
					"name": "LXR Hotels & Resorts"
				},
				{
					"id": 13550,
					"name": "Motto"
				},
				{
					"id": 14303,
					"name": "Signia by Hilton"
				},
				{
					"id": 14304,
					"name": "Tempo by Hilton"
				},
				{
					"id": 14564,
					"name": "Spark by Hilton"
				},
				{
					"id": 14824,
					"name": "NoMad"
				},
				{
					"id": 14825,
					"name": "LivSmart"
				},
				{
					"id": 14827,
					"name": "Graduate by Hilton"
				}
			]
		},
		{
			"id": 34,
			"name": "Marriott",
			"brands": [
				{
					"id": 1080,
					"name": "Marriott Hotels & Resorts"
				},
				{
					"id": 1092,
					"name": "Renaissance Hotels & Resorts"
				},
				{
					"id": 1093,
					"name": "Courtyard by Marriott"
				},
				{
					"id": 1094,
					"name": "The Ritz-Carlton Company, L.L.C"
				},
				{
					"id": 1097,
					"name": "JW Marriott Hotels & Resorts"
				},
				{
					"id": 1098,
					"name": "Residence Inn"
				},
				{
					"id": 2187,
					"name": "SpringHill Suites"
				},
				{
					"id": 2188,
					"name": "Fairfield Inn"
				},
				{
					"id": 2542,
					"name": "Autograph Collection"
				},
				{
					"id": 2811,
					"name": "AC Hotels by Marriott"
				},
				{
					"id": 2910,
					"name": "TownePlace Suites by Marriott"
				},
				{
					"id": 3081,
					"name": "Protea Hotels by Marriott"
				},
				{
					"id": 3169,
					"name": "Marriott Vacation Club"
				},
				{
					"id": 3961,
					"name": "Bulgari Hotels and Resorts"
				},
				{
					"id": 4059,
					"name": "Gaylord Hotels"
				},
				{
					"id": 4060,
					"name": "Edition"
				},
				{
					"id": 4128,
					"name": "Moxy Hotels"
				},
				{
					"id": 4370,
					"name": "Marriott Executive Apartments"
				},
				{
					"id": 4435,
					"name": "Atlantis"
				},
				{
					"id": 5826,
					"name": "Delta"
				},
				{
					"id": 7264,
					"name": "Grand Residence"
				},
				{
					"id": 12732,
					"name": "Design Hotels"
				},
				{
					"id": 12893,
					"name": "Aloft"
				},
				{
					"id": 12894,
					"name": "Element by Westin"
				},
				{
					"id": 12895,
					"name": "Four Points by Sheraton"
				},
				{
					"id": 12896,
					"name": "Le Meridien Hotels & Resorts"
				},
				{
					"id": 12897,
					"name": "Luxury Collection"
				},
				{
					"id": 12898,
					"name": "Westin"
				},
				{
					"id": 12899,
					"name": "W Hotels"
				},
				{
					"id": 12900,
					"name": "Tribute Portfolio"
				},
				{
					"id": 12901,
					"name": "St. Regis"
				},
				{
					"id": 12902,
					"name": "Sheraton"
				},
				{
					"id": 14068,
					"name": "Sheraton Vacation Club"
				},
				{
					"id": 14069,
					"name": "Westin Vacation Club"
				},
				{
					"id": 14529,
					"name": "City Express by Marriott"
				},
				{
					"id": 14653,
					"name": "Four Points Express"
				},
				{
					"id": 14691,
					"name": "Apartments by Marriott Bonvoy"
				}
			]
		},
		{
			"id": 36,
			"name": "Meliá Hotels International",
			"brands": [
				{
					"id": 1082,
					"name": "Meliá Hotels & Resorts"
				},
				{
					"id": 1083,
					"name": "ME by Meliá"
				},
				{
					"id": 1084,
					"name": "TRYP Hoteles"
				},
				{
					"id": 1085,
					"name": "Sol Hotels"
				},
				{
					"id": 1087,
					"name": "Paradisus Resorts"
				},
				{
					"id": 1088,
					"name": "Sol House"
				},
				{
					"id": 2113,
					"name": "Innside by Melia"
				},
				{
					"id": 3128,
					"name": "Gran Melia Hotels & Resorts"
				},
				{
					"id": 3973,
					"name": "Sol Beach House"
				},
				{
					"id": 4715,
					"name": "Sol Katmandu Park & Resort"
				},
				{
					"id": 6980,
					"name": "Sol House MIXED BY IBIZA ROCKS"
				},
				{
					"id": 8670,
					"name": "Sol by Meliá"
				},
				{
					"id": 13896,
					"name": "Meliá Collection"
				},
				{
					"id": 14251,
					"name": "Affiliated by Melia"
				},
				{
					"id": 14252,
					"name": "Falcons Resorts by Melia"
				},
				{
					"id": 14364,
					"name": "ZEL"
				},
				{
					"id": 14431,
					"name": "Meliá Hotels International"
				}
			]
		},
		{
			"id": 37,
			"name": "A&O Hotels and Hostels",
			"brands": [
				{
					"id": 1086,
					"name": "A&O Hotels & Hostels"
				}
			]
		},
		{
			"id": 38,
			"name": "Eurostars Hotels Company",
			"brands": [
				{
					"id": 1090,
					"name": "Eurostars Hotels"
				},
				{
					"id": 3452,
					"name": "Exe Hotels"
				},
				{
					"id": 8630,
					"name": "Ikonik Hotels"
				},
				{
					"id": 9602,
					"name": "Tandem"
				},
				{
					"id": 13667,
					"name": "Áurea"
				},
				{
					"id": 13796,
					"name": "Crisol"
				}
			]
		},
		{
			"id": 46,
			"name": "Warwick Hotels",
			"brands": [
				{
					"id": 1103,
					"name": "Warwick Hotels and Resorts"
				},
				{
					"id": 3369,
					"name": "Warwick International Hotels and Resorts"
				},
				{
					"id": 3415,
					"name": "Warwick International Hotels and Resorts - Internal"
				}
			]
		},
		{
			"id": 50,
			"name": "VP Hotels",
			"brands": [
				{
					"id": 1107,
					"name": "VP Hotels"
				}
			]
		},
		{
			"id": 53,
			"name": "ACHAT Hotels",
			"brands": [
				{
					"id": 1110,
					"name": "ACHAT Hotels"
				}
			]
		},
		{
			"id": 55,
			"name": "Hotels Viva",
			"brands": [
				{
					"id": 1112,
					"name": "Viva Hotels & resorts"
				},
				{
					"id": 2007,
					"name": "Vanity Hotels by Viva"
				}
			]
		},
		{
			"id": 56,
			"name": "VIP Hotels",
			"brands": [
				{
					"id": 1113,
					"name": "VIP Hotels"
				}
			]
		},
		{
			"id": 57,
			"name": "Vincci Hoteles",
			"brands": [
				{
					"id": 1114,
					"name": "Vincci Hoteles"
				}
			]
		},
		{
			"id": 62,
			"name": "Choice Hotels International",
			"brands": [
				{
					"id": 1120,
					"name": "Comfort Inn"
				},
				{
					"id": 1121,
					"name": "Comfort Suites"
				},
				{
					"id": 1122,
					"name": "Quality Inn"
				},
				{
					"id": 1123,
					"name": "Sleep Inn"
				},
				{
					"id": 1124,
					"name": "Clarion"
				},
				{
					"id": 1125,
					"name": "Cambria Hotels"
				},
				{
					"id": 1127,
					"name": "Main Stay Suites"
				},
				{
					"id": 1128,
					"name": "Econo Lodge"
				},
				{
					"id": 1129,
					"name": "Rodeway Inn"
				},
				{
					"id": 2378,
					"name": "Ascend Collection"
				},
				{
					"id": 2714,
					"name": "Suburban"
				},
				{
					"id": 2852,
					"name": "Clarion Collection"
				},
				{
					"id": 8456,
					"name": "Quality Hotel"
				},
				{
					"id": 12747,
					"name": "WoodSpring"
				},
				{
					"id": 12892,
					"name": "RocketFuel (FR)"
				},
				{
					"id": 13834,
					"name": "Comfort"
				},
				{
					"id": 14067,
					"name": "Everhome Suites"
				},
				{
					"id": 14593,
					"name": "Radisson Americas"
				},
				{
					"id": 14594,
					"name": "Park Inn by Radisson Americas"
				},
				{
					"id": 14604,
					"name": "Country Inn & Suites by Radisson Americas"
				},
				{
					"id": 14605,
					"name": "Park Plaza Hotels & Resorts Americas"
				},
				{
					"id": 14606,
					"name": "Radisson Blu Americas"
				},
				{
					"id": 14607,
					"name": "Radisson Individuals Americas"
				},
				{
					"id": 14608,
					"name": "Radisson Red Americas"
				}
			]
		},
		{
			"id": 69,
			"name": "Village Hotels & Leisure Clubs",
			"brands": [
				{
					"id": 1135,
					"name": "Village Hotels & Leisure Clubs"
				}
			]
		},
		{
			"id": 86,
			"name": "Apex Hotels",
			"brands": [
				{
					"id": 1154,
					"name": "Apex Hotels"
				}
			]
		},
		{
			"id": 87,
			"name": "Apogia Hotel Group",
			"brands": [
				{
					"id": 1155,
					"name": "Apogia Hotel Group"
				}
			]
		},
		{
			"id": 88,
			"name": "Appart'City",
			"brands": [
				{
					"id": 1156,
					"name": "Appart'City"
				}
			]
		},
		{
			"id": 91,
			"name": "Aqua Hotel Grup",
			"brands": [
				{
					"id": 1159,
					"name": "AQUA Hotel Grup"
				}
			]
		},
		{
			"id": 97,
			"name": "Arp-Hansen Hotel Group",
			"brands": [
				{
					"id": 1165,
					"name": "Arp-Hansen Hotel Group"
				},
				{
					"id": 5882,
					"name": "Wakeup"
				}
			]
		},
		{
			"id": 98,
			"name": "Astotel",
			"brands": [
				{
					"id": 1166,
					"name": "Astotel"
				}
			]
		},
		{
			"id": 103,
			"name": "Austria Trend Hotels",
			"brands": [
				{
					"id": 1171,
					"name": "Austria Trend Hotels"
				}
			]
		},
		{
			"id": 107,
			"name": "B&B Hotels",
			"brands": [
				{
					"id": 1175,
					"name": "B&B Hotels"
				}
			]
		},
		{
			"id": 109,
			"name": "Baglioni Hotels",
			"brands": [
				{
					"id": 1177,
					"name": "Baglioni Hotels"
				}
			]
		},
		{
			"id": 111,
			"name": "Barata Hotel Group",
			"brands": [
				{
					"id": 1183,
					"name": "Barata Hotel Group"
				}
			]
		},
		{
			"id": 112,
			"name": "Barceló Hotel Group",
			"brands": [
				{
					"id": 1184,
					"name": "Barceló Hotels & Resorts"
				},
				{
					"id": 5876,
					"name": "Occidental Hotels and Resorts by Barcelo Hotel Group"
				},
				{
					"id": 6670,
					"name": "Allegro Hotels Resorts by Barcelo Hotel Group"
				},
				{
					"id": 6671,
					"name": "Royal Hideway Luxury Hotels by Barcelo Hotel Group"
				}
			]
		},
		{
			"id": 119,
			"name": "Vila Gale",
			"brands": [
				{
					"id": 1191,
					"name": "Vila Gale"
				}
			]
		},
		{
			"id": 120,
			"name": "Vienna House",
			"brands": [
				{
					"id": 1192,
					"name": "Vienna House"
				}
			]
		},
		{
			"id": 125,
			"name": "Valentin Hotels",
			"brands": [
				{
					"id": 1197,
					"name": "Valentin Hotels"
				}
			]
		},
		{
			"id": 126,
			"name": "Valamar",
			"brands": [
				{
					"id": 1198,
					"name": "Valamar"
				}
			]
		},
		{
			"id": 129,
			"name": "Macdonald Hotels & Resorts",
			"brands": [
				{
					"id": 1201,
					"name": "Macdonald Hotels & Resorts"
				}
			]
		},
		{
			"id": 132,
			"name": "Falkensteiner Hotels & Residences",
			"brands": [
				{
					"id": 1204,
					"name": "Falkensteiner Hotels & Residences"
				}
			]
		},
		{
			"id": 134,
			"name": "Maistra Hospitality Group",
			"brands": [
				{
					"id": 1206,
					"name": "Maistra Hospitality Group"
				}
			]
		},
		{
			"id": 137,
			"name": "Gruppo UNA",
			"brands": [
				{
					"id": 13926,
					"name": "Gruppo UNA"
				}
			]
		},
		{
			"id": 145,
			"name": "Palladium Hotel Group",
			"brands": [
				{
					"id": 1218,
					"name": "Palladium Hotel Group"
				},
				{
					"id": 14347,
					"name": "Ayre Hoteles"
				},
				{
					"id": 14348,
					"name": "Only You Hoteles"
				}
			]
		},
		{
			"id": 146,
			"name": "First Hotels",
			"brands": [
				{
					"id": 1219,
					"name": "First Hotels"
				},
				{
					"id": 14328,
					"name": "First Hotels Non branded"
				},
				{
					"id": 14663,
					"name": "by First Hotels"
				}
			]
		},
		{
			"id": 151,
			"name": "Islandshotel",
			"brands": [
				{
					"id": 1224,
					"name": "Islandshotel"
				}
			]
		},
		{
			"id": 153,
			"name": "H-Hotels.com",
			"brands": [
				{
					"id": 1226,
					"name": "H-Hotels.com"
				}
			]
		},
		{
			"id": 159,
			"name": "Maritim",
			"brands": [
				{
					"id": 1231,
					"name": "Maritim"
				}
			]
		},
		{
			"id": 161,
			"name": "Martin’s Hotels",
			"brands": [
				{
					"id": 1233,
					"name": "Martins"
				}
			]
		},
		{
			"id": 166,
			"name": "Medplaya",
			"brands": [
				{
					"id": 1238,
					"name": "Medplaya"
				}
			]
		},
		{
			"id": 176,
			"name": "Millennium Hotels and Resorts",
			"brands": [
				{
					"id": 1248,
					"name": "Millennium Hotels"
				},
				{
					"id": 1249,
					"name": "Copthorne Hotels"
				},
				{
					"id": 1944,
					"name": "Kingsgate Hotels"
				},
				{
					"id": 6224,
					"name": "Grand Millennium Hotels"
				},
				{
					"id": 6225,
					"name": "The Leng's Collection"
				}
			]
		},
		{
			"id": 181,
			"name": "Moevenpick Hotels & Resorts",
			"brands": [
				{
					"id": 1257,
					"name": "Mövenpick Hotels & Resorts"
				}
			]
		},
		{
			"id": 197,
			"name": "Oasis Hotels & Resorts",
			"brands": [
				{
					"id": 1272,
					"name": "Oasis Hotels & Resorts"
				},
				{
					"id": 13020,
					"name": "Grand Sens by Oasis"
				}
			]
		},
		{
			"id": 205,
			"name": "Morgans Hotel Group",
			"brands": [
				{
					"id": 1280,
					"name": "Morgans Hotel Group"
				}
			]
		},
		{
			"id": 207,
			"name": "Motel One",
			"brands": [
				{
					"id": 1282,
					"name": "Motel One"
				},
				{
					"id": 14263,
					"name": "The Cloud One"
				}
			]
		},
		{
			"id": 221,
			"name": "Grecotel",
			"brands": [
				{
					"id": 1297,
					"name": "Grecotel"
				}
			]
		},
		{
			"id": 223,
			"name": "Greene King's Pubs & Hotels",
			"brands": [
				{
					"id": 1299,
					"name": "Greene King's Pubs & Hotels"
				}
			]
		},
		{
			"id": 232,
			"name": "Bensaude Hotels Collection",
			"brands": [
				{
					"id": 1308,
					"name": "Bensaude Hotels Collection"
				}
			]
		},
		{
			"id": 233,
			"name": "Bespoke Hotels",
			"brands": [
				{
					"id": 1309,
					"name": "Bespoke Hotels"
				}
			]
		},
		{
			"id": 237,
			"name": "OCA Hotels",
			"brands": [
				{
					"id": 1313,
					"name": "OCA Hotels"
				}
			]
		},
		{
			"id": 240,
			"name": "Vibra Hotels",
			"brands": [
				{
					"id": 1316,
					"name": "Vibra Hotels"
				}
			]
		},
		{
			"id": 248,
			"name": "Omni Hotels",
			"brands": [
				{
					"id": 1324,
					"name": "Omni Hotels"
				}
			]
		},
		{
			"id": 249,
			"name": "Port Hotels",
			"brands": [
				{
					"id": 1325,
					"name": "Port Hotels"
				}
			]
		},
		{
			"id": 252,
			"name": "Belmond",
			"brands": [
				{
					"id": 1328,
					"name": "Belmond"
				}
			]
		},
		{
			"id": 253,
			"name": "Othon",
			"brands": [
				{
					"id": 1329,
					"name": "Othon"
				}
			]
		},
		{
			"id": 260,
			"name": "Grupotel",
			"brands": [
				{
					"id": 1338,
					"name": "Grupotel"
				}
			]
		},
		{
			"id": 269,
			"name": "Pestana Hotel & Resorts",
			"brands": [
				{
					"id": 1348,
					"name": "Pestana Hotel & Resorts"
				}
			]
		},
		{
			"id": 272,
			"name": "H10 Hotels",
			"brands": [
				{
					"id": 1351,
					"name": "H10 Hotels"
				}
			]
		},
		{
			"id": 274,
			"name": "Pierre & Vacances",
			"brands": [
				{
					"id": 1353,
					"name": "Pierre & Vacances Résidences"
				},
				{
					"id": 2604,
					"name": "Hotels Latitudes"
				},
				{
					"id": 2605,
					"name": "Pierre & Vacances Premium"
				},
				{
					"id": 3969,
					"name": "Pierre & Vacances Villages"
				},
				{
					"id": 12743,
					"name": "Pierre & Vacances Hôtel"
				},
				{
					"id": 13871,
					"name": "Maeva Sélection"
				}
			]
		},
		{
			"id": 278,
			"name": "Senator Hotels & Resort",
			"brands": [
				{
					"id": 1357,
					"name": "Senator Hotels & Resort"
				}
			]
		},
		{
			"id": 284,
			"name": "Brit Hotel",
			"brands": [
				{
					"id": 7895,
					"name": "Brit Hotel"
				}
			]
		},
		{
			"id": 285,
			"name": "Britannia Hotels",
			"brands": [
				{
					"id": 1364,
					"name": "Britannia Hotels"
				}
			]
		},
		{
			"id": 311,
			"name": "Hotelatelier",
			"brands": [
				{
					"id": 1391,
					"name": "Petit Palace Hoteles"
				},
				{
					"id": 12360,
					"name": "ICON"
				}
			]
		},
		{
			"id": 316,
			"name": "Premier Inn",
			"brands": [
				{
					"id": 1396,
					"name": "Premier Inn"
				}
			]
		},
		{
			"id": 323,
			"name": "Principal Hotel Company",
			"brands": [
				{
					"id": 1403,
					"name": "Principal Hotel Company"
				},
				{
					"id": 8600,
					"name": "De Vere"
				},
				{
					"id": 8601,
					"name": "Principal"
				}
			]
		},
		{
			"id": 324,
			"name": "Prinsotel",
			"brands": [
				{
					"id": 1404,
					"name": "Prinsotel"
				}
			]
		},
		{
			"id": 328,
			"name": "Protur Hotels",
			"brands": [
				{
					"id": 1408,
					"name": "Protur Hotels"
				}
			]
		},
		{
			"id": 333,
			"name": "Qubus Hotel",
			"brands": [
				{
					"id": 1413,
					"name": "Qubus Hotel"
				}
			]
		},
		{
			"id": 336,
			"name": "Thon Hotels",
			"brands": [
				{
					"id": 1416,
					"name": "Thon Hotels"
				}
			]
		},
		{
			"id": 342,
			"name": "Clermont Hotel Group",
			"brands": [
				{
					"id": 1422,
					"name": "Thistle"
				},
				{
					"id": 6051,
					"name": "Every Hotels"
				},
				{
					"id": 6052,
					"name": "Amba Hotels"
				},
				{
					"id": 9264,
					"name": "GLH"
				},
				{
					"id": 9265,
					"name": "Thistle Express"
				}
			]
		},
		{
			"id": 346,
			"name": "Rafaelhoteles",
			"brands": [
				{
					"id": 1428,
					"name": "Rafaelhoteles"
				}
			]
		},
		{
			"id": 348,
			"name": "Hospes",
			"brands": [
				{
					"id": 1430,
					"name": "Hospes"
				}
			]
		},
		{
			"id": 359,
			"name": "Relais & Châteaux",
			"brands": [
				{
					"id": 1440,
					"name": "Relais & Châteaux"
				}
			]
		},
		{
			"id": 360,
			"name": "THB Hotels",
			"brands": [
				{
					"id": 1441,
					"name": "THB Hotels"
				}
			]
		},
		{
			"id": 370,
			"name": "Reside Etudes Apparthotels",
			"brands": [
				{
					"id": 9326,
					"name": "Residhome Apparthotel"
				},
				{
					"id": 9327,
					"name": "Séjours & Affaires Apparthotel"
				},
				{
					"id": 9328,
					"name": "Relais Spa"
				}
			]
		},
		{
			"id": 375,
			"name": "RH Hoteles",
			"brands": [
				{
					"id": 1456,
					"name": "RH Hoteles"
				}
			]
		},
		{
			"id": 380,
			"name": "Roc Hotels",
			"brands": [
				{
					"id": 1461,
					"name": "Roc Hotels"
				}
			]
		},
		{
			"id": 381,
			"name": "Rocco Forte Hotels",
			"brands": [
				{
					"id": 1462,
					"name": "Rocco Forte Hotels"
				}
			]
		},
		{
			"id": 386,
			"name": "Room Mate",
			"brands": [
				{
					"id": 1467,
					"name": "Room Mate"
				}
			]
		},
		{
			"id": 389,
			"name": "Rotana Hotel Group",
			"brands": [
				{
					"id": 1470,
					"name": "Rotana Hotels & Resorts"
				},
				{
					"id": 5926,
					"name": "Centro Hotels by Rotana"
				},
				{
					"id": 5927,
					"name": "Rayhaan Hotels & Resorts by Rotana"
				},
				{
					"id": 5928,
					"name": "Arjaan Hotel Apartments by Rotana"
				},
				{
					"id": 14261,
					"name": "Edge by Rotana"
				}
			]
		},
		{
			"id": 391,
			"name": "Selenta Group",
			"brands": [
				{
					"id": 1472,
					"name": "Selenta Group"
				}
			]
		},
		{
			"id": 394,
			"name": "Swiss Quality Hotels",
			"brands": [
				{
					"id": 1474,
					"name": "Swiss Quality Hotels"
				}
			]
		},
		{
			"id": 407,
			"name": "Catalonia Hotels & Resorts",
			"brands": [
				{
					"id": 1487,
					"name": "Catalonia Hotels & Resorts"
				}
			]
		},
		{
			"id": 411,
			"name": "H World International",
			"brands": [
				{
					"id": 1490,
					"name": "Steigenberger Hotels & Resorts"
				},
				{
					"id": 1491,
					"name": "IntercityHotels"
				},
				{
					"id": 12119,
					"name": "Jaz"
				},
				{
					"id": 12120,
					"name": "MAXX"
				},
				{
					"id": 12909,
					"name": "Zleep Hotels"
				}
			]
		},
		{
			"id": 412,
			"name": "Hoteles Center",
			"brands": [
				{
					"id": 1492,
					"name": "Hoteles Center"
				}
			]
		},
		{
			"id": 414,
			"name": "Starhotels",
			"brands": [
				{
					"id": 1494,
					"name": "Starhotels"
				}
			]
		},
		{
			"id": 416,
			"name": "Magic Costa Blanca",
			"brands": [
				{
					"id": 1496,
					"name": "Magic Costa Blanca"
				}
			]
		},
		{
			"id": 420,
			"name": "Hoteles Globales",
			"brands": [
				{
					"id": 1500,
					"name": "Hoteles Globales"
				}
			]
		},
		{
			"id": 421,
			"name": "Sorell Hotels",
			"brands": [
				{
					"id": 1501,
					"name": "Sorell Hotels"
				}
			]
		},
		{
			"id": 430,
			"name": "Small Luxury Hotels of the World",
			"brands": [
				{
					"id": 1510,
					"name": "Small Luxury Hotels of the World"
				}
			]
		},
		{
			"id": 437,
			"name": "Shangri-La",
			"brands": [
				{
					"id": 1517,
					"name": "Shangri-La Group"
				},
				{
					"id": 2312,
					"name": "Traders Hotels"
				},
				{
					"id": 4384,
					"name": "Kerry Hotels by Shangri-La"
				},
				{
					"id": 4586,
					"name": "JEN by Shangri-La"
				}
			]
		},
		{
			"id": 440,
			"name": "Sercotel",
			"brands": [
				{
					"id": 1520,
					"name": "Sercotel "
				},
				{
					"id": 7448,
					"name": "Sercotel Hoteles"
				}
			]
		},
		{
			"id": 446,
			"name": "SB Hotels",
			"brands": [
				{
					"id": 1526,
					"name": "SB Hotels"
				}
			]
		},
		{
			"id": 447,
			"name": "Savoy Signature",
			"brands": [
				{
					"id": 1527,
					"name": "Savoy Signature"
				}
			]
		},
		{
			"id": 448,
			"name": "Sandton Hotels",
			"brands": [
				{
					"id": 1528,
					"name": "Sandton Hotels"
				}
			]
		},
		{
			"id": 449,
			"name": "SANA Hotels",
			"brands": [
				{
					"id": 1529,
					"name": "SANA Hotels"
				}
			]
		},
		{
			"id": 451,
			"name": "Port Blue",
			"brands": [
				{
					"id": 1531,
					"name": "Port Blue"
				}
			]
		},
		{
			"id": 453,
			"name": "IBB Hotels",
			"brands": [
				{
					"id": 1533,
					"name": "IBB Hotels"
				}
			]
		},
		{
			"id": 454,
			"name": "Iberotel Hotels & Resorts",
			"brands": [
				{
					"id": 1534,
					"name": "Iberotel Hotels & Resorts"
				}
			]
		},
		{
			"id": 456,
			"name": "Lopesan Hotel Group",
			"brands": [
				{
					"id": 1536,
					"name": "Lopesan Hotel Group"
				}
			]
		},
		{
			"id": 461,
			"name": "Insotel",
			"brands": [
				{
					"id": 1541,
					"name": "Insotel"
				}
			]
		},
		{
			"id": 482,
			"name": "Carlton Hotel Collection",
			"brands": [
				{
					"id": 1561,
					"name": "Carlton Hotel Collection"
				}
			]
		},
		{
			"id": 485,
			"name": "Faranda Hotels",
			"brands": [
				{
					"id": 1564,
					"name": "Faranda Hotels"
				}
			]
		},
		{
			"id": 488,
			"name": "Centerhotels",
			"brands": [
				{
					"id": 1567,
					"name": "Centerhotels"
				}
			]
		},
		{
			"id": 494,
			"name": "Hoteles Porcel",
			"brands": [
				{
					"id": 1573,
					"name": "Hoteles Porcel"
				}
			]
		},
		{
			"id": 497,
			"name": "Hoteles Santos",
			"brands": [
				{
					"id": 1576,
					"name": "Hoteles Santos"
				}
			]
		},
		{
			"id": 498,
			"name": "Hoteles Servigroup",
			"brands": [
				{
					"id": 1577,
					"name": "Hoteles Servi Group"
				}
			]
		},
		{
			"id": 499,
			"name": "Hoteles Silken",
			"brands": [
				{
					"id": 1578,
					"name": "Hoteles Silken"
				},
				{
					"id": 7267,
					"name": "AA Hoteles"
				}
			]
		},
		{
			"id": 500,
			"name": "Hoteles Var",
			"brands": [
				{
					"id": 1579,
					"name": "Hoteles Var"
				}
			]
		},
		{
			"id": 503,
			"name": "Teritoria ex. les Collectionneurs",
			"brands": [
				{
					"id": 1582,
					"name": "Teritoria"
				}
			]
		},
		{
			"id": 506,
			"name": "The Ascott Limited",
			"brands": [
				{
					"id": 1585,
					"name": "Citadines"
				},
				{
					"id": 1764,
					"name": "Somerset"
				},
				{
					"id": 1916,
					"name": "Ascott"
				},
				{
					"id": 8030,
					"name": "The Crest Collection"
				},
				{
					"id": 12505,
					"name": "lyf"
				},
				{
					"id": 14585,
					"name": "Preference"
				},
				{
					"id": 14620,
					"name": "Oakwood"
				},
				{
					"id": 14662,
					"name": "The Unlimited Collection"
				},
				{
					"id": 14773,
					"name": "Adoor Suites"
				},
				{
					"id": 14774,
					"name": "Fox Hotels"
				}
			]
		},
		{
			"id": 523,
			"name": "Classic British Hotels",
			"brands": [
				{
					"id": 1602,
					"name": "Classic British Hotels"
				}
			]
		},
		{
			"id": 539,
			"name": "Ilunion Hotels",
			"brands": [
				{
					"id": 1617,
					"name": "Ilunion Hotels"
				}
			]
		},
		{
			"id": 541,
			"name": "Coral Hotels",
			"brands": [
				{
					"id": 1619,
					"name": "Coral Hotels"
				}
			]
		},
		{
			"id": 542,
			"name": "Coral International Hotel Resort & Spas",
			"brands": [
				{
					"id": 13390,
					"name": "Coral International Hotel Resort & Spas"
				}
			]
		},
		{
			"id": 548,
			"name": "Evenia Hotels",
			"brands": [
				{
					"id": 1626,
					"name": "Evenia Hotels"
				}
			]
		},
		{
			"id": 549,
			"name": "K+K Hotels",
			"brands": [
				{
					"id": 1627,
					"name": "K+K Hotels"
				}
			]
		},
		{
			"id": 550,
			"name": "Keahotels",
			"brands": [
				{
					"id": 1628,
					"name": "Keahotels"
				}
			]
		},
		{
			"id": 551,
			"name": "Kempinski",
			"brands": [
				{
					"id": 1629,
					"name": "Kempinski"
				}
			]
		},
		{
			"id": 558,
			"name": "Danubius Hotels",
			"brands": [
				{
					"id": 1636,
					"name": "Danubius Hotels"
				},
				{
					"id": 12649,
					"name": "Ensana Hotels"
				}
			]
		},
		{
			"id": 563,
			"name": "Derby Hotels Collection",
			"brands": [
				{
					"id": 1640,
					"name": "Barcelona Apartment"
				},
				{
					"id": 2371,
					"name": "Derby Hotels"
				}
			]
		},
		{
			"id": 564,
			"name": "Design Hotels",
			"brands": [
				{
					"id": 1641,
					"name": "Design Hotels"
				}
			]
		},
		{
			"id": 568,
			"name": "Domina Hotels & Resorts",
			"brands": [
				{
					"id": 1646,
					"name": "Domina Hotels & Resorts"
				},
				{
					"id": 1870,
					"name": "Domina Hotel Group"
				}
			]
		},
		{
			"id": 573,
			"name": "Domus Hotels",
			"brands": [
				{
					"id": 1651,
					"name": "Domus Hotels"
				}
			]
		},
		{
			"id": 584,
			"name": "Liburnia Hotels & Villas",
			"brands": [
				{
					"id": 1662,
					"name": "Liburnia Hotels & Villas"
				}
			]
		},
		{
			"id": 586,
			"name": "Lindner Hotels & Resorts",
			"brands": [
				{
					"id": 1664,
					"name": "Lindner Hotels & Resorts"
				}
			]
		},
		{
			"id": 592,
			"name": "Louis Hotels",
			"brands": [
				{
					"id": 2647,
					"name": "Louis Hotels"
				}
			]
		},
		{
			"id": 597,
			"name": "Dusit Hotels & Resorts",
			"brands": [
				{
					"id": 1677,
					"name": "Dusit Hotels & Resorts"
				}
			]
		},
		{
			"id": 599,
			"name": "Logis International",
			"brands": [
				{
					"id": 1679,
					"name": "Logis Hôtels"
				}
			]
		},
		{
			"id": 602,
			"name": "Ecohoteles",
			"brands": [
				{
					"id": 1682,
					"name": "Ecohoteles"
				}
			]
		},
		{
			"id": 616,
			"name": "Hyatt",
			"brands": [
				{
					"id": 1696,
					"name": "Hyatt"
				},
				{
					"id": 3628,
					"name": "Park Hyatt"
				},
				{
					"id": 3629,
					"name": "Andaz"
				},
				{
					"id": 3630,
					"name": "Grand Hyatt"
				},
				{
					"id": 3631,
					"name": "Hyatt Regency"
				},
				{
					"id": 3632,
					"name": "Hyatt Place"
				},
				{
					"id": 3633,
					"name": "Hyatt House"
				},
				{
					"id": 3634,
					"name": "Hyatt Residence Club"
				},
				{
					"id": 5895,
					"name": "Hyatt Centric"
				},
				{
					"id": 5896,
					"name": "Hyatt Zilara"
				},
				{
					"id": 5897,
					"name": "Hyatt Ziva"
				},
				{
					"id": 7220,
					"name": "Unbound Collection by Hyatt"
				},
				{
					"id": 12639,
					"name": "Thompson Hotels"
				},
				{
					"id": 12699,
					"name": "Alila Hotels"
				},
				{
					"id": 12700,
					"name": "JdV by Hyatt"
				},
				{
					"id": 12701,
					"name": "Destination by Hyatt"
				},
				{
					"id": 13809,
					"name": "Alua Hotels & Resorts"
				},
				{
					"id": 13810,
					"name": "Breathless Resorts & Spa"
				},
				{
					"id": 13811,
					"name": "Dreams Resorts & Spa"
				},
				{
					"id": 13812,
					"name": "Now Resorts & Spa"
				},
				{
					"id": 13813,
					"name": "Secrets Resorts & Spa"
				},
				{
					"id": 13814,
					"name": "Sunscape Resorts"
				},
				{
					"id": 13815,
					"name": "Zoetry Wellness & Spa Resorts"
				},
				{
					"id": 14323,
					"name": "AluaSun"
				},
				{
					"id": 14324,
					"name": "AluaSoul"
				},
				{
					"id": 14503,
					"name": "Dream by Hyatt"
				},
				{
					"id": 14560,
					"name": "Impression by Secrets"
				},
				{
					"id": 14750,
					"name": "Caption by Hyatt"
				},
				{
					"id": 14752,
					"name": "Hyatt Vivid"
				}
			]
		},
		{
			"id": 625,
			"name": "Montcalm Collection",
			"brands": [
				{
					"id": 1705,
					"name": "Montcalm Collection"
				}
			]
		},
		{
			"id": 626,
			"name": "SEETELHOTELS",
			"brands": [
				{
					"id": 1706,
					"name": "SEETELHOTELS"
				}
			]
		},
		{
			"id": 633,
			"name": "Scandic Hotels",
			"brands": [
				{
					"id": 1715,
					"name": "Scandic"
				}
			]
		},
		{
			"id": 646,
			"name": "Mar Hotels",
			"brands": [
				{
					"id": 1728,
					"name": "Mar Hotels"
				}
			]
		},
		{
			"id": 653,
			"name": "Dorint Hotels",
			"brands": [
				{
					"id": 1737,
					"name": "Dorint Hotels"
				}
			]
		},
		{
			"id": 657,
			"name": "Loews",
			"brands": [
				{
					"id": 1748,
					"name": "Loews Hotels"
				}
			]
		},
		{
			"id": 668,
			"name": "Jumeirah",
			"brands": [
				{
					"id": 1763,
					"name": "Jumeirah"
				},
				{
					"id": 10248,
					"name": "Zabeel House"
				},
				{
					"id": 10249,
					"name": "Zabeel House MINI"
				}
			]
		},
		{
			"id": 669,
			"name": "Best Hotels",
			"brands": [
				{
					"id": 1765,
					"name": "Best Hotels"
				},
				{
					"id": 12838,
					"name": "Serenade Hotels"
				}
			]
		},
		{
			"id": 675,
			"name": "Fattal Jurys",
			"brands": [
				{
					"id": 1771,
					"name": "Jurys Inns"
				}
			]
		},
		{
			"id": 676,
			"name": "The Leading Hotels of the World",
			"brands": [
				{
					"id": 1772,
					"name": "The Leading Hotels of the World"
				}
			]
		},
		{
			"id": 678,
			"name": "Aquila Hotels",
			"brands": [
				{
					"id": 1774,
					"name": "Aquila Hotels"
				}
			]
		},
		{
			"id": 680,
			"name": "Sokos Hotels",
			"brands": [
				{
					"id": 14133,
					"name": "Sokos Hotels"
				}
			]
		},
		{
			"id": 688,
			"name": "La Quinta Inn and Suites",
			"brands": [
				{
					"id": 1787,
					"name": "La Quinta Inn and Suites"
				}
			]
		},
		{
			"id": 692,
			"name": "MS Hoteles",
			"brands": [
				{
					"id": 1791,
					"name": "MS Hoteles"
				}
			]
		},
		{
			"id": 706,
			"name": "EIX Hotels",
			"brands": [
				{
					"id": 1804,
					"name": "EIX Hotels"
				}
			]
		},
		{
			"id": 725,
			"name": "Elite Hotels of Sweden",
			"brands": [
				{
					"id": 1825,
					"name": "Elite Hotels of Sweden"
				}
			]
		},
		{
			"id": 738,
			"name": "Red Roof Inn",
			"brands": [
				{
					"id": 1839,
					"name": "Red Roof Inn"
				},
				{
					"id": 9239,
					"name": "Red Roof PLUS"
				},
				{
					"id": 11012,
					"name": "HomeTowne"
				},
				{
					"id": 13179,
					"name": "The Red Collection"
				}
			]
		},
		{
			"id": 739,
			"name": "Geovita",
			"brands": [
				{
					"id": 1840,
					"name": "Geovita"
				}
			]
		},
		{
			"id": 743,
			"name": "Leonardo Hotels & Resorts",
			"brands": [
				{
					"id": 1844,
					"name": "Leonardo Hotels"
				},
				{
					"id": 5943,
					"name": "Leonardo Royal Hotels"
				},
				{
					"id": 5944,
					"name": "Leonardo Boutique Hotels"
				},
				{
					"id": 5980,
					"name": "Leonardo Inn"
				},
				{
					"id": 12223,
					"name": "Leonardo Hotels & Resorts"
				},
				{
					"id": 14305,
					"name": "NYX Hotels by Leonardo Hotels"
				}
			]
		},
		{
			"id": 747,
			"name": "Sonesta International Hotels Corporation",
			"brands": [
				{
					"id": 13459,
					"name": "Royal Sonesta"
				},
				{
					"id": 13462,
					"name": "Sonesta Hotel & Resorts"
				},
				{
					"id": 13463,
					"name": "Sonesta ES Suites"
				},
				{
					"id": 13464,
					"name": "Sonesta Select"
				},
				{
					"id": 13465,
					"name": "Sonesta Simply Suites"
				},
				{
					"id": 13508,
					"name": "Sonesta International "
				},
				{
					"id": 13638,
					"name": "Sonesta Hotels"
				},
				{
					"id": 13791,
					"name": "Country Hearth Inn"
				},
				{
					"id": 13792,
					"name": "GuestHouse Inn & Suites"
				},
				{
					"id": 13795,
					"name": "Signature Hotels"
				},
				{
					"id": 14090,
					"name": "America Best Value Inn"
				},
				{
					"id": 14092,
					"name": "Canada Best Value Inn"
				},
				{
					"id": 14093,
					"name": "Hotel RL"
				},
				{
					"id": 14094,
					"name": "Jameson Inn"
				},
				{
					"id": 14095,
					"name": "Knights Inn"
				},
				{
					"id": 14096,
					"name": "Knights Inn and Suites"
				},
				{
					"id": 14097,
					"name": "Lexington by Red Lion"
				},
				{
					"id": 14098,
					"name": "Red Lion Hotels"
				},
				{
					"id": 14099,
					"name": "Red Lion Inn & Suites"
				},
				{
					"id": 14100,
					"name": "GuestHouse Extended Stay"
				},
				{
					"id": 14101,
					"name": "Signature Inn"
				},
				{
					"id": 14541,
					"name": "Classico, A Sonesta Collection"
				},
				{
					"id": 14542,
					"name": "MOD, A Sonesta Collection"
				}
			]
		},
		{
			"id": 749,
			"name": "Meininger Hotels",
			"brands": [
				{
					"id": 1850,
					"name": "Meininger Hotels"
				}
			]
		},
		{
			"id": 755,
			"name": "The Three Corners Hotels & resorts",
			"brands": [
				{
					"id": 1858,
					"name": "The Three Corners Hotels & resorts"
				}
			]
		},
		{
			"id": 759,
			"name": "Precise Hotels & Resorts",
			"brands": [
				{
					"id": 1862,
					"name": " Precise Hotels & Resorts"
				}
			]
		},
		{
			"id": 761,
			"name": "Hoteles Elba",
			"brands": [
				{
					"id": 1864,
					"name": "Hoteles Elba"
				}
			]
		},
		{
			"id": 764,
			"name": "Mac Hotels",
			"brands": [
				{
					"id": 1868,
					"name": "Mac Hotels"
				},
				{
					"id": 14356,
					"name": "Pure Salt Luxury Hotels"
				}
			]
		},
		{
			"id": 765,
			"name": "Grupo Poseidón",
			"brands": [
				{
					"id": 1869,
					"name": "Grupo Poseidón"
				}
			]
		},
		{
			"id": 768,
			"name": "Corinthia Hotels",
			"brands": [
				{
					"id": 1873,
					"name": "Corinthia Hotels"
				}
			]
		},
		{
			"id": 770,
			"name": "Far East Hospitality",
			"brands": [
				{
					"id": 1875,
					"name": "Far East Hospitality"
				}
			]
		},
		{
			"id": 774,
			"name": "Luna Hoteis",
			"brands": [
				{
					"id": 1879,
					"name": "Luna Hoteis"
				}
			]
		},
		{
			"id": 778,
			"name": "Travelodge",
			"brands": [
				{
					"id": 1883,
					"name": "Travelodge"
				}
			]
		},
		{
			"id": 807,
			"name": "Divan Hotels",
			"brands": [
				{
					"id": 1914,
					"name": "Divan Hotels"
				}
			]
		},
		{
			"id": 810,
			"name": "Sallés Hotels",
			"brands": [
				{
					"id": 1918,
					"name": "Sallés Hotels"
				}
			]
		},
		{
			"id": 814,
			"name": "Iberostar Hotels & Resorts",
			"brands": [
				{
					"id": 1922,
					"name": "Iberostar Hotels & Resorts"
				},
				{
					"id": 3953,
					"name": "Olé Hoteles"
				},
				{
					"id": 5976,
					"name": "Iberostar The Grand Collection"
				}
			]
		},
		{
			"id": 815,
			"name": "Worldwide Hotels",
			"brands": [
				{
					"id": 1923,
					"name": "Hotel 81"
				},
				{
					"id": 3367,
					"name": "V Hotel Management Pte Ltd"
				},
				{
					"id": 3368,
					"name": "Venue Hotel"
				},
				{
					"id": 6620,
					"name": "Hotel Boss"
				},
				{
					"id": 14567,
					"name": "Hotel Mi"
				},
				{
					"id": 14568,
					"name": "ICON Hotel"
				}
			]
		},
		{
			"id": 823,
			"name": "Caesars Entertainment",
			"brands": [
				{
					"id": 1932,
					"name": "Caesars Entertainment"
				}
			]
		},
		{
			"id": 832,
			"name": "Parc Hotels",
			"brands": [
				{
					"id": 1945,
					"name": "Parc Hotels"
				}
			]
		},
		{
			"id": 833,
			"name": "Majestic Hotel Group",
			"brands": [
				{
					"id": 1946,
					"name": "Majestic Hotel Group"
				}
			]
		},
		{
			"id": 849,
			"name": "Chincherini Holiday",
			"brands": [
				{
					"id": 1962,
					"name": "Chincherini Holiday"
				}
			]
		},
		{
			"id": 853,
			"name": "Fragrance Hotel Management",
			"brands": [
				{
					"id": 1966,
					"name": "Fragrance Hotel"
				}
			]
		},
		{
			"id": 854,
			"name": "Residhotel",
			"brands": [
				{
					"id": 1967,
					"name": "Residhotel"
				}
			]
		},
		{
			"id": 857,
			"name": "Premier Hotels & Resorts",
			"brands": [
				{
					"id": 1970,
					"name": "Premier Hotels & Resorts"
				}
			]
		},
		{
			"id": 864,
			"name": "pentahotels",
			"brands": [
				{
					"id": 1977,
					"name": "pentahotels"
				}
			]
		},
		{
			"id": 865,
			"name": "Mellow Mood Hotels",
			"brands": [
				{
					"id": 1978,
					"name": "Mellow Mood Hotels"
				}
			]
		},
		{
			"id": 868,
			"name": "Orea Hotels",
			"brands": [
				{
					"id": 1981,
					"name": "Orea Hotels"
				}
			]
		},
		{
			"id": 871,
			"name": "Pan Pacific Hotels Group",
			"brands": [
				{
					"id": 1987,
					"name": "Pan Pacific Hotels & Resorts"
				},
				{
					"id": 2531,
					"name": "Parkroyal Hotels & Resorts"
				},
				{
					"id": 12911,
					"name": "PARKROYAL COLLECTION"
				},
				{
					"id": 14120,
					"name": "Pan Pacific Serviced Suites"
				},
				{
					"id": 14121,
					"name": "PARKROYAL Serviced Suites"
				}
			]
		},
		{
			"id": 885,
			"name": "JS Hotels",
			"brands": [
				{
					"id": 2003,
					"name": "JS Hotels"
				}
			]
		},
		{
			"id": 886,
			"name": "Camino Real Hotels",
			"brands": [
				{
					"id": 2004,
					"name": "Quinta Real"
				},
				{
					"id": 12026,
					"name": "Camino Real"
				},
				{
					"id": 12027,
					"name": "Real Inn"
				}
			]
		},
		{
			"id": 889,
			"name": "AMW Hotele",
			"brands": [
				{
					"id": 2008,
					"name": "AMW Hotele"
				}
			]
		},
		{
			"id": 893,
			"name": "Mandarin Oriental Hotel Group",
			"brands": [
				{
					"id": 2012,
					"name": "Mandarin Oriental"
				}
			]
		},
		{
			"id": 900,
			"name": "Langham Hospitality Group",
			"brands": [
				{
					"id": 2019,
					"name": "Langham Hotels International"
				},
				{
					"id": 14130,
					"name": "Cordis"
				},
				{
					"id": 14131,
					"name": "Eaton"
				}
			]
		},
		{
			"id": 901,
			"name": "The Indian Hotels Co Ltd",
			"brands": [
				{
					"id": 2020,
					"name": "The Indian Hotels Co Ltd"
				},
				{
					"id": 4015,
					"name": "Vivanta by Taj"
				},
				{
					"id": 4463,
					"name": "The Gateway Hotel & Resort"
				},
				{
					"id": 12293,
					"name": "SeleQtions"
				}
			]
		},
		{
			"id": 903,
			"name": "NOVUM Hospitality",
			"brands": [
				{
					"id": 10820,
					"name": "NOVUM Hotels"
				},
				{
					"id": 10821,
					"name": "The niu"
				},
				{
					"id": 10822,
					"name": "NOVUM Select Hotels"
				},
				{
					"id": 10825,
					"name": "NOVUM Hospitality"
				},
				{
					"id": 14579,
					"name": "Acora"
				},
				{
					"id": 14580,
					"name": "Yggotel"
				}
			]
		},
		{
			"id": 911,
			"name": "Centara Hotels and Resorts",
			"brands": [
				{
					"id": 2030,
					"name": "Centara Hotels and Resorts"
				}
			]
		},
		{
			"id": 912,
			"name": "Sowell",
			"brands": [
				{
					"id": 2031,
					"name": "Sowell"
				}
			]
		},
		{
			"id": 919,
			"name": "Nacional Inn",
			"brands": [
				{
					"id": 2038,
					"name": "Nacional Inn"
				}
			]
		},
		{
			"id": 922,
			"name": "Hoposa Hotels",
			"brands": [
				{
					"id": 2042,
					"name": "Hoposa"
				}
			]
		},
		{
			"id": 925,
			"name": "Huazhu Hotels",
			"brands": [
				{
					"id": 11809,
					"name": "Huazhu Hotels"
				}
			]
		},
		{
			"id": 942,
			"name": "Centrepoints",
			"brands": [
				{
					"id": 2062,
					"name": "Centrepoints"
				}
			]
		},
		{
			"id": 948,
			"name": "Compass Hospitality",
			"brands": [
				{
					"id": 2068,
					"name": "Compass Hospitality"
				}
			]
		},
		{
			"id": 950,
			"name": "Gloria Hotels & Resorts",
			"brands": [
				{
					"id": 2070,
					"name": "Gloria Hotels & Resorts"
				}
			]
		},
		{
			"id": 954,
			"name": "Berjaya Hotels & Resorts",
			"brands": [
				{
					"id": 2073,
					"name": "Berjaya Hotels & Resorts"
				}
			]
		},
		{
			"id": 958,
			"name": "Holiday Villa Hotels & Resorts",
			"brands": [
				{
					"id": 2077,
					"name": "Holiday Villa Hotels & Resorts"
				}
			]
		},
		{
			"id": 959,
			"name": "Sunway Hotels & Resorts",
			"brands": [
				{
					"id": 2079,
					"name": "Sunway Hotels & Resorts"
				}
			]
		},
		{
			"id": 971,
			"name": "South Beach Group",
			"brands": [
				{
					"id": 2092,
					"name": "South Beach Group"
				}
			]
		},
		{
			"id": 973,
			"name": "Palace Resorts",
			"brands": [
				{
					"id": 2094,
					"name": "Palace Resorts"
				},
				{
					"id": 14637,
					"name": "Baglioni Hotel"
				}
			]
		},
		{
			"id": 974,
			"name": "Diamond Resorts",
			"brands": [
				{
					"id": 2095,
					"name": "Diamond Resorts"
				}
			]
		},
		{
			"id": 980,
			"name": "Oberoi Hotels & Resorts",
			"brands": [
				{
					"id": 2101,
					"name": "Oberoi Hotels & Resorts"
				}
			]
		},
		{
			"id": 984,
			"name": "Blau Hotels & Resorts",
			"brands": [
				{
					"id": 2105,
					"name": "Blau Hotels & Resorts"
				}
			]
		},
		{
			"id": 993,
			"name": "Continental Hotels",
			"brands": [
				{
					"id": 2120,
					"name": "Continental Hotels"
				}
			]
		},
		{
			"id": 994,
			"name": "Swiss-Belhotel International",
			"brands": [
				{
					"id": 2121,
					"name": "Swiss-Belhotel International"
				},
				{
					"id": 12949,
					"name": "Hotel Ciputra"
				},
				{
					"id": 12950,
					"name": "Swiss-Belboutique"
				},
				{
					"id": 12951,
					"name": "Swiss-Belexpress"
				},
				{
					"id": 12952,
					"name": "Swiss-Belhotel"
				},
				{
					"id": 12953,
					"name": "Swiss-Belinn"
				},
				{
					"id": 12954,
					"name": "Swiss-Belresidences"
				},
				{
					"id": 12955,
					"name": "Swiss-Belresort"
				},
				{
					"id": 12956,
					"name": "Swiss-Inn"
				},
				{
					"id": 12957,
					"name": "Zest Hotel"
				},
				{
					"id": 14756,
					"name": "Grand Swiss-Belhotel"
				},
				{
					"id": 14757,
					"name": "MAUA"
				},
				{
					"id": 14758,
					"name": "Swiss-Belcourt"
				},
				{
					"id": 14759,
					"name": "Swiss-Belsuites"
				}
			]
		},
		{
			"id": 1004,
			"name": "Frasers Hospitality",
			"brands": [
				{
					"id": 5883,
					"name": "Fraser Suites"
				},
				{
					"id": 5884,
					"name": "Fraser Place"
				},
				{
					"id": 5885,
					"name": "Fraser Residence"
				},
				{
					"id": 5886,
					"name": "Modena by Fraser"
				},
				{
					"id": 5887,
					"name": "Capri by Fraser"
				},
				{
					"id": 10696,
					"name": "Frasers Hospitality"
				},
				{
					"id": 10697,
					"name": "Park Hotel"
				}
			]
		},
		{
			"id": 1006,
			"name": "Royal Orchid Hotels",
			"brands": [
				{
					"id": 2133,
					"name": "Royal Orchid Hotels"
				}
			]
		},
		{
			"id": 1008,
			"name": "YIT Hoteles",
			"brands": [
				{
					"id": 2135,
					"name": "YIT Hoteles"
				}
			]
		},
		{
			"id": 1018,
			"name": "Extended Stay America",
			"brands": [
				{
					"id": 2145,
					"name": "Extended Stay America Suites"
				},
				{
					"id": 14238,
					"name": "Extended Stay America Select Suites"
				},
				{
					"id": 14292,
					"name": "Extended Stay America Premier Suites"
				},
				{
					"id": 14293,
					"name": "Extended Stay America"
				}
			]
		},
		{
			"id": 1020,
			"name": "Blau Parc Hotels",
			"brands": [
				{
					"id": 2147,
					"name": "Blau Parc Hotels"
				}
			]
		},
		{
			"id": 1032,
			"name": "Windsor Hotels",
			"brands": [
				{
					"id": 2160,
					"name": "Windsor"
				}
			]
		},
		{
			"id": 1037,
			"name": "Acta Hotels",
			"brands": [
				{
					"id": 2165,
					"name": "Acta Hotels"
				}
			]
		},
		{
			"id": 1043,
			"name": "TFE Hotels",
			"brands": [
				{
					"id": 2172,
					"name": "Medina"
				},
				{
					"id": 2173,
					"name": "Adina"
				},
				{
					"id": 2174,
					"name": "Vibe Hotels"
				},
				{
					"id": 2175,
					"name": "Travelodge Hotels"
				},
				{
					"id": 3711,
					"name": "Rendezous Hotels"
				},
				{
					"id": 9691,
					"name": "TFE Hotels Collection"
				}
			]
		},
		{
			"id": 1055,
			"name": "Event Hospitality & Entertainment",
			"brands": [
				{
					"id": 2189,
					"name": "Rydges"
				},
				{
					"id": 13684,
					"name": "QT"
				},
				{
					"id": 13685,
					"name": "Atura"
				},
				{
					"id": 13686,
					"name": "Independent Collection"
				}
			]
		},
		{
			"id": 1057,
			"name": "Turim Hotels",
			"brands": [
				{
					"id": 2191,
					"name": "Turim Hotels Group"
				}
			]
		},
		{
			"id": 1059,
			"name": "Quest Apartment Hotels",
			"brands": [
				{
					"id": 2193,
					"name": "Quest Apartment Hotels"
				}
			]
		},
		{
			"id": 1060,
			"name": "Grupo Intur",
			"brands": [
				{
					"id": 2194,
					"name": "Grupo Intur"
				}
			]
		},
		{
			"id": 1064,
			"name": "Azimut",
			"brands": [
				{
					"id": 2198,
					"name": "Azimut"
				}
			]
		},
		{
			"id": 1066,
			"name": "GSM Hoteles",
			"brands": [
				{
					"id": 2200,
					"name": "GSM Hoteles"
				}
			]
		},
		{
			"id": 1067,
			"name": "Atlas Hotels",
			"brands": [
				{
					"id": 2201,
					"name": "Atlas Hotels"
				}
			]
		},
		{
			"id": 1069,
			"name": "Prima Hotels Israel",
			"brands": [
				{
					"id": 2203,
					"name": "Prima Hotels Israel"
				}
			]
		},
		{
			"id": 1073,
			"name": "Philian Hotels",
			"brands": [
				{
					"id": 2208,
					"name": "Philian Hotels"
				}
			]
		},
		{
			"id": 1077,
			"name": "Grupo Posadas",
			"brands": [
				{
					"id": 2213,
					"name": "Fiesta Americana Hotels and Resorts"
				},
				{
					"id": 11957,
					"name": "Fiesta Inn"
				},
				{
					"id": 11958,
					"name": "Gamma Hotels"
				},
				{
					"id": 11968,
					"name": "Grand Fiesta Americana"
				},
				{
					"id": 11969,
					"name": "Live Aqua"
				},
				{
					"id": 11970,
					"name": "Grupo Posadas - One Hotels"
				},
				{
					"id": 13447,
					"name": "Curamoria Collection"
				},
				{
					"id": 13694,
					"name": "IOH"
				}
			]
		},
		{
			"id": 1082,
			"name": "Bristol Hotéis and Resorts",
			"brands": [
				{
					"id": 2219,
					"name": "Bristol Hotéis and Resorts"
				}
			]
		},
		{
			"id": 1083,
			"name": "Promenade",
			"brands": [
				{
					"id": 2220,
					"name": "Promenade"
				}
			]
		},
		{
			"id": 1099,
			"name": "Southern Sun Group",
			"brands": [
				{
					"id": 12597,
					"name": "Garden Court"
				},
				{
					"id": 12599,
					"name": "Southern Sun Hotels"
				},
				{
					"id": 12600,
					"name": "StayEasy"
				},
				{
					"id": 12601,
					"name": "Sun1"
				},
				{
					"id": 12602,
					"name": "SunSquare"
				},
				{
					"id": 13041,
					"name": "hi Hotels"
				},
				{
					"id": 13042,
					"name": "Resorts"
				}
			]
		},
		{
			"id": 1102,
			"name": "Isrotel Hotels & Resorts",
			"brands": [
				{
					"id": 2239,
					"name": "Isrotel Hotels & Resorts"
				}
			]
		},
		{
			"id": 1107,
			"name": "Hoteles H2",
			"brands": [
				{
					"id": 2245,
					"name": "Hoteles H2"
				}
			]
		},
		{
			"id": 1114,
			"name": "Staying Valencia",
			"brands": [
				{
					"id": 2252,
					"name": "Stayingvalencia"
				}
			]
		},
		{
			"id": 1122,
			"name": "Maybourne",
			"brands": [
				{
					"id": 2260,
					"name": "Maybourne "
				}
			]
		},
		{
			"id": 1128,
			"name": "Minor Hotel Group",
			"brands": [
				{
					"id": 7315,
					"name": "Tivoli Hotels & Resorts"
				},
				{
					"id": 7316,
					"name": "Anantara Hotels & Resorts"
				},
				{
					"id": 7317,
					"name": "Avani Hotels & Resorts"
				},
				{
					"id": 7318,
					"name": "Elawara Collection"
				},
				{
					"id": 7319,
					"name": "Oaks Hotels & Resorts"
				},
				{
					"id": 7320,
					"name": "PER AQUUM Hotels & Resorts"
				},
				{
					"id": 10303,
					"name": "MINT Residential"
				},
				{
					"id": 14311,
					"name": "NH Hotels "
				},
				{
					"id": 14326,
					"name": "Minor Hotel Group"
				}
			]
		},
		{
			"id": 1133,
			"name": "Rede Atlântico",
			"brands": [
				{
					"id": 2271,
					"name": "Rede Atlântico"
				}
			]
		},
		{
			"id": 1134,
			"name": "Transamerica Hospitality Group",
			"brands": [
				{
					"id": 2272,
					"name": "Transamerica Hospitality Group"
				}
			]
		},
		{
			"id": 1135,
			"name": "Slaviero Hoteis",
			"brands": [
				{
					"id": 2273,
					"name": "Slaviero Hoteis"
				}
			]
		},
		{
			"id": 1154,
			"name": "Grupo Diestra Hotels & Resorts",
			"brands": [
				{
					"id": 12539,
					"name": "Emporio Hotels"
				}
			]
		},
		{
			"id": 1158,
			"name": "Select Hotels",
			"brands": [
				{
					"id": 2305,
					"name": "Select Hotels"
				}
			]
		},
		{
			"id": 1163,
			"name": "Plava Laguna",
			"brands": [
				{
					"id": 2310,
					"name": "Plava Laguna"
				}
			]
		},
		{
			"id": 1173,
			"name": "Mantra Group",
			"brands": [
				{
					"id": 2322,
					"name": "Mantra Hotels"
				},
				{
					"id": 2627,
					"name": "Peppers Hotels"
				},
				{
					"id": 2628,
					"name": "BreakFree Hotels"
				}
			]
		},
		{
			"id": 1186,
			"name": "Alvarez Arguelles Hoteles",
			"brands": [
				{
					"id": 2336,
					"name": "Alvarez Arguelles Hoteles"
				}
			]
		},
		{
			"id": 1189,
			"name": "Victoria Group Hotels & Resorts",
			"brands": [
				{
					"id": 2339,
					"name": "Victoria Group Hotels & Resorts"
				}
			]
		},
		{
			"id": 1194,
			"name": "Karisma Hotels",
			"brands": [
				{
					"id": 2344,
					"name": "Karisma Hotels"
				}
			]
		},
		{
			"id": 1195,
			"name": "CGH",
			"brands": [
				{
					"id": 2345,
					"name": "CGH"
				}
			]
		},
		{
			"id": 1196,
			"name": "Dedeman Hotels",
			"brands": [
				{
					"id": 2347,
					"name": "Dedeman Hotels"
				}
			]
		},
		{
			"id": 1197,
			"name": "Albena",
			"brands": [
				{
					"id": 2348,
					"name": "Albena"
				}
			]
		},
		{
			"id": 1217,
			"name": "Sandman Hotel Group",
			"brands": [
				{
					"id": 2368,
					"name": "Sandman Hotel Group"
				}
			]
		},
		{
			"id": 1228,
			"name": "Emaar Hospitality Group",
			"brands": [
				{
					"id": 2382,
					"name": "The Address Hotels and Resorts"
				},
				{
					"id": 5768,
					"name": "Vida Hotels and Resorts"
				},
				{
					"id": 9297,
					"name": "Rove Hotels"
				},
				{
					"id": 11616,
					"name": "Armani Hotels"
				}
			]
		},
		{
			"id": 1230,
			"name": "Meriton Suites",
			"brands": [
				{
					"id": 2384,
					"name": "Meriton Suites"
				}
			]
		},
		{
			"id": 1236,
			"name": "Six Senses Resorts & Spas",
			"brands": [
				{
					"id": 2391,
					"name": "Six Senses Resorts & Spas"
				},
				{
					"id": 10987,
					"name": "Evason Resorts & Spas"
				}
			]
		},
		{
			"id": 1239,
			"name": "Bluesun Hotels & Resorts",
			"brands": [
				{
					"id": 2395,
					"name": "Bluesun Hotels & Resorts"
				}
			]
		},
		{
			"id": 1241,
			"name": "MGM Resorts International",
			"brands": [
				{
					"id": 2397,
					"name": "MGM Resorts International"
				}
			]
		},
		{
			"id": 1243,
			"name": "Dan Hotels & Resort",
			"brands": [
				{
					"id": 2399,
					"name": "Dan Hotels & Resort"
				}
			]
		},
		{
			"id": 1246,
			"name": "Four Seasons Hotels and Resorts",
			"brands": [
				{
					"id": 2402,
					"name": "Four Seasons Hotels and Resorts"
				},
				{
					"id": 12702,
					"name": "Four Seasons Private Residences"
				}
			]
		},
		{
			"id": 1247,
			"name": "the b hotels",
			"brands": [
				{
					"id": 2403,
					"name": "the b hotels"
				}
			]
		},
		{
			"id": 1271,
			"name": "Grupo Brisas",
			"brands": [
				{
					"id": 14297,
					"name": "Grupo Brisas"
				}
			]
		},
		{
			"id": 1274,
			"name": "Youth Hostels Association",
			"brands": [
				{
					"id": 2437,
					"name": "Youth Hostels Association"
				}
			]
		},
		{
			"id": 1279,
			"name": "Jaz Hotel Group",
			"brands": [
				{
					"id": 2442,
					"name": "Jaz Hotel Group"
				}
			]
		},
		{
			"id": 1282,
			"name": "Lagrange",
			"brands": [
				{
					"id": 2445,
					"name": "Lagrange "
				},
				{
					"id": 11496,
					"name": "Grand Bleu Vacances"
				},
				{
					"id": 13742,
					"name": "Lagrange City"
				}
			]
		},
		{
			"id": 1283,
			"name": "Hallmark Hotels",
			"brands": [
				{
					"id": 2446,
					"name": "Hallmark Hotels"
				}
			]
		},
		{
			"id": 1294,
			"name": "Lotte Hotels and Resorts",
			"brands": [
				{
					"id": 2458,
					"name": "Lotte Hotels"
				},
				{
					"id": 14114,
					"name": "L7 Hotels"
				},
				{
					"id": 14115,
					"name": "Lotte City Hotels"
				},
				{
					"id": 14116,
					"name": "Lotte Resort"
				},
				{
					"id": 14117,
					"name": "Signiel"
				}
			]
		},
		{
			"id": 1303,
			"name": "Archipelago International Hotels, Resorts & Residences",
			"brands": [
				{
					"id": 2467,
					"name": "Aston"
				},
				{
					"id": 12940,
					"name": "Collection by Aston"
				},
				{
					"id": 12941,
					"name": "Grand Aston"
				},
				{
					"id": 12942,
					"name": "Harper"
				},
				{
					"id": 12943,
					"name": "Kamuela"
				},
				{
					"id": 12944,
					"name": "Neo"
				},
				{
					"id": 12945,
					"name": "Quest"
				},
				{
					"id": 12946,
					"name": "The Alana"
				},
				{
					"id": 12947,
					"name": "Favehotel"
				},
				{
					"id": 13695,
					"name": "Maison Privee"
				},
				{
					"id": 14598,
					"name": "Powered by Archipelago"
				},
				{
					"id": 14768,
					"name": "Megaworld Hotels & Resorts"
				},
				{
					"id": 14778,
					"name": "Archipelago International Hotels, Resorts & Residences"
				},
				{
					"id": 14779,
					"name": "Seda Hotels"
				}
			]
		},
		{
			"id": 1305,
			"name": "Hotel du Vin & Malmaison",
			"brands": [
				{
					"id": 9447,
					"name": "Hotel du Vin"
				},
				{
					"id": 9448,
					"name": "Malmaison"
				}
			]
		},
		{
			"id": 1316,
			"name": "Casa Andina",
			"brands": [
				{
					"id": 2480,
					"name": "Casa Andina"
				}
			]
		},
		{
			"id": 1325,
			"name": "Focus Hotels",
			"brands": [
				{
					"id": 2488,
					"name": "Focus Hotels"
				}
			]
		},
		{
			"id": 1330,
			"name": "Banyan Group",
			"brands": [
				{
					"id": 2494,
					"name": "Banyan Tree Hotels & Resorts"
				},
				{
					"id": 5923,
					"name": "Angsana Hotels & Resorts"
				},
				{
					"id": 6103,
					"name": "Cassia"
				},
				{
					"id": 13830,
					"name": "Dhawa"
				},
				{
					"id": 13831,
					"name": "Homm"
				},
				{
					"id": 14060,
					"name": "Garrya"
				},
				{
					"id": 14061,
					"name": "Escape"
				}
			]
		},
		{
			"id": 1338,
			"name": "Daiwa Roynet Hotels",
			"brands": [
				{
					"id": 2501,
					"name": "Daiwa Roynet Hotels"
				}
			]
		},
		{
			"id": 1343,
			"name": "Dormy Inn",
			"brands": [
				{
					"id": 2506,
					"name": "Dormy Inn"
				}
			]
		},
		{
			"id": 1344,
			"name": "Mystays Hotel Group",
			"brands": [
				{
					"id": 2507,
					"name": "Mystays Hotel Group"
				}
			]
		},
		{
			"id": 1359,
			"name": "Rede Mirador",
			"brands": [
				{
					"id": 2522,
					"name": "Rede Mirador"
				}
			]
		},
		{
			"id": 1360,
			"name": "Bourbon Hotéis e Resorts",
			"brands": [
				{
					"id": 2523,
					"name": " Bourbon Hotéis e Resorts"
				}
			]
		},
		{
			"id": 1361,
			"name": "Atlantica Hotels International",
			"brands": [
				{
					"id": 2524,
					"name": "Atlantica Hotels International"
				},
				{
					"id": 13442,
					"name": "Go Inn"
				},
				{
					"id": 13443,
					"name": "eSuites"
				}
			]
		},
		{
			"id": 1362,
			"name": "Blue Tree Hotels",
			"brands": [
				{
					"id": 2525,
					"name": "Blue Tree Hotels"
				}
			]
		},
		{
			"id": 1363,
			"name": "Rixos Hotels",
			"brands": [
				{
					"id": 2526,
					"name": "Rixos Hotels"
				}
			]
		},
		{
			"id": 1378,
			"name": "Grupo Wish",
			"brands": [
				{
					"id": 13899,
					"name": "Wish"
				},
				{
					"id": 13900,
					"name": "Prodigy"
				},
				{
					"id": 13901,
					"name": "Linx"
				}
			]
		},
		{
			"id": 1385,
			"name": "Nobile Hoteis",
			"brands": [
				{
					"id": 2553,
					"name": "Nobile Hoteis"
				}
			]
		},
		{
			"id": 1388,
			"name": "Aitken Spence Hotels",
			"brands": [
				{
					"id": 2556,
					"name": "Aitken Spence Hotels"
				},
				{
					"id": 2557,
					"name": "Heritance Hotels"
				}
			]
		},
		{
			"id": 1396,
			"name": "Candeo Hotels",
			"brands": [
				{
					"id": 2565,
					"name": "Candeo Hotels"
				}
			]
		},
		{
			"id": 1400,
			"name": "Master Hotels",
			"brands": [
				{
					"id": 2569,
					"name": "Master Hotels"
				}
			]
		},
		{
			"id": 1401,
			"name": "Universal Orlando Resort",
			"brands": [
				{
					"id": 2570,
					"name": "Universal Orlando Resort"
				}
			]
		},
		{
			"id": 1405,
			"name": "Plaza Inn Hoteis",
			"brands": [
				{
					"id": 2574,
					"name": "Plaza Inn Hoteis"
				}
			]
		},
		{
			"id": 1411,
			"name": "Mitsui Fudosan Hotel Management",
			"brands": [
				{
					"id": 2580,
					"name": "Mitsui Garden"
				},
				{
					"id": 14110,
					"name": "sequence"
				},
				{
					"id": 14111,
					"name": "THE CELESTINE HOTELS"
				}
			]
		},
		{
			"id": 1415,
			"name": "Porto Bay Hotels & Resorts",
			"brands": [
				{
					"id": 2584,
					"name": "Porto Bay Hotels & Resorts"
				}
			]
		},
		{
			"id": 1419,
			"name": "Kerzner International Resorts",
			"brands": [
				{
					"id": 2588,
					"name": "Atlantis Resorts"
				},
				{
					"id": 2589,
					"name": "One&Only Resorts"
				},
				{
					"id": 6072,
					"name": "Mazagan Resorts"
				},
				{
					"id": 14262,
					"name": "Rare Finds Hotels & Resorts"
				},
				{
					"id": 14688,
					"name": "SIRO"
				}
			]
		},
		{
			"id": 1421,
			"name": "LiVEMAX",
			"brands": [
				{
					"id": 2591,
					"name": "LiveMax"
				}
			]
		},
		{
			"id": 1431,
			"name": "Golden Chain Motels",
			"brands": [
				{
					"id": 2601,
					"name": "Golden Chain Motels"
				}
			]
		},
		{
			"id": 1432,
			"name": "Be Live Hotels",
			"brands": [
				{
					"id": 2602,
					"name": "Be Live Hotels"
				}
			]
		},
		{
			"id": 1444,
			"name": "Sandos Hotels & Resorts",
			"brands": [
				{
					"id": 2618,
					"name": "Sandos Hotels & Resorts"
				}
			]
		},
		{
			"id": 1450,
			"name": "Intercity Hoteis",
			"brands": [
				{
					"id": 2624,
					"name": "Intercity Hoteis"
				}
			]
		},
		{
			"id": 1463,
			"name": "Blue Bay Resorts",
			"brands": [
				{
					"id": 2640,
					"name": "Blue Bay Resorts"
				}
			]
		},
		{
			"id": 1474,
			"name": "Tauzia Hotel Management",
			"brands": [
				{
					"id": 12948,
					"name": "FOX Lite"
				}
			]
		},
		{
			"id": 1475,
			"name": "Okura Nikko Hotels",
			"brands": [
				{
					"id": 14789,
					"name": "Okura Nikko Hotels"
				}
			]
		},
		{
			"id": 1486,
			"name": "Strawberry",
			"brands": [
				{
					"id": 2664,
					"name": "Strawberry"
				}
			]
		},
		{
			"id": 1500,
			"name": "Laris Hotels",
			"brands": [
				{
					"id": 2681,
					"name": "Laris Hotels"
				}
			]
		},
		{
			"id": 1512,
			"name": "Amerian",
			"brands": [
				{
					"id": 2695,
					"name": "Amerian"
				}
			]
		},
		{
			"id": 1515,
			"name": "Jetwing Hotels Limited",
			"brands": [
				{
					"id": 2698,
					"name": "Jetwing Hotels Limited"
				}
			]
		},
		{
			"id": 1529,
			"name": "GHL",
			"brands": [
				{
					"id": 2713,
					"name": "GHL"
				}
			]
		},
		{
			"id": 1533,
			"name": "Groupe Germain",
			"brands": [
				{
					"id": 13645,
					"name": "Alt Hotels"
				}
			]
		},
		{
			"id": 1534,
			"name": "Resorts World Genting",
			"brands": [
				{
					"id": 2719,
					"name": "Resorts World Genting"
				}
			]
		},
		{
			"id": 1539,
			"name": "Unita Turism Holding",
			"brands": [
				{
					"id": 2724,
					"name": "Unita Turism Holding"
				}
			]
		},
		{
			"id": 1567,
			"name": "Interhome",
			"brands": [
				{
					"id": 2752,
					"name": "Interhome"
				}
			]
		},
		{
			"id": 1571,
			"name": "Unizo",
			"brands": [
				{
					"id": 2756,
					"name": "Unizo"
				}
			]
		},
		{
			"id": 1575,
			"name": "Santika Indonesia Hotels & Resorts",
			"brands": [
				{
					"id": 12958,
					"name": "Amaris"
				},
				{
					"id": 12959,
					"name": "Other"
				},
				{
					"id": 12960,
					"name": "Samaya"
				},
				{
					"id": 12961,
					"name": "Santika"
				},
				{
					"id": 12962,
					"name": "Santika Premiere"
				}
			]
		},
		{
			"id": 1578,
			"name": "Dann Hotels",
			"brands": [
				{
					"id": 2763,
					"name": "Dann Hotels"
				}
			]
		},
		{
			"id": 1579,
			"name": "Cinnamon Hotels & Resorts",
			"brands": [
				{
					"id": 2764,
					"name": "Cinnamon Hotels & Resorts"
				}
			]
		},
		{
			"id": 1587,
			"name": "25hours Hotels",
			"brands": [
				{
					"id": 2773,
					"name": "25hours Hotels"
				}
			]
		},
		{
			"id": 1590,
			"name": "YOTEL",
			"brands": [
				{
					"id": 2776,
					"name": "YOTEL"
				}
			]
		},
		{
			"id": 1624,
			"name": "Amaks Hotels & Resorts",
			"brands": [
				{
					"id": 2810,
					"name": "Amaks Hotels & Resorts"
				}
			]
		},
		{
			"id": 1639,
			"name": "Skypark",
			"brands": [
				{
					"id": 2826,
					"name": "Skypark"
				}
			]
		},
		{
			"id": 1644,
			"name": "Prince Hotels & Resorts",
			"brands": [
				{
					"id": 2831,
					"name": "Prince Hotels & Resorts"
				}
			]
		},
		{
			"id": 1648,
			"name": "Route Inn Group",
			"brands": [
				{
					"id": 2835,
					"name": "Route Inn"
				}
			]
		},
		{
			"id": 1651,
			"name": "RZD - ZDOROVIE",
			"brands": [
				{
					"id": 2839,
					"name": "RZD-Resorts and Spa Hotels"
				}
			]
		},
		{
			"id": 1655,
			"name": "Union Hotels Collection",
			"brands": [
				{
					"id": 2843,
					"name": "Union Hotels Collection"
				}
			]
		},
		{
			"id": 1657,
			"name": "City Express Hotels",
			"brands": [
				{
					"id": 2846,
					"name": "City Express Hotels"
				},
				{
					"id": 3009,
					"name": "City Junior"
				},
				{
					"id": 3010,
					"name": "City Suites"
				},
				{
					"id": 12307,
					"name": "City Plus"
				},
				{
					"id": 12308,
					"name": "City Centro"
				}
			]
		},
		{
			"id": 1660,
			"name": "VR Hotels and Resorts",
			"brands": [
				{
					"id": 2849,
					"name": "VR Hotels and Resorts"
				}
			]
		},
		{
			"id": 1661,
			"name": "RIU Hotels & Resorts",
			"brands": [
				{
					"id": 2850,
					"name": "RIU Hotels & Resorts"
				},
				{
					"id": 13669,
					"name": "RIU Plaza"
				}
			]
		},
		{
			"id": 1663,
			"name": "Urlaub am Bauernhof",
			"brands": [
				{
					"id": 2853,
					"name": "Urlaub am Bauernhof"
				}
			]
		},
		{
			"id": 1666,
			"name": "Andrade Hoteis",
			"brands": [
				{
					"id": 2856,
					"name": "Andrade Hoteis"
				}
			]
		},
		{
			"id": 1690,
			"name": "Toyoko Inn",
			"brands": [
				{
					"id": 2880,
					"name": "Toyoko Inn"
				}
			]
		},
		{
			"id": 1691,
			"name": "Silver Cloud Hotels",
			"brands": [
				{
					"id": 2881,
					"name": "Silver Cloud Hotels"
				}
			]
		},
		{
			"id": 1696,
			"name": "Hoteles Estelar",
			"brands": [
				{
					"id": 2886,
					"name": "Hoteles Estelar"
				}
			]
		},
		{
			"id": 1698,
			"name": "Adonis",
			"brands": [
				{
					"id": 2888,
					"name": "Adonis"
				}
			]
		},
		{
			"id": 1702,
			"name": "Solare Hotels and Resorts",
			"brands": [
				{
					"id": 2892,
					"name": "Solare Hotels and Resorts"
				}
			]
		},
		{
			"id": 1711,
			"name": "easyHotel",
			"brands": [
				{
					"id": 2901,
					"name": "easyHotel"
				}
			]
		},
		{
			"id": 1712,
			"name": "Montebelo Hotels & Resorts",
			"brands": [
				{
					"id": 2902,
					"name": " Montebelo Hotels & Resorts"
				}
			]
		},
		{
			"id": 1730,
			"name": "Mitsis Hotels",
			"brands": [
				{
					"id": 2922,
					"name": "Mitsis Hotels"
				}
			]
		},
		{
			"id": 1740,
			"name": "Centro Hotel Group",
			"brands": [
				{
					"id": 2932,
					"name": "Centro Hotel Group"
				}
			]
		},
		{
			"id": 1741,
			"name": "Sun International",
			"brands": [
				{
					"id": 2933,
					"name": "Sun International"
				}
			]
		},
		{
			"id": 1750,
			"name": "Universal Beach Hotels",
			"brands": [
				{
					"id": 2942,
					"name": "Universal Beach Hotels"
				}
			]
		},
		{
			"id": 1755,
			"name": "Cordial Hotels & Resorts",
			"brands": [
				{
					"id": 2946,
					"name": "Cordial Hotels & Resorts"
				}
			]
		},
		{
			"id": 1757,
			"name": "Decameron Hotels & Resorts",
			"brands": [
				{
					"id": 2948,
					"name": "Decameron Hotels & Resorts"
				}
			]
		},
		{
			"id": 1758,
			"name": "Dorchester Collection",
			"brands": [
				{
					"id": 2949,
					"name": "Dorchester Collection"
				}
			]
		},
		{
			"id": 1767,
			"name": "Jadran Hotels & Camps",
			"brands": [
				{
					"id": 2959,
					"name": "Jadran Hotels & Camps"
				}
			]
		},
		{
			"id": 1775,
			"name": "Kenzi",
			"brands": [
				{
					"id": 2967,
					"name": "Kenzi"
				}
			]
		},
		{
			"id": 1776,
			"name": "Atlas Hospitality",
			"brands": [
				{
					"id": 2968,
					"name": "Atlas"
				}
			]
		},
		{
			"id": 1779,
			"name": "Hotel Brain",
			"brands": [
				{
					"id": 2971,
					"name": "Hotel Brain"
				}
			]
		},
		{
			"id": 1793,
			"name": "Motel6",
			"brands": [
				{
					"id": 2985,
					"name": "Motel6"
				},
				{
					"id": 4387,
					"name": "Studio 6"
				}
			]
		},
		{
			"id": 1811,
			"name": "The Peninsula Hotels",
			"brands": [
				{
					"id": 3007,
					"name": "The Peninsula Hotels"
				}
			]
		},
		{
			"id": 1834,
			"name": "Beachcomber Resorts & Hotels",
			"brands": [
				{
					"id": 3032,
					"name": "Beachcomber Resorts & Hotels"
				}
			]
		},
		{
			"id": 1840,
			"name": "Tune Hotels",
			"brands": [
				{
					"id": 3038,
					"name": "Tune Hotels"
				}
			]
		},
		{
			"id": 1866,
			"name": "Dalata Hotel",
			"brands": [
				{
					"id": 3064,
					"name": "Maldron"
				},
				{
					"id": 8137,
					"name": "Clayton"
				}
			]
		},
		{
			"id": 1871,
			"name": "C-Hotels",
			"brands": [
				{
					"id": 3069,
					"name": "C-Hotels"
				}
			]
		},
		{
			"id": 1876,
			"name": "AHORN Hotels & Resorts",
			"brands": [
				{
					"id": 3072,
					"name": "AHORN Hotels & Resorts"
				}
			]
		},
		{
			"id": 1900,
			"name": "Hard Rock",
			"brands": [
				{
					"id": 3100,
					"name": "Hard Rock"
				}
			]
		},
		{
			"id": 1926,
			"name": "Bahía Príncipe Hotels & Resorts",
			"brands": [
				{
					"id": 3127,
					"name": "Bahía Príncipe Hotels & Resorts"
				}
			]
		},
		{
			"id": 1936,
			"name": "Onyx Hospitality Group",
			"brands": [
				{
					"id": 3147,
					"name": "Amari"
				},
				{
					"id": 3148,
					"name": "Saffron Collection"
				},
				{
					"id": 3149,
					"name": "Shama"
				},
				{
					"id": 3150,
					"name": "OZO"
				},
				{
					"id": 14109,
					"name": "The Mosaic Collection"
				}
			]
		},
		{
			"id": 1948,
			"name": "Magnuson Hotels",
			"brands": [
				{
					"id": 12132,
					"name": "Magnuson Hotels"
				}
			]
		},
		{
			"id": 1953,
			"name": "Sentido",
			"brands": [
				{
					"id": 3162,
					"name": "Sentido"
				}
			]
		},
		{
			"id": 1957,
			"name": "CitizenM",
			"brands": [
				{
					"id": 3166,
					"name": "CitizenM"
				}
			]
		},
		{
			"id": 1988,
			"name": "Aminess Hotels & Resorts",
			"brands": [
				{
					"id": 3199,
					"name": "Aminess Hotels & Resorts"
				}
			]
		},
		{
			"id": 2002,
			"name": "Super Hotel",
			"brands": [
				{
					"id": 3215,
					"name": "Super Hotel"
				}
			]
		},
		{
			"id": 2005,
			"name": "Brownsword Hotels",
			"brands": [
				{
					"id": 3219,
					"name": "Brownsword Hotels"
				}
			]
		},
		{
			"id": 2006,
			"name": "Tierra Viva Hotels",
			"brands": [
				{
					"id": 3220,
					"name": "Tierra Viva Hotels"
				}
			]
		},
		{
			"id": 2011,
			"name": "Serenata Group",
			"brands": [
				{
					"id": 3225,
					"name": "Serenata Hotels & Resorts"
				}
			]
		},
		{
			"id": 2012,
			"name": "Beauty Hotels",
			"brands": [
				{
					"id": 3226,
					"name": "Beauty Hotels"
				}
			]
		},
		{
			"id": 2026,
			"name": "Hostelling International",
			"brands": [
				{
					"id": 3243,
					"name": "Hostelling International"
				}
			]
		},
		{
			"id": 2048,
			"name": "Gooderson Leisure",
			"brands": [
				{
					"id": 3265,
					"name": "Gooderson Leisure"
				}
			]
		},
		{
			"id": 2075,
			"name": "Belvilla",
			"brands": [
				{
					"id": 3292,
					"name": "Belvilla"
				}
			]
		},
		{
			"id": 2097,
			"name": "Esperia Hotel Group",
			"brands": [
				{
					"id": 3314,
					"name": "Esperia Hotel Group"
				}
			]
		},
		{
			"id": 2108,
			"name": "Elegance Hotels & Resorts",
			"brands": [
				{
					"id": 3326,
					"name": "Elegance Hotels & Resorts"
				}
			]
		},
		{
			"id": 2116,
			"name": "Nord Hotéis",
			"brands": [
				{
					"id": 3334,
					"name": "Nord Hotéis"
				}
			]
		},
		{
			"id": 2128,
			"name": "The Capital Hotel & Apartments",
			"brands": [
				{
					"id": 3346,
					"name": "The Capital Hotel & Apartments"
				}
			]
		},
		{
			"id": 2131,
			"name": "JA Resorts & Hotels",
			"brands": [
				{
					"id": 3349,
					"name": "JA Resorts & Hotels"
				}
			]
		},
		{
			"id": 2136,
			"name": "Rede Brisa de Hotéis",
			"brands": [
				{
					"id": 3354,
					"name": "Rede Brisa de Hotéis"
				}
			]
		},
		{
			"id": 2160,
			"name": "APA Hotels&Resorts",
			"brands": [
				{
					"id": 3380,
					"name": "APA Hotels&Resorts"
				}
			]
		},
		{
			"id": 2161,
			"name": "Green Rich Hotels",
			"brands": [
				{
					"id": 3381,
					"name": "Green Rich Hotels"
				}
			]
		},
		{
			"id": 2170,
			"name": "JR-EAST HOTELS",
			"brands": [
				{
					"id": 14630,
					"name": "JR-EAST HOTELS"
				}
			]
		},
		{
			"id": 2175,
			"name": "Sotetsu Hotels",
			"brands": [
				{
					"id": 3395,
					"name": "Sotetsu Fresa Inn"
				},
				{
					"id": 9744,
					"name": "Hotel Sunroute"
				},
				{
					"id": 14122,
					"name": "THE POCKET HOTEL"
				},
				{
					"id": 14123,
					"name": "Sotetsu Grand Fresa"
				},
				{
					"id": 14259,
					"name": "Sotetsu Hotels The Splaisir"
				}
			]
		},
		{
			"id": 2202,
			"name": "The Leela",
			"brands": [
				{
					"id": 3422,
					"name": "The Leela"
				}
			]
		},
		{
			"id": 2234,
			"name": "Outrigger Resorts & Hotels",
			"brands": [
				{
					"id": 3454,
					"name": "OUTRIGGER Resorts and Hotels"
				}
			]
		},
		{
			"id": 2240,
			"name": "Art Series Hotels",
			"brands": [
				{
					"id": 3459,
					"name": "Art Series Hotels"
				}
			]
		},
		{
			"id": 2254,
			"name": "JUFA Hotels",
			"brands": [
				{
					"id": 3474,
					"name": "JUFA Hotels"
				}
			]
		},
		{
			"id": 2307,
			"name": "LIVVO	Hotel Group",
			"brands": [
				{
					"id": 3527,
					"name": "LIVVO Hotel Group"
				}
			]
		},
		{
			"id": 2312,
			"name": "NOVASOL",
			"brands": [
				{
					"id": 3531,
					"name": "NOVASOL"
				}
			]
		},
		{
			"id": 2317,
			"name": "Rogers Hospitality",
			"brands": [
				{
					"id": 3536,
					"name": "Rogers Hospitality"
				},
				{
					"id": 14360,
					"name": "Heritage Resorts"
				},
				{
					"id": 14361,
					"name": "Veranda Resorts"
				},
				{
					"id": 14362,
					"name": "Kaz'alala Hosted B&B"
				},
				{
					"id": 14363,
					"name": "Voila Hotel"
				}
			]
		},
		{
			"id": 2324,
			"name": "Marston's Inns",
			"brands": [
				{
					"id": 3543,
					"name": "Marston's Inns"
				}
			]
		},
		{
			"id": 2359,
			"name": "Rosewood Hotel Group",
			"brands": [
				{
					"id": 3578,
					"name": "Rosewood Hotel & Resorts"
				},
				{
					"id": 14683,
					"name": "New World Hotel & Resorts"
				}
			]
		},
		{
			"id": 2408,
			"name": "Royal Park Hotels",
			"brands": [
				{
					"id": 3627,
					"name": "Royal Park Hotels"
				}
			]
		},
		{
			"id": 2412,
			"name": "Drury Hotels",
			"brands": [
				{
					"id": 3638,
					"name": "Drury"
				},
				{
					"id": 9891,
					"name": "Pear Tree Inn"
				}
			]
		},
		{
			"id": 2416,
			"name": "Absolute Hotel Services",
			"brands": [
				{
					"id": 3642,
					"name": "Absolute Hotel Services"
				},
				{
					"id": 3643,
					"name": "U Hotels & Resorts"
				},
				{
					"id": 3646,
					"name": "Eastin Easy"
				},
				{
					"id": 4115,
					"name": "Eastin Hotels & Residences"
				}
			]
		},
		{
			"id": 2454,
			"name": "Landal Greenparks",
			"brands": [
				{
					"id": 3687,
					"name": "Landal Greenparks"
				}
			]
		},
		{
			"id": 2618,
			"name": "ESTIVAL GROUP HOTELS",
			"brands": [
				{
					"id": 3850,
					"name": "ESTIVAL GROUP HOTELS"
				}
			]
		},
		{
			"id": 2641,
			"name": "Grifid Hotels",
			"brands": [
				{
					"id": 3873,
					"name": "Grifid Hotels"
				}
			]
		},
		{
			"id": 2647,
			"name": "Centurion Hotel",
			"brands": [
				{
					"id": 3879,
					"name": "Centurion Hotel"
				}
			]
		},
		{
			"id": 2666,
			"name": "Oakwood Asia Pacific Limited",
			"brands": [
				{
					"id": 14506,
					"name": "Oakwood"
				}
			]
		},
		{
			"id": 2685,
			"name": "Onahotels",
			"brands": [
				{
					"id": 3920,
					"name": "Onahotels"
				},
				{
					"id": 12767,
					"name": "Ona hotels"
				},
				{
					"id": 12875,
					"name": "Club"
				},
				{
					"id": 12876,
					"name": "Excel"
				},
				{
					"id": 12877,
					"name": "Wympen"
				}
			]
		},
		{
			"id": 2718,
			"name": "Discovery Holiday Parks",
			"brands": [
				{
					"id": 3951,
					"name": "Discovery Holiday Parks"
				}
			]
		},
		{
			"id": 2721,
			"name": "Accent Hotels",
			"brands": [
				{
					"id": 3955,
					"name": "Accent Hotels"
				}
			]
		},
		{
			"id": 2777,
			"name": "Laghetto Hotéis",
			"brands": [
				{
					"id": 4019,
					"name": "Laghetto Hotéis"
				}
			]
		},
		{
			"id": 2801,
			"name": "Che Lagarto",
			"brands": [
				{
					"id": 4047,
					"name": "Che Lagarto"
				}
			]
		},
		{
			"id": 2803,
			"name": "Menada",
			"brands": [
				{
					"id": 4049,
					"name": "Menada"
				}
			]
		},
		{
			"id": 2806,
			"name": "Optima Hotels & Resorts",
			"brands": [
				{
					"id": 4052,
					"name": "Optima Hotels & Resorts"
				}
			]
		},
		{
			"id": 2824,
			"name": "Fort Noks Resorts",
			"brands": [
				{
					"id": 4072,
					"name": "Fort Noks Resorts"
				}
			]
		},
		{
			"id": 2825,
			"name": "Dinevi Resort",
			"brands": [
				{
					"id": 4073,
					"name": "Dinevi Resort"
				}
			]
		},
		{
			"id": 2827,
			"name": "Hoteis Suarez",
			"brands": [
				{
					"id": 4075,
					"name": "Hoteis Suarez"
				}
			]
		},
		{
			"id": 2844,
			"name": "Ligula Hospitality Group",
			"brands": [
				{
					"id": 12537,
					"name": "Profil Hotels by Ligula"
				},
				{
					"id": 13369,
					"name": "Collection by Ligula"
				},
				{
					"id": 13370,
					"name": "Motel L by Ligula"
				},
				{
					"id": 13371,
					"name": "Good Morning Hotels by Ligula"
				}
			]
		},
		{
			"id": 2933,
			"name": "Elite Havens",
			"brands": [
				{
					"id": 4182,
					"name": "Elite Havens"
				}
			]
		},
		{
			"id": 2956,
			"name": "Rovinj Advisor",
			"brands": [
				{
					"id": 4204,
					"name": "Rovinj Advisor"
				}
			]
		},
		{
			"id": 2965,
			"name": "Park Hotel Group",
			"brands": [
				{
					"id": 4212,
					"name": "Park Hotel Group"
				}
			]
		},
		{
			"id": 2969,
			"name": "Eurotours",
			"brands": [
				{
					"id": 4216,
					"name": "Eurotours"
				}
			]
		},
		{
			"id": 3049,
			"name": "YMCA",
			"brands": [
				{
					"id": 4297,
					"name": "YMCA"
				}
			]
		},
		{
			"id": 3050,
			"name": "Muthu Hotels",
			"brands": [
				{
					"id": 4298,
					"name": "Muthu Hotels"
				}
			]
		},
		{
			"id": 3084,
			"name": "Generator Hostels",
			"brands": [
				{
					"id": 4333,
					"name": "Generator Hostels"
				}
			]
		},
		{
			"id": 3138,
			"name": "Hoseasons",
			"brands": [
				{
					"id": 4393,
					"name": "Hoseasons"
				}
			]
		},
		{
			"id": 3149,
			"name": "Plateno Hotels Group",
			"brands": [
				{
					"id": 4404,
					"name": "[Plateno Hotels Group] 7 Days Premium"
				},
				{
					"id": 6038,
					"name": "[Plateno Hotels Group] James Joyce Coffetel"
				},
				{
					"id": 6039,
					"name": "[Plateno Hotels Group] Lavande Hotel"
				},
				{
					"id": 8719,
					"name": "[Plateno Hotels Group] IU Hotel"
				},
				{
					"id": 9725,
					"name": "[Plateno Hotels Group] Pai Hotel"
				}
			]
		},
		{
			"id": 3153,
			"name": "Dancenter",
			"brands": [
				{
					"id": 4408,
					"name": "Dancenter"
				}
			]
		},
		{
			"id": 3154,
			"name": "Interchalet",
			"brands": [
				{
					"id": 4409,
					"name": "Interchalet"
				}
			]
		},
		{
			"id": 3179,
			"name": "Hoshino Resort",
			"brands": [
				{
					"id": 14573,
					"name": "OMO by Hoshino Resorts"
				},
				{
					"id": 14574,
					"name": "BEB"
				},
				{
					"id": 14575,
					"name": "Kai"
				},
				{
					"id": 14576,
					"name": "Risonare"
				},
				{
					"id": 14577,
					"name": "HOSHINOYA"
				}
			]
		},
		{
			"id": 3229,
			"name": "Hplus Hotelaria",
			"brands": [
				{
					"id": 4487,
					"name": "Hplus Hotelaria"
				}
			]
		},
		{
			"id": 3252,
			"name": "Nau Hotels & Resorts",
			"brands": [
				{
					"id": 4510,
					"name": "Nau Hotels & Resorts"
				}
			]
		},
		{
			"id": 3318,
			"name": "Flat Manager",
			"brands": [
				{
					"id": 4578,
					"name": "Flat Manager"
				}
			]
		},
		{
			"id": 3333,
			"name": "Ohtels",
			"brands": [
				{
					"id": 4595,
					"name": "Ohtels"
				}
			]
		},
		{
			"id": 3359,
			"name": "Odalys",
			"brands": [
				{
					"id": 9702,
					"name": "Odalys Chalet"
				},
				{
					"id": 9703,
					"name": "Odalys City/Campus"
				},
				{
					"id": 9704,
					"name": "Résidence Vacances Odalys"
				},
				{
					"id": 10962,
					"name": "Odalys Plein Air"
				}
			]
		},
		{
			"id": 3361,
			"name": "Résidence Néméa",
			"brands": [
				{
					"id": 9986,
					"name": "Néméa Campagne "
				},
				{
					"id": 9987,
					"name": "Néméa City "
				},
				{
					"id": 9988,
					"name": "Néméa Résidences Etudiante"
				},
				{
					"id": 9989,
					"name": "Néméa Mer"
				},
				{
					"id": 9990,
					"name": "Néméa Montagne "
				}
			]
		},
		{
			"id": 3367,
			"name": "Cottages.com",
			"brands": [
				{
					"id": 4631,
					"name": "Cottages.com"
				}
			]
		},
		{
			"id": 3385,
			"name": "Italianway",
			"brands": [
				{
					"id": 4649,
					"name": "Italianway"
				}
			]
		},
		{
			"id": 3403,
			"name": "VVF Villages",
			"brands": [
				{
					"id": 4666,
					"name": "VVF Villages"
				}
			]
		},
		{
			"id": 3430,
			"name": "Paradores de Turismo",
			"brands": [
				{
					"id": 14859,
					"name": "Paradores de Turismo"
				}
			]
		},
		{
			"id": 3666,
			"name": "First Group",
			"brands": [
				{
					"id": 4930,
					"name": "First Group"
				}
			]
		},
		{
			"id": 3738,
			"name": "Tsokkos Hotels & Resorts",
			"brands": [
				{
					"id": 5001,
					"name": "Tsokkos Hotels & Resorts"
				}
			]
		},
		{
			"id": 4066,
			"name": "Adriagate",
			"brands": [
				{
					"id": 5330,
					"name": "Adriagate"
				}
			]
		},
		{
			"id": 4079,
			"name": "Hosteeva",
			"brands": [
				{
					"id": 5343,
					"name": "Hosteeva"
				}
			]
		},
		{
			"id": 4497,
			"name": "Adriatic.hr",
			"brands": [
				{
					"id": 5761,
					"name": "Adriatic.hr"
				}
			]
		},
		{
			"id": 4526,
			"name": "Homerti",
			"brands": [
				{
					"id": 5790,
					"name": "Homerti"
				}
			]
		},
		{
			"id": 4592,
			"name": "James Villa Holidays",
			"brands": [
				{
					"id": 5858,
					"name": "James Villa Holidays"
				}
			]
		},
		{
			"id": 4616,
			"name": "Aqua-Aston Hospitality",
			"brands": [
				{
					"id": 13182,
					"name": "Aqua-Aston Hospitality"
				}
			]
		},
		{
			"id": 4626,
			"name": "Vacancéole",
			"brands": [
				{
					"id": 5914,
					"name": "Vacancéole"
				}
			]
		},
		{
			"id": 4678,
			"name": "Treebo Hotels",
			"brands": [
				{
					"id": 5975,
					"name": "Treebo Hotels"
				}
			]
		},
		{
			"id": 4689,
			"name": "Dorsett Hospitality International",
			"brands": [
				{
					"id": 5990,
					"name": "Dorsett Hotels & Resorts"
				},
				{
					"id": 5991,
					"name": "Silka Hotels"
				},
				{
					"id": 5992,
					"name": "d.Collection"
				}
			]
		},
		{
			"id": 4705,
			"name": "Casa Booker Montenegro",
			"brands": [
				{
					"id": 6008,
					"name": "Casa Booker Montenegro"
				}
			]
		},
		{
			"id": 4713,
			"name": "OYO Rooms",
			"brands": [
				{
					"id": 6015,
					"name": "OYO Rooms"
				},
				{
					"id": 14501,
					"name": "Sunday"
				}
			]
		},
		{
			"id": 4715,
			"name": "Zostel",
			"brands": [
				{
					"id": 6017,
					"name": "Zostel"
				}
			]
		},
		{
			"id": 4723,
			"name": "AVE hotels",
			"brands": [
				{
					"id": 6025,
					"name": "AVE hotels"
				}
			]
		},
		{
			"id": 4742,
			"name": "Atiram Hotels",
			"brands": [
				{
					"id": 6047,
					"name": "Atiram Hotels"
				}
			]
		},
		{
			"id": 4747,
			"name": "Euroloix",
			"brands": [
				{
					"id": 6054,
					"name": "Euroloix"
				}
			]
		},
		{
			"id": 4820,
			"name": "FabHotels",
			"brands": [
				{
					"id": 6129,
					"name": "FabHotels"
				}
			]
		},
		{
			"id": 4834,
			"name": "COMO",
			"brands": [
				{
					"id": 6143,
					"name": "COMO"
				}
			]
		},
		{
			"id": 4944,
			"name": "Apartelius",
			"brands": [
				{
					"id": 6264,
					"name": "Apartelius"
				}
			]
		},
		{
			"id": 5115,
			"name": "Cheerfulway Hotels & Resorts",
			"brands": [
				{
					"id": 6519,
					"name": "Cheerfulway Hotels & Resorts"
				}
			]
		},
		{
			"id": 5127,
			"name": "RedDoorz",
			"brands": [
				{
					"id": 6539,
					"name": "RedDoorz"
				},
				{
					"id": 14631,
					"name": "Lavana by RedDoorz"
				},
				{
					"id": 14632,
					"name": "Sans by RedDoorz"
				}
			]
		},
		{
			"id": 5145,
			"name": "JD Wetherspoon",
			"brands": [
				{
					"id": 6582,
					"name": "JD Wetherspoon PLC"
				}
			]
		},
		{
			"id": 5179,
			"name": "ZEN Rooms",
			"brands": [
				{
					"id": 6627,
					"name": "ZEN Rooms"
				},
				{
					"id": 8223,
					"name": "ZEN Premium"
				},
				{
					"id": 8224,
					"name": "ZEN Villa"
				}
			]
		},
		{
			"id": 5204,
			"name": "Shilla",
			"brands": [
				{
					"id": 6667,
					"name": "Shilla Stay"
				},
				{
					"id": 14533,
					"name": "The Shilla"
				},
				{
					"id": 14586,
					"name": "Shilla Monogram"
				}
			]
		},
		{
			"id": 5211,
			"name": "Hotel 10",
			"brands": [
				{
					"id": 6676,
					"name": "Hotel 10"
				}
			]
		},
		{
			"id": 5387,
			"name": "CZECH INN HOTELS",
			"brands": [
				{
					"id": 6868,
					"name": "CZECH INN HOTELS"
				}
			]
		},
		{
			"id": 5388,
			"name": "Fattal Hotels",
			"brands": [
				{
					"id": 6869,
					"name": "Fattal Hotels"
				}
			]
		},
		{
			"id": 5407,
			"name": "EA Hotels",
			"brands": [
				{
					"id": 6939,
					"name": "EA Hotels"
				}
			]
		},
		{
			"id": 5426,
			"name": "Beekman Group",
			"brands": [
				{
					"id": 6996,
					"name": "Beekman Group"
				}
			]
		},
		{
			"id": 5434,
			"name": "Elia Hotels",
			"brands": [
				{
					"id": 7008,
					"name": "Elia Hotels"
				}
			]
		},
		{
			"id": 5457,
			"name": "FIH Regent Group",
			"brands": [
				{
					"id": 7039,
					"name": "FIH Regent Group"
				}
			]
		},
		{
			"id": 5561,
			"name": "Hop Inn Group",
			"brands": [
				{
					"id": 7167,
					"name": "Hop Inn Group"
				}
			]
		},
		{
			"id": 5643,
			"name": "The Originals, Human Hotels & Resorts",
			"brands": [
				{
					"id": 11665,
					"name": "The Originals Access"
				},
				{
					"id": 11666,
					"name": "The Originals Boutique"
				},
				{
					"id": 11667,
					"name": "The Originals City"
				},
				{
					"id": 11668,
					"name": "The Originals Collection"
				},
				{
					"id": 11669,
					"name": "The Originals Relais"
				},
				{
					"id": 11670,
					"name": "The Originals Résidence"
				}
			]
		},
		{
			"id": 5644,
			"name": "LDC Group",
			"brands": [
				{
					"id": 7277,
					"name": "LDC Group"
				}
			]
		},
		{
			"id": 5682,
			"name": "Latitud Group",
			"brands": [
				{
					"id": 7328,
					"name": "Latitud Group"
				}
			]
		},
		{
			"id": 5686,
			"name": "HipHop Hostels",
			"brands": [
				{
					"id": 7352,
					"name": "HipHop Hostels"
				}
			]
		},
		{
			"id": 5738,
			"name": "Dream Hotel Group",
			"brands": [
				{
					"id": 7435,
					"name": "Dream Hotel Group"
				}
			]
		},
		{
			"id": 5772,
			"name": "Attitude Hotels",
			"brands": [
				{
					"id": 7547,
					"name": "Attitude Hotels"
				}
			]
		},
		{
			"id": 5824,
			"name": "Meeting Point Hotels",
			"brands": [
				{
					"id": 7639,
					"name": "Meeting Point Hotels"
				}
			]
		},
		{
			"id": 5828,
			"name": "Sivek Hotels",
			"brands": [
				{
					"id": 7657,
					"name": "Sivek Hotels"
				}
			]
		},
		{
			"id": 5834,
			"name": "A Hotels",
			"brands": [
				{
					"id": 7680,
					"name": "A Hotels"
				}
			]
		},
		{
			"id": 5835,
			"name": "Riviera Holiday Club",
			"brands": [
				{
					"id": 7681,
					"name": "Riviera Holiday Club"
				}
			]
		},
		{
			"id": 5842,
			"name": "iH Hotels",
			"brands": [
				{
					"id": 7705,
					"name": "iH Hotels"
				}
			]
		},
		{
			"id": 5855,
			"name": "Newmark Hotels, Reserves and Lodges",
			"brands": [
				{
					"id": 7731,
					"name": "Newmark Hotels, Reserves and Lodges"
				}
			]
		},
		{
			"id": 5916,
			"name": "MPM Hotels",
			"brands": [
				{
					"id": 7856,
					"name": "MPM Hotels"
				}
			]
		},
		{
			"id": 5930,
			"name": "Vinpearl Hotels and Resorts",
			"brands": [
				{
					"id": 7891,
					"name": "Vinpearl Hotels and Resorts"
				}
			]
		},
		{
			"id": 5931,
			"name": "Muong Thanh Hospitality",
			"brands": [
				{
					"id": 7892,
					"name": "Muong Thanh Hospitality"
				}
			]
		},
		{
			"id": 5937,
			"name": "Totalstay",
			"brands": [
				{
					"id": 8032,
					"name": "Totalstay"
				}
			]
		},
		{
			"id": 5942,
			"name": "Moshamanla hotel group",
			"brands": [
				{
					"id": 8072,
					"name": "Moshamanla hotel group"
				}
			]
		},
		{
			"id": 5945,
			"name": "Two Roads Hospitality (inactive)",
			"brands": [
				{
					"id": 8126,
					"name": "Destination hotels"
				},
				{
					"id": 8127,
					"name": "Joie De Vivre hotels"
				}
			]
		},
		{
			"id": 5950,
			"name": "Amritara",
			"brands": [
				{
					"id": 8135,
					"name": "Amritara"
				}
			]
		},
		{
			"id": 5962,
			"name": "Okko Hotels",
			"brands": [
				{
					"id": 8162,
					"name": "Okko Hotels"
				}
			]
		},
		{
			"id": 5963,
			"name": "Bella Villa Group",
			"brands": [
				{
					"id": 8165,
					"name": "Bella Villa Group"
				}
			]
		},
		{
			"id": 5987,
			"name": "Siblu",
			"brands": [
				{
					"id": 8273,
					"name": "Siblu"
				}
			]
		},
		{
			"id": 5997,
			"name": "SOHO Boutique",
			"brands": [
				{
					"id": 8329,
					"name": "SOHO Boutique"
				}
			]
		},
		{
			"id": 6003,
			"name": "Casual Hoteles",
			"brands": [
				{
					"id": 8338,
					"name": "Casual Hoteles"
				}
			]
		},
		{
			"id": 6008,
			"name": "Wanda Hotels & Resorts",
			"brands": [
				{
					"id": 8408,
					"name": "Wanda Reign"
				},
				{
					"id": 8409,
					"name": "Wanda Vista"
				},
				{
					"id": 8410,
					"name": "Wanda Realm"
				},
				{
					"id": 8411,
					"name": "Wanda Jin"
				},
				{
					"id": 8549,
					"name": "Wanda Hotels & Resorts"
				}
			]
		},
		{
			"id": 6014,
			"name": "Top 3 Star Hotels",
			"brands": [
				{
					"id": 8441,
					"name": "Top 3 Star Hotels"
				}
			]
		},
		{
			"id": 6032,
			"name": "Friends hotels and hostels",
			"brands": [
				{
					"id": 8464,
					"name": "Friends hotels and hostels"
				}
			]
		},
		{
			"id": 6041,
			"name": "Aqueen",
			"brands": [
				{
					"id": 8502,
					"name": "Aqueen"
				}
			]
		},
		{
			"id": 6093,
			"name": "Z Hotels",
			"brands": [
				{
					"id": 8718,
					"name": "Z Hotels"
				}
			]
		},
		{
			"id": 6104,
			"name": "Neemrana Hotels Private Limited",
			"brands": [
				{
					"id": 8763,
					"name": "Neemrana Hotels"
				}
			]
		},
		{
			"id": 6111,
			"name": "Apartamentos & Hoteles Viaggio",
			"brands": [
				{
					"id": 8828,
					"name": "Apartamentos & Hoteles Viaggio"
				}
			]
		},
		{
			"id": 6113,
			"name": "Disney® Hotels",
			"brands": [
				{
					"id": 8832,
					"name": "Disney® Hotels"
				}
			]
		},
		{
			"id": 6223,
			"name": "Provenance Hotels",
			"brands": [
				{
					"id": 9029,
					"name": "Provenance Hotels"
				}
			]
		},
		{
			"id": 6252,
			"name": "Solis Porec",
			"brands": [
				{
					"id": 9090,
					"name": "Solis Porec"
				}
			]
		},
		{
			"id": 6254,
			"name": "Direct Booker Slovenia",
			"brands": [
				{
					"id": 9093,
					"name": "Direct Booker Slovenia"
				}
			]
		},
		{
			"id": 6365,
			"name": "Brown Hotels",
			"brands": [
				{
					"id": 9364,
					"name": "Brown Hotels"
				}
			]
		},
		{
			"id": 6367,
			"name": "AX Hotels",
			"brands": [
				{
					"id": 9392,
					"name": "AX Hotels"
				}
			]
		},
		{
			"id": 6388,
			"name": "Kastel Hotéis",
			"brands": [
				{
					"id": 9441,
					"name": "Kastel Hotéis"
				}
			]
		},
		{
			"id": 6390,
			"name": "Yoho Bed",
			"brands": [
				{
					"id": 9450,
					"name": "Yoho Bed"
				}
			]
		},
		{
			"id": 6428,
			"name": "Poduszka",
			"brands": [
				{
					"id": 9605,
					"name": "GRUPA PODUSZKA"
				}
			]
		},
		{
			"id": 6430,
			"name": "CFI Hotels Group",
			"brands": [
				{
					"id": 9650,
					"name": "CFI Hotels Group"
				}
			]
		},
		{
			"id": 6436,
			"name": "Auberge Resorts",
			"brands": [
				{
					"id": 9662,
					"name": "Auberge Resorts"
				}
			]
		},
		{
			"id": 6485,
			"name": "Velinn Hotel e Pousadas",
			"brands": [
				{
					"id": 9747,
					"name": "Velinn"
				}
			]
		},
		{
			"id": 6487,
			"name": "MHB Hotelaria",
			"brands": [
				{
					"id": 9773,
					"name": "MHB Hotelaria"
				}
			]
		},
		{
			"id": 6499,
			"name": "Hestia Hotel Group",
			"brands": [
				{
					"id": 9790,
					"name": "Hestia Hotel Group"
				}
			]
		},
		{
			"id": 6530,
			"name": "Locatour",
			"brands": [
				{
					"id": 9831,
					"name": "Sunissim"
				},
				{
					"id": 9833,
					"name": "Travelski"
				}
			]
		},
		{
			"id": 6531,
			"name": "CGH Earth",
			"brands": [
				{
					"id": 9834,
					"name": "CGH Earth"
				}
			]
		},
		{
			"id": 6542,
			"name": "WAM Hoteis",
			"brands": [
				{
					"id": 9849,
					"name": "WAM Hoteis"
				}
			]
		},
		{
			"id": 6548,
			"name": "Point A Hotels",
			"brands": [
				{
					"id": 9857,
					"name": "Point A Hotels"
				}
			]
		},
		{
			"id": 6567,
			"name": "Amadria Park",
			"brands": [
				{
					"id": 9898,
					"name": "Amadria Park"
				}
			]
		},
		{
			"id": 6571,
			"name": "Zafiro Hotels",
			"brands": [
				{
					"id": 9902,
					"name": "Zafiro Hotels"
				}
			]
		},
		{
			"id": 6575,
			"name": "Crown & Champa Resorts",
			"brands": [
				{
					"id": 9918,
					"name": "Crown & Champa Resorts"
				}
			]
		},
		{
			"id": 6666,
			"name": "Alegria Hotels",
			"brands": [
				{
					"id": 10103,
					"name": "Alegria Hotels"
				}
			]
		},
		{
			"id": 6683,
			"name": "Details Hotels & Resorts",
			"brands": [
				{
					"id": 10134,
					"name": "Details Hotels & Resorts"
				}
			]
		},
		{
			"id": 6693,
			"name": "Fênix",
			"brands": [
				{
					"id": 10150,
					"name": "Fênix "
				}
			]
		},
		{
			"id": 6695,
			"name": "KD HOTELS SANTORINI",
			"brands": [
				{
					"id": 10152,
					"name": "KD HOTELS SANTORINI"
				}
			]
		},
		{
			"id": 6703,
			"name": "XO Hotels",
			"brands": [
				{
					"id": 10171,
					"name": "XO Hotels"
				}
			]
		},
		{
			"id": 6707,
			"name": "9 Hotel Collection",
			"brands": [
				{
					"id": 10176,
					"name": "9 Hotel Collection"
				}
			]
		},
		{
			"id": 6723,
			"name": "Selina",
			"brands": [
				{
					"id": 10207,
					"name": "Selina Hotels"
				}
			]
		},
		{
			"id": 6746,
			"name": "Eden Hotels - The Netherlands",
			"brands": [
				{
					"id": 10246,
					"name": "Eden Hotels - The Netherlands"
				}
			]
		},
		{
			"id": 6751,
			"name": "Your.Rentals",
			"brands": [
				{
					"id": 10257,
					"name": "Your.Rentals"
				}
			]
		},
		{
			"id": 6765,
			"name": "AP Hotels & Resorts",
			"brands": [
				{
					"id": 10280,
					"name": "AP Hotels & Resorts"
				}
			]
		},
		{
			"id": 6803,
			"name": "Ribas Hotels Group",
			"brands": [
				{
					"id": 10340,
					"name": "Ribas Hotels Group"
				}
			]
		},
		{
			"id": 6813,
			"name": "HMC Colombia",
			"brands": [
				{
					"id": 10354,
					"name": "HMC Colombia"
				}
			]
		},
		{
			"id": 6831,
			"name": "Solar Hoteles",
			"brands": [
				{
					"id": 10379,
					"name": "Solar Hoteles"
				}
			]
		},
		{
			"id": 6833,
			"name": "Tang's Living",
			"brands": [
				{
					"id": 10381,
					"name": "Tang's Living"
				}
			]
		},
		{
			"id": 6847,
			"name": "Lux* Resorts and Hotels",
			"brands": [
				{
					"id": 10401,
					"name": "Lux* Resorts and Hotels"
				}
			]
		},
		{
			"id": 6856,
			"name": "Saint Jane",
			"brands": [
				{
					"id": 10416,
					"name": "Saint Jane"
				}
			]
		},
		{
			"id": 6883,
			"name": "die Originale",
			"brands": [
				{
					"id": 10458,
					"name": "die Originale"
				}
			]
		},
		{
			"id": 6891,
			"name": "Human Company",
			"brands": [
				{
					"id": 10466,
					"name": "Human Company"
				},
				{
					"id": 13786,
					"name": "hu openair"
				}
			]
		},
		{
			"id": 6894,
			"name": "Concept Hotel Group",
			"brands": [
				{
					"id": 10469,
					"name": "Concept Hotel Group"
				}
			]
		},
		{
			"id": 6906,
			"name": "Alfagar Aparthotels & Resorts",
			"brands": [
				{
					"id": 10487,
					"name": "Alfagar Aparthotels & Resorts"
				}
			]
		},
		{
			"id": 6918,
			"name": "Lake Como Holiday",
			"brands": [
				{
					"id": 10511,
					"name": "Lake Como Holiday"
				}
			]
		},
		{
			"id": 7092,
			"name": "Jinyi Chain Hotels",
			"brands": [
				{
					"id": 10778,
					"name": "Jinyi Chain Hotels"
				}
			]
		},
		{
			"id": 7109,
			"name": "SaffronStays",
			"brands": [
				{
					"id": 10810,
					"name": "Saffron Stays"
				}
			]
		},
		{
			"id": 7123,
			"name": "Holidu",
			"brands": [
				{
					"id": 10845,
					"name": "Holidu"
				}
			]
		},
		{
			"id": 7129,
			"name": "Graduate Hotels",
			"brands": [
				{
					"id": 10852,
					"name": "Graduate"
				}
			]
		},
		{
			"id": 7139,
			"name": "H Hotels Collection",
			"brands": [
				{
					"id": 10870,
					"name": "H Hotels Collection"
				}
			]
		},
		{
			"id": 7156,
			"name": "Duna Parque Group",
			"brands": [
				{
					"id": 10896,
					"name": "Duna Parque Group"
				}
			]
		},
		{
			"id": 7165,
			"name": "WTB Hotels",
			"brands": [
				{
					"id": 10909,
					"name": "WTB Hotels"
				}
			]
		},
		{
			"id": 7270,
			"name": "Hoteles Extended Suites",
			"brands": [
				{
					"id": 11069,
					"name": "Hoteles Extended Suites"
				}
			]
		},
		{
			"id": 7294,
			"name": "Misty Blue Group",
			"brands": [
				{
					"id": 11097,
					"name": "Misty Blue Group"
				}
			]
		},
		{
			"id": 7306,
			"name": "All Suites",
			"brands": [
				{
					"id": 11117,
					"name": "All Suites"
				}
			]
		},
		{
			"id": 7309,
			"name": "Eldorado",
			"brands": [
				{
					"id": 11120,
					"name": "Eldorado"
				}
			]
		},
		{
			"id": 7330,
			"name": "Sohoma",
			"brands": [
				{
					"id": 14315,
					"name": "Orso Hotels"
				}
			]
		},
		{
			"id": 7347,
			"name": "Manquehue Hoteles",
			"brands": [
				{
					"id": 11180,
					"name": "Manquehue"
				}
			]
		},
		{
			"id": 7372,
			"name": "Camplus",
			"brands": [
				{
					"id": 11230,
					"name": "Camplus"
				}
			]
		},
		{
			"id": 7392,
			"name": "Ruby Hotels",
			"brands": [
				{
					"id": 11268,
					"name": "Ruby Hotels"
				}
			]
		},
		{
			"id": 7393,
			"name": "Jinjiang Inns",
			"brands": [
				{
					"id": 11272,
					"name": "Metropolo"
				}
			]
		},
		{
			"id": 7446,
			"name": "Checkin Hoteles",
			"brands": [
				{
					"id": 11345,
					"name": "Checkin Hoteles"
				}
			]
		},
		{
			"id": 7481,
			"name": "Eko Stay",
			"brands": [
				{
					"id": 11389,
					"name": "Eko Stay"
				}
			]
		},
		{
			"id": 7506,
			"name": "Sun Siyam",
			"brands": [
				{
					"id": 11419,
					"name": "Sun Siyam"
				}
			]
		},
		{
			"id": 7604,
			"name": "ONOMO HOTELS",
			"brands": [
				{
					"id": 11562,
					"name": "ONOMO HOTELS"
				}
			]
		},
		{
			"id": 7611,
			"name": "Hesperia Hotels & Resorts",
			"brands": [
				{
					"id": 11572,
					"name": "Hesperia Hotels & Resorts"
				}
			]
		},
		{
			"id": 7612,
			"name": "StayVista",
			"brands": [
				{
					"id": 11573,
					"name": "StayVista"
				}
			]
		},
		{
			"id": 7843,
			"name": "HappyCulture",
			"brands": [
				{
					"id": 11860,
					"name": "HappyCulture"
				}
			]
		},
		{
			"id": 7848,
			"name": "Emeraude France",
			"brands": [
				{
					"id": 11865,
					"name": "Emeraude France"
				}
			]
		},
		{
			"id": 7849,
			"name": "BBHotels",
			"brands": [
				{
					"id": 11866,
					"name": "BBHotels"
				}
			]
		},
		{
			"id": 7890,
			"name": "Mogotel Hotel Group",
			"brands": [
				{
					"id": 11912,
					"name": "Rija"
				},
				{
					"id": 11923,
					"name": "Wellton"
				},
				{
					"id": 11925,
					"name": "Rixwell"
				}
			]
		},
		{
			"id": 7917,
			"name": "Naos Hotel Groupe",
			"brands": [
				{
					"id": 11950,
					"name": "Naos Hotel Groupe"
				}
			]
		},
		{
			"id": 7920,
			"name": "d'primahotel",
			"brands": [
				{
					"id": 11956,
					"name": "d'primahotel"
				}
			]
		},
		{
			"id": 7926,
			"name": "Mahabaleshwar Stay",
			"brands": [
				{
					"id": 11975,
					"name": "Mahabaleshwar Stay"
				}
			]
		},
		{
			"id": 7928,
			"name": "Travelio",
			"brands": [
				{
					"id": 11977,
					"name": "Travelio"
				}
			]
		},
		{
			"id": 7930,
			"name": "DP Murphy Hotels",
			"brands": [
				{
					"id": 11979,
					"name": "DP Murphy Hotels"
				}
			]
		},
		{
			"id": 7931,
			"name": "Groupe Kejja",
			"brands": [
				{
					"id": 11980,
					"name": "Groupe Kejja"
				}
			]
		},
		{
			"id": 7932,
			"name": "Superior Lodging / Masterbuilt Hotels",
			"brands": [
				{
					"id": 11981,
					"name": "Superior Lodging / Masterbuilt Hotels"
				}
			]
		},
		{
			"id": 7949,
			"name": "RCD Hotels",
			"brands": [
				{
					"id": 12278,
					"name": "Eden Roc"
				},
				{
					"id": 12279,
					"name": "Nobu"
				}
			]
		},
		{
			"id": 7963,
			"name": "OXO Hoteles",
			"brands": [
				{
					"id": 12028,
					"name": "OXO Hoteles"
				}
			]
		},
		{
			"id": 7972,
			"name": "VBA Hospitality Group",
			"brands": [
				{
					"id": 12051,
					"name": "VBA Hospitality Group"
				}
			]
		},
		{
			"id": 7976,
			"name": "The Setai, Herbert Samuel & Orchid Hotels",
			"brands": [
				{
					"id": 12090,
					"name": "The Setai, Herbert Samuel & Orchid Hotels"
				}
			]
		},
		{
			"id": 7994,
			"name": "OD Hotels",
			"brands": [
				{
					"id": 12109,
					"name": "OD Hotels"
				}
			]
		},
		{
			"id": 8003,
			"name": "Larco Hospitality",
			"brands": [
				{
					"id": 12124,
					"name": "Larco Hospitality"
				}
			]
		},
		{
			"id": 8010,
			"name": "DREAM Hostels",
			"brands": [
				{
					"id": 12134,
					"name": "DREAM Hostels"
				}
			]
		},
		{
			"id": 8011,
			"name": "BON Hotels",
			"brands": [
				{
					"id": 12135,
					"name": "BON Hotels"
				}
			]
		},
		{
			"id": 8019,
			"name": "Lixi",
			"brands": [
				{
					"id": 12151,
					"name": "Lixi"
				}
			]
		},
		{
			"id": 8027,
			"name": "Thema Collection",
			"brands": [
				{
					"id": 12184,
					"name": "Thema Collection"
				}
			]
		},
		{
			"id": 8029,
			"name": "Les Hôtels de Beauval",
			"brands": [
				{
					"id": 12197,
					"name": "Les Hôtels de Beauval"
				}
			]
		},
		{
			"id": 8031,
			"name": "Sahid Hotels & Resort",
			"brands": [
				{
					"id": 12204,
					"name": "Sahid Hotels & Resort"
				}
			]
		},
		{
			"id": 8049,
			"name": "Aldiana",
			"brands": [
				{
					"id": 12262,
					"name": "Aldiana"
				}
			]
		},
		{
			"id": 8051,
			"name": "StayPineapple Hotels",
			"brands": [
				{
					"id": 12264,
					"name": "StayPineapple Hotels"
				}
			]
		},
		{
			"id": 8056,
			"name": "Thracian Hoteliers",
			"brands": [
				{
					"id": 12269,
					"name": "Thracian Hoteliers"
				}
			]
		},
		{
			"id": 8061,
			"name": "Olive Hotels& Resorts",
			"brands": [
				{
					"id": 12275,
					"name": "Olive Hotels"
				}
			]
		},
		{
			"id": 8067,
			"name": "MIX Hotels",
			"brands": [
				{
					"id": 12287,
					"name": "MIX Hotels"
				}
			]
		},
		{
			"id": 8069,
			"name": "Aquilini",
			"brands": [
				{
					"id": 12289,
					"name": "Aquilini"
				}
			]
		},
		{
			"id": 8075,
			"name": "Aviva Parques & Resorts",
			"brands": [
				{
					"id": 13678,
					"name": "Costa do Sauipe"
				},
				{
					"id": 13679,
					"name": "Rio Quente Resorts"
				}
			]
		},
		{
			"id": 8076,
			"name": "Ayenda Hoteles",
			"brands": [
				{
					"id": 12310,
					"name": "Ayenda Hoteles"
				}
			]
		},
		{
			"id": 8083,
			"name": "Unlock Boutique Hotels",
			"brands": [
				{
					"id": 12321,
					"name": "Unlock Boutique Hotels"
				}
			]
		},
		{
			"id": 8084,
			"name": "Hawaiian Hotels & Resorts",
			"brands": [
				{
					"id": 12322,
					"name": "Hawaiian Hotels & Resorts"
				}
			]
		},
		{
			"id": 8096,
			"name": "Jeevawasa",
			"brands": [
				{
					"id": 12343,
					"name": "Jeevawasa"
				}
			]
		},
		{
			"id": 8097,
			"name": "Prime Plaza Hotels & Resorts",
			"brands": [
				{
					"id": 12344,
					"name": "Prime Plaza Hotels & Resorts"
				}
			]
		},
		{
			"id": 8108,
			"name": "B&B Austria",
			"brands": [
				{
					"id": 12355,
					"name": "B&B Austria"
				}
			]
		},
		{
			"id": 8126,
			"name": "DesenzanoLoft",
			"brands": [
				{
					"id": 12378,
					"name": "DesenzanoLoft"
				}
			]
		},
		{
			"id": 8127,
			"name": "Górskie Resorty",
			"brands": [
				{
					"id": 12379,
					"name": "Górskie Resorty"
				}
			]
		},
		{
			"id": 8128,
			"name": "The Q Project",
			"brands": [
				{
					"id": 12385,
					"name": "The Q Project"
				}
			]
		},
		{
			"id": 8129,
			"name": "Brava Hoteles",
			"brands": [
				{
					"id": 12386,
					"name": "Brava Hoteles"
				}
			]
		},
		{
			"id": 8144,
			"name": "CLLIX Apartments and Hotels",
			"brands": [
				{
					"id": 12403,
					"name": "CLLIX Apartments and Hotels"
				}
			]
		},
		{
			"id": 8145,
			"name": "Locals",
			"brands": [
				{
					"id": 12404,
					"name": "Lotel"
				}
			]
		},
		{
			"id": 8146,
			"name": "AfriCamps Boutique Camping",
			"brands": [
				{
					"id": 12405,
					"name": "AfriCamps Boutique Camping"
				}
			]
		},
		{
			"id": 8156,
			"name": "Ephoenix",
			"brands": [
				{
					"id": 12418,
					"name": "Ephoenix"
				}
			]
		},
		{
			"id": 8157,
			"name": "HS Hotsson",
			"brands": [
				{
					"id": 12419,
					"name": "HS Hotsson"
				}
			]
		},
		{
			"id": 8165,
			"name": "Sky Hotéis",
			"brands": [
				{
					"id": 12451,
					"name": "Sky Hotéis"
				}
			]
		},
		{
			"id": 8166,
			"name": "TrevPAR World Group",
			"brands": [
				{
					"id": 12452,
					"name": "TrevPAR World Group"
				}
			]
		},
		{
			"id": 8168,
			"name": "Westmont Hospitality Canada",
			"brands": [
				{
					"id": 12459,
					"name": "Westmont Hospitality Canada"
				}
			]
		},
		{
			"id": 8171,
			"name": "MIMARU",
			"brands": [
				{
					"id": 12485,
					"name": "MIMARU"
				}
			]
		},
		{
			"id": 8173,
			"name": "City Relay",
			"brands": [
				{
					"id": 12495,
					"name": "City Relay"
				}
			]
		},
		{
			"id": 8204,
			"name": "Hotel Royal",
			"brands": [
				{
					"id": 12606,
					"name": "Hotel Royal"
				}
			]
		},
		{
			"id": 8214,
			"name": "Mane Hotel Collection",
			"brands": [
				{
					"id": 12626,
					"name": "Mane Hotel Collection"
				}
			]
		},
		{
			"id": 8216,
			"name": "Robinsons Hotels and Resorts",
			"brands": [
				{
					"id": 12628,
					"name": "Robinsons Hotels and Resorts"
				}
			]
		},
		{
			"id": 8217,
			"name": "Wombats City Hostel",
			"brands": [
				{
					"id": 12629,
					"name": "Wombats City Hostel"
				}
			]
		},
		{
			"id": 8223,
			"name": "GuestReady",
			"brands": [
				{
					"id": 12642,
					"name": "GuestReady"
				}
			]
		},
		{
			"id": 8226,
			"name": "Agrisal",
			"brands": [
				{
					"id": 12644,
					"name": "Agrisal"
				}
			]
		},
		{
			"id": 8227,
			"name": "PRAN Hotels Inc.",
			"brands": [
				{
					"id": 12662,
					"name": "PRAN Hotels Inc."
				}
			]
		},
		{
			"id": 8230,
			"name": "Noktos",
			"brands": [
				{
					"id": 12667,
					"name": "Noktos"
				}
			]
		},
		{
			"id": 8232,
			"name": "My Second Home",
			"brands": [
				{
					"id": 12670,
					"name": "My Second Home"
				}
			]
		},
		{
			"id": 8237,
			"name": "Mida Hotels and Resorts",
			"brands": [
				{
					"id": 12717,
					"name": "Mida Hotels and Resorts"
				}
			]
		},
		{
			"id": 8241,
			"name": "Cobblestone Hotels",
			"brands": [
				{
					"id": 12727,
					"name": "Cobblestone Hotels"
				}
			]
		},
		{
			"id": 8246,
			"name": "Allegroitalia Hotels & Condo",
			"brands": [
				{
					"id": 12736,
					"name": "Allegroitalia Hotels & Condo"
				}
			]
		},
		{
			"id": 8247,
			"name": "Old Town Hotels",
			"brands": [
				{
					"id": 12738,
					"name": "Old Town Hotels"
				}
			]
		},
		{
			"id": 8248,
			"name": "ANA Hotels",
			"brands": [
				{
					"id": 12739,
					"name": "ANA Hotels"
				}
			]
		},
		{
			"id": 8249,
			"name": "Mera Hotels",
			"brands": [
				{
					"id": 12740,
					"name": "Mera Hotels"
				}
			]
		},
		{
			"id": 8254,
			"name": "DOT Hotels",
			"brands": [
				{
					"id": 12748,
					"name": "DOT Hotels"
				}
			]
		},
		{
			"id": 8255,
			"name": "AG Hotels",
			"brands": [
				{
					"id": 12749,
					"name": "AG Hotels"
				}
			]
		},
		{
			"id": 8257,
			"name": "Tridente Collection",
			"brands": [
				{
					"id": 12756,
					"name": "Tridente Collection"
				}
			]
		},
		{
			"id": 8267,
			"name": "CitySuites",
			"brands": [
				{
					"id": 12774,
					"name": "CitySuites"
				},
				{
					"id": 12775,
					"name": "Midtown Richardson"
				},
				{
					"id": 12776,
					"name": "Hotel PaPa Whale"
				}
			]
		},
		{
			"id": 8295,
			"name": "Travcotels",
			"brands": [
				{
					"id": 12814,
					"name": "Travcotels"
				}
			]
		},
		{
			"id": 8301,
			"name": "Rede Summit Hotels",
			"brands": [
				{
					"id": 12832,
					"name": "Rede Summit Hotels"
				}
			]
		},
		{
			"id": 8304,
			"name": "Club Esse Hotels and Resorts",
			"brands": [
				{
					"id": 12835,
					"name": "Club Esse Hotels and Resorts"
				}
			]
		},
		{
			"id": 8314,
			"name": "Roter Hahn - Urlaub auf dem Bauernhof",
			"brands": [
				{
					"id": 12883,
					"name": "Roter Hahn - Urlaub auf dem Bauernhof"
				}
			]
		},
		{
			"id": 8327,
			"name": "Extra Holidays Australia",
			"brands": [
				{
					"id": 12927,
					"name": "Extra Holidays Australia"
				}
			]
		},
		{
			"id": 8330,
			"name": "Mangia's Resorts and Clubs",
			"brands": [
				{
					"id": 12964,
					"name": "Mangia's Resorts and Clubs"
				}
			]
		},
		{
			"id": 8335,
			"name": "Prizeotel",
			"brands": [
				{
					"id": 12974,
					"name": "Prizeotel"
				}
			]
		},
		{
			"id": 8342,
			"name": "Xima Hotels",
			"brands": [
				{
					"id": 13007,
					"name": "Xima Hotels"
				}
			]
		},
		{
			"id": 8349,
			"name": "Costa del Sol",
			"brands": [
				{
					"id": 13016,
					"name": "Costa del Sol"
				}
			]
		},
		{
			"id": 8353,
			"name": "Plataran Group",
			"brands": [
				{
					"id": 13037,
					"name": "Plataran Group"
				}
			]
		},
		{
			"id": 8355,
			"name": "4L Collection",
			"brands": [
				{
					"id": 13039,
					"name": "4L Collection"
				}
			]
		},
		{
			"id": 8356,
			"name": "Omnia Hotels",
			"brands": [
				{
					"id": 13040,
					"name": "Omnia Hotels"
				}
			]
		},
		{
			"id": 8363,
			"name": "Asteri hotels",
			"brands": [
				{
					"id": 13061,
					"name": "Asteri hotels"
				}
			]
		},
		{
			"id": 8373,
			"name": "Destigo Hotels",
			"brands": [
				{
					"id": 13072,
					"name": "Destigo Hotels"
				}
			]
		},
		{
			"id": 8374,
			"name": "Arche",
			"brands": [
				{
					"id": 13073,
					"name": "Arche"
				}
			]
		},
		{
			"id": 8381,
			"name": "Panama Jack",
			"brands": [
				{
					"id": 13093,
					"name": "Panama Jack"
				}
			]
		},
		{
			"id": 8382,
			"name": "Jewel",
			"brands": [
				{
					"id": 13094,
					"name": "Jewel"
				}
			]
		},
		{
			"id": 8413,
			"name": "City Lodge Hotel Group",
			"brands": [
				{
					"id": 13143,
					"name": "City Lodge Hotels"
				},
				{
					"id": 13144,
					"name": "Courtyard Hotels"
				},
				{
					"id": 13145,
					"name": "Town Lodges"
				},
				{
					"id": 13146,
					"name": "Fairview Hotel"
				},
				{
					"id": 13147,
					"name": "Road Lodges"
				}
			]
		},
		{
			"id": 8416,
			"name": "Vaya Resorts",
			"brands": [
				{
					"id": 13150,
					"name": "Vaya Resorts"
				}
			]
		},
		{
			"id": 8422,
			"name": "Sorea Hotelová Spoločnosť",
			"brands": [
				{
					"id": 13160,
					"name": "Sorea"
				}
			]
		},
		{
			"id": 8423,
			"name": "Tatry Mountain Resorts Hotels (Hotels Operated by TMR)",
			"brands": [
				{
					"id": 13161,
					"name": "TMR"
				}
			]
		},
		{
			"id": 8425,
			"name": "BB Hotels",
			"brands": [
				{
					"id": 13163,
					"name": "BB Hotels"
				}
			]
		},
		{
			"id": 8427,
			"name": "UHG",
			"brands": [
				{
					"id": 13165,
					"name": "UHG Hospitality"
				}
			]
		},
		{
			"id": 8428,
			"name": "Local Chains ISC",
			"brands": [
				{
					"id": 13167,
					"name": "Leisure Hotels Group"
				},
				{
					"id": 14129,
					"name": "Orchid Hotels and Resorts"
				}
			]
		},
		{
			"id": 8434,
			"name": "Taua Resorts",
			"brands": [
				{
					"id": 13184,
					"name": "Taua Resorts"
				}
			]
		},
		{
			"id": 8436,
			"name": "Atmosphere Hotels & Resorts",
			"brands": [
				{
					"id": 13186,
					"name": "Atmosphere Hotels & Resorts"
				}
			]
		},
		{
			"id": 8439,
			"name": "Pytloun Hotels",
			"brands": [
				{
					"id": 13189,
					"name": "Pytloun"
				}
			]
		},
		{
			"id": 8441,
			"name": "El Mouradi Hotels",
			"brands": [
				{
					"id": 13194,
					"name": "El Mouradi"
				}
			]
		},
		{
			"id": 8444,
			"name": "Castlewood Hotels",
			"brands": [
				{
					"id": 13200,
					"name": "Castlewood Hotels"
				}
			]
		},
		{
			"id": 8448,
			"name": "Rotamundos",
			"brands": [
				{
					"id": 13212,
					"name": "Rotamundos"
				}
			]
		},
		{
			"id": 8495,
			"name": "Commonwealth Hotels",
			"brands": [
				{
					"id": 13293,
					"name": "Commonwealth Hotels"
				}
			]
		},
		{
			"id": 8560,
			"name": "Seazone",
			"brands": [
				{
					"id": 13362,
					"name": "Seazone"
				}
			]
		},
		{
			"id": 8566,
			"name": "Magic hotels",
			"brands": [
				{
					"id": 13367,
					"name": "Magic hotels"
				}
			]
		},
		{
			"id": 8568,
			"name": "MarSenses Hotels",
			"brands": [
				{
					"id": 13379,
					"name": "MarSenses Hotels"
				}
			]
		},
		{
			"id": 8569,
			"name": "3HB Hotels",
			"brands": [
				{
					"id": 13380,
					"name": "3HB Hotels"
				}
			]
		},
		{
			"id": 8570,
			"name": "Aman Hotels and Resorts",
			"brands": [
				{
					"id": 13382,
					"name": "Aman Hotels and Resorts"
				}
			]
		},
		{
			"id": 8573,
			"name": "Kavia Hotels",
			"brands": [
				{
					"id": 13384,
					"name": "Kavia Hotels"
				}
			]
		},
		{
			"id": 8574,
			"name": "HMH – Hospitality Management Holding",
			"brands": [
				{
					"id": 13391,
					"name": "HMH – Hospitality Management Holding"
				}
			]
		},
		{
			"id": 8575,
			"name": "Lucerna Hotels",
			"brands": [
				{
					"id": 13392,
					"name": "Lucerna Hotels"
				}
			]
		},
		{
			"id": 8576,
			"name": "GHL Hoteles",
			"brands": [
				{
					"id": 13394,
					"name": "GHL Hoteles"
				}
			]
		},
		{
			"id": 8588,
			"name": "ANEW Hotels & Resorts",
			"brands": [
				{
					"id": 13412,
					"name": "ANEW Hotels & Resorts"
				}
			]
		},
		{
			"id": 8598,
			"name": "Bachcare",
			"brands": [
				{
					"id": 13437,
					"name": "Bachcare"
				}
			]
		},
		{
			"id": 8599,
			"name": "R Collection Hotel",
			"brands": [
				{
					"id": 13438,
					"name": "R Collection Hotel"
				}
			]
		},
		{
			"id": 8610,
			"name": "Bally’s Corporation",
			"brands": [
				{
					"id": 13454,
					"name": "Bally’s Corporation"
				}
			]
		},
		{
			"id": 8611,
			"name": "Emerald Stay",
			"brands": [
				{
					"id": 13455,
					"name": "Emerald Stay"
				}
			]
		},
		{
			"id": 8614,
			"name": "Life House",
			"brands": [
				{
					"id": 13461,
					"name": "Life House"
				}
			]
		},
		{
			"id": 8616,
			"name": "Atlantica Hotels and Resorts",
			"brands": [
				{
					"id": 13469,
					"name": "Atlantica Hotels and Resorts"
				}
			]
		},
		{
			"id": 8634,
			"name": "Fatima Hotels Group",
			"brands": [
				{
					"id": 13519,
					"name": "Fatima Hotels Group"
				}
			]
		},
		{
			"id": 8637,
			"name": "TUI Hotels & Resorts",
			"brands": [
				{
					"id": 14353,
					"name": "TUI BLUE"
				},
				{
					"id": 14354,
					"name": "TUI MAGIC LIFE"
				},
				{
					"id": 14355,
					"name": "ROBINSON"
				},
				{
					"id": 14499,
					"name": "TUI non branded"
				},
				{
					"id": 14589,
					"name": "AQI"
				},
				{
					"id": 14760,
					"name": "The Mora"
				}
			]
		},
		{
			"id": 8639,
			"name": "DimHotels",
			"brands": [
				{
					"id": 13549,
					"name": "DimHotels"
				}
			]
		},
		{
			"id": 8641,
			"name": "YourHouse Group",
			"brands": [
				{
					"id": 13552,
					"name": "YourHouse Group"
				}
			]
		},
		{
			"id": 8648,
			"name": "TLG",
			"brands": [
				{
					"id": 13646,
					"name": "TLG"
				}
			]
		},
		{
			"id": 8649,
			"name": "Smy Hotels",
			"brands": [
				{
					"id": 13647,
					"name": "Smy Hotels"
				}
			]
		},
		{
			"id": 8653,
			"name": "Test Chain 2021",
			"brands": [
				{
					"id": 13668,
					"name": "Test Chain 2021"
				}
			]
		},
		{
			"id": 8654,
			"name": "Agni Travel",
			"brands": [
				{
					"id": 13672,
					"name": "Agni Travel"
				}
			]
		},
		{
			"id": 8655,
			"name": "Ruralidays",
			"brands": [
				{
					"id": 13673,
					"name": "Ruralidays"
				}
			]
		},
		{
			"id": 8656,
			"name": "Villa Plus",
			"brands": [
				{
					"id": 13674,
					"name": "Villa Plus"
				}
			]
		},
		{
			"id": 8658,
			"name": "One Shot Hotels",
			"brands": [
				{
					"id": 13696,
					"name": "One Shot Hotels"
				}
			]
		},
		{
			"id": 8659,
			"name": "Aureli Hotels",
			"brands": [
				{
					"id": 13697,
					"name": "Aureli Hotels"
				}
			]
		},
		{
			"id": 8665,
			"name": "Hotel Seri Malaysia",
			"brands": [
				{
					"id": 13712,
					"name": "Hotel Seri Malaysia"
				}
			]
		},
		{
			"id": 8666,
			"name": "Ostello Bello",
			"brands": [
				{
					"id": 13721,
					"name": "Ostello Bello"
				}
			]
		},
		{
			"id": 8668,
			"name": "C-Hotels Belgium",
			"brands": [
				{
					"id": 13722,
					"name": "C-Hotels Belgium"
				}
			]
		},
		{
			"id": 8670,
			"name": "Butcombe Brewing Co.",
			"brands": [
				{
					"id": 13724,
					"name": "Butcombe Brewing Co."
				},
				{
					"id": 13725,
					"name": "Liberation Brewing Co."
				}
			]
		},
		{
			"id": 8675,
			"name": "MORE Lodges & Hotels",
			"brands": [
				{
					"id": 13730,
					"name": "MORE Lodges & Hotels"
				}
			]
		},
		{
			"id": 8677,
			"name": "B.Zar Hotel&Co.",
			"brands": [
				{
					"id": 13741,
					"name": "B.Zar Hotel&Co."
				}
			]
		},
		{
			"id": 8678,
			"name": "Villa for You",
			"brands": [
				{
					"id": 13743,
					"name": "Villa for You"
				}
			]
		},
		{
			"id": 8679,
			"name": "ARTOTELGROUP",
			"brands": [
				{
					"id": 13768,
					"name": "ARTOTELGROUP"
				},
				{
					"id": 14681,
					"name": "Artotel Brand"
				}
			]
		},
		{
			"id": 8680,
			"name": "Vertice hoteles",
			"brands": [
				{
					"id": 13769,
					"name": "Vertice hoteles"
				}
			]
		},
		{
			"id": 8682,
			"name": "numa",
			"brands": [
				{
					"id": 13777,
					"name": "numa"
				}
			]
		},
		{
			"id": 8683,
			"name": "Paradise Wilderness",
			"brands": [
				{
					"id": 13778,
					"name": "Paradise Wilderness"
				}
			]
		},
		{
			"id": 8684,
			"name": "SleepOver",
			"brands": [
				{
					"id": 13779,
					"name": "SleepOver"
				}
			]
		},
		{
			"id": 8686,
			"name": "Plaza Hotels",
			"brands": [
				{
					"id": 13782,
					"name": "Plaza Hotels"
				}
			]
		},
		{
			"id": 8687,
			"name": "ISRAEL CANADA HOTELS",
			"brands": [
				{
					"id": 13783,
					"name": "ISRAEL CANADA HOTELS"
				}
			]
		},
		{
			"id": 8690,
			"name": "Sono Hotels & Resorts",
			"brands": [
				{
					"id": 13787,
					"name": "Sono Hotels & Resorts"
				}
			]
		},
		{
			"id": 8691,
			"name": "Happy.Rentals",
			"brands": [
				{
					"id": 13797,
					"name": "Happy.Rentals"
				}
			]
		},
		{
			"id": 8698,
			"name": "Maeva",
			"brands": [
				{
					"id": 13864,
					"name": "Maeva Camping"
				},
				{
					"id": 13865,
					"name": "Maeva Home"
				}
			]
		},
		{
			"id": 8700,
			"name": "limehome",
			"brands": [
				{
					"id": 13837,
					"name": "limehome"
				}
			]
		},
		{
			"id": 8701,
			"name": "AFI Hotels",
			"brands": [
				{
					"id": 13840,
					"name": "AFI Hotels"
				}
			]
		},
		{
			"id": 8702,
			"name": "Ferretti Hotels Group",
			"brands": [
				{
					"id": 13868,
					"name": "Ferretti Hotels Group"
				}
			]
		},
		{
			"id": 8704,
			"name": "Serena Hotels",
			"brands": [
				{
					"id": 13870,
					"name": "Serena Hotels"
				}
			]
		},
		{
			"id": 8705,
			"name": "Virgin Hotels",
			"brands": [
				{
					"id": 13872,
					"name": "Virgin Hotels"
				}
			]
		},
		{
			"id": 8710,
			"name": "Omega Hotel Management",
			"brands": [
				{
					"id": 13904,
					"name": "Omega Hotel Management"
				}
			]
		},
		{
			"id": 8711,
			"name": "Urbane Room",
			"brands": [
				{
					"id": 13905,
					"name": "Urbane Room"
				}
			]
		},
		{
			"id": 8712,
			"name": "Nakula",
			"brands": [
				{
					"id": 13906,
					"name": "Nakula"
				}
			]
		},
		{
			"id": 8713,
			"name": "House Of Reservations",
			"brands": [
				{
					"id": 13907,
					"name": "House Of Reservations"
				}
			]
		},
		{
			"id": 8715,
			"name": "Hamner Holiday Homes",
			"brands": [
				{
					"id": 13909,
					"name": "Hamner Holiday Homes"
				}
			]
		},
		{
			"id": 8725,
			"name": "People Hostel",
			"brands": [
				{
					"id": 13921,
					"name": "People Hostel"
				}
			]
		},
		{
			"id": 8729,
			"name": "Clévacances",
			"brands": [
				{
					"id": 14056,
					"name": "Clévacances"
				}
			]
		},
		{
			"id": 8730,
			"name": "Garibaldi Hotels",
			"brands": [
				{
					"id": 14058,
					"name": "Garibaldi Hotels"
				}
			]
		},
		{
			"id": 8731,
			"name": "Phi Hotels",
			"brands": [
				{
					"id": 14059,
					"name": "Phi Hotels"
				}
			]
		},
		{
			"id": 8732,
			"name": "KAO",
			"brands": [
				{
					"id": 14063,
					"name": "KAO"
				}
			]
		},
		{
			"id": 8735,
			"name": "Bluserena",
			"brands": [
				{
					"id": 14070,
					"name": "Bluserena"
				}
			]
		},
		{
			"id": 8736,
			"name": "Blueberry Hospitality",
			"brands": [
				{
					"id": 14071,
					"name": "Blueberry Hospitality"
				}
			]
		},
		{
			"id": 8739,
			"name": "GRANBELL HOTELS & RESORTS",
			"brands": [
				{
					"id": 14103,
					"name": "GRANBELL HOTELS & RESORTS"
				}
			]
		},
		{
			"id": 8740,
			"name": "Hotel Green Chain Sendai",
			"brands": [
				{
					"id": 14104,
					"name": "Hotel Green Chain Sendai"
				}
			]
		},
		{
			"id": 8750,
			"name": "Henann Group Resorts",
			"brands": [
				{
					"id": 14128,
					"name": "Henann Group Resorts"
				}
			]
		},
		{
			"id": 8757,
			"name": "The Inn Collection Group",
			"brands": [
				{
					"id": 14236,
					"name": "The Inn Collection Group"
				}
			]
		},
		{
			"id": 8758,
			"name": "Rebel Hotel Company",
			"brands": [
				{
					"id": 14237,
					"name": "Rebel Hotel Company"
				}
			]
		},
		{
			"id": 8762,
			"name": "The Star Entertainment Group",
			"brands": [
				{
					"id": 14246,
					"name": "Star Entertainment"
				}
			]
		},
		{
			"id": 8764,
			"name": "Tay Hotels",
			"brands": [
				{
					"id": 14248,
					"name": "Tay Hotels"
				}
			]
		},
		{
			"id": 8765,
			"name": "Via Inn Hotels",
			"brands": [
				{
					"id": 14249,
					"name": "Via Inn Hotels"
				}
			]
		},
		{
			"id": 8767,
			"name": "Katathani",
			"brands": [
				{
					"id": 14254,
					"name": "Katathani"
				}
			]
		},
		{
			"id": 8769,
			"name": "Stonegate Pub",
			"brands": [
				{
					"id": 14258,
					"name": "Stonegate Pub"
				}
			]
		},
		{
			"id": 8770,
			"name": "Tsogo Sun Gaming",
			"brands": [
				{
					"id": 14260,
					"name": "Tsogo Sun Gaming"
				}
			]
		},
		{
			"id": 8797,
			"name": "TERRESENS VACANCES",
			"brands": [
				{
					"id": 14295,
					"name": "TERRESENS VACANCES"
				}
			]
		},
		{
			"id": 8800,
			"name": "Ekman Hotels",
			"brands": [
				{
					"id": 14300,
					"name": "Ekman Hotels"
				}
			]
		},
		{
			"id": 8801,
			"name": "Kloeg Collection",
			"brands": [
				{
					"id": 14301,
					"name": "Kloeg Collection"
				}
			]
		},
		{
			"id": 8802,
			"name": "Rio Hotel Group",
			"brands": [
				{
					"id": 14302,
					"name": "Rio Hotel Group"
				}
			]
		},
		{
			"id": 8803,
			"name": "Garda FeWo",
			"brands": [
				{
					"id": 14307,
					"name": "Garda FeWo"
				}
			]
		},
		{
			"id": 8804,
			"name": "Armas Hotels",
			"brands": [
				{
					"id": 14310,
					"name": "Armas Hotels"
				}
			]
		},
		{
			"id": 8814,
			"name": "Imperial Hotel Group",
			"brands": [
				{
					"id": 14335,
					"name": "Imperial Hotel Group"
				}
			]
		},
		{
			"id": 8815,
			"name": "Sea Resorts Hotels",
			"brands": [
				{
					"id": 14339,
					"name": "Sea Resorts Hotels"
				}
			]
		},
		{
			"id": 8816,
			"name": "Ninety-Six Hotel Collection",
			"brands": [
				{
					"id": 14340,
					"name": "Ninety-Six Hotel Collection"
				}
			]
		},
		{
			"id": 8818,
			"name": "ONDA",
			"brands": [
				{
					"id": 14342,
					"name": "ONDA"
				}
			]
		},
		{
			"id": 8823,
			"name": "SAii Hotel & Resorts",
			"brands": [
				{
					"id": 14349,
					"name": " SAii Hotel & Resorts"
				}
			]
		},
		{
			"id": 8827,
			"name": "Borealis Hotel Group",
			"brands": [
				{
					"id": 14357,
					"name": "Borealis Hotel Group"
				}
			]
		},
		{
			"id": 8830,
			"name": "Columbia Hospitality",
			"brands": [
				{
					"id": 14406,
					"name": "Loge"
				},
				{
					"id": 14407,
					"name": "Columbia"
				}
			]
		},
		{
			"id": 8831,
			"name": "Moustache Hostels",
			"brands": [
				{
					"id": 14429,
					"name": "Moustache Hostels"
				}
			]
		},
		{
			"id": 8832,
			"name": "HRH Group of Hotels",
			"brands": [
				{
					"id": 14496,
					"name": "HRH Group of Hotels"
				}
			]
		},
		{
			"id": 8834,
			"name": "Hotele Diament",
			"brands": [
				{
					"id": 14498,
					"name": "Hotele Diament"
				}
			]
		},
		{
			"id": 8838,
			"name": "Cape & Kantary Hotels",
			"brands": [
				{
					"id": 14509,
					"name": "Cape & Kantary Hotels"
				}
			]
		},
		{
			"id": 8839,
			"name": "Futura Club",
			"brands": [
				{
					"id": 14510,
					"name": "Futura Club"
				}
			]
		},
		{
			"id": 8841,
			"name": "Aspira Hotels and Resorts",
			"brands": [
				{
					"id": 14514,
					"name": "Aspira Hotels and Resorts"
				}
			]
		},
		{
			"id": 8843,
			"name": "Relo Hotels＆Resorts",
			"brands": [
				{
					"id": 14518,
					"name": "Relo Hotels＆Resorts"
				}
			]
		},
		{
			"id": 8855,
			"name": "Umbral",
			"brands": [
				{
					"id": 14532,
					"name": "Umbral"
				}
			]
		},
		{
			"id": 8860,
			"name": "HUSWELL",
			"brands": [
				{
					"id": 14538,
					"name": "HUSWELL"
				}
			]
		},
		{
			"id": 8871,
			"name": "Up Hoteles",
			"brands": [
				{
					"id": 14557,
					"name": "Up Hoteles"
				}
			]
		},
		{
			"id": 8875,
			"name": "Lamin Hotels",
			"brands": [
				{
					"id": 14562,
					"name": "Lamin Hotels"
				}
			]
		},
		{
			"id": 8877,
			"name": "Ross Hotels Group",
			"brands": [
				{
					"id": 14565,
					"name": "Ross Hotels Group"
				}
			]
		},
		{
			"id": 8878,
			"name": "Eklo",
			"brands": [
				{
					"id": 14566,
					"name": "Eklo"
				}
			]
		},
		{
			"id": 8879,
			"name": "SMARTments",
			"brands": [
				{
					"id": 14569,
					"name": "SMARTments"
				}
			]
		},
		{
			"id": 8881,
			"name": "The Hosteller",
			"brands": [
				{
					"id": 14571,
					"name": "The Hosteller"
				}
			]
		},
		{
			"id": 8882,
			"name": "Sonder",
			"brands": [
				{
					"id": 14572,
					"name": "Sonder"
				}
			]
		},
		{
			"id": 8884,
			"name": "IQ Hospitality",
			"brands": [
				{
					"id": 14578,
					"name": "IQ Hospitality"
				}
			]
		},
		{
			"id": 8886,
			"name": "Bohemian Hostels & Hotels",
			"brands": [
				{
					"id": 14582,
					"name": "Bohemian Hostels & Hotels"
				}
			]
		},
		{
			"id": 8889,
			"name": "Mad Monkey Hostels",
			"brands": [
				{
					"id": 14587,
					"name": "Mad Monkey Hostels"
				}
			]
		},
		{
			"id": 8890,
			"name": "Dijiwa Sanctuaries",
			"brands": [
				{
					"id": 14588,
					"name": "Dijiwa Sanctuaries"
				}
			]
		},
		{
			"id": 8896,
			"name": "Emma Villas",
			"brands": [
				{
					"id": 14599,
					"name": "Emma Villas"
				}
			]
		},
		{
			"id": 8900,
			"name": "Maestro Hotels",
			"brands": [
				{
					"id": 14603,
					"name": "Maestro Hotels"
				}
			]
		},
		{
			"id": 8912,
			"name": "Göbel Hotels",
			"brands": [
				{
					"id": 14621,
					"name": "Göbel Hotels"
				}
			]
		},
		{
			"id": 8915,
			"name": "Ini Vie",
			"brands": [
				{
					"id": 14624,
					"name": "Ini Vie"
				}
			]
		},
		{
			"id": 8921,
			"name": "LovelyStay",
			"brands": [
				{
					"id": 14633,
					"name": "LovelyStay"
				}
			]
		},
		{
			"id": 8924,
			"name": "Room007",
			"brands": [
				{
					"id": 14638,
					"name": "Room007"
				}
			]
		},
		{
			"id": 8931,
			"name": "Affitti Brevi Italia",
			"brands": [
				{
					"id": 14645,
					"name": "Affitti Brevi Italia"
				}
			]
		},
		{
			"id": 8932,
			"name": "Hoteles Xcaret",
			"brands": [
				{
					"id": 14649,
					"name": "Hoteles Xcaret"
				}
			]
		},
		{
			"id": 8933,
			"name": "BYPILLOW",
			"brands": [
				{
					"id": 14651,
					"name": "BYPILLOW"
				}
			]
		},
		{
			"id": 8937,
			"name": "Wonderful Italy",
			"brands": [
				{
					"id": 14657,
					"name": "Wonderful Italy"
				}
			]
		},
		{
			"id": 8938,
			"name": "QC Terme",
			"brands": [
				{
					"id": 14658,
					"name": "QC Terme"
				}
			]
		},
		{
			"id": 8939,
			"name": "Young & Co.’s Brewery",
			"brands": [
				{
					"id": 14659,
					"name": "Young & Co.’s Brewery"
				}
			]
		},
		{
			"id": 8943,
			"name": "Prague Days",
			"brands": [
				{
					"id": 14665,
					"name": "Prague Days"
				}
			]
		},
		{
			"id": 8944,
			"name": "Harry's Home Holding AG",
			"brands": [
				{
					"id": 14666,
					"name": "Harry's Home Holding AG"
				}
			]
		},
		{
			"id": 8946,
			"name": "Gregori Hotels",
			"brands": [
				{
					"id": 14669,
					"name": "Gregori Hotels"
				}
			]
		},
		{
			"id": 8947,
			"name": "Charlie",
			"brands": [
				{
					"id": 14670,
					"name": "Charlie"
				}
			]
		},
		{
			"id": 8949,
			"name": "Travelbook Hotels",
			"brands": [
				{
					"id": 14673,
					"name": "Travelbook Hotels"
				}
			]
		},
		{
			"id": 8951,
			"name": "Cocotel Hotels & Resorts",
			"brands": [
				{
					"id": 14676,
					"name": "Cocotel Hotels & Resorts"
				}
			]
		},
		{
			"id": 8953,
			"name": "SECRA",
			"brands": [
				{
					"id": 14678,
					"name": "SECRA"
				}
			]
		},
		{
			"id": 8955,
			"name": "Alpin Rentals GmbH",
			"brands": [
				{
					"id": 14680,
					"name": "Alpin Rentals GmbH"
				}
			]
		},
		{
			"id": 8956,
			"name": "LyvInn Hotels",
			"brands": [
				{
					"id": 14682,
					"name": "LyvInn Hotels"
				}
			]
		},
		{
			"id": 8957,
			"name": "Alpha-One",
			"brands": [
				{
					"id": 14689,
					"name": "Alpha-One"
				}
			]
		},
		{
			"id": 8958,
			"name": "Sowell Hotels et résidences",
			"brands": [
				{
					"id": 14692,
					"name": "Sowell Hotels et résidences"
				}
			]
		},
		{
			"id": 8960,
			"name": "Easylife Srl",
			"brands": [
				{
					"id": 14744,
					"name": "Easylife House"
				}
			]
		},
		{
			"id": 8964,
			"name": "halu!",
			"brands": [
				{
					"id": 14751,
					"name": "halu!"
				}
			]
		},
		{
			"id": 8974,
			"name": "Castelo Itaipava",
			"brands": [
				{
					"id": 14767,
					"name": "Castelo Itaipava"
				}
			]
		},
		{
			"id": 8975,
			"name": "Apartamentos Mar Menor S.A.",
			"brands": [
				{
					"id": 14769,
					"name": "Apartamentos Mar Menor S.A."
				}
			]
		},
		{
			"id": 8977,
			"name": "Antwerp Stays",
			"brands": [
				{
					"id": 14772,
					"name": "Antwerp Stays"
				}
			]
		},
		{
			"id": 8978,
			"name": "Direct Booker",
			"brands": [
				{
					"id": 14777,
					"name": "Direct Booker"
				}
			]
		},
		{
			"id": 8980,
			"name": "SaltStayz",
			"brands": [
				{
					"id": 14782,
					"name": "SaltStayz"
				}
			]
		},
		{
			"id": 8983,
			"name": "hotel MONday Group",
			"brands": [
				{
					"id": 14788,
					"name": "hotel MONday Group"
				}
			]
		},
		{
			"id": 8985,
			"name": "Kozystay",
			"brands": [
				{
					"id": 14791,
					"name": "Kozystay"
				}
			]
		},
		{
			"id": 8987,
			"name": "Istrabook",
			"brands": [
				{
					"id": 14793,
					"name": "Istrabook"
				}
			]
		},
		{
			"id": 8990,
			"name": "Campaya",
			"brands": [
				{
					"id": 14796,
					"name": "Campaya"
				}
			]
		},
		{
			"id": 8991,
			"name": "Schladming Appartements Maria Gruber GmbH",
			"brands": [
				{
					"id": 14797,
					"name": "Schladming Appartements Maria Gruber GmbH"
				}
			]
		},
		{
			"id": 8992,
			"name": "Garden City",
			"brands": [
				{
					"id": 14798,
					"name": "Garden City"
				}
			]
		},
		{
			"id": 8995,
			"name": "Arbio",
			"brands": [
				{
					"id": 14801,
					"name": "Arbio"
				}
			]
		},
		{
			"id": 8996,
			"name": "#FLH- Feels Like Home",
			"brands": [
				{
					"id": 14802,
					"name": "#FLH- Feels Like Home"
				}
			]
		},
		{
			"id": 8997,
			"name": "Urban Rest",
			"brands": [
				{
					"id": 14803,
					"name": "Urban Rest"
				}
			]
		},
		{
			"id": 8998,
			"name": "Algarve Vacation",
			"brands": [
				{
					"id": 14804,
					"name": "Algarve Vacation"
				}
			]
		},
		{
			"id": 8999,
			"name": "Casas & Papéis",
			"brands": [
				{
					"id": 14805,
					"name": "Casas & Papéis"
				}
			]
		},
		{
			"id": 9001,
			"name": "Homaris Apartments",
			"brands": [
				{
					"id": 14807,
					"name": "Homaris Apartments"
				}
			]
		},
		{
			"id": 9005,
			"name": "Beaumier",
			"brands": [
				{
					"id": 14811,
					"name": "Beaumier"
				}
			]
		},
		{
			"id": 9006,
			"name": "Fomich Hotels",
			"brands": [
				{
					"id": 14812,
					"name": "Fomich Hotels"
				}
			]
		},
		{
			"id": 9008,
			"name": "Andronis",
			"brands": [
				{
					"id": 14814,
					"name": "Andronis"
				}
			]
		},
		{
			"id": 9010,
			"name": "Enorme Hotels",
			"brands": [
				{
					"id": 14816,
					"name": "Enorme Hotels"
				}
			]
		},
		{
			"id": 9011,
			"name": "Domes Hotels",
			"brands": [
				{
					"id": 14817,
					"name": "Domes Hotels"
				}
			]
		},
		{
			"id": 9012,
			"name": "Delonix Betterwood",
			"brands": [
				{
					"id": 14818,
					"name": "Delonix Betterwood"
				}
			]
		},
		{
			"id": 9013,
			"name": "Maryland",
			"brands": [
				{
					"id": 14819,
					"name": "Maryland"
				}
			]
		},
		{
			"id": 9014,
			"name": "Pillowhite Feels Like Home",
			"brands": [
				{
					"id": 14820,
					"name": "Pillowhite Feels Like Home"
				}
			]
		},
		{
			"id": 9015,
			"name": "Konnect",
			"brands": [
				{
					"id": 14821,
					"name": "Konnect"
				}
			]
		},
		{
			"id": 9016,
			"name": "Vacedo",
			"brands": [
				{
					"id": 14822,
					"name": "Vacedo"
				}
			]
		},
		{
			"id": 9018,
			"name": "LK Group Hotels",
			"brands": [
				{
					"id": 14828,
					"name": "LK Group Hotels"
				}
			]
		},
		{
			"id": 9024,
			"name": "Di Roma",
			"brands": [
				{
					"id": 14835,
					"name": "Di Roma"
				}
			]
		},
		{
			"id": 9034,
			"name": "FJ Hotels",
			"brands": [
				{
					"id": 14845,
					"name": "FJ Hotels"
				}
			]
		},
		{
			"id": 9035,
			"name": "HotelPraxis Z.O.O",
			"brands": [
				{
					"id": 14846,
					"name": "HotelPraxis Z.O.O"
				}
			]
		},
		{
			"id": 9036,
			"name": "Holiday Suites",
			"brands": [
				{
					"id": 14847,
					"name": "Holiday Suites"
				}
			]
		},
		{
			"id": 9037,
			"name": "Destination Solutions",
			"brands": [
				{
					"id": 14848,
					"name": "Destination Solutions"
				}
			]
		},
		{
			"id": 9038,
			"name": "Maxi Hoteles",
			"brands": [
				{
					"id": 14849,
					"name": "Maxi Hoteles"
				}
			]
		},
		{
			"id": 9041,
			"name": "Top Collection Apart Hotel",
			"brands": [
				{
					"id": 14853,
					"name": "Top Collection Apart Hotel"
				}
			]
		},
		{
			"id": 9043,
			"name": "Providence Hotels",
			"brands": [
				{
					"id": 14858,
					"name": "Providence Hotels"
				}
			]
		},
		{
			"id": 9044,
			"name": "Canarian Hospitality SL",
			"brands": [
				{
					"id": 14860,
					"name": "Canarian Hospitality SL"
				}
			]
		},
		{
			"id": 9045,
			"name": "Buena Vista Holidays",
			"brands": [
				{
					"id": 14863,
					"name": "Buena Vista Holidays"
				}
			]
		},
		{
			"id": 9046,
			"name": "Bali Management Villas",
			"brands": [
				{
					"id": 14864,
					"name": "Bali Management Villas"
				}
			]
		},
		{
			"id": 9047,
			"name": "BaliSuperHost",
			"brands": [
				{
					"id": 14865,
					"name": "BaliSuperHost"
				}
			]
		},
		{
			"id": 9050,
			"name": "Ooedo Onsen Monogatari",
			"brands": [
				{
					"id": 14868,
					"name": "Ooedo Onsen Monogatari"
				}
			]
		}
	]
}