curl -i -X POST https://demandapi.booking.com/3.1/accommodations/search  -H 'Authorization: Bearer otS61_xecSKVYHBOM39i3IZou5lhZjBf2jGHnXODyTx99u0V58Ne' -H 'Content-Type: application/json' -H 'X-Affiliate-Id: 2426026' -d '{"city": 20033173, "booker": {"country": "us", "platform": "desktop"}, "checkin": "2024-10-10", "checkout": "2024-10-12", "extras": ["extra_charges", "products"], "guests": {"number_of_adults": 1, "number_of_rooms": 1}}'

curl -i -X POST https://demandapi.booking.com/3.1/accommodations/chains  -H 'Authorization: Bearer otS61_xecSKVYHBOM39i3IZou5lhZjBf2jGHnXODyTx99u0V58Ne' -H 'Content-Type: application/json' -H 'X-Affiliate-Id: 2426026' -d '{}'


curl -i -X POST \
  https://demandapi.booking.com/3.1/common/locations/cities \
  -H 'Authorization: Bearer otS61_xecSKVYHBOM39i3IZou5lhZjBf2jGHnXODyTx99u0V58Ne' \
  -H 'Content-Type: application/json' \
  -H 'X-Affiliate-Id: 2426026' \
  -d '{
    "country": "us",
    "page": "eyJhbGciOiJIUzI1NiJ9.eyJwIjp7ImNvdW50cnkiOiJ1cyIsImxhbmd1YWdlcyI6WyJlbi11cyJdLCJwYWdlX2luZGV4IjoyMDAwMTg0N30sImF1ZCI6Ii9jb21tb24vbG9jYXRpb25zL2NpdGllcyIsImV4cCI6MTcyNTY1Njk2NX0.vPTVaNvPbnEpryI1Y6L0rY5kEeXlLvC3vJoOsMrn8aY",
    "languages": [
      "en-us"
    ]
  }'
  


curl -i -X POST \
  https://demandapi.booking.com/3.1/common/locations/cities \
  -H 'Authorization: Bearer otS61_xecSKVYHBOM39i3IZou5lhZjBf2jGHnXODyTx99u0V58Ne' \
  -H 'Content-Type: application/json' \
  -H 'X-Affiliate-Id: 2426026' \
  -d '{
    "page": "eyJhbGciOiJIUzI1NiJ9.eyJwIjp7ImNvdW50cnkiOiJ1cyIsImxhbmd1YWdlcyI6WyJlbi11cyJdLCJwYWdlX2luZGV4IjoyMDAwMzk0M30sImF1ZCI6Ii9jb21tb24vbG9jYXRpb25zL2NpdGllcyIsImV4cCI6MTcyNTY1OTA2N30.KGzvxmoCPkiOPCw1jRsrFkpcGtpQ60Iq460rvwNsElE"
  }'
