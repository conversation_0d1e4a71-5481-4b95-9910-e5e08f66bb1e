{"categories": {"OTHERS": {"description": "Others", "subcategories": [{"code": "OTHS", "subcategory": "Other requests", "isFreeTextMandatory": true, "isFreeTextAllowed": true}]}, "BAGGAGE": {"description": "Baggage", "subcategories": [{"code": "SPEQ", "subcategory": "Sports equipment", "isFreeTextMandatory": true, "isFreeTextAllowed": true}]}, "PETS": {"description": "Pets", "subcategories": [{"code": "PETC", "subcategory": "Pet in cabin compartment", "isFreeTextMandatory": true, "isFreeTextAllowed": true}, {"code": "AVIH", "subcategory": "Pet in cabin or in hold", "isFreeTextMandatory": true, "isFreeTextAllowed": true}, {"code": "SVAN", "subcategory": "Service animal in cabin", "isFreeTextMandatory": true, "isFreeTextAllowed": true}, {"code": "ESAN", "subcategory": "Traveling w/emotional support animal", "isFreeTextMandatory": true, "isFreeTextAllowed": true}]}, "MEET_AND_ASSIST": {"description": "Meet & Assist", "subcategories": [{"code": "MAAS", "subcategory": "Meet & Assist", "isFreeTextMandatory": true, "isFreeTextAllowed": true}]}, "SPECIAL_ASSISTANCE_DISABILITY": {"description": "Special Assistance - Disability", "subcategories": [{"code": "BLND", "subcategory": "Blind or reduced vision", "isFreeTextMandatory": false, "isFreeTextAllowed": true}, {"code": "DEAF", "subcategory": "Deaf or hard of hearing", "isFreeTextMandatory": false, "isFreeTextAllowed": true}, {"code": "DPNA", "subcategory": "Development/intellect disability assistance", "isFreeTextMandatory": true, "isFreeTextAllowed": true}]}, "SPECIAL_ASSISTANCE_WHEELCHAIR": {"description": "Special Assistance - Wheelchair", "subcategories": [{"code": "WCHR", "subcategory": "Wheelchair/Passenger can walk up stairs", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "WCHS", "subcategory": "Wheelchair/Passenger can walk to seat", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "WCHC", "subcategory": "Wheelchair/Passenger must be carried", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "WCBD", "subcategory": "Wheelchair with dry cell battery", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "WCBW", "subcategory": "Wheelchair with wet cell battery", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "WCLB", "subcategory": "Wheelchair with lithium battery", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "WCMP", "subcategory": "Wheelchair: manual power", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "WCOB", "subcategory": "Wheelchair: onboard", "isFreeTextMandatory": false, "isFreeTextAllowed": false}]}, "UNACCOMPANIED_MINOR": {"description": "Unaccompanied minor", "subcategories": [{"code": "UMNR", "subcategory": "Unaccompanied minor - UMNR", "isFreeTextMandatory": true, "isFreeTextAllowed": true}, {"code": "UMZZ", "subcategory": "Unaccompanied minor - UMZZ", "isFreeTextMandatory": true, "isFreeTextAllowed": true}]}, "MEAL": {"description": "<PERSON><PERSON>", "subcategories": [{"code": "SPML", "subcategory": "Special meal", "isFreeTextMandatory": true, "isFreeTextAllowed": true}, {"code": "AVML", "subcategory": "Asian Vegetarian Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "BBML", "subcategory": "Baby/Infant Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "BLML", "subcategory": "<PERSON><PERSON>", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "CHML", "subcategory": "Child Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "DBML", "subcategory": "Diabetic Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "FPML", "subcategory": "Fruit Platter", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "GFML", "subcategory": "Gluten Free Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "HNML", "subcategory": "Hindu Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "KSML", "subcategory": "<PERSON><PERSON>", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "LCML", "subcategory": "Low Calorie Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "LFML", "subcategory": "Low Cholesterol / Low Fat Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "LSML", "subcategory": "Low Sodium / Low Salt Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "MOML", "subcategory": "Muslim Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "NLML", "subcategory": "Non-Lactose Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "NOML", "subcategory": "No Meal Required", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "NSML", "subcategory": "No Salt Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "PFML", "subcategory": "Peanut Free Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "RVML", "subcategory": "Raw Vegetarian Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "SFML", "subcategory": "Seafood Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "VGML", "subcategory": "Vegetarian Non-Dairy/Egg (Vegan) Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "VJML", "subcategory": "Vegetarian <PERSON> Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "VLML", "subcategory": "Ovo-Lacto Vegetarian Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}, {"code": "VOML", "subcategory": "Vegetarian Oriental Meal", "isFreeTextMandatory": false, "isFreeTextAllowed": false}]}, "BASSINET": {"description": "Bassinet", "subcategories": [{"code": "BSCT", "subcategory": "Bassinet", "isFreeTextMandatory": false, "isFreeTextAllowed": false}]}}, "metadata": {"generated_at": "2025-07-02T15:04:05.204997", "api_host": "https://api.spotnana.com"}}