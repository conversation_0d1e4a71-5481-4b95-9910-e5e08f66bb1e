{"events": [{"kind": "calendar#event", "etag": "\"2930907535260000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegon6ebme1jn4db4e0pj4p9mcdp66d9gdko3is1h6pmmesj56ph72c336cs62qbecppjerb2elj32tjhdgpme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=******************************************************************************************************************************************************************************", "created": "2016-04-14T13:16:24.000Z", "updated": "2016-06-09T06:29:27.630Z", "summary": "Flight to Paris", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMwvNhww8Oa5RYjcOJD4N83Vq4OpJKZlrY\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-06-08T13:20:00-07:00"}, "end": {"dateTime": "2016-06-08T23:30:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1s9vpgr5dp32e6crc50m09p16mgre6bq0c38ainfs7mbuf1vql3g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMwvNhww8Oa5RYjcOJD4N83Vq4OpJKZlrY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2932381929724000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegq76qr76pj6adbh75hncsb5cdp3ecbmd1k7adbe6or7ac31clpm2cje65r70oj575k3arr461in0e3lepl6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=******************************************************************************************************************************************************************************", "created": "2016-06-17T07:24:51.000Z", "updated": "2016-06-17T19:16:04.862Z", "summary": "Flight to Paris", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMlD5v4cKox5ZbPgyY9HG5_N7fKob9uNm4\n", "location": "Nice NCE", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-06-17T09:20:00-07:00"}, "end": {"dateTime": "2016-06-17T10:55:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t4skg6fe5q9cvqecr71vhhu5n66u0aesa2n1vpbe9h5od0ep8uvjg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMlD5v4cKox5ZbPgyY9HG5_N7fKob9uNm4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2937570435592000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehojeohjclomcqrccoqncobdchlmqopoeop3arpoedl3et9ldhl7acpoddjj8cjbdhp6grj5clijisbfd1gj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=******************************************************************************************************************************************************************************", "created": "2016-07-16T08:38:32.000Z", "updated": "2016-07-17T19:53:37.796Z", "summary": "Flight to Dallas", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNhAAusZ6799AjlyK5WqfRE_D_ya7IGiW8\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-07-17T02:35:00-07:00"}, "end": {"dateTime": "2016-07-17T13:00:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tq7b3eqfklf5vamdkmc8v25o8sj7u5lju38kg42klrhneee9qoha0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNhAAusZ6799AjlyK5WqfRE_D_ya7IGiW8", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2938686744320000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehm70drkdpi38tb9ctn66d9k75lmotj371i68qb5ecoj0ojedpi7ad9md4q62eb86dh6urr465j32sbc6dgj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=******************************************************************************************************************************************************************************", "created": "2016-07-23T08:10:02.000Z", "updated": "2016-07-24T06:56:12.160Z", "summary": "Flight to Biarritz", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMJafkRDSrbNskz0tocFt7-I4gfdX6PMgg\n", "location": "Paris ORY", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-07-23T22:10:00-07:00"}, "end": {"dateTime": "2016-07-23T23:30:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tlp7tnd4uignc549klvc8ddies10bnndu56i4a9h3bood1f1ql3a0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMJafkRDSrbNskz0tocFt7-I4gfdX6PMgg", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2939412957324000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehq76rhgcppnar9lc9imsrbmchh74dr5ccpjer1jcsqnaoj7dlkn6rrf6sq3gpjid8r6cdjkctpmsqbgddr6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=******************************************************************************************************************************************************************************", "created": "2016-05-23T23:17:32.000Z", "updated": "2016-07-28T11:47:58.662Z", "summary": "Flight to Copenhagen", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOapEZSo0jovfzXHyadEFh9YbQOH0fDf0g\n", "location": "Biarritz BIQ", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-07-28T02:00:00-07:00"}, "end": {"dateTime": "2016-07-28T04:35:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ttsn0fsum5benmvdbr7ec37l3g5ubgmisoo748frj6f6tgsnipkvg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOapEZSo0jovfzXHyadEFh9YbQOH0fDf0g", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2940930936990000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegomgcjmcdj6gc3icoonctb5cssjgsrfdln66q3l6or7cor3ctnm4oj6d5q76s39c9qm4d9hd1ij6q9oecr6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb21nY2ptY2RqNmdjM2ljb29uY3RiNWNzc2pnc3JmZGxuNjZxM2w2b3I3Y29yM2N0bm00b2o2ZDVxNzZzMzljOXFtNGQ5aGQxaWo2cTlvZWNyNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2016-05-25T20:54:41.000Z", "updated": "2016-08-06T06:37:48.495Z", "summary": "Flight to Paris", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMsl4r0413bNlT8Euszlbzi6yNQKzY5QZ8\n", "location": "Copenhagen CPH", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-08-05T21:50:00-07:00"}, "end": {"dateTime": "2016-08-05T23:50:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1h2vcfh0rf1vueg98somnchu66vccgobbfitspibub51he3i8s6g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMsl4r0413bNlT8Euszlbzi6yNQKzY5QZ8", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2941019475066000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpego6ktb5dkp38sb9ehkjes9n61j6kcb969o6kpb3dtm6adpn61kmgsj3ehk30rb5dpoj4sjce4qmipja75lj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnbzZrdGI1ZGtwMzhzYjllaGtqZXM5bjYxajZrY2I5NjlvNmtwYjNkdG02YWRwbjYxa21nc2ozZWhrMzByYjVkcG9qNHNqY2U0cW1pcGphNzVsajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2016-08-05T08:36:15.000Z", "updated": "2016-08-06T18:55:37.533Z", "summary": "Flight to Seattle", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPGb0BMPUKLOAIE4yHkXsQIT_6hQTkMelg\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-08-06T01:30:00-07:00"}, "end": {"dateTime": "2016-08-06T11:57:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t0juem24qiti7q70fj1i2pjecole770ihrcth0menq2rlq5ifj9k0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPGb0BMPUKLOAIE4yHkXsQIT_6hQTkMelg", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2925291520902000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehjm6sjgc5jn8t36e9q70s3he9qjecpp69j6udjfe0qj2djgdhjmodho74rmacje6kpm2pjjd9hjgcj7dgr6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoam02c2pnYzVqbjh0MzZlOXE3MHMzaGU5cWplY3BwNjlqNnVkamZlMHFqMmRqZ2Roam1vZGhvNzRybWFjamU2a3BtMnBqamQ5aGpnY2o3ZGdyNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2016-04-14T13:16:36.000Z", "updated": "2016-05-07T18:29:20.451Z", "summary": "Flight to Seattle", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMca7e0OdyZVWT7s26SGezJxPltRgJeFA8\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-08-08T01:30:00-07:00"}, "end": {"dateTime": "2016-08-08T11:57:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tgcrpagttfrtppqru7392fo6op516plgl6897e2n53afsjc82gl6g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMca7e0OdyZVWT7s26SGezJxPltRgJeFA8", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226954562000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegomsrr7cti78p3bdhh6qchld4r3er1o6kqn2o9n6dkj4sjidtin8cj26kpncspl65h6icb76os66rr565ime", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb21zcnI3Y3RpNzhwM2JkaGg2cWNobGQ0cjNlcjFvNmtxbjJvOW42ZGtqNHNqaWR0aW44Y2oyNmtwbmNzcGw2NWg2aWNiNzZvczY2cnI1NjVpbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2016-11-18T06:57:28.000Z", "updated": "2020-10-02T04:37:57.281Z", "summary": "Flight to Amsterdam (DL 8529)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOT2t3SNQbn2We9g-WzgAU9NqujT_a8Gzk\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-11-18T22:50:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2016-11-19T00:10:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1noggdtdklbm25i67l855qa73i2rroet2b53vs51bi1g68coe1eg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOT2t3SNQbn2We9g-WzgAU9NqujT_a8Gzk", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226954562000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegon0drfd1q64rpp6go34r386dnmqrjjd8smioree9k38cbj6hgm8oj76cr74c1kdpqjed9pdti6kdbkepgme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb24wZHJmZDFxNjRycHA2Z28zNHIzODZkbm1xcmpqZDhzbWlvcmVlOWszOGNiajZoZ204b2o3NmNyNzRjMWtkcHFqZWQ5cGR0aTZrZGJrZXBnbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2016-11-18T06:57:28.000Z", "updated": "2020-10-02T04:37:57.281Z", "summary": "Flight to Seattle (DL 143)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOT2t3SNQbn2We9g-WzgAU9NqujT_a8Gzk\n", "location": "Amsterdam AMS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2016-11-19T01:00:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2016-11-19T11:32:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1p7ohtbo9402lh3omnsj9icnrh41s4adbg36r04nu759odj5tvag", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOT2t3SNQbn2We9g-WzgAU9NqujT_a8Gzk", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202463500064000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehlmet1m70s7cr3kc8s6crpke9omacbfd0s64db7dpqjes3175j3irbge9mjarr7dkqmco9ncpgjitbld5hj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobG1ldDFtNzBzN2NyM2tjOHM2Y3Jwa2U5b21hY2JmZDBzNjRkYjdkcHFqZXMzMTc1ajNpcmJnZTltamFycjdka3FtY285bmNwZ2ppdGJsZDVoajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2016-09-22T03:39:52.000Z", "updated": "2020-09-27T18:35:50.032Z", "summary": "Stay at Courtyard New York Manhattan/Central Park", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPgCnHHr92COzlXJ0IHXSTzmgrnByTYHQw\n", "location": "1717 Broadway, New York, NY 10019, USA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2016-12-15"}, "end": {"date": "2016-12-20"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tkgt688vltb8fo4rqe1oh8b5gnu7pa9f9mprm5ogm5fa7fa9uuic0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPgCnHHr92COzlXJ0IHXSTzmgrnByTYHQw", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203222127998000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegp38r9p6ppj8s3f65nm6dbdd1j6mor3dpqmecr46po6irj874pj6r9k65r6uchid1l66qjddhi6mqhp6pr6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncDM4cjlwNnBwajhzM2Y2NW5tNmRiZGQxajZtb3IzZHBxbWVjcjQ2cG82aXJqODc0cGo2cjlrNjVyNnVjaGlkMWw2NnFqZGRoaTZtcWhwNnByNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-01-16T09:10:02.000Z", "updated": "2020-10-02T03:57:43.999Z", "summary": "Flight to Chicago (AS 26)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP1myjcerbSaB6q_dPLgzhoqFbCdudI_M0\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-03-07T17:55:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-03-07T21:53:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t24m96s4po1oc5mhfkccnug3d6pinh933m41vo22hjcjmldkj96vg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP1myjcerbSaB6q_dPLgzhoqFbCdudI_M0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224542612000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehm78r35cco6mqj4e9l3irrbclk74pjg60r6gpbi6pqn0q1ocdl36p3i6dim2sr464r30c9kcpk68oj5d0rme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobTc4cjM1Y2NvNm1xajRlOWwzaXJyYmNsazc0cGpnNjByNmdwYmk2cHFuMHExb2NkbDM2cDNpNmRpbTJzcjQ2NHIzMGM5a2NwazY4b2o1ZDBybWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-03-30T11:08:30.000Z", "updated": "2020-10-02T04:17:51.306Z", "summary": "Stay at AC Hotel by <PERSON><PERSON><PERSON>", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPJGTL6Vz2o_y6u1C_3wKDwgWoivXAXux8\n", "location": "Corso Europa, 1075, 16148 Genoa, Italy", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2017-04-06"}, "end": {"date": "2017-04-09"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tltlec0kjdrj9okehrfp06her6uph8cj3dr3easd16014fhdbeh7g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPJGTL6Vz2o_y6u1C_3wKDwgWoivXAXux8", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2987145955652000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehl7csr561jmusjbedl76cbl74o3ce9jd5m3iqj770r3icrl75o6sqr765p7asbd75qj0pbad1mncr34e8ome", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobDdjc3I1NjFqbXVzamJlZGw3NmNibDc0bzNjZTlqZDVtM2lxajc3MHIzaWNybDc1bzZzcXI3NjVwN2FzYmQ3NXFqMHBiYWQxbW5jcjM0ZThvbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-04-29T22:20:20.000Z", "updated": "2017-04-30T17:22:57.826Z", "summary": "Flight to Seattle", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPQvaJB1lM2uUadeXj2Ut98tBm4snM7lqs\n", "location": "Las Vegas LAS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-04-30T07:35:00-07:00"}, "end": {"dateTime": "2017-04-30T10:22:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tjvse0gorksjs1u90693il9jg8693u9pnkg1ruqm9u0ejhmvldr1g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPQvaJB1lM2uUadeXj2Ut98tBm4snM7lqs", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3147030798036000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehk6qojgelijeqppc5mmkobcclhmup3gedk3gcb3ctj6ur1ge8rj6drfcoq7ad3kelp7cqr965r3eshgctjj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoazZxb2pnZWxpamVxcHBjNW1ta29iY2NsaG11cDNnZWRrM2djYjNjdGo2dXIxZ2U4cmo2ZHJmY29xN2FkM2tlbHA3Y3FyOTY1cjNlc2hnY3RqajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-05-09T07:10:46.000Z", "updated": "2019-11-11T23:36:39.018Z", "summary": "Flight to Amsterdam", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMO7D9J580MlUow1ZIvGHl766LqQPDDmws\n", "location": "Aalborg AAL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-05-12T21:20:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-05-12T22:40:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9thmbpue7k9amjalecodpsh81cgfol0r737of4u4turvki1v7r0gg0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMO7D9J580MlUow1ZIvGHl766LqQPDDmws", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3147030798036000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpego7acblcgp66p3lehjn8tjg6pjj4sjacdp3arbh6sr32qj76lkm6t3cd8r32eb1csq78chncdjmoohjddkj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnbzdhY2JsY2dwNjZwM2xlaGpuOHRqZzZwamo0c2phY2RwM2FyYmg2c3IzMnFqNzZsa202dDNjZDhyMzJlYjFjc3E3OGNobmNkam1vb2hqZGRrajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-05-09T07:10:46.000Z", "updated": "2019-11-11T23:36:39.018Z", "summary": "Flight to Seattle", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMO7D9J580MlUow1ZIvGHl766LqQPDDmws\n", "location": "Amsterdam AMS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-05-13T00:50:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-05-13T11:03:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t0u1ud2cdutgtvp6g2rjcr5mq761jg5ictlj619ag4t27cglb3ki0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMO7D9J580MlUow1ZIvGHl766LqQPDDmws", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"2994428461740000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegsn2or4c5n36d3371on4e3hd1ij2dj4cgqjas9h6kp6iq1hc8qj4sbidhjjcdb6edijaohp6dh38q3fdgqme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnc24yb3I0YzVuMzZkMzM3MW9uNGUzaGQxaWoyZGo0Y2dxamFzOWg2a3A2aXExaGM4cWo0c2JpZGhqamNkYjZlZGlqYW9ocDZkaDM4cTNmZGdxbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-06-11T20:50:30.000Z", "updated": "2017-06-11T20:50:30.870Z", "summary": "Flight to Paris (DL 1014)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOwQ_QZQc8gK3jLK6DL9vNOjTTza31B2bk\n", "location": "New York City JFK", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-06-12T18:50:00-07:00"}, "end": {"dateTime": "2017-06-13T02:10:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t9qcdan34c8qr8qhe16dd55q152ih1b52qrlg65fse5b93b4hol5g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOwQ_QZQc8gK3jLK6DL9vNOjTTza31B2bk", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873053630000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehkm2rric9qj0c38d1q36e3m6hhm4dbk60smsrrgd8rncc35ccs66sbbcsr6mprfdpqmmohje9mj2qbhcdoj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoa20ycnJpYzlxajBjMzhkMXEzNmUzbTZoaG00ZGJrNjBzbXNycmdkOHJuY2MzNWNjczY2c2JiY3NyNm1wcmZkcHFtbW9oamU5bWoycWJoY2RvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-05-18T22:59:16.000Z", "updated": "2021-03-11T04:22:06.815Z", "summary": "Flight to Split (TO 3570)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNSS2aeBOpZGTk5Uev2RqxZknNv2HoeVEc\n", "location": "Paris ORY", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-06-25T07:10:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-06-25T09:20:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tiaorbu00hht38v4cb5t09nopj7v0ec8cqkg6kgonukb3rm1iqcq0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNSS2aeBOpZGTk5Uev2RqxZknNv2HoeVEc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202463501218000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegp6qtbj6lmmqdba6hjmkd9odcs32drme0qmic3b6oomicbedhpjid3cd4rncpb9ctn6arrddkq3eqr76orme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncDZxdGJqNmxtbXFkYmE2aGpta2Q5b2RjczMyZHJtZTBxbWljM2I2b29taWNiZWRocGppZDNjZDRybmNwYjljdG42YXJyZGRrcTNlcXI3Nm9ybWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-05-18T22:59:16.000Z", "updated": "2020-09-27T18:35:50.609Z", "summary": "Flight to Paris (TO 3747)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNSS2aeBOpZGTk5Uev2RqxZknNv2HoeVEc\n", "location": "Dubrovnik DBV", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-07-03T00:10:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-07-03T02:40:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t2mus5mm5j4gj58k817vp5i0k61i1nls94li7veigneomm47kg67g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNSS2aeBOpZGTk5Uev2RqxZknNv2HoeVEc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203222127026000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehomgc1jccq3ao9ke0omkqrdc4o6eoj6clqnap36c8pjarrccko38p36dks74ojce4qmqphl6ho70qj4eln30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVob21nYzFqY2NxM2FvOWtlMG9ta3FyZGM0bzZlb2o2Y2xxbmFwMzZjOHBqYXJyY2NrbzM4cDM2ZGtzNzRvamNlNHFtcXBobDZobzcwcWo0ZWxuMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-07-16T09:10:37.000Z", "updated": "2020-10-02T03:57:43.513Z", "summary": "Flight to Boston (DL 119)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNtwF4EcKBDJnAXuvXadaIaf17YGX1IVWE\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-07-17T01:35:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-07-17T09:33:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tqh03c45a4p1jkma0gbfeuudfb35ole04dfm8rblq5mf54ppjdun0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNtwF4EcKBDJnAXuvXadaIaf17YGX1IVWE", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202461085212000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpeho6mc1lchljidb5ecpj4t1je5lmocrk74r3iqb8e5nm6qj5edgmudph6hkmes1g61m62ebddoqmschmclk30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobzZtYzFsY2hsamlkYjVlY3BqNHQxamU1bG1vY3JrNzRyM2lxYjhlNW5tNnFqNWVkZ211ZHBoNmhrbWVzMWc2MW02MmViZGRvcW1zY2htY2xrMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-10-03T16:32:01.000Z", "updated": "2020-09-27T18:15:42.606Z", "summary": "Stay at Willows Lodge", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPD3qvxuXvdUK8t4QWgePzbF92068d3yi0\n", "location": "Willows Lodge, Woodinville", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2017-10-06"}, "end": {"date": "2017-10-08"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tpk05dk95es32t3qkl3t969ihqocjesao714igp00la9mn5n26eh0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPD3qvxuXvdUK8t4QWgePzbF92068d3yi0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226959094000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehqm2p9md9p6gd9pcdo78qr8egp70pppegp30p3c6lh76rj5elljge3ac9jmirpnd9q6otj3ctlncd9kdlp6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocW0ycDltZDlwNmdkOXBjZG83OHFyOGVncDcwcHBwZWdwMzBwM2M2bGg3NnJqNWVsbGpnZTNhYzlqbWlycG5kOXE2b3RqM2N0bG5jZDlrZGxwNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-11-05T01:18:47.000Z", "updated": "2020-10-02T04:37:59.547Z", "summary": "Flight to Dubai (EK 230)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOBYunSLvBRqi7-Y8l7vtGrWdgNhtbMAVQ\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-11-06T17:10:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-11-07T07:40:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tuae6jrh59cptkht2pg9t20dl5bsneuk88jbgio7jtlvcgkv54mrg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOBYunSLvBRqi7-Y8l7vtGrWdgNhtbMAVQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226959094000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegqmuq9pdcr6eqjed5ijcd1icsqnct376gp7arj96ll78tjd6tp6qpbc6co30d9iehmm4qjke1pn4cj3eoo30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncW11cTlwZGNyNmVxamVkNWlqY2QxaWNzcW5jdDM3NmdwN2Fyajk2bGw3OHRqZDZ0cDZxcGJjNmNvMzBkOWllaG1tNHFqa2UxcG40Y2ozZW9vMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-11-05T01:18:47.000Z", "updated": "2020-10-02T04:37:59.547Z", "summary": "Flight to New Delhi (EK 512)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOBYunSLvBRqi7-Y8l7vtGrWdgNhtbMAVQ\n", "location": "Dubai DXB", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-11-07T10:00:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-11-07T13:10:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t5oi9k6gjnie642g5vtg42uni5jtvm7rmel30052tmbjtpsr2cv00", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOBYunSLvBRqi7-Y8l7vtGrWdgNhtbMAVQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226959094000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegoj6rr7d4on8rr5elm3csbbepomqtbmd1jmodpk75h62pj1e5m6morichkn2dj861pmurb3d1qmkd9kcco30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb2o2cnI3ZDRvbjhycjVlbG0zY3NiYmVwb21xdGJtZDFqbW9kcGs3NWg2MnBqMWU1bTZtb3JpY2hrbjJkajg2MXBtdXJiM2QxcW1rZDlrY2NvMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-11-08T22:47:55.000Z", "updated": "2020-10-02T04:37:59.547Z", "summary": "Flight to Dubai (EK 513)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNol8c96ca5xSeS4MjG5ZNAdYBuFMQ9PK4\n", "location": "New Delhi DEL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-11-10T14:40:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-11-10T18:35:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t13ogi1toeul6qkvqmuvhgl749bafaqlkcrdiq6h0somchuj54c00", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNol8c96ca5xSeS4MjG5ZNAdYBuFMQ9PK4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226959094000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehi6aq1jd4o34e376tp76djkedi6sohncpo6qohkddnjie3b71kmqrj4edm6qcb1clk30q31dkp36ebed1k6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaTZhcTFqZDRvMzRlMzc2dHA3NmRqa2VkaTZzb2huY3BvNnFvaGtkZG5qaWUzYjcxa21xcmo0ZWRtNnFjYjFjbGszMHEzMWRrcDM2ZWJlZDFrNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-10-12T02:20:49.000Z", "updated": "2020-10-02T04:37:59.547Z", "summary": "Flight to Seattle (EK 229)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOujhpV6AAKn7CKJBr-6KsXbswTXbCLT_s\n", "location": "Dubai DXB", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-11-10T20:05:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-11-11T10:45:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tdeh3i028g7rs6tsdnb7fpmb4ko98k8imndslm1aeh0ham239nhhg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOujhpV6AAKn7CKJBr-6KsXbswTXbCLT_s", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226957560000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehhj4p356li62t3aehl6asradlo38rr9epkm8e31epk34ob26so3ar9jd0r74t1o6hhjgp1h6tgmis9je1lme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaGo0cDM1NmxpNjJ0M2FlaGw2YXNyYWRsbzM4cnI5ZXBrbThlMzFlcGszNG9iMjZzbzNhcjlqZDByNzR0MW82aGhqZ3AxaDZ0Z21pczlqZTFsbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-08-08T01:27:29.000Z", "updated": "2020-10-02T04:37:58.780Z", "summary": "Flight to Salt Lake City (DL 508)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOEjhwT0Nh1veJPLNLnGp9eh4fwzCz7YHY\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-11-16T18:10:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-11-16T20:07:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tc2de5datjtjesjmp4oivid8avh2ab705m3h6rt84c8d17aiq3pkg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOEjhwT0Nh1veJPLNLnGp9eh4fwzCz7YHY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226957560000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegqmuc3be4p62cb5cso6msr5ccrmcdrbccq34priclnm2qb2ddn6mrb6ctkmiqrmc5q6mrppedojcsbc6dkj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncW11YzNiZTRwNjJjYjVjc282bXNyNWNjcm1jZHJiY2NxMzRwcmljbG5tMnFiMmRkbjZtcmI2Y3RrbWlxcm1jNXE2bXJwcGVkb2pjc2JjNmRrajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-08-08T01:27:29.000Z", "updated": "2020-10-02T04:37:58.780Z", "summary": "Flight to Fort Lauderdale (DL 1331)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOEjhwT0Nh1veJPLNLnGp9eh4fwzCz7YHY\n", "location": "Salt Lake City SLC", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-11-16T22:25:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-11-17T02:45:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t5o0kq2a1eg0ksec7f7kc42greoaibknkmfgiikvatko9sq6ql3i0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOEjhwT0Nh1veJPLNLnGp9eh4fwzCz7YHY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226957560000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegp74e1le9lm4rbac9kmgr1jctn76shj61p6kq3961m36cb1d8pn0rhiccp6iphoepk6ar9kdthmmoplctm30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncDc0ZTFsZTlsbTRyYmFjOWttZ3IxamN0bjc2c2hqNjFwNmtxMzk2MW0zNmNiMWQ4cG4wcmhpY2NwNmlwaG9lcGs2YXI5a2R0aG1tb3BsY3RtMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-08-08T01:27:29.000Z", "updated": "2020-10-02T04:37:58.780Z", "summary": "Flight to Atlanta (DL 2584)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOEjhwT0Nh1veJPLNLnGp9eh4fwzCz7YHY\n", "location": "Fort Myers RSW", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-11-24T04:00:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-11-24T05:47:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t2r85rkbmjbihl3gnsr30rjhi0l31aj3pn2c2if8vhem4ockc5gl0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOEjhwT0Nh1veJPLNLnGp9eh4fwzCz7YHY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226957560000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegs6ie9lehl6at3661o76shn6sp6up3ictomeopodso3gc346tqmmrr86gsmirj1ccpnashi64pn4obe6pmj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnczZpZTlsZWhsNmF0MzY2MW83NnNobjZzcDZ1cDNpY3RvbWVvcG9kc28zZ2MzNDZ0cW1tcnI4NmdzbWlyajFjY3BuYXNoaTY0cG40b2JlNnBtajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2017-08-08T01:27:29.000Z", "updated": "2020-10-02T04:37:58.780Z", "summary": "Flight to Seattle (DL 39)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOEjhwT0Nh1veJPLNLnGp9eh4fwzCz7YHY\n", "location": "Atlanta ATL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2017-11-24T07:50:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2017-11-24T13:14:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t8i95tjetf0psr772odrgqgc8o080d7ukoh49inac3ur213ran6m0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOEjhwT0Nh1veJPLNLnGp9eh4fwzCz7YHY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3063323661646000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegqjgdjm6kp6uphnd5qj2sb9clj6aqbkdloj8r1kdss3ec3keor6cc3d70oj8ople1r6gtbcd1k78spp6op6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncWpnZGptNmtwNnVwaG5kNXFqMnNiOWNsajZhcWJrZGxvajhyMWtkc3MzZWMza2VvcjZjYzNkNzBvajhvcGxlMXI2Z3RiY2Qxazc4c3BwNm9wNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-03-05T22:03:22.000Z", "updated": "2018-07-15T13:37:10.823Z", "summary": "Flight to Paris (AF 3653)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPMayC66aVXDNawi2wQGSzhRxjGeJzjfL0\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-03-12T13:18:00-07:00"}, "end": {"dateTime": "2018-03-12T23:20:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t586v52of7iu1qiefeitmq4l4o870tv6f0m814c5pvhulhhts962g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPMayC66aVXDNawi2wQGSzhRxjGeJzjfL0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3063323661646000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehnj6s3ed5k7asr7elm6gsjk6go6uq31e4qmie386ppjes1lc5q6qohp71kmat1ne8sm2e1j74q76qrjdcoj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobmo2czNlZDVrN2FzcjdlbG02Z3NqazZnbzZ1cTMxZTRxbWllMzg2cHBqZXMxbGM1cTZxb2hwNzFrbWF0MW5lOHNtMmUxajc0cTc2cXJqZGNvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-03-11T20:20:10.000Z", "updated": "2018-07-15T13:37:10.823Z", "summary": "Flight to Singapore (AF 256)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMbgnN49Chqr_d_6cdJEAOvqaaWmWtjL-M\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-03-17T12:50:00-07:00"}, "end": {"dateTime": "2018-03-18T01:35:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9to3pnihusgulhrt40ohaq5i8h6s7p5atmb98iet7r9a8394sksk10", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMbgnN49Chqr_d_6cdJEAOvqaaWmWtjL-M", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3063323661646000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehlmcq34e9k3ce1md5pmgr9mdhh3epb9egq64q9p6dmnct3ke5njccjac5j32t9gd9lmqprgc5m76qjic5h30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobG1jcTM0ZTlrM2NlMW1kNXBtZ3I5bWRoaDNlcGI5ZWdxNjRxOXA2ZG1uY3Qza2U1bmpjY2phYzVqMzJ0OWdkOWxtcXByZ2M1bTc2cWppYzVoMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-03-11T20:20:10.000Z", "updated": "2018-07-15T13:37:10.823Z", "summary": "Flight to Seattle (DL 166)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMbgnN49Chqr_d_6cdJEAOvqaaWmWtjL-M\n", "location": "Singapore SIN", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-03-23T15:45:00-07:00"}, "end": {"dateTime": "2018-03-24T10:48:00-07:00"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tkfhdrh686ishm6lb7eit4bi93mvttqo62jaf1u0jkmgpalsjrab0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMbgnN49Chqr_d_6cdJEAOvqaaWmWtjL-M", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874255614000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpeho34dpo6tj3cqjfddpmkcjlcpj6esrkdtl6uqbfcpq6edbee9n32cpo6tijcshk6pqjarrfc8qn8d1n65hj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobzM0ZHBvNnRqM2NxamZkZHBta2NqbGNwajZlc3JrZHRsNnVxYmZjcHE2ZWRiZWU5bjMyY3BvNnRpamNzaGs2cHFqYXJyZmM4cW44ZDFuNjVoajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-05-22T21:02:16.000Z", "updated": "2021-03-11T04:32:07.807Z", "summary": "Flight to Paris (DL 34)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMLblyRGgKJChX_aEIFGEUfOVZ-UQt9bQc\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-05-29T13:20:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-05-29T23:10:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tp2787f6joksj2uffgstojoioftg5nrn1387e6r46u5oob5t471c0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMLblyRGgKJChX_aEIFGEUfOVZ-UQt9bQc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873052796000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegon4r39d9l7ctjld1kmso9j69pmec3kc4p32p3eepn74rr36lhj2rrdc9i7arhkcsr6ipbhe0omsdbi71i6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb240cjM5ZDlsN2N0amxkMWttc285ajY5cG1lYzNrYzRwMzJwM2VlcG43NHJyMzZsaGoycnJkYzlpN2FyaGtjc3I2aXBiaGUwb21zZGJpNzFpNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-06-07T23:32:37.000Z", "updated": "2021-03-11T04:22:06.398Z", "summary": "Flight to Paris (DL 8402)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO8LSokq-JuFlfOYbBO7aieJvMUOmaKXoU\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-06-08T16:30:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-06-09T02:10:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1rlijjvvuhina32sg0ta21dnvnroc5c1ombdun4g6ieqp1n5r8dg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO8LSokq-JuFlfOYbBO7aieJvMUOmaKXoU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202462290210000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegsjeq3365qjeor5epij0cbfe9omsd1od4on8s1gd9q6isj26pn3aoj2cgp74dr261gj8d9pctl6kc1le1ome", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnc2plcTMzNjVxamVvcjVlcGlqMGNiZmU5b21zZDFvZDRvbjhzMWdkOXE2aXNqMjZwbjNhb2oyY2dwNzRkcjI2MWdqOGQ5cGN0bDZrYzFsZTFvbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-07-06T12:25:19.000Z", "updated": "2020-09-27T18:25:45.105Z", "summary": "Flight to Paris (KM 476)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM2_-mvAlmiX2m5sJuJpcebUJB-yalkwzE\n", "location": "Malta MLA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-07-07T04:50:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-07-07T07:35:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t97hc1u7ceve01orqn48i1tp0jtirb6n5bbd2r7b0a459gjj05pqg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM2_-mvAlmiX2m5sJuJpcebUJB-yalkwzE", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873052796000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehom2qj6d8qj8phhe9mmmsb9e1jjarr5cpp36prkcth3irb1epjjepj4dhnjarji6hmn0dppe5p3ed9mddqj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVob20ycWo2ZDhxajhwaGhlOW1tbXNiOWUxamphcnI1Y3BwMzZwcmtjdGgzaXJiMWVwamplcGo0ZGhuamFyamk2aG1uMGRwcGU1cDNlZDltZGRxajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-06-26T14:13:04.000Z", "updated": "2021-03-11T04:22:06.398Z", "summary": "Stay at Newly Refurbished 3 Bedroom Mews House, Marylebone", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN-d0B3l022CvbH8w76RqkFGYaKtaO__D0\n", "location": "Newly Refurbished 3 Bedroom Mews House, Marylebone, London", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2018-07-12"}, "end": {"date": "2018-07-21"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tqajfj54f1rmkqipg5oefr3gtgb9mavg7fdlo5nr4mp79qr756ku0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN-d0B3l022CvbH8w76RqkFGYaKtaO__D0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873052796000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegqjiqbhdlnj6s1kelh3edj5d4o36srd75incsbgdso3edr9elijas36dpm76r1h65l64e3jclm66pjledkme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncWppcWJoZGxuajZzMWtlbGgzZWRqNWQ0bzM2c3JkNzVpbmNzYmdkc28zZWRyOWVsaWphczM2ZHBtNzZyMWg2NWw2NGUzamNsbTY2cGpsZWRrbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-07-17T13:09:27.000Z", "updated": "2021-03-11T04:22:06.398Z", "summary": "Flight to Copenhagen (D8 3645)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNlQZHNC_hzo1bnAaiW-kEGdevdT3BVbzY\n", "location": "Nice NCE", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-07-20T05:00:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-07-20T07:15:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t59iqmo3p4ub76ei03sm9evqpo077iue5pfnlsl11jb8selcfusig", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNlQZHNC_hzo1bnAaiW-kEGdevdT3BVbzY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873052796000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehoncqjee8qmuq3464r62ojfd9kncrj875o6crjf6ssmocr1dsp6kr1i6gq70pr16kr6cs3lctl3acr5ekoj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVob25jcWplZThxbXVxMzQ2NHI2Mm9qZmQ5a25jcmo4NzVvNmNyamY2c3Ntb2NyMWRzcDZrcjFpNmdxNzBwcjE2a3I2Y3MzbGN0bDNhY3I1ZWtvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-06-07T23:32:37.000Z", "updated": "2021-03-11T04:22:06.398Z", "summary": "Flight to Amsterdam (DL 9435)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO8LSokq-JuFlfOYbBO7aieJvMUOmaKXoU\n", "location": "Copenhagen CPH", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-07-28T02:50:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-07-28T04:15:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tqvjnr5ohd16abojivnh9pfno79l3ao2jl244pga56fpugj53eu10", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO8LSokq-JuFlfOYbBO7aieJvMUOmaKXoU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873052796000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegomqc39e9mn8pbid9m74r3be4q6ocph6somseb670o62d1melj76e32dhgm8t1p65gmsqb7doqmos3465gme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb21xYzM5ZTltbjhwYmlkOW03NHIzYmU0cTZvY3BoNnNvbXNlYjY3MG82MmQxbWVsajc2ZTMyZGhnbTh0MXA2NWdtc3FiN2RvcW1vczM0NjVnbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-06-07T23:32:37.000Z", "updated": "2021-03-11T04:22:06.398Z", "summary": "Flight to Seattle (DL 145)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO8LSokq-JuFlfOYbBO7aieJvMUOmaKXoU\n", "location": "Amsterdam AMS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-07-28T05:30:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-07-28T15:46:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1m0irmterjlrlkq4l3171n9f80a46ufs8bladt91anign5lpd1ag", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO8LSokq-JuFlfOYbBO7aieJvMUOmaKXoU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223333952000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehk6ocr461hmed9gcph62pj7dso6crbj6sr6acre6tqmmdhj68s6ur9l69r7ceb461hn8c1j75r3ip9jdloj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoazZvY3I0NjFobWVkOWdjcGg2MnBqN2RzbzZjcmJqNnNyNmFjcmU2dHFtbWRoajY4czZ1cjlsNjlyN2NlYjQ2MWhuOGMxajc1cjNpcDlqZGxvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-06T21:38:36.000Z", "updated": "2020-10-02T04:07:46.976Z", "summary": "Flight to San Diego (DL 2389)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPUGoDI9gcA3wxhcm1l66Dsd1JUP67h6KM\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-08-13T07:50:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-08-13T10:52:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9thl3d0cg50fbafgo0fms76e3n7uk6328om52vv9d0ct039v9e3mq0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPUGoDI9gcA3wxhcm1l66Dsd1JUP67h6KM", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203222130672000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegrmicjactlj4p1le1l6ccrhellmqdridhmn2pj36djnad3aekp32qre6ksn0cj9ekomop3idho6qd9iclh30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncm1pY2phY3RsajRwMWxlMWw2Y2NyaGVsbG1xZHJpZGhtbjJwajM2ZGpuYWQzYWVrcDMycXJlNmtzbjBjajlla29tb3AzaWRobzZxZDlpY2xoMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-25T15:00:48.000Z", "updated": "2020-10-02T03:57:45.336Z", "summary": "Flight to Boston (B6 298)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx06TtbtIsKcWmguja3LjinvjyKX9sSrQ\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-08-26T08:00:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-08-26T13:20:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t7i2jgk2d5pjf3qukm7rlmqfc3gu4ju21kn59p2iu1ldrlpm52eb0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx06TtbtIsKcWmguja3LjinvjyKX9sSrQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203222130672000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegq3apr169hm4ohk61o6cr3k6tknadpodlnn8rjmc4p70shnepn32rbacdq6odbbdgojcrj86cr3arjj65jj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncTNhcHIxNjlobTRvaGs2MW82Y3IzazZ0a25hZHBvZGxubjhyam1jNHA3MHNobmVwbjMycmJhY2RxNm9kYmJkZ29qY3JqODZjcjNhcmpqNjVqajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-29T01:56:53.000Z", "updated": "2020-10-02T03:57:45.336Z", "summary": "Flight to Seattle (B6 497)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMkmJhNvIkhh95qN_dlijAggqSpwq3-SBo\n", "location": "Boston BOS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-08-29T19:39:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-08-30T00:40:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t45ga2cbb40pflt7iu78motnva2pr7vn1mjctl5kl16nh365ns1g0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMkmJhNvIkhh95qN_dlijAggqSpwq3-SBo", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224541394000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehhj2thhd1jmuqj1dhim8qbbdhm3crj66pkmcs1lctkn2db1c4oj0pr6ddpn6p1me9r6cc3861q34rbme1oj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaGoydGhoZDFqbXVxajFkaGltOHFiYmRobTNjcmo2NnBrbWNzMWxjdGtuMmRiMWM0b2owcHI2ZGRwbjZwMW1lOXI2Y2MzODYxcTM0cmJtZTFvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-24T23:03:03.000Z", "updated": "2020-10-02T04:17:50.697Z", "summary": "Flight to Newark (UA 1155)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMq8bDnF5VV0zbevPNVaKIZ_3GPPJSC0e4\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-09-22T11:15:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-09-22T16:27:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tc1v1hgojaledikll6nf6ifp5giq5aa10gfkssd6rvf0h0t2mvpq0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMq8bDnF5VV0zbevPNVaKIZ_3GPPJSC0e4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224541394000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegom6cjl64s76o9me5i72prhdpq6ud3eclln4ob46hm6eebb6tm6kd3be5jmic38d0pj6pj464q6gdr5coo6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb202Y2psNjRzNzZvOW1lNWk3MnByaGRwcTZ1ZDNlY2xsbjRvYjQ2aG02ZWViYjZ0bTZrZDNiZTVqbWljMzhkMHBqNnBqNDY0cTZnZHI1Y29vNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-24T23:03:03.000Z", "updated": "2020-10-02T04:17:50.697Z", "summary": "Flight to New Delhi (UA 82)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMq8bDnF5VV0zbevPNVaKIZ_3GPPJSC0e4\n", "location": "Newark EWR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-09-22T18:50:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-09-23T09:00:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1c2u18sa6qdqgqnto4nekrad4lg9k7lj4kqgi0hh33fd14h7ef0g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMq8bDnF5VV0zbevPNVaKIZ_3GPPJSC0e4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224541394000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehkn4cbhdcqn6qrcdtl70opp6ppmgob7cli70p38dtomgtjedhk7ctb571h70or66di6qqbjccq6ee31e8qj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoa240Y2JoZGNxbjZxcmNkdGw3MG9wcDZwcG1nb2I3Y2xpNzBwMzhkdG9tZ3RqZWRoazdjdGI1NzFoNzBvcjY2ZGk2cXFiamNjcTZlZTMxZThxajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-24T23:03:03.000Z", "updated": "2020-10-02T04:17:50.697Z", "summary": "Flight to Newark (UA 83)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMq8bDnF5VV0zbevPNVaKIZ_3GPPJSC0e4\n", "location": "New Delhi DEL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-09-27T11:05:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-09-28T01:50:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tir1qk5sklojpc96shagedpdhoqhvnlhvue8bpcf3dmisc4g8ar50", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMq8bDnF5VV0zbevPNVaKIZ_3GPPJSC0e4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224541394000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegq32or8ctqjedblehgj2djld9gmid9l6os3cqrkcdgmkr3lddlmsqjkehgmeqrh70pncpbk6pqm8r1ge1nme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncTMyb3I4Y3RxamVkYmxlaGdqMmRqbGQ5Z21pZDlsNm9zM2NxcmtjZGdta3IzbGRkbG1zcWprZWhnbWVxcmg3MHBuY3BiazZwcW04cjFnZTFubWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-24T23:03:03.000Z", "updated": "2020-10-02T04:17:50.697Z", "summary": "Flight to Seattle (UA 280)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMq8bDnF5VV0zbevPNVaKIZ_3GPPJSC0e4\n", "location": "Newark EWR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-09-28T04:40:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-09-28T10:39:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t41chgu75uta16ujai55686ktcajlukknjttagkq83vet6udl0pog", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMq8bDnF5VV0zbevPNVaKIZ_3GPPJSC0e4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874256404000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehjncdbieksjepjdelj72src71njadjectpn2p1hdkq3gdr7c8o6qdjgc9jmgd1iedo6gpppc5r6ce336pp30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoam5jZGJpZWtzamVwamRlbGo3MnNyYzcxbmphZGplY3RwbjJwMWhka3EzZ2RyN2M4bzZxZGpnYzlqbWdkMWllZG82Z3BwcGM1cjZjZTMzNnBwMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-11-22T08:34:34.000Z", "updated": "2021-03-11T04:32:08.202Z", "summary": "Flight to Las Vegas (DL 1656)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOX67K-Fg1VtcHdXecneH1KEU-nkvjEZIA\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-12-05T07:50:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-12-05T10:28:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tgv5ru97fmufqsl8o56ngsqd1m487gb0m6pbgh42sphg9avf8c6r0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOX67K-Fg1VtcHdXecneH1KEU-nkvjEZIA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874256404000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehmmar32dcoj0t3j6so7cprg69pjip3j60rjetj8epo6id38e4smmeb2doq7as3k6ln62t3fd5jm2dhndop30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobW1hcjMyZGNvajB0M2o2c283Y3ByZzY5cGppcDNqNjByamV0ajhlcG82aWQzOGU0c21tZWIyZG9xN2FzM2s2bG42MnQzZmQ1am0yZGhuZG9wMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-11-22T08:34:34.000Z", "updated": "2021-03-11T04:32:08.202Z", "summary": "Flight to Seattle (DL 2414)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOX67K-Fg1VtcHdXecneH1KEU-nkvjEZIA\n", "location": "Las Vegas LAS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-12-07T13:53:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-12-07T16:24:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tmelbk10ts70vgp2s9ds077vhvpi4hq9k9bn4upt5natoiga67n20", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOX67K-Fg1VtcHdXecneH1KEU-nkvjEZIA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3147031094654000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehj70sr5cssjeqpjc5pm6dpl64sj8pbddkr72e3ledq6qeb1ctk3gcbjc9p68rbaddjm6s3m71kmecjjctjme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoajcwc3I1Y3NzamVxcGpjNXBtNmRwbDY0c2o4cGJkZGtyNzJlM2xlZHE2cWViMWN0azNnY2JqYzlwNjhyYmFkZGptNnMzbTcxa21lY2pqY3RqbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-22T23:22:25.000Z", "updated": "2019-11-11T23:39:07.327Z", "summary": "Flight to New York City (DL 1576)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP0mQRi7Hfha8Z0ITpzhfqlIANGQw0hZkU\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-12-12T23:08:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-12-13T04:20:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tfpseg97k3asc75194emm6q8ustm9agh81sbrdmjkgcpv8ig2sggg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP0mQRi7Hfha8Z0ITpzhfqlIANGQw0hZkU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3147031094654000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehpmkcj6d4o6ee9nc9im6prldti6eqbk6orn8qj5e1mj6cj4c5r7cebfcpkmktjed5qnccr9d9q36t3j6tnj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocG1rY2o2ZDRvNmVlOW5jOWltNnBybGR0aTZlcWJrNm9ybjhxajVlMW1qNmNqNGM1cjdjZWJmY3BrbWt0amVkNXFuY2NyOWQ5cTM2dDNqNnRuajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-08-22T23:21:57.000Z", "updated": "2019-11-11T23:39:07.327Z", "summary": "Flight to Seattle (DL 1206)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP0mQRi7Hfha8Z0ITpzhfqlIANGQw0hZkU\n", "location": "New York City JFK", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2018-12-16T12:55:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2018-12-16T19:25:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tsj2fi0g97becguodgit67tjepm32davv9ofijvniuv3ijt3ts7o0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP0mQRi7Hfha8Z0ITpzhfqlIANGQw0hZkU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223336682000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehjn6s3cctkj6rbadcp6kob3e5hmusbaeppmqtj5d9lm2t1pc5jmksr56pimceb1dpkmatjlecq6eqrg75kme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoam42czNjY3RrajZyYmFkY3A2a29iM2U1aG11c2JhZXBwbXF0ajVkOWxtMnQxcGM1am1rc3I1NnBpbWNlYjFkcGttYXRqbGVjcTZlcXJnNzVrbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-11-15T00:10:34.000Z", "updated": "2020-10-02T04:07:48.341Z", "summary": "Flight to Paris (DL 34)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1M-DXnQCVouxXimqMg-Ve-7GaJLC_nGQ\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-01-06T11:43:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-01-06T22:15:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tgsplgi3mjk2jacqcoqjvsmvejkat9agjse6ef9anievus4gkp9ig", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1M-DXnQCVouxXimqMg-Ve-7GaJLC_nGQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223336682000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehln0c9nepl7arhldplm2c1jcks3aq3268r6is33dhq3arbj60r68dhi65h3ioj4dtomstjg6cojgchkccq30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobG4wYzluZXBsN2FyaGxkcGxtMmMxamNrczNhcTMyNjhyNmlzMzNkaHEzYXJiajYwcjY4ZGhpNjVoM2lvajRkdG9tc3RqZzZjb2pnY2hrY2NxMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-11-15T00:10:34.000Z", "updated": "2020-10-02T04:07:48.341Z", "summary": "Flight to Dublin (DL 8577)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1M-DXnQCVouxXimqMg-Ve-7GaJLC_nGQ\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-01-07T01:05:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-01-07T02:50:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tkp17vjun5nka03e85hb26ipclt5ms06d621b9bdoqnvp31824c40", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1M-DXnQCVouxXimqMg-Ve-7GaJLC_nGQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223336682000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehq7cdjbchj30thhehi64t3hdhr3ge3j6gs6spj574r6sdj5edm3arppe1i64orkelkj6e1h75k7cq1jedo30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocTdjZGpiY2hqMzB0aGhlaGk2NHQzaGRocjNnZTNqNmdzNnNwajU3NHI2c2RqNWVkbTNhcnBwZTFpNjRvcmtlbGtqNmUxaDc1azdjcTFqZWRvMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-11-15T00:10:34.000Z", "updated": "2020-10-02T04:07:48.341Z", "summary": "Flight to Amsterdam (DL 9609)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1M-DXnQCVouxXimqMg-Ve-7GaJLC_nGQ\n", "location": "Dublin DUB", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-01-10T21:55:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-01-10T23:35:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ttv6kdf0v1tdbtqlv88s48nfe96n6esl5o9pdbctui3819hvh3sp0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1M-DXnQCVouxXimqMg-Ve-7GaJLC_nGQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223336682000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehpmet316gsmupjjdsqj2phldgs7aebac5nn6opmccr6id9o71hmkdrlcsomiq9kc9p70s3169r6qor871pme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocG1ldDMxNmdzbXVwampkc3FqMnBobGRnczdhZWJhYzVubjZvcG1jY3I2aWQ5bzcxaG1rZHJsY3NvbWlxOWtjOXA3MHMzMTY5cjZxb3I4NzFwbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2018-11-15T00:10:34.000Z", "updated": "2020-10-02T04:07:48.341Z", "summary": "Flight to Seattle (DL 143)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1M-DXnQCVouxXimqMg-Ve-7GaJLC_nGQ\n", "location": "Amsterdam AMS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-01-11T00:50:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-01-11T11:20:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tsgta49ofso51f5l8u9jaosc6c6i588cj7ug1ii4brppa2vmch8sg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1M-DXnQCVouxXimqMg-Ve-7GaJLC_nGQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202461085484000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehq68rb66dmmmqbkdsomutbjd9qj4rb4cphn6chnctnmqd9h69l68d1l6hk38drl6homeprl6ljmiormcco6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocTY4cmI2NmRtbW1xYmtkc29tdXRiamQ5cWo0cmI0Y3BobjZjaG5jdG5tcWQ5aDY5bDY4ZDFsNmhrMzhkcmw2aG9tZXBybDZsam1pb3JtY2NvNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-02-16T22:13:17.000Z", "updated": "2020-09-27T18:15:42.742Z", "summary": "Stay at Fairmont Hotel Vancouver", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPXckRlL-yxYQWSwAUF6yVV35pu9U9x3r8\n", "location": "Fairmont Hotel Vancouver, Vancouver", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2019-02-17"}, "end": {"date": "2019-02-19"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ttdmf3mkito1ousju2mdfcs27gom512jd454h47u4qggu5gicvc0g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPXckRlL-yxYQWSwAUF6yVV35pu9U9x3r8", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202461085484000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpeho3cq32elpm4oja75lj8s3ee1o6cqj2d4smiobi71hmeobfclgjcsbmehq38c39epgm6sjme4rm8orkcln6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobzNjcTMyZWxwbTRvamE3NWxqOHMzZWUxbzZjcWoyZDRzbWlvYmk3MWhtZW9iZmNsZ2pjc2JtZWhxMzhjMzllcGdtNnNqbWU0cm04b3JrY2xuNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-02-07T04:14:19.000Z", "updated": "2020-09-27T18:15:42.742Z", "summary": "Stay at The Westin Resort & Spa, Whistler", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPfLrbnmKyM--gncOOeupkRrEyJ5QFMmns\n", "location": "The Westin Resort & Spa, Whistler, Whistler", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2019-02-18"}, "end": {"date": "2019-02-24"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tp6hbusbbj9k4pnppfjbi9iar8cgaoea6qvtt40ivacrvq7dcteng", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPfLrbnmKyM--gncOOeupkRrEyJ5QFMmns", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874259850000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehgn4sjidkpmmqr268sj2rhne8o38e9l61l3ishkcdo3atjbcll7aprd70sn2rbc65n66s9mccoj2sb9e1r6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoZ240c2ppZGtwbW1xcjI2OHNqMnJobmU4bzM4ZTlsNjFsM2lzaGtjZG8zYXRqYmNsbDdhcHJkNzBzbjJyYmM2NW42NnM5bWNjb2oyc2I5ZTFyNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-16T22:04:32.000Z", "updated": "2021-03-11T04:32:09.925Z", "summary": "Flight to London (DL 4349)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOxHhBu8LE9jlNFWpJw8bYGKM9i3IF7c0s\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-02-26T17:55:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-02-27T03:15:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tarrrm3kkb291n7r04950j9r4cp5vkejugm89qml1ncq6c11qipvg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOxHhBu8LE9jlNFWpJw8bYGKM9i3IF7c0s", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874259850000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehi3cc1md4omspj2dkqn8c3dc8rmutbd65ij4c3kdosm4dhme9q64s3je4pn0d1l75q6isrhd0qjesho6lpme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaTNjYzFtZDRvbXNwajJka3FuOGMzZGM4cm11dGJkNjVpajRjM2tkb3NtNGRobWU5cTY0czNqZTRwbjBkMWw3NXE2aXNyaGQwcWplc2hvNmxwbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-16T22:04:32.000Z", "updated": "2021-03-11T04:32:09.925Z", "summary": "Flight to New Delhi (AF 226)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOxHhBu8LE9jlNFWpJw8bYGKM9i3IF7c0s\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-02T01:20:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-02T09:45:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9td606i1nfbm5t0mb7oum1e20tn9b66rtbpsq3p459tisqh57r85sg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOxHhBu8LE9jlNFWpJw8bYGKM9i3IF7c0s", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874259850000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehlm2or6ctk6edho75gmiebdckp6up3mdhjm2pppdhh78dpgd0rn6r1h6lqjee1icpqmgspie5l6od9m6dm30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobG0yb3I2Y3RrNmVkaG83NWdtaWViZGNrcDZ1cDNtZGhqbTJwcHBkaGg3OGRwZ2Qwcm42cjFoNmxxamVlMWljcHFtZ3NwaWU1bDZvZDltNmRtMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-16T22:04:32.000Z", "updated": "2021-03-11T04:32:09.925Z", "summary": "Flight to Amsterdam (DL 9438)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOxHhBu8LE9jlNFWpJw8bYGKM9i3IF7c0s\n", "location": "New Delhi DEL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-06T13:50:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-06T22:50:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tkacfghg689ai9me2odvlgag9lbt70h7sl15u782fuhs2qjl563l0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOxHhBu8LE9jlNFWpJw8bYGKM9i3IF7c0s", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203225749200000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehgncsr86ll6ct3i6lo6us1pepgn4tj9dphmodb9clj64t32epn6mr9pedijcr37epkjgrhh69gn4pbb75m6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoZ25jc3I4NmxsNmN0M2k2bG82dXMxcGVwZ240dGo5ZHBobW9kYjljbGo2NHQzMmVwbjZtcjlwZWRpamNyMzdlcGtqZ3JoaDY5Z240cGJiNzVtNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-03-06T21:02:00.000Z", "updated": "2020-10-02T04:27:54.600Z", "summary": "Flight to Vancouver (AC 45)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPX3mPvQlE7koB4eOQ7UInknIWdvHToiZY\n", "location": "New Delhi DEL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-06T16:35:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-07T07:00:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tavsh5jftr5pop9varvincl5iefbtbvnkm9se6lgvi8n12arek9lg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPX3mPvQlE7koB4eOQ7UInknIWdvHToiZY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3147030800734000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehn66c1j75o38p32dco6krr5ddr74qbe6ljj6d3ec5k7atj96do6mpja65q64rrc6op6cshje4p76t1pc9r6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobjY2YzFqNzVvMzhwMzJkY282a3JyNWRkcjc0cWJlNmxqajZkM2VjNWs3YXRqOTZkbzZtcGphNjVxNjRycmM2b3A2Y3NoamU0cDc2dDFwYzlyNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-16T22:04:32.000Z", "updated": "2019-11-11T23:36:40.367Z", "summary": "Flight to Minneapolis (DL 163)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOxHhBu8LE9jlNFWpJw8bYGKM9i3IF7c0s\n", "location": "Amsterdam AMS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-07T03:50:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-07T13:04:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tnc039p4dbk0joekvrin5g34nahuvi3pkfj1tbol62fr3q2st9bvg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOxHhBu8LE9jlNFWpJw8bYGKM9i3IF7c0s", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3147030796096000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehq70pho69kjepj1clhm6prad5jmkthkcco6cc3c6li7cc1pd5mjcebec8sn0rr8dpmmedhp6hl66rre6lqj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocTcwcGhvNjlramVwajFjbGhtNnByYWQ1am1rdGhrY2NvNmNjM2M2bGk3Y2MxcGQ1bWpjZWJlYzhzbjBycjhkcG1tZWRocDZobDY2cnJlNmxxajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-16T20:56:44.000Z", "updated": "2019-11-11T23:36:38.048Z", "summary": "Stay at Kimpton Hotel Monaco Portland", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOWtHObPXeo7B_enbGCEuPsQv5KFdTvWJc\n", "location": "Kimpton Hotel Monaco Portland, Portland", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2019-03-15"}, "end": {"date": "2019-03-18"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ttpf82i7faeccgjigjv4c0f0l5dv09im69nb9pohnmg694jcon5u0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOWtHObPXeo7B_enbGCEuPsQv5KFdTvWJc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3209205580864000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegon6pb3cli3ichje5nncqbee9ijaohldgrmic3d61hmge1kccqnac9l6hpn6sj9dsp3ccjlegpn4crachlme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb242cGIzY2xpM2ljaGplNW5uY3FiZWU5aWphb2hsZGdybWljM2Q2MWhtZ2Uxa2NjcW5hYzlsNmhwbjZzajlkc3AzY2NqbGVncG40Y3JhY2hsbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-02-07T22:34:13.000Z", "updated": "2020-11-05T18:59:50.432Z", "summary": "Flight to San Francisco (UA 816)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOovgdWlB55aJW5HQtbvmWrHRdGN9KkWk4\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-18T14:13:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-18T16:31:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1seced923qovinre5b5l7i0m0ch84c5u154ssrio262ut3r3jdkg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOovgdWlB55aJW5HQtbvmWrHRdGN9KkWk4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325189048000\"", "id": "_8ksjah1h6spk4ba38ksjab9k70o3gba16gpk6b9g650kachp6csj8dpg84", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=Xzhrc2phaDFoNnNwazRiYTM4a3NqYWI5azcwbzNnYmExNmdwazZiOWc2NTBrYWNocDZjc2o4ZHBnODQgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-03-06T06:08:08.000Z", "updated": "2023-01-25T16:03:14.524Z", "summary": "Flight  UA816 SEA to SFO", "description": "United Airlines Confirmation #: C6K7Y5", "location": "Departing Seattle, WA (SEA)", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2019-03-18T14:13:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-18T16:31:00-07:00", "timeZone": "UTC"}, "iCalUID": "E95D173B-CE95-4808-A43C-01AE2939470A", "sequence": 0, "attendees": [{"email": "<EMAIL>", "responseStatus": "needsAction"}, {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "organizer": true, "self": true, "responseStatus": "accepted"}], "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3209205580864000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehj34rhp65nmus1jcpn6mp3b61ijct37edm6otb275m66rhh6dr6odb2ehlmqe3f6lp3cdracss36spm70o6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoajM0cmhwNjVubXVzMWpjcG42bXAzYjYxaWpjdDM3ZWRtNm90YjI3NW02NnJoaDZkcjZvZGIyZWhsbXFlM2Y2bHAzY2RyYWNzczM2c3BtNzBvNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-02-07T22:34:13.000Z", "updated": "2020-11-05T18:59:50.432Z", "summary": "Flight to Seattle (UA 447)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOovgdWlB55aJW5HQtbvmWrHRdGN9KkWk4\n", "location": "San Francisco SFO", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-21T16:10:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-21T18:19:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tf2n91oop3fnkdk0e6tgsllub9lcn13vl5btkm8o5r67jg83s680g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOovgdWlB55aJW5HQtbvmWrHRdGN9KkWk4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325577768000\"", "id": "_8osjchhh6ook4ba284rjab9k60qjeba185148b9m6gs34ga374sj6dph6s", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=Xzhvc2pjaGhoNm9vazRiYTI4NHJqYWI5azYwcWplYmExODUxNDhiOW02Z3MzNGdhMzc0c2o2ZHBoNnMgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-03-06T06:08:10.000Z", "updated": "2023-01-25T16:06:28.884Z", "summary": "Flight  UA447 SFO to SEA", "description": "United Airlines Confirmation #: C6K7Y5", "location": "Departing San Francisco, CA (SFO)", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2019-03-21T16:10:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-21T18:19:00-07:00", "timeZone": "UTC"}, "iCalUID": "F96F161B-BA75-4057-AABD-6482AC993717", "sequence": 0, "attendees": [{"email": "<EMAIL>", "responseStatus": "needsAction"}, {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "organizer": true, "self": true, "responseStatus": "accepted"}], "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226955668000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehjmiq1nd0r7aq38dtpmgs3lddp38e3569hj6sbados7ao9mcdjjerbmedl3gdhnclin0sjd6grn4ojme9lme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoam1pcTFuZDByN2FxMzhkdHBtZ3MzbGRkcDM4ZTM1NjloajZzYmFkb3M3YW85bWNkamplcmJtZWRsM2dkaG5jbGluMHNqZDZncm40b2ptZTlsbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-03-18T21:40:50.000Z", "updated": "2020-10-02T04:37:57.834Z", "summary": "Flight to Minneapolis (DL 943)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx9FCv1gxWUZ79QfO89Q7Ip9eON3zAVms\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-27T07:55:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-27T11:17:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tgih7h6uhhoshpukr48e2c3qjn8ua6cg7mvsj867eeprm47rbvrkg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx9FCv1gxWUZ79QfO89Q7Ip9eON3zAVms", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226955668000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegojgsjg6pl6gsjdcssmuojicdljiq3bclln0d34ecrmqc9o6hkmme1j6dmjcojm71o6qp9o6dp3crjk69jme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb2pnc2pnNnBsNmdzamRjc3NtdW9qaWNkbGppcTNiY2xsbjBkMzRlY3JtcWM5bzZoa21tZTFqNmRtamNvam03MW82cXA5bzZkcDNjcmprNjlqbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-03-18T21:40:50.000Z", "updated": "2020-10-02T04:37:57.834Z", "summary": "Flight to Memphis (DL 3577)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx9FCv1gxWUZ79QfO89Q7Ip9eON3zAVms\n", "location": "Minneapolis MSP", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-27T12:36:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-27T14:45:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t18rp6jhrmg9obrck9hkekp4ds7m184ik833m6bv8pme83r6nt2gg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx9FCv1gxWUZ79QfO89Q7Ip9eON3zAVms", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226955668000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegr70qb2c9oj4rbj61h38rpj69mjcd9pd5ln0shndkp3gsb26gs3er3gc4q36qjm69nmgdrkelj74d9pd1h6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncjcwcWIyYzlvajRyYmo2MWgzOHJwajY5bWpjZDlwZDVsbjBzaG5ka3AzZ3NiMjZnczNlcjNnYzRxMzZxam02OW5tZ2Rya2Vsajc0ZDlwZDFoNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-03-18T21:40:50.000Z", "updated": "2020-10-02T04:37:57.834Z", "summary": "Flight to Atlanta (DL 1898)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx9FCv1gxWUZ79QfO89Q7Ip9eON3zAVms\n", "location": "Memphis MEM", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-28T15:30:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-28T16:52:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t6pibbq2ms0b4o32m659ikpr7m28qb487lpa43jv2oh7tufr59hbg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx9FCv1gxWUZ79QfO89Q7Ip9eON3zAVms", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226955668000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehm74d9mdsom6chlddkmer9mc9o6uchh61nmat1neco76opod0rjge32epjn4qbgehon8srae4p6mdhjeksme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobTc0ZDltZHNvbTZjaGxkZGttZXI5bWM5bzZ1Y2hoNjFubWF0MW5lY283Nm9wb2QwcmpnZTMyZXBqbjRxYmdlaG9uOHNyYWU0cDZtZGhqZWtzbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-03-18T21:40:50.000Z", "updated": "2020-10-02T04:37:57.834Z", "summary": "Flight to Seattle (DL 2806)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx9FCv1gxWUZ79QfO89Q7Ip9eON3zAVms\n", "location": "Atlanta ATL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-03-28T17:55:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-03-28T23:31:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tlr56o1c25kigm6bpo210oet7s0sc8h788bvgriptqtsjq2k63u9g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMx9FCv1gxWUZ79QfO89Q7Ip9eON3zAVms", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203225749994000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehq76phhcsomec1nelm6ut1hddp6csjeedp34q9h6sqmsdj4ehkn4q3ldhgmks3c64p34qr7chl32pjd6co6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocTc2cGhoY3NvbWVjMW5lbG02dXQxaGRkcDZjc2plZWRwMzRxOWg2c3Ftc2RqNGVoa240cTNsZGhnbWtzM2M2NHAzNHFyN2NobDMycGpkNmNvNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-30T23:46:00.000Z", "updated": "2020-10-02T04:27:54.997Z", "summary": "Flight to Phoenix (DL 1622)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN3DBk7IYaQh1yieurT-qHOtoW4C0E9fAs\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-04-22T07:45:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-04-22T10:38:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ttsf1g1g07ulot1krfrnsr2i175n6dtirhulajpl122kgdj1fm30g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN3DBk7IYaQh1yieurT-qHOtoW4C0E9fAs", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3147030797074000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegpn4drmc9q66r3669pmkohj64qmct316pqmisj2cgo30t3775nmioj3c4sn8tjjdtljcrb3c5kmks9jcdhj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncG40ZHJtYzlxNjZyMzY2OXBta29oajY0cW1jdDMxNnBxbWlzajJjZ28zMHQzNzc1bm1pb2ozYzRzbjh0ampkdGxqY3JiM2M1a21rczlqY2RoajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-30T23:46:00.000Z", "updated": "2019-11-11T23:36:38.537Z", "summary": "Flight to Seattle (DL 1211)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN3DBk7IYaQh1yieurT-qHOtoW4C0E9fAs\n", "location": "Phoenix PHX", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-04-29T18:56:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-04-29T22:10:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t3r7vbtclf2sjb315fta6uirbd00tg9oibca9tvsok6mcaijq3cc0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN3DBk7IYaQh1yieurT-qHOtoW4C0E9fAs", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874258218000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegojcqrmc8rmscb96tj74drfd5r70rbedtp30dr36tgmorhpckq3cr1kcgqmes3eelr6gshicpon8pb7csoj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb2pjcXJtYzhybXNjYjk2dGo3NGRyZmQ1cjcwcmJlZHRwMzBkcjM2dGdtb3JocGNrcTNjcjFrY2dxbWVzM2VlbHI2Z3NoaWNwb244cGI3Y3NvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-03T21:30:14.000Z", "updated": "2021-03-11T04:32:09.109Z", "summary": "Flight to Paris (DL 34)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMjBku53fYf1qfFzdnTW7RE4BUpVEoKxUk\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-05-28T12:52:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-05-28T23:10:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t16kvb7n1i7fr7oivpmnor07c7aln9e46l4d5gpnuvhr2fqtegg10", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMjBku53fYf1qfFzdnTW7RE4BUpVEoKxUk", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873051492000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehn6is326ko3gorechjjgrbicson4or7ednmce9le5mm4r1od8qjgojb69r62t1p60s3ae1mc5ijgs3ce5nme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobjZpczMyNmtvM2dvcmVjaGpqZ3JiaWNzb240b3I3ZWRubWNlOWxlNW1tNHIxb2Q4cWpnb2piNjlyNjJ0MXA2MHMzYWUxbWM1aWpnczNjZTVubWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-03T22:07:19.000Z", "updated": "2021-03-11T04:22:05.746Z", "summary": "Flight to Paris (DL 8402)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMEbI_ickhRDMXnBBxvb1fCcZHQjkoyF6M\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-06-06T16:20:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-06-07T02:15:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tnipb508cndg8mrg1rcgsof95qmbl8j58bk2vat908586ae8plqog", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMEbI_ickhRDMXnBBxvb1fCcZHQjkoyF6M", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202465914356000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehon2cj2egr68t3lepmmkp1ndpj3eorkc5gm8tb3cssn4tbieph6eqj56hqmidjacgp6orrg6gr70phge9k30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVob24yY2oyZWdyNjh0M2xlcG1ta3AxbmRwajNlb3JrYzVnbTh0YjNjc3NuNHRiaWVwaDZlcWo1NmhxbWlkamFjZ3A2b3JyZzZncjcwcGhnZTlrMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-06-13T08:34:35.000Z", "updated": "2020-09-27T18:55:57.178Z", "summary": "Stay at Domaine du Ferret Balnéo & Spa", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNiIktuFzirL3j5QLOhV51XKE4Om_NC0BI\n", "location": "Domaine du Ferret Balnéo & Spa, Lège-Cap-Ferret", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2019-06-21"}, "end": {"date": "2019-06-25"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tqq2bt6dtuvmjd7nf7ctaaducg9rurvbgje4ui6jd2lop46pf0rh0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNiIktuFzirL3j5QLOhV51XKE4Om_NC0BI", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202462292782000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehhj0d1l65p64rrfeoo34e37d0sncrpj74p6it3l65j7atbmcdhj8s3iedo6up3hehpjicrdedhmcp39elo6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaGowZDFsNjVwNjRycmZlb28zNGUzN2Qwc25jcnBqNzRwNml0M2w2NWo3YXRibWNkaGo4czNpZWRvNnVwM2hlaHBqaWNyZGVkaG1jcDM5ZWxvNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-07-12T10:43:45.000Z", "updated": "2020-09-27T18:25:46.391Z", "summary": "Stay at AC Hotel by Marriott Seattle Bellevue/Downtown", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNjhfdqLCojdTUwuNsPsb6Q7RE72KezAxA\n", "location": "AC Hotel by Marriott Seattle Bellevue/Downtown, Bellevue", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2019-07-14"}, "end": {"date": "2019-07-16"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tc0451rboov028gh9vo392itu1fuuvcc4prspodqts93mscfdiupg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNjhfdqLCojdTUwuNsPsb6Q7RE72KezAxA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223335680000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegrmgqjhchk72rb56dm34dhpcphnadpjd9gmot366osmapbmegp6ce3464r70drf6dp68r3ad1in4dbde8sj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncm1ncWpoY2hrNzJyYjU2ZG0zNGRocGNwaG5hZHBqZDlnbW90MzY2b3NtYXBibWVncDZjZTM0NjRyNzBkcmY2ZHA2OHIzYWQxaW40ZGJkZThzajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-07-13T17:08:40.000Z", "updated": "2020-10-02T04:07:47.840Z", "summary": "Flight to Seattle (BA 49)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNZ2aGPdqzfrH6_mJYSqlBvtf-U3Cwg7sU\n", "location": "London LHR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-07-14T07:25:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-07-14T17:15:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t7hjqdhqme3l269fcu73jaltf69eevt2f8d16p7o3rdljher5mr90", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNZ2aGPdqzfrH6_mJYSqlBvtf-U3Cwg7sU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223335680000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegsncphodhi3et1g6lhmodhke5l72rj66dnmirbjcpmmesjleljjgsr4e1l3iqrldcr64c1g6kpn4cpj6sq6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnc25jcGhvZGhpM2V0MWc2bGhtb2Roa2U1bDcycmo2NmRubWlyYmpjcG1tZXNqbGVsampnc3I0ZTFsM2lxcmxkY3I2NGMxZzZrcG40Y3BqNnNxNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-07-13T17:08:40.000Z", "updated": "2020-10-02T04:07:47.840Z", "summary": "Flight to London (BA 48)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOZz_8PO0LWMVwb5XUd4mE54TjbypQRBoE\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-07-15T19:20:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-07-16T04:40:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t9vf8ld7t05cl64qjqnf3oimsfmgruug8sdpj9kuk6b0053r3374g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOZz_8PO0LWMVwb5XUd4mE54TjbypQRBoE", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873047814000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehkj8t34dgq76s3fcco3gdjbe5ln6cbe6dh3cdrectn78rra6pk64rpgdos38q1o6csn6ohhe8smkcjmddpme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoa2o4dDM0ZGdxNzZzM2ZjY28zZ2RqYmU1bG42Y2JlNmRoM2NkcmVjdG43OHJyYTZwazY0cnBnZG9zMzhxMW82Y3NuNm9oaGU4c21rY2ptZGRwbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-07-03T16:15:14.000Z", "updated": "2021-03-11T04:22:03.907Z", "summary": "Flight to Rome (D8 3730)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOHB4H5oxQu6aJp0xSVLr1i7194xzLX24k\n", "location": "Copenhagen CPH", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-07-21T22:25:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-07-22T01:00:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ti4tdl4spoc086kqks1n3b67ngntoj6hbo0n84h839sb1r9j2vksg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOHB4H5oxQu6aJp0xSVLr1i7194xzLX24k", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874258218000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegp3gpbfddojgsj1c5h6oe3be0o7apb960r62e38dlr3ecb6chk70dbg61k62drieoo76pj7ehqj6tbd6tgj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncDNncGJmZGRvamdzajFjNWg2b2UzYmUwbzdhcGI5NjByNjJlMzhkbHIzZWNiNmNoazcwZGJnNjFrNjJkcmllb283NnBqN2VocWo2dGJkNnRnajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-03T21:30:14.000Z", "updated": "2021-03-11T04:32:09.109Z", "summary": "Flight to Seattle (DL 35)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMjBku53fYf1qfFzdnTW7RE4BUpVEoKxUk\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-07-22T01:20:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-07-22T12:20:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t28eokq8raabl8kp0uei06a8hmv71fdhp5p0ha7rv0sfgtu3um7a0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMjBku53fYf1qfFzdnTW7RE4BUpVEoKxUk", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873047814000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehqm2ored5mmsppictr6idrk68ojcc31eor6urb461p6gt1n6sq6ge1p65njie37d4q62qb3egqj8qhpdllme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocW0yb3JlZDVtbXNwcGljdHI2aWRyazY4b2pjYzMxZW9yNnVyYjQ2MXA2Z3QxbjZzcTZnZTFwNjVuamllMzdkNHE2MnFiM2VncWo4cWhwZGxsbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-07-03T16:15:14.000Z", "updated": "2021-03-11T04:22:03.907Z", "summary": "Flight to Paris (AF 1005)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM4qRz5Ac-FHj3g-McR3Sn0gOn2UU9Q41I\n", "location": "Rome FCO", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-07-24T21:30:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-07-24T23:40:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tuacnimng2gvi7t2160av6omd0rht774h891o98gi4aict54j9mkg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM4qRz5Ac-FHj3g-McR3Sn0gOn2UU9Q41I", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873051492000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegpncpba64rnadhje8rmqpjcdlj74qrfdtijetb7ehp6udrhcllj0cr8cpknadbjelom4sjccpj6ce3b6pome", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncG5jcGJhNjRybmFkaGplOHJtcXBqY2Rsajc0cXJmZHRpamV0YjdlaHA2dWRyaGNsbGowY3I4Y3BrbmFkYmplbG9tNHNqY2NwajZjZTNiNnBvbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-01-03T22:07:19.000Z", "updated": "2021-03-11T04:22:05.746Z", "summary": "Flight to Seattle (DL 8401)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMEbI_ickhRDMXnBBxvb1fCcZHQjkoyF6M\n", "location": "Paris CDG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-07-25T04:10:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-07-25T14:30:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t3vej17u63r7mflmfrkooe7ugtro7qek03hfiu5suqbrlfff8k6qg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMEbI_ickhRDMXnBBxvb1fCcZHQjkoyF6M", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203225751066000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegp6gd3hddim6e1jctmn2sblchpmot1oclpj6sr96hh3aqhj71p7ad9odcs70t3h6tnmir3kedkmcpb975o30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncDZnZDNoZGRpbTZlMWpjdG1uMnNibGNocG1vdDFvY2xwajZzcjk2aGgzYXFoajcxcDdhZDlvZGNzNzB0M2g2dG5taXIza2Vka21jcGI5NzVvMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-08-22T13:28:22.000Z", "updated": "2020-10-02T04:27:55.533Z", "summary": "Flight to Newark (UA 480)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPlDTtOZf8YPfj4351kBLXb-KkYlhKyTGI\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-08-22T22:45:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-08-23T03:51:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t2h4qkec83gmqqudslt8es3si4b5j38ru58k8ptq7oiltsifei9p0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPlDTtOZf8YPfj4351kBLXb-KkYlhKyTGI", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3202461086438000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehj6urpkctlncohn61r3ed3b65kjgd1k6dln8q3961j6qqjeegp38sr369m74rjje1i78rrm6poj2spld9o30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoajZ1cnBrY3RsbmNvaG42MXIzZWQzYjY1a2pnZDFrNmRsbjhxMzk2MWo2cXFqZWVncDM4c3IzNjltNzRyamplMWk3OHJybTZwb2oyc3BsZDlvMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-08-22T13:28:22.000Z", "updated": "2020-09-27T18:15:43.219Z", "summary": "Stay at Newark Liberty International Airport Marriott", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPlDTtOZf8YPfj4351kBLXb-KkYlhKyTGI\n", "location": "Newark Liberty International Airport Marriott, Newark", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2019-08-23"}, "end": {"date": "2019-08-25"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tfoo4gkvb70v74k1i8443kthi0fmjnt24sc2lrnspdtov6q1s5jp0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPlDTtOZf8YPfj4351kBLXb-KkYlhKyTGI", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203225751066000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehpnaprgd8o6srj4e5nmasjhdtqn6sjb6so3cob8c8o7adrgcdnjacj5egpj4qbkecr6gojld4sj6pj3e5mj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocG5hcHJnZDhvNnNyajRlNW5tYXNqaGR0cW42c2piNnNvM2NvYjhjOG83YWRyZ2NkbmphY2o1ZWdwajRxYmtlY3I2Z29qbGQ0c2o2cGozZTVtajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-08-22T13:28:22.000Z", "updated": "2020-10-02T04:27:55.533Z", "summary": "Flight to Seattle (UA 1932)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPlDTtOZf8YPfj4351kBLXb-KkYlhKyTGI\n", "location": "Newark EWR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-08-23T11:00:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-08-23T17:06:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tsugpj0nndqoerqousrk706ahb0u7pco52et32its6hbui93fcqm0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPlDTtOZf8YPfj4351kBLXb-KkYlhKyTGI", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224540190000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegr32r1oehnj2tbmc9mmaspo69h78djadlnm2ojid5hj6ora70sj4pb461gmkq1pc9m72d9nd5p76c39edj30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncjMycjFvZWhuajJ0Ym1jOW1tYXNwbzY5aDc4ZGphZGxubTJvamlkNWhqNm9yYTcwc2o0cGI0NjFnbWtxMXBjOW03MmQ5bmQ1cDc2YzM5ZWRqMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-08-01T17:38:07.000Z", "updated": "2020-10-02T04:17:50.095Z", "summary": "Flight to Chicago (UA 278)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM_syPCzV29drh-b9KsfHYjFxVXnR1OivU\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-09-18T10:30:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-09-18T14:39:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t61l8to1uvbmes82bt6jmoabric3cj892ed0ajh9blq57irs0isf0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM_syPCzV29drh-b9KsfHYjFxVXnR1OivU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224540190000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehij8cb2dsq3it1o75h32rb2epq34dppdtp6kppnctl64t1o6ss62dr1dpjjatj6ecomqqhpdss7cd33d1h30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaWo4Y2IyZHNxM2l0MW83NWgzMnJiMmVwcTM0ZHBwZHRwNmtwcG5jdGw2NHQxbzZzczYyZHIxZHBqamF0ajZlY29tcXFocGRzczdjZDMzZDFoMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-08-01T17:38:07.000Z", "updated": "2020-10-02T04:17:50.095Z", "summary": "Flight to Seattle (UA 262)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM_syPCzV29drh-b9KsfHYjFxVXnR1OivU\n", "location": "Chicago ORD", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-09-19T17:50:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-09-19T22:28:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9te41bo49t89b1mbvt279orjg7gjbt878a7ang5vfs1mj9o8v4chb0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM_syPCzV29drh-b9KsfHYjFxVXnR1OivU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224543886000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehljieb969jmecpkchhmesjmegp3esbb6do3eshhchpm2oja6kp6oprkdtk6mchkd5hm6ore6tm64dr76dlj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobGppZWI5NjlqbWVjcGtjaGhtZXNqbWVncDNlc2JiNmRvM2VzaGhjaHBtMm9qYTZrcDZvcHJrZHRrNm1jaGtkNWhtNm9yZTZ0bTY0ZHI3NmRsajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-08-01T17:20:58.000Z", "updated": "2020-10-02T04:17:51.943Z", "summary": "Flight to Chicago (UA 670)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM_syPCzV29drh-b9KsfHYjFxVXnR1OivU\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-09-29T13:41:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-09-29T17:50:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tk99i2gg34dcgrvt27qk3p7r1dsabj52lgtohk24icccn7lb7g3k0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM_syPCzV29drh-b9KsfHYjFxVXnR1OivU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224543886000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehomutj7d9m76p1mchl6eori61i72cjd71r6ot3bc8o62qb7e9pmidb7dpq3ie3c6gs6kcj7dljmksba71mj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVob211dGo3ZDltNzZwMW1jaGw2ZW9yaTYxaTcyY2pkNzFyNm90M2JjOG82MnFiN2U5cG1pZGI3ZHBxM2llM2M2Z3M2a2NqN2Rsam1rc2JhNzFtajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-07-31T00:39:35.000Z", "updated": "2020-10-02T04:17:51.943Z", "summary": "Flight to Chicago (UA 278)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPQai_MsUfVzAm6Pg7XSRzqc3H8u8VPIOU\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-10-03T10:30:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-10-03T14:39:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tqovgjlsd6djgcr0dq2m8vltkb0aigrsi5gnt98l48j2gmgjqj8m0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPQai_MsUfVzAm6Pg7XSRzqc3H8u8VPIOU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203224543886000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehlmoc9gctqjasja6lgj6cb5cssmed9ke4s3iqb8c5jj6dr5edk72qb5e5o70tb7cssjip1l6tk3cppi6hjme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobG1vYzlnY3RxamFzamE2bGdqNmNiNWNzc21lZDlrZTRzM2lxYjhjNWpqNmRyNWVkazcycWI1ZTVvNzB0Yjdjc3NqaXAxbDZ0azNjcHBpNmhqbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-07-31T00:39:35.000Z", "updated": "2020-10-02T04:17:51.943Z", "summary": "Flight to Seattle (UA 635)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPQai_MsUfVzAm6Pg7XSRzqc3H8u8VPIOU\n", "location": "Chicago ORD", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-10-06T08:05:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-10-06T12:43:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tkl10gu5rj5a31eg9g54q89ihag37eshqieqppugg99d57h6g24gg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPQai_MsUfVzAm6Pg7XSRzqc3H8u8VPIOU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873046380000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegrn2rrmchl7cpplcpm70oj2e5q76d3bchoj8dradppjedjectj6sdb5csq3asb6d1omodr165m74o9le8rme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncm4ycnJtY2hsN2NwcGxjcG03MG9qMmU1cTc2ZDNiY2hvajhkcmFkcHBqZWRqZWN0ajZzZGI1Y3NxM2FzYjZkMW9tb2RyMTY1bTc0bzlsZThybWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-11-06T21:42:18.000Z", "updated": "2021-03-11T04:22:03.190Z", "summary": "Flight to Las Vegas (DL 2661)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPtPeRcvUJfssYEcF02SGYSPJoAIWwr_sA\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-11-11T09:49:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-11-11T12:20:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t7qovdjvg5flpbbqts4kdq47jns76ngfn5eg45qfhql7a1lra5r7g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPtPeRcvUJfssYEcF02SGYSPJoAIWwr_sA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873046380000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehlmid3kcop34sjkehk34q35ctr6kq3fcpnmar9k6do32qrcdtp3cp3i6lk78qhpeos66ore6lnjacrccss6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobG1pZDNrY29wMzRzamtlaGszNHEzNWN0cjZrcTNmY3BubWFyOWs2ZG8zMnFyY2R0cDNjcDNpNmxrNzhxaHBlb3M2Nm9yZTZsbmphY3JjY3NzNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-11-06T21:42:18.000Z", "updated": "2021-03-11T04:22:03.190Z", "summary": "Flight to Seattle (DL 377)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPtPeRcvUJfssYEcF02SGYSPJoAIWwr_sA\n", "location": "Las Vegas LAS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-11-14T20:30:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-11-14T23:12:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tki4tf22rtth2hegvjhofoem43p1klor6dr5htj9v8ccn5o53lg8g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPtPeRcvUJfssYEcF02SGYSPJoAIWwr_sA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226960598000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehj72drbctgn8cr9chi3iqr16hl6grhgctojgd39c9im8p37ctp6mtbichq30rpgcksm2q33edp6krpocpqme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoajcyZHJiY3RnbjhjcjljaGkzaXFyMTZobDZncmhnY3RvamdkMzljOWltOHAzN2N0cDZtdGJpY2hxMzBycGdja3NtMnEzM2VkcDZrcnBvY3BxbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-08-21T01:20:08.000Z", "updated": "2020-10-02T04:38:00.299Z", "summary": "Flight to Salt Lake City (DL 528)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN2V7EVgKVTe6xr5ZNUx62mrSmx0A1qPlw\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-11-21T18:50:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-11-21T20:45:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tfq7kgat3idd9ka4jhn0gq84ibeddggrkurdt0o0e9ahcsrjo8fug", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN2V7EVgKVTe6xr5ZNUx62mrSmx0A1qPlw", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203226960598000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehqm2sr7csqjipr46thn6p9mddl6gp3fepl3gsrjehgmqphlekpm8djh6cp6oprkc4o7cprfe5qmqs1o71qj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocW0yc3I3Y3NxamlwcjQ2dGhuNnA5bWRkbDZncDNmZXBsM2dzcmplaGdtcXBobGVrcG04ZGpoNmNwNm9wcmtjNG83Y3ByZmU1cW1xczFvNzFxajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-08-21T01:20:08.000Z", "updated": "2020-10-02T04:38:00.299Z", "summary": "Flight to Fort Lauderdale (DL 2274)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN2V7EVgKVTe6xr5ZNUx62mrSmx0A1qPlw\n", "location": "Salt Lake City SLC", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2019-11-21T22:20:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2019-11-22T02:44:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tuasgg59gd7cse6kjhdovj8sstamf5u3d6q32lgta0vgoqump88u0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN2V7EVgKVTe6xr5ZNUx62mrSmx0A1qPlw", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325375726000\"", "id": "_cdgjae316opj4b9p6or30b9k70o64b9o6grjeb9gcosj0p1i71hj2o9p6k", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=X2NkZ2phZTMxNm9wajRiOXA2b3IzMGI5azcwbzY0YjlvNmdyamViOWdjb3NqMHAxaTcxaGoybzlwNmsgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-11-12T17:22:36.000Z", "updated": "2023-01-25T16:04:47.863Z", "summary": "Alaska Airlines Itinerary", "description": "  \n Name(s): MICHAEL E GULMANN, AMY E GULMANN, CAROLINE L GULMANN, ALEXANDER M GULMANN, OLIVIA E GULMANN\n Confirmation Code: SHCASZ\n Ticket Number: 0277392050952\n\n ITINERARY\n\n Friday, November 29, 2019\n   Depart: Fort Lauderdale (FLL) at 5:20 PM\n   Arrive: Seattle (SEA) at 9:15 PM\n   Flight: Alaska 35\n   Seat(s): 27E\n   Aircraft: Boeing 737-800\n   Mileage: 2,712\n   Duration: 6 hours 55 minutes\n   Meal: Food for Purchase\n\n Check in and print your boarding pass 1 to 24 hours prior to your scheduled departure.\n\n Thank you for choosing Alaska Airlines\n", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2019-11-29T14:20:00-08:00", "timeZone": "America/New_York"}, "end": {"dateTime": "2019-11-29T21:15:00-08:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "ca58a632-9660-480b-8477-0f90d28c1a95", "sequence": 1, "attendees": [{"email": "<EMAIL>", "responseStatus": "needsAction"}, {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "organizer": true, "self": true, "responseStatus": "accepted"}], "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223334860000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehk32t326plm2qjgc9k6msbec5kmiojg75qmep3j6or62rrge9h7crb86tk6gc3i6lhn2oj561l7ap1ie9lme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoazMydDMyNnBsbTJxamdjOWs2bXNiZWM1a21pb2pnNzVxbWVwM2o2b3I2MnJyZ2U5aDdjcmI4NnRrNmdjM2k2bGhuMm9qNTYxbDdhcDFpZTlsbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-09-14T20:53:03.000Z", "updated": "2020-10-02T04:07:47.430Z", "summary": "Flight to San José del Cabo (DL 357)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPIql7PQb9gjaM6H-5j0En14Jpd7BPmTFs\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-01-23T09:50:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-01-23T14:10:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9th1tb6kajpbhkqnaiibp9ugds66aoprbvmh7hh0r5cqbe0jud2rkg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPIql7PQb9gjaM6H-5j0En14Jpd7BPmTFs", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203223334860000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehgmac9me0r72c9kedlm2d1icspmsrj5dgrj8qhldkomadhoc9m78p1ge9oj8ora6sqjec3dchpm6d34edqme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoZ21hYzltZTByNzJjOWtlZGxtMmQxaWNzcG1zcmo1ZGdyajhxaGxka29tYWRob2M5bTc4cDFnZTlvajhvcmE2c3FqZWMzZGNocG02ZDM0ZWRxbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-09-14T20:53:03.000Z", "updated": "2020-10-02T04:07:47.430Z", "summary": "Flight to Seattle (DL 975)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPIql7PQb9gjaM6H-5j0En14Jpd7BPmTFs\n", "location": "San José del Cabo SJD", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-01-27T15:10:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-01-27T19:49:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tae16p6q14ska42g3nnel74j5m1e68bltd0rq4cj7570mdsc4dsug", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPIql7PQb9gjaM6H-5j0En14Jpd7BPmTFs", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230875462848000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehq34c9ne9ijeoho6dr6icpm6pn76d9meoo76cpn6hgj4rj4ddkmcdhm6hln0d3fe5r6aqj2eljm4c1j75nj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocTM0YzluZTlpamVvaG82ZHI2aWNwbTZwbjc2ZDltZW9vNzZjcG42aGdqNHJqNGRka21jZGhtNmhsbjBkM2ZlNXI2YXFqMmVsam00YzFqNzVuajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2019-12-24T10:50:42.000Z", "updated": "2021-03-11T04:42:11.424Z", "summary": "Stay at Idéal Hôtel Design", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNmHssjzZ-ZWgn_7nLQ3KGXYvRs7wrDmFI\n", "location": "<PERSON><PERSON><PERSON><PERSON> Design, Paris", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-02-03"}, "end": {"date": "2020-02-05"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tt217re7b83vi366ns56v0s374a2ndkif664kp4oqvejbugb039o0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNmHssjzZ-ZWgn_7nLQ3KGXYvRs7wrDmFI", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230874256882000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegsnadbcdcr6is1ge1m6sebgcsr70rj4cdn6gphnctn3ic3gcdn6stj2d8p6kebe61l34qb3e4rn8cb2c8p6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnc25hZGJjZGNyNmlzMWdlMW02c2ViZ2Nzcjcwcmo0Y2RuNmdwaG5jdG4zaWMzZ2NkbjZzdGoyZDhwNmtlYmU2MWwzNHFiM2U0cm44Y2IyYzhwNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-03-02T13:09:18.000Z", "updated": "2021-03-11T04:32:08.441Z", "summary": "Stay at Starhotels Du Parc", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP8VVrXtcGpUcLlgcZ-nZl2mqKNyM5Jdzs\n", "location": "Starhotels Du Parc, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-03-03"}, "end": {"date": "2020-03-05"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t9u5lk6ip0pln9pg6pndcnhf7gn90pcnnvbj2j9n0j2icq7t1bb2g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP8VVrXtcGpUcLlgcZ-nZl2mqKNyM5Jdzs", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3173602816042000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehn34q3kc5r6oqracko6mtbg6dhmgq386dp78s9g65mj6p3id9hmgdji6himkp1kddq32qbf6limao9j6kpme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobjM0cTNrYzVyNm9xcmFja282bXRiZzZkaG1ncTM4NmRwNzhzOWc2NW1qNnAzaWQ5aG1nZGppNmhpbWtwMWtkZHEzMnFiZjZsaW1hbzlqNmtwbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-04-10T17:18:06.000Z", "updated": "2020-04-13T18:10:08.021Z", "summary": "Flight to Los Angeles (DL 746)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPXeEUCyqcw6g9DMdH36Q3wjaF9ic9Bxwk\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-04-13T11:25:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-04-13T13:59:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tn2htavlkje0kup3chhh3rtq01m3drjch6r4ejd4kt1io5eea353g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPXeEUCyqcw6g9DMdH36Q3wjaF9ic9Bxwk", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3173078173204000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehqj6d1kdlojiobfdcrmupjkdtk7ce3ce1j3ipr668rmes3ld1pm2or8chhm4c3ed5pj2tj269pn0q34dllme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocWo2ZDFrZGxvamlvYmZkY3JtdXBqa2R0azdjZTNjZTFqM2lwcjY2OHJtZXMzbGQxcG0yb3I4Y2hobTRjM2VkNXBqMnRqMjY5cG4wcTM0ZGxsbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-01-20T18:52:41.000Z", "updated": "2020-04-10T17:18:06.602Z", "summary": "Flight to Kahului (DL 1219)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMOLSA34aeGKqXt9zqyNN34WqbzIKTZCxk\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-04-13T16:45:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-04-13T22:20:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tu344mq9aok7oftohv8lpf9gf27gpuhsachdcb0nis1vb2sphdmkg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMOLSA34aeGKqXt9zqyNN34WqbzIKTZCxk", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3159092738430000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpeho78pj2e5p3gp31e1imoe31ctl6eoj265o70or4elgmsqrj60rmethn74o6usj1cpi6mcbie5p6sdr5e5h6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobzc4cGoyZTVwM2dwMzFlMWltb2UzMWN0bDZlb2oyNjVvNzBvcjRlbGdtc3FyajYwcm1ldGhuNzRvNnVzajFjcGk2bWNiaWU1cDZzZHI1ZTVoNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-01-20T18:52:41.000Z", "updated": "2020-01-20T18:52:49.215Z", "summary": "Flight to Seattle (DL 1768)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMOLSA34aeGKqXt9zqyNN34WqbzIKTZCxk\n", "location": "Kahului OGG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-04-20T23:32:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-04-21T05:00:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tptfbqr8dapel8agjgbb1ppcduanks07gv790orafdk1rqrn7eqbg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMOLSA34aeGKqXt9zqyNN34WqbzIKTZCxk", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3166655129966000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehj3ccpoe9i64ebbcpjmae3j70q78t3mehl6upracgs34dr9eoqn4rbm61hn4ebdegqn6t3ed4pm8qrfegq30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoajNjY3BvZTlpNjRlYmJjcGptYWUzajcwcTc4dDNtZWhsNnVwcmFjZ3MzNGRyOWVvcW40cmJtNjFobjRlYmRlZ3FuNnQzZWQ0cG04cXJmZWdxMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-03-04T13:12:44.000Z", "updated": "2020-03-04T13:12:44.983Z", "summary": "Flight to Seattle (AC 541)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP5CkSBau3Hvv9HVxuAM7lciBOyzpiCDi8\n", "location": "Toronto YYZ", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-06-26T15:05:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-06-26T20:20:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tf638rdb9kfge8s84ttvtjogjd827iv5rmv0cr9mt5stni3dkot40", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP5CkSBau3Hvv9HVxuAM7lciBOyzpiCDi8", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3166655129966000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpego6uqr5epr6cprfdhijgcb1ckq32phhcoo6uq37dtkmkc1hd9k68srlcdlmgqr4clkm2cr465m6mdr168rj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnbzZ1cXI1ZXByNmNwcmZkaGlqZ2NiMWNrcTMycGhoY29vNnVxMzdkdGtta2MxaGQ5azY4c3JsY2RsbWdxcjRjbGttMmNyNDY1bTZtZHIxNjhyajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-03-04T13:12:44.000Z", "updated": "2020-03-04T13:12:44.983Z", "summary": "Flight to Toronto (AC 530)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP5CkSBau3Hvv9HVxuAM7lciBOyzpiCDi8\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-06-30T23:05:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-07-01T03:36:00-07:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t0okevvfgole81ae41f1f0ohgoij01jhdsuckhkdeia3d1lk7a270", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP5CkSBau3Hvv9HVxuAM7lciBOyzpiCDi8", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873046062000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegpmmtj76sr6is9m6pnmkq346lr62qrcd5q6iqhl71m3gsph6ksj6d3idlknadr6clgjcdjeddm3ip3ieos6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncG1tdGo3NnNyNmlzOW02cG5ta3EzNDZscjYycXJjZDVxNmlxaGw3MW0zZ3NwaDZrc2o2ZDNpZGxrbmFkcjZjbGdqY2RqZWRkbTNpcDNpZW9zNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-06-30T08:51:56.000Z", "updated": "2021-03-11T04:22:03.031Z", "summary": "Stay at Ideal Hotel Design", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPdFJKc2mZ7L2d1T4e0roy0Ks5tjN2Nwis\n", "location": "Ideal Hotel Design, Paris", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-07-06"}, "end": {"date": "2020-07-08"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t3kvg76iq66ojhd5vaklitij58l8s15934rmiu7fea66nkl9drv8g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPdFJKc2mZ7L2d1T4e0roy0Ks5tjN2Nwis", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230875461876000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegojgqrj6ks78djid9l6oob36hj3gojdc9kjcq3771o7csj1ctl68tb26lij0rr46gon4thmd0rm8rhj71o30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb2pncXJqNmtzNzhkamlkOWw2b29iMzZoajNnb2pkYzlramNxMzc3MW83Y3NqMWN0bDY4dGIyNmxpajBycjQ2Z29uNHRobWQwcm04cmhqNzFvMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-07-21T13:39:13.000Z", "updated": "2021-03-11T04:42:10.938Z", "summary": "Stay at Hotel Forlanini 52 (52 V", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMkwxrpQufNeCCsBdSi02M26YbruoXS4h4\n", "location": "52 <PERSON>, Parma, Provincia di Parma, Emilia-Romagna 43122, \nItalia", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-07-30"}, "end": {"date": "2020-08-01"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t18ks58t6rjjlac4f8bmbi6hg8pvragjdub5e0od41rv6h7dn38p0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMkwxrpQufNeCCsBdSi02M26YbruoXS4h4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230875461876000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehhnadjl65r6krjac9jj6rb5dhnmgeb1d5ijgrrbegpmcshhdhm72sjgckqmgohi6dm3ct1jd1h6schmdop6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaG5hZGpsNjVyNmtyamFjOWpqNnJiNWRobm1nZWIxZDVpamdycmJlZ3BtY3NoaGRobTcyc2pnY2txbWdvaGk2ZG0zY3QxamQxaDZzY2htZG9wNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-07-13T14:29:46.000Z", "updated": "2021-03-11T04:42:10.938Z", "summary": "Stay at Hotel TRE VILLE (97a Via", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNVwpVoGHrfgYToj5WmIcBpjwy-Ql_BVHU\n", "location": "97/A Via Benedetta, Parma, Provincia di Parma, Emilia-Romagna, Italia", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-08-04"}, "end": {"date": "2020-08-06"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tcu6u1vjnjbg3meloh9aie8okt3fr1llqrpe5hb23l6t3hbn26n2g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNVwpVoGHrfgYToj5WmIcBpjwy-Ql_BVHU", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873050444000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegq6iq39e1in2cja6lk66rba71nm8r38cdr3coj9cdo6sr366cp34d3l65p6kcriedhn4qbje5h6cdrc61hj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncTZpcTM5ZTFpbjJjamE2bGs2NnJiYTcxbm04cjM4Y2RyM2NvajljZG82c3IzNjZjcDM0ZDNsNjVwNmtjcmllZGhuNHFiamU1aDZjZHJjNjFoajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-08-05T21:02:07.000Z", "updated": "2021-03-11T04:22:05.222Z", "summary": "Stay at Squaw Rock RV Resort & Campground", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM6bRTIj5xjK2CF1QS6_c_kOomx1lsdjKI\n", "location": "Squaw Rock RV Resort & Campground, Naches", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-08-14"}, "end": {"date": "2020-08-17"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t4ihipeq2j5hcmj8odlhcv6bicpnlf3224u1rj3rscrisqbf7l0c0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM6bRTIj5xjK2CF1QS6_c_kOomx1lsdjKI", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325336210000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehh7atbm6tj6qo9g6tk34t1l6hpjirrdd4on4ebke0r32pbbd9ojap9gelq6aqjke4p6qq1nddr78rb16lh30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaDdhdGJtNnRqNnFvOWc2dGszNHQxbDZocGppcnJkZDRvbjRlYmtlMHIzMnBiYmQ5b2phcDlnZWxxNmFxamtlNHA2cXExbmRkcjc4cmIxNmxoMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-07-23T07:16:58.000Z", "updated": "2023-01-25T16:04:28.105Z", "summary": "Stay at Ideal Hotel Design", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPlC5wbwKzy9kahjauVx1Wlv1bglJysOaQ\n", "location": "Ideal Hotel Design, Paris", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-08-26"}, "end": {"date": "2020-08-28"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tbuuv7fma07h2t54s9omi1r9tp61ekjq5e0utejtq2mh7kvtma5b0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPlC5wbwKzy9kahjauVx1Wlv1bglJysOaQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873048584000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehl6edpgc9gmitb568pn6t37dth72p3k6lh70cba6tmmoqhoe1m7ctb8dlm3ed34chr3isbceopmedppckr30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobDZlZHBnYzlnbWl0YjU2OHBuNnQzN2R0aDcycDNrNmxoNzBjYmE2dG1tb3Fob2UxbTdjdGI4ZGxtM2VkMzRjaHIzaXNiY2VvcG1lZHBwY2tyMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-07-29T11:05:02.000Z", "updated": "2021-03-11T04:22:04.292Z", "summary": "Stay at Starhotels Du Parc (12 V", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOj_DDMnu0tK7EzklDAeNd0LShGvb8h6e0\n", "location": "Starhotels Du Parc (12 V, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-09-01"}, "end": {"date": "2020-09-03"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tjg70baiue23stgobqdt5bp1j7mlj8plvuhml74ddv9qlv3g79e60", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOj_DDMnu0tK7EzklDAeNd0LShGvb8h6e0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873048584000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpego38pb9cdn3ap1k6cs64rrb6to6qcjcc5kmecrlegqmkob571n6ishge5mmedrfeco74p1k69o6os3hddm6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnbzM4cGI5Y2RuM2FwMWs2Y3M2NHJyYjZ0bzZxY2pjYzVrbWVjcmxlZ3Fta29iNTcxbjZpc2hnZTVtbWVkcmZlY283NHAxazY5bzZvczNoZGRtNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-01-10T12:43:41.000Z", "updated": "2021-03-11T04:22:04.292Z", "summary": "Stay at Leonardo Hotel", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMdqzQFMHTD4nXpjH-biI5e8_9LXUy_8fQ\n", "location": "Leonardo Hotel, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-09-08"}, "end": {"date": "2020-09-10"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t04eicn5d438bok7pm2laig3ut5jae8nir0qmg7os0rd42plpqklg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMdqzQFMHTD4nXpjH-biI5e8_9LXUy_8fQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873048584000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehnnco9ne5in8e1idpinae32c5l6gsrj6ss3ge3k6dnnaojgdspj6e9j6dn30qbmctmj0rjh6pmmkdbcd9i30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobm5jbzluZTVpbjhlMWlkcGluYWUzMmM1bDZnc3JqNnNzM2dlM2s2ZG5uYW9qZ2RzcGo2ZTlqNmRuMzBxYm1jdG1qMHJqaDZwbW1rZGJjZDlpMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-01-10T12:46:27.000Z", "updated": "2021-03-11T04:22:04.292Z", "summary": "Stay at Starhotels Du Parc", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNb7ieBFYmRVktAT4mv6RxfA0Q4-SVSvGo\n", "location": "Starhotels Du Parc, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-09-16"}, "end": {"date": "2020-09-18"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tova7qet82neu8bajhss7888t3oubpo33933n0ivgm0nq6mj5ljd0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNb7ieBFYmRVktAT4mv6RxfA0Q4-SVSvGo", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3203222125626000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegp36sbmdoq6udre70omgtje6hpm4rrddgoj8drad9gm2rjj68rmst3b60r7cdpkd9h6ge3gd9n3id1gelr30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncDM2c2JtZG9xNnVkcmU3MG9tZ3RqZTZocG00cnJkZGdvajhkcmFkOWdtMnJqajY4cm1zdDNiNjByN2NkcGtkOWg2Z2UzZ2Q5bjNpZDFnZWxyMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-10-02T03:57:42.000Z", "updated": "2020-10-02T03:57:42.813Z", "summary": "Stay at Rivertide Suites Hotel", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMT6rLe80eQWOKQi6UYJZsJYmB0NX7G82k\n", "location": "Rivertide Suites Hotel, Seaside", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-10-02"}, "end": {"date": "2020-10-04"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t23qvn4o7n81hvn4sboml147jjaans27ntk06v74jbh8pjn940uv0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMT6rLe80eQWOKQi6UYJZsJYmB0NX7G82k", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230875464010000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpego34tb5cpqn2thidtgmccrjdhj36qbiclqj0cbicdl6ir9pe8r70p31d5n66ojhecp34ohp75q6cq1nd4qj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnbzM0dGI1Y3BxbjJ0aGlkdGdtY2NyamRoajM2cWJpY2xxajBjYmljZGw2aXI5cGU4cjcwcDMxZDVuNjZvamhlY3AzNG9ocDc1cTZjcTFuZDRxajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-01-10T12:49:20.000Z", "updated": "2021-03-11T04:42:12.005Z", "summary": "Stay at Leonardo Hotel", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMdqzQFMHTD4nXpjH-biI5e8_9LXUy_8fQ\n", "location": "Leonardo Hotel, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-10-06"}, "end": {"date": "2020-10-08"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t02uefuqv2oaf3slf3ireu01rcjim9r6pdaincbqs22b99tfh7i50", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMdqzQFMHTD4nXpjH-biI5e8_9LXUy_8fQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230875464010000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehgn2eb569n6csjj64qmmq1jellmkpr575q34drge8rn4draccpjaq31epq3coph6dn78thpd4p6ssra6sr30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoZ24yZWI1NjluNmNzamo2NHFtbXExamVsbG1rcHI1NzVxMzRkcmdlOHJuNGRyYWNjcGphcTMxZXBxM2NvcGg2ZG43OHRocGQ0cDZzc3JhNnNyMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-10-02T07:11:25.000Z", "updated": "2021-03-11T04:42:12.005Z", "summary": "Stay at Starhotels Du Parc", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPqT0Lr098P8yfduL1SDtYMpAu4GPFhyXs\n", "location": "Starhotels Du Parc, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-10-06"}, "end": {"date": "2020-10-08"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9taq9e2nfrs15kh3ukjge9t27pr7r7jc35havt6c13ntv9i2nsj760", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPqT0Lr098P8yfduL1SDtYMpAu4GPFhyXs", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230875464010000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehp6opb6ekqmed1id5gmcr3j6dq6idb4ddi7ctbmchr74thjddlj2db5copm4thlddp70rjee5j3gob1dli6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocDZvcGI2ZWtxbWVkMWlkNWdtY3IzajZkcTZpZGI0ZGRpN2N0Ym1jaHI3NHRoamRkbGoyZGI1Y29wbTR0aGxkZHA3MHJqZWU1ajNnb2IxZGxpNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-08-30T16:06:16.000Z", "updated": "2021-03-11T04:42:12.005Z", "summary": "Stay at Hotel The Savoy (Strand)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNTJItPb0P37G1zmD9UUNHCOdL1j62PsEI\n", "location": "Hotel The Savoy (Strand), Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-10-14"}, "end": {"date": "2020-10-16"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9trlefu5g42iafls3ti5dkdvuvdvrv3kk15ef3bv5krpnnqf8aamdg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNTJItPb0P37G1zmD9UUNHCOdL1j62PsEI", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873049682000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegqn0ojbcti6oo9hdoo3asr9d8oj2d38edo6qprce9m64cjg6pnmks1pc9mj4db36po7aoj6egr6ocb7d5jme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncW4wb2piY3RpNm9vOWhkb28zYXNyOWQ4b2oyZDM4ZWRvNnFwcmNlOW02NGNqZzZwbm1rczFwYzltajRkYjM2cG83YW9qNmVncjZvY2I3ZDVqbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-10-02T06:39:06.000Z", "updated": "2021-03-11T04:22:04.841Z", "summary": "Stay at Hotel Button", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DODCykwq4_ZTj9BkVCZMFr0JSGpzwKIX6Y\n", "location": "Hotel Button, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-11-10"}, "end": {"date": "2020-11-12"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t5pbkgdla1n05sij114hspmglrlb2p6ojp9bm25c6pubft6l1gigg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DODCykwq4_ZTj9BkVCZMFr0JSGpzwKIX6Y", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3210617503616000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehnjcq3mdgsjiopkeop3is9jd1qj0d9gdln72sbde8p64qj7e8s6udpjcthmaobbd1lm6s39cdn62ebfe5m6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobmpjcTNtZGdzamlvcGtlb3AzaXM5amQxcWowZDlnZGxuNzJzYmRlOHA2NHFqN2U4czZ1ZHBqY3RobWFvYmJkMWxtNnMzOWNkbjYyZWJmZTVtNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-10-21T18:18:28.000Z", "updated": "2020-11-13T23:05:51.808Z", "summary": "Flight to Atlanta (DL 362)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOzZ_S0ZBqFZO9Tyc_4GNwU7FlK1ph0ciQ\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-11-13T22:10:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-11-14T02:32:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9to6hvl99c4v29q3hu050mnqqmr2bjgr8o73gceakhkcpicna9oqlg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOzZ_S0ZBqFZO9Tyc_4GNwU7FlK1ph0ciQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3210617503616000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegomurjdcspn0qbdehm72qjaepinashkdpn3ipjed8sjgpr66lon4e9mcdj3go9p6tjmephi75o30cbc74o30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb211cmpkY3NwbjBxYmRlaG03MnFqYWVwaW5hc2hrZHBuM2lwamVkOHNqZ3ByNjZsb240ZTltY2RqM2dvOXA2dGptZXBoaTc1bzMwY2JjNzRvMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-10-21T18:18:28.000Z", "updated": "2020-11-13T23:05:51.808Z", "summary": "Flight to Fort Myers (DL 1663)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOzZ_S0ZBqFZO9Tyc_4GNwU7FlK1ph0ciQ\n", "location": "Atlanta ATL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-11-14T05:10:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-11-14T06:39:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1onmg3pimtlqjjveur4nn9fnj98gf5qr96cf8a97ggf29p01l900", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOzZ_S0ZBqFZO9Tyc_4GNwU7FlK1ph0ciQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873049682000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehlm8dpl6sq6csrhdlhjgrhkecomsqpiedln8ppk6pnj8q1oe0o66tb9epk7ac9h6osmerbge8on8sj7clm30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobG04ZHBsNnNxNmNzcmhkbGhqZ3Joa2Vjb21zcXBpZWRsbjhwcGs2cG5qOHExb2UwbzY2dGI5ZXBrN2FjOWg2b3NtZXJiZ2U4b244c2o3Y2xtMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-01-10T12:52:35.000Z", "updated": "2021-03-11T04:22:04.841Z", "summary": "Stay at Starhotels Du Parc", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNb7ieBFYmRVktAT4mv6RxfA0Q4-SVSvGo\n", "location": "Starhotels Du Parc, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-11-18"}, "end": {"date": "2020-11-20"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tkd7574fsqmc8n4s1nk2sktg46o4h8p0cuivhu1169gmpr1trgel0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNb7ieBFYmRVktAT4mv6RxfA0Q4-SVSvGo", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873049682000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehknacr7dos74t1kddk62c1neli72sb5cphmkpj6cgo6ksjldto6ucj4dtr6ecpoegpm6e3365hmkqhm75r30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoa25hY3I3ZG9zNzR0MWtkZGs2MmMxbmVsaTcyc2I1Y3BobWtwajZjZ282a3NqbGR0bzZ1Y2o0ZHRyNmVjcG9lZ3BtNmUzMzY1aG1rcWhtNzVyMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-10-02T07:19:01.000Z", "updated": "2021-03-11T04:22:04.841Z", "summary": "Stay at Hotel Forlanini 52", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMBVURLUVYDVGFI8hsGR9kFei1B3mYgroA\n", "location": "52 <PERSON>, Parma, Provincia di Parma, Emilia-Romagna 43122, \nItalia", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2020-11-24"}, "end": {"date": "2020-11-26"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tiu3gn8rt4kha07udqqefcjffd0jruopo2dovg38t3c8c1cjj69v0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMBVURLUVYDVGFI8hsGR9kFei1B3mYgroA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3212979802316000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehi70ebmehm68s3e6ljj2pb1eks6uq9gd9h74p34chk3cc9hd1m70s1ledq7ccr3d1j3ir36d9o6mob3edm30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaTcwZWJtZWhtNjhzM2U2bGpqMnBiMWVrczZ1cTlnZDloNzRwMzRjaGszY2M5aGQxbTcwczFsZWRxN2NjcjNkMWozaXIzNmQ5bzZtb2IzZWRtMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2020-10-13T04:54:52.000Z", "updated": "2020-11-27T15:11:41.158Z", "summary": "Flight to Seattle (AS 547)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOglZ-TQ_Lkyx1Q7exmblv-mCjq13f6Zsw\n", "location": "Fort Myers RSW", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2020-11-28T07:00:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2020-11-28T13:35:00-08:00", "timeZone": "UTC"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tdp9vtldpn5g1eau8oi0jbrdddh611hlpp5stv3chf9lfjpkacsl0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOglZ-TQ_Lkyx1Q7exmblv-mCjq13f6Zsw", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873047158000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehq62sb1cdn3iqra75pjct1pd5jmad3jcgq78qjm6hkjir37cco6soriedi78r3668s6mdj271mj2sbfdkpj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocTYyc2IxY2RuM2lxcmE3NXBqY3QxcGQ1am1hZDNqY2dxNzhxam02aGtqaXIzN2NjbzZzb3JpZWRpNzhyMzY2OHM2bWRqMjcxbWoyc2JmZGtwajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-01-26T14:17:38.000Z", "updated": "2021-03-11T04:22:03.579Z", "summary": "Stay at Hotel Forlanini 52", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMW529x4NNqogiOFNj5S9RYvF38A7VKEw8\n", "location": "<PERSON>, 52, 43122 Parma Provincia di Parma, Italia", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2021-02-24"}, "end": {"date": "2021-02-26"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ttaqacn9kj9s6t9ige4sd4tjv4i9lgc0ncrsdtlf28k6b8m1qom30", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMW529x4NNqogiOFNj5S9RYvF38A7VKEw8", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873047158000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehkmkp33dcp6ior6edpm2o9o69kmgdj5dgqn6qjbdtp6eshncho3atb3chq68dbldlj76p9odpq6edja69p30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoa21rcDMzZGNwNmlvcjZlZHBtMm85bzY5a21nZGo1ZGdxbjZxamJkdHA2ZXNobmNobzNhdGIzY2hxNjhkYmxkbGo3NnA5b2RwcTZlZGphNjlwMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-01-26T14:30:24.000Z", "updated": "2021-03-11T04:22:03.579Z", "summary": "Stay at Starhotels Du Parc", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNSPlujnBU4OivZvejBwACgc0g4qOzya3Q\n", "location": "Starhotels Du Parc, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2021-03-02"}, "end": {"date": "2021-03-04"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tijdck2icfssaa82ih6el5sjkorgr7dp5ucdtd5umfse8ntg6j2r0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNSPlujnBU4OivZvejBwACgc0g4qOzya3Q", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3230873047158000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehp6mt1n6cpm8orccpjn0d3mcppm4c3jc5l6qspge5q6arbad1km4q1gd0sm2eb361o76s346hk66s9peoq6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocDZtdDFuNmNwbThvcmNjcGpuMGQzbWNwcG00YzNqYzVsNnFzcGdlNXE2YXJiYWQxa200cTFnZDBzbTJlYjM2MW83NnMzNDZoazY2czlwZW9xNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-02-02T21:16:09.000Z", "updated": "2021-03-11T04:22:03.579Z", "summary": "Stay at Hotel Daniel Parma & Ristorante Cocchi", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNi7UxS24zHZvAhXF1WgUkYQdssHOGnNF0\n", "location": "Hotel Daniel Parma & Ristorante Cocchi, Parma", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2021-03-09"}, "end": {"date": "2021-03-11"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9trkt733dclfgp4vfsb0sajms0qtemjhibh0h9a9c0pspd4hcq9v4g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNi7UxS24zHZvAhXF1WgUkYQdssHOGnNF0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3236546366654000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegr6ipbaeppmucr6dlr76pj5e1hncq3c75gjad1odlm32sjmdpjmso9he0qj8eb574o36crg71qj0tjmd5ij0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncjZpcGJhZXBwbXVjcjZkbHI3NnBqNWUxaG5jcTNjNzVnamFkMW9kbG0zMnNqbWRwam1zbzloZTBxajhlYjU3NG8zNmNyZzcxcWowdGptZDVpajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-04-13T00:19:43.000Z", "updated": "2021-04-13T00:19:43.327Z", "summary": "Stay at Plaza Mar on Los Muertos Beach in Old Town Puerto Vallarta", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNSqW_4OwiSUpKM8teXfnqs8Xax08X6XvM\n", "location": "Plaza Mar on Los Muertos Beach in Old Town Puerto Vallarta, Puerto Vallarta", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2021-04-12"}, "end": {"date": "2021-04-15"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t6iejvso3fmvsfepcvhl9a548ml1rvngna1p549e9033p8u0vvie0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNSqW_4OwiSUpKM8teXfnqs8Xax08X6XvM", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325561578000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehj34c1hehp6ir1gddp3gdjcdsrncqra6ti62c1mcor6ur9lcpn7cs32c9p36rjb69i3ctb3c4s6mqb1d1lme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoajM0YzFoZWhwNmlyMWdkZHAzZ2RqY2Rzcm5jcXJhNnRpNjJjMW1jb3I2dXI5bGNwbjdjczMyYzlwMzZyamI2OWkzY3RiM2M0czZtcWIxZDFsbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-06-02T02:33:08.000Z", "updated": "2023-01-25T16:06:20.789Z", "summary": "Stay at Bay Center / Willapa Bay KOA Journey", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNxr_wsPe3YGuTt2tJbnthIpQf1dUxeQJQ\n", "location": "Bay Center / Willapa Bay KOA Journey, Bay Center", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2021-06-15"}, "end": {"date": "2021-06-17"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tf201tril0kr86lo7vkj7da06f6om5fnvpbbr3nk2d6uca8kiahkg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNxr_wsPe3YGuTt2tJbnthIpQf1dUxeQJQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325181170000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegom2qr8c4oj4dpl6pk38q9i71hj4r3ae1n6uohl6hljgr1pcko3edjf65p66sbm6pq70tjh68oj8o9i65nj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnb20ycXI4YzRvajRkcGw2cGszOHE5aTcxaGo0cjNhZTFuNnVvaGw2aGxqZ3IxcGNrbzNlZGpmNjVwNjZzYm02cHE3MHRqaDY4b2o4bzlpNjVuajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-07-08T18:38:06.000Z", "updated": "2023-01-25T16:03:10.585Z", "summary": "Stay at Hotel Caza Fisherman’s Wharf", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP8r7B4JcfFJHJW6yPz3iNiioNXiy_V1ko\n", "location": "Hotel Caza Fisherman’s Wharf, San Francisco", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2021-07-05"}, "end": {"date": "2021-07-09"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t1akha12756h4i28c2ljpnob54k8l9e076o1rcqv6tpvq214a21o0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DP8r7B4JcfFJHJW6yPz3iNiioNXiy_V1ko", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325224768000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehij6r3d6pq76cble9p32q31cdm66pj8edm76cr561l66sr1ccs6sppo6lkm2phicsqjgs9jeopmut1l6op30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaWo2cjNkNnBxNzZjYmxlOXAzMnEzMWNkbTY2cGo4ZWRtNzZjcjU2MWw2NnNyMWNjczZzcHBvNmxrbTJwaGljc3FqZ3M5amVvcG11dDFsNm9wMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-07-06T23:59:54.000Z", "updated": "2023-01-25T16:03:32.384Z", "summary": "Stay at Santa Cruz North / Costanoa KOA", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNek8OFPiDuwpAwNH3rC0qk_Fqi8j14Xk4\n", "location": "Santa Cruz North / Costanoa KOA, Pescadero", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2021-07-08"}, "end": {"date": "2021-07-10"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9te3lm6ts1urr1haclcfhsls3e0jcsac8ng85iaf2g58q3v3ot5620", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNek8OFPiDuwpAwNH3rC0qk_Fqi8j14Xk4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325455058000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehi7cc1lc9pm6r3j71h78dpg68p78cjce8s7csr9e5q66crid4q6ud9lcor6cdj1dhh64c3964s6gt9le8s6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaTdjYzFsYzlwbTZyM2o3MWg3OGRwZzY4cDc4Y2pjZThzN2NzcjllNXE2NmNyaWQ0cTZ1ZDlsY29yNmNkajFkaGg2NGMzOTY0czZndDlsZThzNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-05-24T17:54:37.000Z", "updated": "2023-01-25T16:05:27.529Z", "summary": "Flight to Seattle (B6 531)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM8w3DEvsUk2oAhr1zSMDtKXzCC2MeAaGQ\n", "location": "Newark EWR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2021-07-08T15:32:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2021-07-08T21:58:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tdv05bscls8bt7022t2lr8vsiqtc3ri4o55f6f6albb0i18hu5r8g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM8w3DEvsUk2oAhr1zSMDtKXzCC2MeAaGQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325497260000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehnmsdjkc4pj4qpmcth3ic37cop66rrm6tj78c3celqmmrhkc8r78dbk6gr6qqb2e9n6qqr1dcp3gebg6pn30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobm1zZGprYzRwajRxcG1jdGgzaWMzN2NvcDY2cnJtNnRqNzhjM2NlbHFtbXJoa2M4cjc4ZGJrNmdyNnFxYjJlOW42cXFyMWRjcDNnZWJnNnBuMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2021-07-25T00:00:51.000Z", "updated": "2023-01-25T16:05:48.630Z", "summary": "Stay at Manchester Grand Hyatt San Diego", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM4rfLa9z-6PcRYZ6T5H-biWotJoQB2TZs\n", "location": "Manchester Grand Hyatt San Diego, San Diego", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2021-07-21"}, "end": {"date": "2021-07-25"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ton6ta32k6gb90gf2cov7ft0luukn4b6t5t46mibrnmkak289p6n0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM4rfLa9z-6PcRYZ6T5H-biWotJoQB2TZs", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325252590000\"", "id": "_6oom2c9j69h66p1mckpji", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZvb20yYzlqNjloNjZwMW1ja3BqaSBtaWNoYWVsQGd1bG1hbm4uY29t", "created": "2021-11-27T15:00:45.000Z", "updated": "2023-01-25T16:03:46.295Z", "summary": "Season Ski Rental", "description": "When: Saturday, November 27, 2021\nTime: 4:30 PM\nDuration: 30 min\nClient Name: <PERSON>\nClient Phone: +12069026546\nClient Mobile: +12069026546\nClient Email: <PERSON>@gulmann.com\nClient Custom Request: \nWith: Rental Tech 1\nWhere: 2215 15th AVE W Seattle 98119 WA US\nService: Season Ski Rental\nService description: This is for Skis only. This is for one person. Please make multiple appointments for multiple people.\n", "location": "2215 15th AVE W Seattle 98119 WA US", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>", "displayName": "booxi no-reply"}, "start": {"dateTime": "2021-11-27T16:30:00-08:00", "timeZone": "UTC"}, "end": {"dateTime": "2021-11-27T17:00:00-08:00", "timeZone": "UTC"}, "iCalUID": "61a132bcd6e39", "sequence": 2, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "needsAction"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3349325231276000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegrj2dj968q3gc9ke4o6qdr2elj6acriedkmcdrjd9lmme9jctp3cd3jctom8db5coomiobm6tqmmtj7dlk30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncmoyZGo5NjhxM2djOWtlNG82cWRyMmVsajZhY3JpZWRrbWNkcmpkOWxtbWU5amN0cDNjZDNqY3RvbThkYjVjb29taW9ibTZ0cW1tdGo3ZGxrMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-01-06T21:28:38.000Z", "updated": "2023-01-25T16:03:35.638Z", "summary": "Flight to Seattle (AS 8)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMOy9VUGELRaNPsJdUjWPhtOv1UQbebjAA\n", "location": "Newark EWR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-02-27T14:10:00-08:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-02-27T20:28:00-08:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t716i24814q0m7bufe3rsif7sjkk93gr64sgqd5ef1iav7ukvgmh0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMOy9VUGELRaNPsJdUjWPhtOv1UQbebjAA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3299731806090000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegpjgp33ehl3ipbgelkmsrpn6op78ojhc4sn8eb861njgqbaddo66cb368p6qpr7d9j68criedo6oeb8ddij0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncGpncDMzZWhsM2lwYmdlbGttc3JwbjZvcDc4b2poYzRzbjhlYjg2MW5qZ3FiYWRkbzY2Y2IzNjhwNnFwcjdkOWo2OGNyaWVkbzZvZWI4ZGRpajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-04-13T16:05:03.000Z", "updated": "2022-04-13T16:05:03.045Z", "summary": "Stay at Marival Distinct Luxury Residences", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM6NCaUqsBojY3QrF_YWz0fIa1lSi_EegE\n", "location": "Marival Distinct Luxury Residences, Nuevo Vallarta", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2022-04-13"}, "end": {"date": "2022-04-21"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t38dctj9epuino762tbqa9t9h0o8ijkpc1c22mggjfd3rspl9hke0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM6NCaUqsBojY3QrF_YWz0fIa1lSi_EegE", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3299731806090000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehkmuprm6os74r9ndpjmeqhnc5j76cbldhk6kr1pd5p6scr8d1r78d3169oj2qrjchomudjdc8qn8s1keho6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoa211cHJtNm9zNzRyOW5kcGptZXFobmM1ajc2Y2JsZGhrNmtyMXBkNXA2c2NyOGQxcjc4ZDMxNjlvajJxcmpjaG9tdWRqZGM4cW44czFrZWhvNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-04-02T00:46:08.000Z", "updated": "2022-04-13T16:05:03.045Z", "summary": "Flight to Seattle (DL 1922)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNQ4vrYYrrbNqF0bR2nrga5NN9Jd2Pni64\n", "location": "Puerto Vallarta PVR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-04-20T14:15:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-04-20T19:30:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tiogv68rm7nggj7afs1ulhjl9irn3hhvt4a2q1ksdqo6mb5tp4tpg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNQ4vrYYrrbNqF0bR2nrga5NN9Jd2Pni64", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3301137516804000\"", "id": "2srq4456929bpn4her7cn0r0kl", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=MnNycTQ0NTY5MjlicG40aGVyN2NuMHIwa2wgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-04-21T19:15:14.000Z", "updated": "2022-04-21T19:19:18.402Z", "summary": "Michael flight to Vegas - DL 1290", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2022-05-02T15:30:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-05-02T16:30:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "<EMAIL>", "sequence": 0, "attendees": [{"email": "<EMAIL>", "responseStatus": "needsAction"}, {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "organizer": true, "self": true, "responseStatus": "accepted"}], "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3301137538786000\"", "id": "7o4miv2ais0igkp7v7m49hmr4m", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=N280bWl2MmFpczBpZ2twN3Y3bTQ5aG1yNG0gbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-04-21T19:16:54.000Z", "updated": "2022-04-21T19:19:29.393Z", "summary": "<PERSON> flight home from Vegas  - DL 1290", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2022-05-05T19:00:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-05-05T21:45:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "<EMAIL>", "sequence": 0, "attendees": [{"email": "<EMAIL>", "responseStatus": "needsAction"}, {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "organizer": true, "self": true, "responseStatus": "accepted"}], "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3303695936210000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehk70cr271jn0r1pdtj3erj3ctqncqjce5hj6s9k74p72sr36tnmitj774r78opm74smer36eoojicpgd8qme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoazcwY3IyNzFqbjByMXBkdGozZXJqM2N0cW5jcWpjZTVoajZzOWs3NHA3MnNyMzZ0bm1pdGo3NzRyNzhvcG03NHNtZXIzNmVvb2ppY3BnZDhxbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-05-04T11:25:55.000Z", "updated": "2022-05-06T14:39:28.105Z", "summary": "Flight to Doha (QR 720)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPReSqHEhsNB07JlJSpkFJLjQZbfvVgQKc\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-05-08T17:10:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-05-09T07:20:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9thp3b8gpl9of7ncguvjlqc3q492qsc7oivg96tc699glfv1930j5g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPReSqHEhsNB07JlJSpkFJLjQZbfvVgQKc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3303695936210000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegqj0ohj64r74e9gelp74oj4dkr7cobg60p6qs9n6pn3idrjctq6gq1hc5o78qjie4om6q3bc9i6odrc6hh30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncWowb2hqNjRyNzRlOWdlbHA3NG9qNGRrcjdjb2JnNjBwNnFzOW42cG4zaWRyamN0cTZncTFoYzVvNzhxamllNG9tNnEzYmM5aTZvZHJjNmhoMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-05-04T11:25:55.000Z", "updated": "2022-05-06T14:39:28.105Z", "summary": "Flight to New Delhi (QR 578)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPReSqHEhsNB07JlJSpkFJLjQZbfvVgQKc\n", "location": "Doha DOH", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-05-09T09:35:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-05-09T13:25:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t50b316r90urrbdm6vap02mq76n97sgthh1aptjrq1chkbdl7l4b0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPReSqHEhsNB07JlJSpkFJLjQZbfvVgQKc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3303695936210000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegs30dphe0s68t1kc8pmcthje5jnadr7cplmsr9ieljjcp9hd1lm4qrmehljaohm6spmmt39d5mmmrj9cgome", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnczMwZHBoZTBzNjh0MWtjOHBtY3RoamU1am5hZHI3Y3BsbXNyOWllbGpqY3A5aGQxbG00cXJtZWhsamFvaG02c3BtbXQzOWQ1bW1tcmo5Y2dvbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-05-04T11:25:55.000Z", "updated": "2022-05-06T14:39:28.105Z", "summary": "Flight to Doha (QR 579)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPReSqHEhsNB07JlJSpkFJLjQZbfvVgQKc\n", "location": "New Delhi DEL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-05-12T14:55:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-05-12T18:55:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t8071p8dt4b3fv3qgu7gfknm2ug6e1hkbkvtk5b673ktiimknid1g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPReSqHEhsNB07JlJSpkFJLjQZbfvVgQKc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3303695936210000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehim6c3e64qjgp3hd0smqc9hepi34q1hcljj2phgd1lmioj5cdjncqr26gq78p31epp62db5dssn0prjedk6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaW02YzNlNjRxamdwM2hkMHNtcWM5aGVwaTM0cTFoY2xqajJwaGdkMWxtaW9qNWNkam5jcXIyNmdxNzhwMzFlcHA2MmRiNWRzc24wcHJqZWRrNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-05-04T11:25:55.000Z", "updated": "2022-05-06T14:39:28.105Z", "summary": "Flight to Seattle (QR 719)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPReSqHEhsNB07JlJSpkFJLjQZbfvVgQKc\n", "location": "Doha DOH", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-05-12T21:55:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-05-13T12:30:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tec0n158dqh9m11vd2h1eg1f0hkibecgvkb44tdavra5eo9pgsshg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPReSqHEhsNB07JlJSpkFJLjQZbfvVgQKc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3303353471950000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegs62sjmdhp6gq9o6pq32chkd5omuqjcd1n3gdrm71l3gr9h6gp3csr86gs6usrldlimmo9hcdhm8tj8ckq30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnczYyc2ptZGhwNmdxOW82cHEzMmNoa2Q1b211cWpjZDFuM2dkcm03MWwzZ3I5aDZncDNjc3I4NmdzNnVzcmxkbGltbW85aGNkaG04dGo4Y2txMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-05-04T15:05:35.000Z", "updated": "2022-05-04T15:05:35.975Z", "summary": "Stay at The Village Inn", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOMoTqUrBTSQDUiztMJgSHCMbzuIFqIxso\n", "location": "606 Vermont 114, <PERSON>, VT 05832, USA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2022-05-13"}, "end": {"date": "2022-05-15"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t8arvlrhi86t124iqojlhn87v8j8m1426sh48osumeka1ccdvhe40", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOMoTqUrBTSQDUiztMJgSHCMbzuIFqIxso", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3312991682128000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegsmepbkckomoojkc5j7ad9m69hmidr975hncrjf74o72tjmepm6ocbfd1n7adrcccqjeopneli7adbh6oq30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnc21lcGJrY2tvbW9vamtjNWo3YWQ5bTY5aG1pZHI5NzVobmNyamY3NG83MnRqbWVwbTZvY2JmZDFuN2FkcmNjY3FqZW9wbmVsaTdhZGJoNm9xMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-06-24T12:18:01.000Z", "updated": "2022-06-29T09:44:01.064Z", "summary": "Flight to Lisbon (TP 1351)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMBcoWjNVmYX9Yw9AeVyFSZbOHVhWnqo4c\n", "location": "London LHR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-06-29T00:00:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-06-29T02:40:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t9gete1lbtafu562ci7i9cvno90qvvvll1ohnu7lc57c7udu5q640", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMBcoWjNVmYX9Yw9AeVyFSZbOHVhWnqo4c", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3310258711384000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehmjcspgd5p38ojddkpmmcrce9ln8sr2c8r30pj161o6qcr7dcqn2p9m6lo6os1icdmm6djbcgsm6s3edtqme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobWpjc3BnZDVwMzhvamRka3BtbWNyY2U5bG44c3IyYzhyMzBwajE2MW82cWNyN2RjcW4ycDltNmxvNm9zMWljZG1tNmRqYmNnc202czNlZHRxbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-06-13T14:09:15.000Z", "updated": "2022-06-13T14:09:15.692Z", "summary": "Stay at Homeaway 437572ha", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPBbTt-brM0RFnXkoPTVAYjQgP3ZSIQ-jA\n", "location": "Homeaway 437572ha, <PERSON><PERSON><PERSON>", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2022-06-29"}, "end": {"date": "2022-07-04"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tm6s0ir4bmm3k3lrktsbb60fa0pm3gk5qe65plp2cmc6kd9cpnoug", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPBbTt-brM0RFnXkoPTVAYjQgP3ZSIQ-jA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3318522344736000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehhjgc3lcpm6se9h65kmos1oc8qm4qrmc5m72p32e8p6uq9k64r6gc9pcpj3gtbi69kj6cb8e9q74spk74p6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaGpnYzNsY3BtNnNlOWg2NWttb3Mxb2M4cW00cXJtYzVtNzJwMzJlOHA2dXE5azY0cjZnYzlwY3BqM2d0Ymk2OWtqNmNiOGU5cTc0c3BrNzRwNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-06-23T17:54:41.000Z", "updated": "2022-07-31T09:52:52.368Z", "summary": "Stay at Convent's House", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO37DOO9F-sG-EI2f8C4WQGfji7O73gUnc\n", "location": "Convent's House, Évora", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2022-07-03"}, "end": {"date": "2022-07-05"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tc80ufln911ilp8b5bkvalqdbr2oi416h19ff8ur2i31hrtrs492g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO37DOO9F-sG-EI2f8C4WQGfji7O73gUnc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3314176847336000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehnmoprk6lgm4c33d5gnatj665ij8rbhegr3aojdd9inccrle1im2pbmcopn0r1h6ksmqtbd6hnn8r9hegr30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobm1vcHJrNmxnbTRjMzNkNWduYXRqNjY1aWo4cmJoZWdyM2FvamRkOWluY2NybGUxaW0ycGJtY29wbjByMWg2a3NtcXRiZDZobm44cjloZWdyMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-06-24T12:18:01.000Z", "updated": "2022-07-06T06:20:23.668Z", "summary": "Flight to Copenhagen (TP 754)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMBcoWjNVmYX9Yw9AeVyFSZbOHVhWnqo4c\n", "location": "Lisbon LIS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-07-05T23:35:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-07-06T03:10:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tolgt5ab0ciauvf1e4mqt65bmjev3upeaevf3pl159mum4otm1t60", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMBcoWjNVmYX9Yw9AeVyFSZbOHVhWnqo4c", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3309982849364000\"", "id": "_64ok8cpg711j2ba38crk6b9k6p1j4ba16kp46b9m8co32cpi752j8d1l74", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzY0b2s4Y3BnNzExajJiYTM4Y3JrNmI5azZwMWo0YmExNmtwNDZiOW04Y28zMmNwaTc1Mmo4ZDFsNzQgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-06-11T23:50:24.000Z", "updated": "2022-06-11T23:50:24.682Z", "summary": "Budget Car Pickup Reminder", "description": "Reservation Confirmation Number: 11236041US5 \n\nThis rental is for: <PERSON><PERSON>****n \n\nPick-Up Location: Kastrup \nPick-Up Date: Wed., July 06, 2022 \nPick-Up Time: 12:30 PM \nPick-Up Location Phone: (45) 3328 6360 \n\nReturn Location: Kast<PERSON> \nReturn Date: Sat., July 09, 2022 \nReturn Time: 12:00 PM \n\nContact Us: **************", "location": "Lufthavnsboulevarden 2 Car Rental Centre Kastrup XX 2770 DK ", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2022-07-06T03:30:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-07-06T03:45:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "11D308C1-CC7C-46C2-A52C-6C01329E4459", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3309982850414000\"", "id": "_8p136dhg6cq46b9j6orjgb9k6p13gb9o652j6b9h8p136ca588sj6ca664", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzhwMTM2ZGhnNmNxNDZiOWo2b3JqZ2I5azZwMTNnYjlvNjUyajZiOWg4cDEzNmNhNTg4c2o2Y2E2NjQgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-06-11T23:50:25.000Z", "updated": "2022-06-11T23:50:25.207Z", "summary": "Budget Car Return Reminder", "description": "Reservation Confirmation Number: 11236041US5 \n\nThis rental is for: <PERSON><PERSON>****n \n\nPick-Up Location: Kastrup \nPick-Up Date: Wed., July 06, 2022 \nPick-Up Time: 12:30 PM \nPick-Up Location Phone: (45) 3328 6360 \n\nReturn Location: Kast<PERSON> \nReturn Date: Sat., July 09, 2022 \nReturn Time: 12:00 PM \n\nContact Us: **************", "location": "Lufthavnsboulevarden 2 Car Rental Centre Kastrup XX 2770 DK ", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2022-07-09T02:45:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-07-09T03:00:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "FB36034C-3678-46B8-81E3-1FB31EB931F1", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3323858884052000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehijerbl6hqj0p9o64om8pja75q3iohk6pj6erjge4qn8s9gdsq3ccbie1ln8ebfdtom6srm61r6usbc6gp6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaWplcmJsNmhxajBwOW82NG9tOHBqYTc1cTNpb2hrNnBqNmVyamdlNHFuOHM5Z2RzcTNjY2JpZTFsbjhlYmZkdG9tNnNybTYxcjZ1c2JjNmdwNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-08-31T07:04:02.000Z", "updated": "2022-08-31T07:04:02.026Z", "summary": "Stay at Logis Hôtel le Relais de Brocéliande Spa", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOFzVkLhTamKRd4xE6dCUukGV-D3rSj9IY\n", "location": "Logis <PERSON> Relais de Brocéliande Spa, Paimpont", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2022-08-31"}, "end": {"date": "2022-09-02"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9te7mu4u0e811dfj9t9b46fgnpq5tq0o461rpkt9ooqcsv0voql42g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOFzVkLhTamKRd4xE6dCUukGV-D3rSj9IY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3327485589708000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegs6arraedpjerpp6hnjeprme1j32t3cc8oj8p34dpi6ocjh65q36cbacoq62cj3e1q6sr9k6cp78tbjchi6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnczZhcnJhZWRwamVycHA2aG5qZXBybWUxajMydDNjYzhvajhwMzRkcGk2b2NqaDY1cTM2Y2JhY29xNjJjajNlMXE2c3I5azZjcDc4dGJqY2hpNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-09-13T16:42:12.000Z", "updated": "2022-09-21T06:46:34.854Z", "summary": "Flight to Salt Lake City (DL 2847)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPhbJZ0hrWuRa72T34IyzHKLFTB2WTjkf0\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-09-23T07:15:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-09-23T09:14:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t8eojss7o94o7gvpf1tlb14ddndl2q1t31jf4a2cptnm432tusddg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPhbJZ0hrWuRa72T34IyzHKLFTB2WTjkf0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3327485589708000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehk64e1id9gjcc9pe0qm8orbd0pmgqbg75jn4c3469l3cebcc9p78d33cosj0q1ge1qnctj2chmj0pbceph6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoazY0ZTFpZDlnamNjOXBlMHFtOG9yYmQwcG1ncWJnNzVqbjRjMzQ2OWwzY2ViY2M5cDc4ZDMzY29zajBxMWdlMXFuY3RqMmNobWowcGJjZXBoNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-09-13T16:42:12.000Z", "updated": "2022-09-21T06:46:34.854Z", "summary": "Flight to Charlotte (DL 1064)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPhbJZ0hrWuRa72T34IyzHKLFTB2WTjkf0\n", "location": "Salt Lake City SLC", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-09-23T10:40:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-09-23T14:38:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9thb82ja619p5dckh3hip9gr0d2j69lbrt4cf90h0puvvbdm0elvbg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPhbJZ0hrWuRa72T34IyzHKLFTB2WTjkf0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3328200462980000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehi76ohmdgpmsqpoctij6qjbdpimcq3hcpk70r3c6ho6cc9me0o68d1pcopm2e3971o3adr6c4p70crhdlnme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaTc2b2htZGdwbXNxcG9jdGlqNnFqYmRwaW1jcTNoY3BrNzByM2M2aG82Y2M5bWUwbzY4ZDFwY29wbTJlMzk3MW8zYWRyNmM0cDcwY3JoZGxubWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-09-13T16:42:12.000Z", "updated": "2022-09-25T10:03:51.490Z", "summary": "Flight to Atlanta (DL 1719)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPhbJZ0hrWuRa72T34IyzHKLFTB2WTjkf0\n", "location": "Charlotte CLT", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-09-26T03:00:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-09-26T04:11:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tdsb6l3nk8ge3jknefhqfhpll4pf16p0d49f3a8i8p57fa2p3qmog", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPhbJZ0hrWuRa72T34IyzHKLFTB2WTjkf0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3328200462980000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehgn2tbf6krj6e3ac9i6ksr56lomqob1d5r6uprdedqm4t3471o6gohlcdgjaopm64q6ke3gd5o68cjgd0oj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoZ24ydGJmNmtyajZlM2FjOWk2a3NyNTZsb21xb2IxZDVyNnVwcmRlZHFtNHQzNDcxbzZnb2hsY2RnamFvcG02NHE2a2UzZ2Q1bzY4Y2pnZDBvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-09-13T16:42:12.000Z", "updated": "2022-09-25T10:03:51.490Z", "summary": "Flight to Seattle (DL 334)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPhbJZ0hrWuRa72T34IyzHKLFTB2WTjkf0\n", "location": "Atlanta ATL", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-09-26T05:10:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-09-26T10:16:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9taquo5738jbdjse5qmaaivogmsubtd8phb5ca5c614j8pipd2ph10", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPhbJZ0hrWuRa72T34IyzHKLFTB2WTjkf0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3329373847892000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehmm8c1kdlijiq3j69p3cppie1m70qpocpq6orriehh6cp9pd5gjge1oc8rmkorec8s7aohhchi64ebhehq30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobW04YzFrZGxpamlxM2o2OXAzY3BwaWUxbTcwcXBvY3BxNm9ycmllaGg2Y3A5cGQ1Z2pnZTFvYzhybWtvcmVjOHM3YW9oaGNoaTY0ZWJoZWhxMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-09-20T00:42:43.000Z", "updated": "2022-10-02T05:02:03.946Z", "summary": "Flight to Mexico City (AM 495)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOz1dfxQR82tl6xa3-aE7Nz9WBCxZP5f7o\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-10-02T22:00:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-10-03T03:20:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tmd04me9hs2r6g2plpk8ftlortbfe9ia888b7jcnb8ub1ddb9qtt0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOz1dfxQR82tl6xa3-aE7Nz9WBCxZP5f7o", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3327281068494000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehjn6o9lctr78e36dtm3espgedimaqr870om8cpgckpn0e1kd1h74sbleln6eeb86pkj8t3361qmiob9eoome", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoam42bzlsY3RyNzhlMzZkdG0zZXNwZ2VkaW1hcXI4NzBvbThjcGdja3BuMGUxa2QxaDc0c2JsZWxuNmVlYjg2cGtqOHQzMzYxcW1pb2I5ZW9vbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-09-20T02:22:14.000Z", "updated": "2022-09-20T02:22:14.247Z", "summary": "Stay at hotel Live Aqua San Miguel de Allende Urban Resort", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN-smAKcdqBbuzeghPQCr4qN4cNZVUlPNw\n", "location": "https://www.expedia.com/itin.h27065399.Hotel-Information?langid=1033 \nCalzada de la Presa No. 85, Zona Centro, San Miguel de Allende, GTO, 37700 \nMexico", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2022-10-03"}, "end": {"date": "2022-10-06"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tgsa5gvt8fol7s0seekh81d30e3p84hbrquung9h6i4tc0uiaiv1g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN-smAKcdqBbuzeghPQCr4qN4cNZVUlPNw", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3327280726978000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehp6uo9kc5pn2e9kego68or66os3ac3c68q6ktje69imkoplcpgmodrm61kmqqrkedhn2r1hcdimqdjb61pj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocDZ1bzlrYzVwbjJlOWtlZ282OG9yNjZvczNhYzNjNjhxNmt0amU2OWlta29wbGNwZ21vZHJtNjFrbXFxcmtlZGhuMnIxaGNkaW1xZGpiNjFwajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-09-20T02:19:23.000Z", "updated": "2022-09-20T02:19:23.489Z", "summary": "Stay at hotel Brick Hotel", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPHzutWgZ7LSa2KQVMKpyMB-kqxU_YHH4k\n", "location": "https://www.expedia.com/itin.h3374393.Hotel-Information?langid=1033 Orizaba \n95 Roma Norte, Mexico City, 6700 Mexico", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2022-10-05"}, "end": {"date": "2022-10-09"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9troa4asq94t0dcf6850l24jvn2ejc5fal7v0imktscql1cem6k0s0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPHzutWgZ7LSa2KQVMKpyMB-kqxU_YHH4k", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3330351360884000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehr6kdbid9in6orbcphmaqbjdks6ce1pdti30phld9im6cppcos6ke1le9q6ctbm60r6sdraddqj0q1gd5i30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocjZrZGJpZDlpbjZvcmJjcGhtYXFiamRrczZjZTFwZHRpMzBwaGxkOWltNmNwcGNvczZrZTFsZTlxNmN0Ym02MHI2c2RyYWRkcWowcTFnZDVpMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-09-20T00:42:43.000Z", "updated": "2022-10-07T20:48:00.442Z", "summary": "Flight to Seattle (AM 494)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOz1dfxQR82tl6xa3-aE7Nz9WBCxZP5f7o\n", "location": "Mexico City MEX", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2022-10-08T13:45:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-10-08T20:00:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tvj5rjesckfceism8f89od0f5jec39f8j85rtfuv06n7jku0h0id0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOz1dfxQR82tl6xa3-aE7Nz9WBCxZP5f7o", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3332298456414000\"", "id": "an1jbrg01gv88a0mi1e1i3qq70", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=YW4xamJyZzAxZ3Y4OGEwbWkxZTFpM3FxNzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-10-19T03:01:14.000Z", "updated": "2022-10-19T03:13:48.207Z", "summary": "StAnne - COLA for teach", "description": "For last 15 years: for QA\n- Cost of living increase\n- rental increase\n- house purchase /value increase\n - Teachers salary at st Anne. \n- Teachers salary at SPS\n-Salary/income/como\n- tuition\n\nFocus on ages with kids. \nTime on QA\n\nTuition over year", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2022-10-19T09:05:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2022-10-19T10:05:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "<EMAIL>", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3338274216442000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegs64d37e5i76ojid4s66qbbcgsmqchgcllm6srmddh6goj1d9l6eo9hdlpj4dhkelgmgt3168s62cr268rj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnczY0ZDM3ZTVpNzZvamlkNHM2NnFiYmNnc21xY2hnY2xsbTZzcm1kZGg2Z29qMWQ5bDZlbzloZGxwajRkaGtlbGdtZ3QzMTY4czYyY3IyNjhyajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-10-16T21:57:45.000Z", "updated": "2022-11-22T17:11:48.221Z", "summary": "Stay at Homeaway 598234", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMWojBHFkGqGnoXAm6l2Kptdl6Ax9wzwIY\n", "location": "Upper Village, Whistler, British Columbia, Canada", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2022-11-25"}, "end": {"date": "2022-11-29"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t8b4gqdsbri8cikd9m20ekcsvkbhbajjga1ms264uahta28a3b270", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMWojBHFkGqGnoXAm6l2Kptdl6Ax9wzwIY", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3351431607754000\"", "id": "4ves0d64sjcrm2f8mkrrju8rms", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=NHZlczBkNjRzamNybTJmOG1rcnJqdThybXMgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-06T20:36:43.000Z", "updated": "2023-02-06T20:36:43.877Z", "summary": "Drive F&F to airport.", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-02-11T06:00:00-08:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-02-11T07:20:00-08:00", "timeZone": "America/Los_Angeles"}, "visibility": "private", "iCalUID": "<EMAIL>", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3341078225030000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegp72qbcc4s6iorkcpl38t1id8s68rbj6ks38rra6hlmip36cgrjiqr6dln6ut3mcth6oc1k69kncd9g71qme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncDcycWJjYzRzNmlvcmtjcGwzOHQxaWQ4czY4cmJqNmtzMzhycmE2aGxtaXAzNmNncmppcXI2ZGxuNnV0M21jdGg2b2MxazY5a25jZDlnNzFxbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-12-08T22:38:32.000Z", "updated": "2022-12-08T22:38:32.515Z", "summary": "Flight to Kahului (DL 481)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMr45vRs5AZMoG5v9ldAGZxxKcweW9yqY4\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-02-18T15:47:00-08:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-02-18T22:06:00-08:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t2qila8ictfj4t2j8dms584oj4kidfd79kfmnotvgbl042iv508ug", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMr45vRs5AZMoG5v9ldAGZxxKcweW9yqY4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3354360280574000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehpn0ohne5nmgqhn74pn6t9gd9mmooj6epi7cdhhclommdbc6tkj2sbie9n32rjiddo34e9pd9mmss3mcks6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVocG4wb2huZTVubWdxaG43NHBuNnQ5Z2Q5bW1vb2o2ZXBpN2NkaGhjbG9tbWRiYzZ0a2oyc2JpZTluMzJyamlkZG8zNGU5cGQ5bW1zczNtY2tzNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-12-08T22:38:32.000Z", "updated": "2023-02-23T19:22:20.287Z", "summary": "Flight to Los Angeles (UA 706)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMr45vRs5AZMoG5v9ldAGZxxKcweW9yqY4\n", "location": "Kahului OGG", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-02-23T16:35:00-08:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-02-23T21:51:00-08:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tspb7qohj793su0jmlbfvdv61eqk5l7i1qrrn1nrkp299jmnpve8g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMr45vRs5AZMoG5v9ldAGZxxKcweW9yqY4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3352792365738000\"", "id": "_85232h1j84q3ab9g6os44b9k6spj6ba18oq36b9p6kr38gq56cq46gpk74", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=Xzg1MjMyaDFqODRxM2FiOWc2b3M0NGI5azZzcGo2YmExOG9xMzZiOXA2a3IzOGdxNTZjcTQ2Z3BrNzQgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-14T17:36:22.000Z", "updated": "2023-02-14T17:36:22.869Z", "summary": "Flight DL1569", "description": "Confirmation #: GVZ8LX\n", "location": "SEA ▸ LAX", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-02-23T17:32:00-08:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-02-23T20:10:00-08:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "AD1D3A45-068B-4733-AF43-9564CE34CC49", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3354266920862000\"", "id": "jdmhsl05lf4ublr8mjuspvkasc", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=amRtaHNsMDVsZjR1YmxyOG1qdXNwdmthc2MgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-23T06:23:56.000Z", "updated": "2023-02-23T06:24:20.431Z", "summary": "Enterprise Rent-A-Car Reservation #1645573457", "description": "<html><head></head><body><h3>Enterprise Rent-A-Car Reservation #1645573457</h3><h4>Pick-up:</h4>February 23, 2023 9:00 PM\nLos Angeles International Airport\n9020 Aviation Blvd\nInglewood, CA 90301\n+1 310-649-5400\n<h4>Return:</h4>February 26, 2023 10:30 AM\nLos Angeles International Airport\n9020 Aviation Blvd\nInglewood, CA 90301\n+1 310-649-5400\n</body></html>", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-02-23T21:00:00-08:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-02-23T22:30:00-08:00", "timeZone": "America/Los_Angeles"}, "visibility": "private", "iCalUID": "<EMAIL>", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3341078225030000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegqn6rr4ckrm6rrfeco30crhelq32s3ac5mmco9keosmqtj8c9m62eb9d1p6gcpictnj0s9nd9pjicjgcpm6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncW42cnI0Y2tybTZycmZlY28zMGNyaGVscTMyczNhYzVtbWNvOWtlb3NtcXRqOGM5bTYyZWI5ZDFwNmdjcGljdG5qMHM5bmQ5cGppY2pnY3BtNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2022-12-08T22:38:32.000Z", "updated": "2022-12-08T22:38:32.515Z", "summary": "Flight to Seattle (DL 1714)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMr45vRs5AZMoG5v9ldAGZxxKcweW9yqY4\n", "location": "Los Angeles LAX", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-02-26T09:57:00-08:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-02-26T12:55:00-08:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t5sode7coos003qut1pjamfa4v9mvhbla9ihrh32go0q7js92pflg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMr45vRs5AZMoG5v9ldAGZxxKcweW9yqY4", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3352792386778000\"", "id": "_70s38d1l89136ba260ojab9k70r38ba174q46ba56so3aghp6d1k2cq170", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzcwczM4ZDFsODkxMzZiYTI2MG9qYWI5azcwcjM4YmExNzRxNDZiYTU2c28zYWdocDZkMWsyY3ExNzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-14T17:36:33.000Z", "updated": "2023-02-14T17:36:33.389Z", "summary": "Flight DL1714", "description": "Confirmation #: GVZ8LX\n", "location": "LAX ▸ SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-02-26T09:57:00-08:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-02-26T12:55:00-08:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "88445BB3-B015-4864-A94C-E705B93CA3A8", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3359173774146000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpego3crhkednmgcjmegon2chne5pmme3j6lm70dbf6grmgt1kd1lmeebcednmktb86dh6gr38dtkmmsrfegoj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnbzNjcmhrZWRubWdjam1lZ29uMmNobmU1cG1tZTNqNmxtNzBkYmY2Z3JtZ3Qxa2QxbG1lZWJjZWRubWt0Yjg2ZGg2Z3IzOGR0a21tc3JmZWdvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-03-23T15:54:47.000Z", "updated": "2023-03-23T15:54:47.073Z", "summary": "Stay at Venetian tower", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNQszRKGjGernWsPU1FU44pDjtL8LFqsVA\n", "location": "Paradise, Nevada, United States", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2023-03-20"}, "end": {"date": "2023-03-24"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t06n4soh2vt1q27qsk8s5lp5o47ht4hkg9lsojuh3bhlhoiksot10", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNQszRKGjGernWsPU1FU44pDjtL8LFqsVA", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3362048997374000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehm68s3mc9j30c9ldkp72drf6pi62rjjdho64pph64q78c39d9gj8e3jcgsjep38csq72rb5clj6mr1ie4sme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobTY4czNtYzlqMzBjOWxka3A3MmRyZjZwaTYycmpqZGhvNjRwcGg2NHE3OGMzOWQ5Z2o4ZTNqY2dzamVwMzhjc3E3MnJiNWNsajZtcjFpZTRzbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-03T18:41:19.000Z", "updated": "2023-04-09T07:14:58.687Z", "summary": "Flight to Houston (UA 2379)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOqG-S2DyfJt-_aiArp3HXxGDW-EhduB7M\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-04-09T23:59:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-04-10T04:16:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tldpvbf015m2q7o6danslpbg114t0ija48sd97dhg4qmeefkl2q9g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOqG-S2DyfJt-_aiArp3HXxGDW-EhduB7M", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3359815637330000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegrn0c3m71o3esr26ph3cshh64sjipj7e9p34sj3dgrj8t1oeooj2phnchj36shm6oq3at1pccp6atjl65m6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncm4wYzNtNzFvM2VzcjI2cGgzY3NoaDY0c2ppcGo3ZTlwMzRzajNkZ3JqOHQxb2Vvb2oycGhuY2hqMzZzaG02b3EzYXQxcGNjcDZhdGpsNjVtNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-02T22:07:56.000Z", "updated": "2023-03-27T09:03:38.665Z", "summary": "Stay at The Westin Reserva Conchal, an All-Inclusive Golf Resort & Spa", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOVhHt46zJFjwSSerFzHF-t-0kXQejGxi0\n", "location": "Playa Conchal <PERSON>acaste Costa Rica", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2023-04-10"}, "end": {"date": "2023-04-18"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t7p0v8p7sb6b6r1199fgrr2rcl74t8v11f7df3r6645t9c2evu1lg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOVhHt46zJFjwSSerFzHF-t-0kXQejGxi0", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3362048997374000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehhm4drj6csnadjldoo30p9k6lm6edb9cln70cpl6gsj4rr3d5k78chmdpjjce32e5r6itj9e4rjisbbd4s30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoaG00ZHJqNmNzbmFkamxkb28zMHA5azZsbTZlZGI5Y2xuNzBjcGw2Z3NqNHJyM2Q1azc4Y2htZHBqamNlMzJlNXI2aXRqOWU0cmppc2JiZDRzMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-03T18:41:19.000Z", "updated": "2023-04-09T07:14:58.687Z", "summary": "Flight to Liberia (UA 1512)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOqG-S2DyfJt-_aiArp3HXxGDW-EhduB7M\n", "location": "Houston IAH", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-04-10T07:41:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-04-10T11:10:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tcb7s39u6un00e45lg5ienp35492ociht26ng68bqviviq79qki80", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOqG-S2DyfJt-_aiArp3HXxGDW-EhduB7M", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3350900970288000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpego3aor9e1oj0obg6ssj6qb66lq36d3a75k34rple5oj6d3d71j6asr3e5o6icbicpl6mc34e1k6gsbdcco30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnbzNhb3I5ZTFvajBvYmc2c3NqNnFiNjZscTM2ZDNhNzVrMzRycGxlNW9qNmQzZDcxajZhc3IzZTVvNmljYmljcGw2bWMzNGUxazZnc2JkY2NvMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-03T18:41:19.000Z", "updated": "2023-02-03T18:54:45.144Z", "summary": "Flight to Houston (UA 1516)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOqG-S2DyfJt-_aiArp3HXxGDW-EhduB7M\n", "location": "Liberia LIR", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-04-17T12:14:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-04-17T15:51:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t05cipq0ap793if5t34j9h2o5qq34m8fescqpi1rfjk0dphhqmc00", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOqG-S2DyfJt-_aiArp3HXxGDW-EhduB7M", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3350900970288000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehk64r1ke4pmed3l70q34qj5cph76sjcdos64c31eks76djb6tj6cs9idgq3adjddhm76djl6homcsb168oj0", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoazY0cjFrZTRwbWVkM2w3MHEzNHFqNWNwaDc2c2pjZG9zNjRjMzFla3M3NmRqYjZ0ajZjczlpZGdxM2FkamRkaG03NmRqbDZob21jc2IxNjhvajAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-03T18:41:19.000Z", "updated": "2023-02-03T18:54:45.144Z", "summary": "Flight to Seattle (UA 1905)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOqG-S2DyfJt-_aiArp3HXxGDW-EhduB7M\n", "location": "Houston IAH", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-04-17T18:08:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-04-17T22:53:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9thbl4q3g4u842jefbsrln8b0au8s6k7ffq2l456mlls6u4qfqa210", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOqG-S2DyfJt-_aiArp3HXxGDW-EhduB7M", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3367326890310000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehgn0opmd1mmaoriedgjaojl6tij2qbad9j3et3cd5h6aobddtm6st3ec9h36r35ekrmmchjeoqm4c366cr30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoZ24wb3BtZDFtbWFvcmllZGdqYW9qbDZ0aWoycWJhZDlqM2V0M2NkNWg2YW9iZGR0bTZzdDNlYzloMzZyMzVla3JtbWNoamVvcW00YzM2NmNyMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-01T01:33:03.000Z", "updated": "2023-05-09T20:17:25.155Z", "summary": "Flight to Palm Springs (AS 1068)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNHNxGAlce8RCppu50uSQzNg1R7zuy12OQ\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-05-10T12:55:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-05-10T15:35:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tapc6hmecrsa5bu7e1ijjf7tlibeamolntnbb3leu7k23v5b0f360", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNHNxGAlce8RCppu50uSQzNg1R7zuy12OQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3368003632634000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehjjgs1k6pljcshjehmmesrgdlmmidj4ehm6orph6kojcp3jckr62d346osn8cphd9ijes1ke0rnarph75q6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoampnczFrNnBsamNzaGplaG1tZXNyZ2RsbW1pZGo0ZWhtNm9ycGg2a29qY3AzamNrcjYyZDM0Nm9zbjhjcGhkOWlqZXMxa2Uwcm5hcnBoNzVxNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-02-01T01:33:03.000Z", "updated": "2023-05-13T18:16:56.317Z", "summary": "Flight to Seattle (AS 1257)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNHNxGAlce8RCppu50uSQzNg1R7zuy12OQ\n", "location": "Palm Springs PSP", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-05-14T10:30:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-05-14T13:23:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tg8p46k6r3tmgspmmi6dtllo1516dse6a4d69t31je7p4p7uo19tg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DNHNxGAlce8RCppu50uSQzNg1R7zuy12OQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3370160620326000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehn78p1jdtnmcebf6tnmesjh6osj4p3m60o6kp3l69m3gdr464pjat3ldop66qrfc9o3acpk70o7ccj1d1jme", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVobjc4cDFqZHRubWNlYmY2dG5tZXNqaDZvc2o0cDNtNjBvNmtwM2w2OW0zZ2RyNDY0cGphdDNsZG9wNjZxcmZjOW8zYWNwazcwbzdjY2oxZDFqbWUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-05-26T05:51:49.000Z", "updated": "2023-05-26T05:51:50.163Z", "summary": "Flight to New York (AS 26)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO8Od6LfKqffpY2pc0akyVPjMmX7gJskdw\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-06-17T23:35:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-06-18T04:52:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tntd3oof9o7ogrq692dv00jdu2l87d135tun2ckobp53480v2ahgg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DO8Od6LfKqffpY2pc0akyVPjMmX7gJskdw", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3370160644896000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegrn2ob1cho6oq1ndhl7at1lcpnmks1icdo3ep376dr70t9j68smqqr5dpmn8p9ie1qn6q9oc9mjarj5ctn6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncm4yb2IxY2hvNm9xMW5kaGw3YXQxbGNwbm1rczFpY2RvM2VwMzc2ZHI3MHQ5ajY4c21xcXI1ZHBtbjhwOWllMXFuNnE5b2M5bWphcmo1Y3RuNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-05-26T05:52:02.000Z", "updated": "2023-05-26T05:52:02.448Z", "summary": "Flight to New York (AS 26)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM9s1mWXzVklXXq2hYQyzZf_8UtmjJXCrQ\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-06-27T23:35:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-06-28T04:52:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t7qaadplh7ljut5fojp2cp7dg3vpu329mkenmte2pusi8bm5negng", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM9s1mWXzVklXXq2hYQyzZf_8UtmjJXCrQ", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3370161316768000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehjmcsrjclkjcpbfe0s6qdppd0p62t1h68omstjddcpjarj7cpjjedrg6tjm4qj7ehnj2qre6osmsor8e1i6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoam1jc3JqY2xramNwYmZlMHM2cWRwcGQwcDYydDFoNjhvbXN0amRkY3BqYXJqN2NwamplZHJnNnRqbTRxajdlaG5qMnFyZTZvc21zb3I4ZTFpNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-05-26T05:57:38.000Z", "updated": "2023-05-26T05:57:38.384Z", "summary": "Flight to Seattle (AS 17)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMhBFux42kzSZzAtWQPe0304VtKQrMecdc\n", "location": "New York JFK", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-07-02T13:55:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-07-02T20:20:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9tgfssei6eop8m79h2at121nvmk35ngfg77p7gbjgto1kn69nchpdg", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DMhBFux42kzSZzAtWQPe0304VtKQrMecdc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3370941355276000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegpmupj5dhpmmpbac8rmetje74r74d1md5l62tj9d5lm6spidhp6osjbeoo36p9i6cq30r35dsrj2shlc4p6e", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncG11cGo1ZGhwbW1wYmFjOHJtZXRqZTc0cjc0ZDFtZDVsNjJ0ajlkNWxtNnNwaWRocDZvc2piZW9vMzZwOWk2Y3EzMHIzNWRzcmoyc2hsYzRwNmUgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-05-30T18:17:57.000Z", "updated": "2023-05-30T18:17:57.638Z", "summary": "Flight to Boston (B6 498)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM5hGQCVC-Iy6MTwWCCXPg553L_Q2tNj0k\n", "location": "Seattle SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-07-02T21:10:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-07-03T02:23:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t3ofelskejb7gvn96r46ijaviikcs2lrlrkv03e2340leo71r5a2g", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM5hGQCVC-Iy6MTwWCCXPg553L_Q2tNj0k", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3375776634474000\"", "id": "_6ks3eci668q36b9g8l23ib9k74q44b9o60r3aba46sp34d9h8533gh256o", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZrczNlY2k2NjhxMzZiOWc4bDIzaWI5azc0cTQ0YjlvNjByM2FiYTQ2c3AzNGQ5aDg1MzNnaDI1Nm8gbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-06-27T17:51:57.000Z", "updated": "2023-06-27T17:51:57.237Z", "summary": "Flight DL1517", "description": "Confirmation #: GBY5PX\n", "location": "SEA ▸ SFO", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-07-03T11:24:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-07-03T13:38:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "5872F243-0ED9-494B-8065-D72251AF8DE6", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3377711363456000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpehkjcdbhd1p38dhkdtn3iohjedk72db471mmiq9h6opn0rjfdli3epjm75imeqhj68s64rphdtqm8srdelq30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVoa2pjZGJoZDFwMzhkaGtkdG4zaW9oamVkazcyZGI0NzFtbWlxOWg2b3BuMHJqZmRsaTNlcGptNzVpbWVxaGo2OHM2NHJwaGR0cW04c3JkZWxxMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-07-08T08:38:09.000Z", "updated": "2023-07-08T22:34:41.728Z", "summary": "Stay at Best Western Woodburn", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN3I5syW1wjGhTmPqk-hxixwrPI2w7S4jc\n", "location": "Best Western Woodburn, Woodburn", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"date": "2023-07-07"}, "end": {"date": "2023-07-09"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9ti65qhr464on9b3shq5d8mii163pnomd7fv9egj328bo1oudsmut0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false, "overrides": [{"method": "popup", "minutes": 420}]}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DN3I5syW1wjGhTmPqk-hxixwrPI2w7S4jc", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3375776651636000\"", "id": "_612k4dph890k2ba56p1k4b9k68q36b9p68p3ab9j6so4ah9j64s32g9o74", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzYxMms0ZHBoODkwazJiYTU2cDFrNGI5azY4cTM2YjlwNjhwM2FiOWo2c280YWg5ajY0czMyZzlvNzQgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-06-27T17:52:05.000Z", "updated": "2023-06-27T17:52:05.818Z", "summary": "Flight DL3842", "description": "Confirmation #: GB9D3X\n", "location": "MFR ▸ SEA", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-07-08T11:57:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-07-08T13:25:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "0EB71BAA-E6CB-4243-9225-370EE3181A89", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3370941355276000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegrj4cr2dth6kpbb65j7acj46th6adb274q6etb561hjcpj9dtr68sjlcosmidbdctpj8rjg6hl3cr9l65o30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVncmo0Y3IyZHRoNmtwYmI2NWo3YWNqNDZ0aDZhZGIyNzRxNmV0YjU2MWhqY3BqOWR0cjY4c2psY29zbWlkYmRjdHBqOHJqZzZobDNjcjlsNjVvMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-05-30T18:17:57.000Z", "updated": "2023-05-30T18:17:57.638Z", "summary": "Flight to Seattle (B6 197)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM5hGQCVC-Iy6MTwWCCXPg553L_Q2tNj0k\n", "location": "Boston BOS", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-07-12T13:20:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-07-12T19:46:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t723bobjek1fu2d7be5b94gue0c6fiovdruf9i5mgs4np4j6m51p0", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM5hGQCVC-Iy6MTwWCCXPg553L_Q2tNj0k", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3355744670100000\"", "id": "_68o34cpg6co36l1g6gp3ccpkb8miqc9j64r3cdho64p3eg3netrisojid5q6isr8c5kn4tr1f5pisorfdk", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzY4bzM0Y3BnNmNvMzZsMWc2Z3AzY2Nwa2I4bWlxYzlqNjRyM2NkaG82NHAzZWczbmV0cmlzb2ppZDVxNmlzcjhjNWtuNHRyMWY1cGlzb3JmZGsgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-03-03T04:26:33.000Z", "updated": "2023-03-03T19:38:55.050Z", "summary": "SEA - LHR Flight BA0052 departs Mon, 24 Jul 2023 13:35 (Seattle time)", "description": "BA0052 Departs Seattle-Tacoma International (WA) on Mon, 24 Jul 2023 13:35 (Seattle time). Arrives Heathrow (London)  terminal 5 on Tue, 25 Jul 2023 06:50 (London time). Your booking reference is N6VR9Z.", "location": "Seattle-Tacoma International (WA)", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-07-24T13:35:00-07:00", "timeZone": "America/Phoenix"}, "end": {"dateTime": "2023-07-24T22:50:00-07:00", "timeZone": "Africa/Algiers"}, "iCalUID": "<EMAIL>", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3379235254300000\"", "id": "gr1l300rrpinjgdk6lil2nk6d4", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=Z3IxbDMwMHJycGluamdkazZsaWwybms2ZDQgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-03-02T16:35:20.000Z", "updated": "2023-07-17T18:13:47.150Z", "summary": "Sixt car rental 9703873159", "description": "Your booking is confirmed!\n-\nSUV\nCitroën C3 Aircross or similar\nReservation ID 9700955038\n-\tBari Airport\n \tSat, Jul 29, 2023 | 5:00 PM\n-\tBari Airport\n \tSat, Aug 05, 2023 | 9:00 AM", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-07-29T09:30:00-07:00", "timeZone": "Europe/Rome"}, "end": {"dateTime": "2023-07-29T10:30:00-07:00", "timeZone": "Europe/Rome"}, "iCalUID": "<EMAIL>", "sequence": 1, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3382428701814000\"", "id": "_6hj36c9j75hj4b9h6cqjcb9k69gjibb2c4ojib9h6pgjcp9oc4q3io9gc4", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZoajM2YzlqNzVoajRiOWg2Y3FqY2I5azY5Z2ppYmIyYzRvamliOWg2cGdqY3A5b2M0cTNpbzlnYzQgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-05-19T02:53:24.000Z", "updated": "2023-08-05T05:45:50.907Z", "summary": "Journey Bari Centrale-Venezia S. Lucia, <PERSON> Frecciarossa 8816,  Coach 1, <PERSON><PERSON> 5B, CP 134001, PNR W3XPAN Coach 1, Seats 5A, CP 134001, PNR W3XPAN Coach 1, Seats 6B, CP 134001, PNR W3XPAN Coach 1, Seats 6A, CP 134001, PNR W3XPAN Coach 1, Seats 6D, CP 134001, PNR W3XPAN,  ", "description": "Bari Centrale-Venezia S. Lucia;Train: Frecciarossa 8816, departing from Bari Centrale Time: 08:30; arriving at Venezia S. Lucia Time: 16:08 Coach 1, Seats 5B, CP 134001, PNR W3XPAN Coach 1, Seats 5A, CP 134001, PNR W3XPAN Coach 1, Seats 6B, CP 134001, PNR W3XPAN Coach 1, Seats 6A, CP 134001, PNR W3XPAN Coach 1, Seats 6D, CP 134001, PNR W3XPAN", "location": "Bari Centrale", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-08-04T23:30:00-07:00", "timeZone": "UTC"}, "end": {"dateTime": "2023-08-05T07:08:00-07:00", "timeZone": "UTC"}, "iCalUID": "4f3139c2-1356-42a9-ba19-16a6e8a49a0a", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "organizer": true, "self": true, "responseStatus": "accepted"}], "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3382431666040000\"", "id": "_74o3eo9g64r36bb164pjab9k70s68b9o6ko30b9mcgpmcdr56thjgp1m6c", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=Xzc0bzNlbzlnNjRyMzZiYjE2NHBqYWI5azcwczY4YjlvNmtvMzBiOW1jZ3BtY2RyNTZ0aGpncDFtNmMgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "1900-01-01T12:00:00.000Z", "updated": "2023-08-05T06:10:33.020Z", "summary": "Trip Bari Centrale-Venezia S. Lucia, <PERSON> Frecciarossa 8816, Coach 1, <PERSON><PERSON><PERSON>, 5B, 5A, 6B, 6A, 6D, CP 134001, 134001, 134001, 134001, 134001, PNR W3XPAN,  ", "description": "Bari Centrale-Venezia S. Lucia;Train: Frecciarossa 8816, departing from Bari Centrale Hours: 08:30; arriving at Venezia S. Lucia Hours: 16:08 Coach 1, Position 5B, Position 5A, Position 6B, Position 6A, Position 6D, CP 134001, CP 134001, CP 134001, CP 134001, CP 134001; pnr code W3XPAN", "location": "Bari Centrale", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>", "displayName": "Unknown Organizer"}, "start": {"dateTime": "2023-08-05T08:30:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-08-05T16:08:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "907a0163-a135-488d-8500-6d3f7e7c8d63", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "needsAction"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": true}, "attachments": [{"fileUrl": "https://mail.google.com/?view=att&th=18831ecbbc3473b6&attid=0.1&disp=attd&zw", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>-1902605367.pdf", "iconLink": ""}], "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3383296044032000\"", "id": "_6tlnaqrle5p6cpb4dhmj4phpegsn8db7econ2p9id0pj0dbdd1mj8r3hcdjmce9ldss3gqpm6lln8sr9ccqjet3569omgp386tjj8s9n6cp30", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzZ0bG5hcXJsZTVwNmNwYjRkaG1qNHBocGVnc244ZGI3ZWNvbjJwOWlkMHBqMGRiZGQxbWo4cjNoY2RqbWNlOWxkc3MzZ3FwbTZsbG44c3I5Y2NxamV0MzU2OW9tZ3AzODZ0amo4czluNmNwMzAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-08-10T05:56:23.000Z", "updated": "2023-08-10T06:31:00.045Z", "summary": "Flight to Paris (DL 8567)", "description": "To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOz8MCH4r_B79h2svieBICTaBc26w32_5Y\n", "location": "Venice VCE", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "<EMAIL>"}, "start": {"dateTime": "2023-08-10T01:35:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-08-10T03:10:00-07:00", "timeZone": "America/Los_Angeles"}, "transparency": "transparent", "visibility": "private", "iCalUID": "7kukuqrfedlm2f9t9t5gs1qe2h305mhm4lqcgf95o88k65ktsic57te2qhdh7g4q7320", "sequence": 0, "attendees": [{"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true, "responseStatus": "accepted"}], "guestsCanInviteOthers": false, "privateCopy": true, "reminders": {"useDefault": false}, "source": {"url": "https://mail.google.com/mail?extsrc=cal&plid=ACUX6DOz8MCH4r_B79h2svieBICTaBc26w32_5Y", "title": ""}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3355744670100000\"", "id": "_68o34cpg6co36l1g6gp3ccpmb8miqc9j64r3cdho64p3ag3netrisojid5q6isr8c5kn4tr1f5pisorfdk", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=XzY4bzM0Y3BnNmNvMzZsMWc2Z3AzY2NwbWI4bWlxYzlqNjRyM2NkaG82NHAzYWczbmV0cmlzb2ppZDVxNmlzcjhjNWtuNHRyMWY1cGlzb3JmZGsgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-03-03T04:26:33.000Z", "updated": "2023-03-03T19:38:55.050Z", "summary": "LHR - SEA Flight BA0049 departs Thu, 10 Aug 2023 16:10 (London time)", "description": "BA0049 De<PERSON><PERSON> Heathrow (London)  terminal 5 on Thu, 10 Aug 2023 16:10 (London time). Arrives Seattle-Tacoma International (WA) on Thu, 10 Aug 2023 17:55 (Seattle time). Your booking reference is N6VR9Z.", "location": "<PERSON>row (London)", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-08-10T08:10:00-07:00", "timeZone": "Africa/Algiers"}, "end": {"dateTime": "2023-08-10T17:55:00-07:00", "timeZone": "America/Phoenix"}, "iCalUID": "<EMAIL>", "sequence": 0, "reminders": {"useDefault": true}, "eventType": "default"}, {"kind": "calendar#event", "etag": "\"3397797034442000\"", "id": "jki1a9fgrcam77e65gkom9h050", "status": "confirmed", "htmlLink": "https://www.google.com/calendar/event?eid=amtpMWE5ZmdyY2FtNzdlNjVna29tOWgwNTAgbWljaGFlbEBndWxtYW5uLmNvbQ", "created": "2023-11-02T04:15:04.000Z", "updated": "2023-11-02T04:15:17.221Z", "summary": "4:45 leave for airport", "creator": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "organizer": {"email": "micha<PERSON>@gulmann.com", "displayName": "<PERSON>", "self": true}, "start": {"dateTime": "2023-11-02T04:40:00-07:00", "timeZone": "America/Los_Angeles"}, "end": {"dateTime": "2023-11-02T05:40:00-07:00", "timeZone": "America/Los_Angeles"}, "iCalUID": "<EMAIL>", "sequence": 1, "reminders": {"useDefault": true}, "eventType": "default"}]}