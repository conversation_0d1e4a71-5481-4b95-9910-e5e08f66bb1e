from langchain_core.messages import AIMessage

from baml_client import b
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.schemas.authenticate.user import User
from server.utils.logger import logger
from server.utils.settings import settings
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.timings import Timings


class OtherHelper:
    def __init__(self, user: User, timezone: str | None = None):
        self.user = user
        self.timezone = timezone

    async def other_topics_runnable_function(self, state):
        messages = state["messages"]
        # Convert our message buffer
        message_buffer_strs = get_message_buffer_as_strings(messages)
        travel_context_str = state["travel_context"].model_dump_json()

        t = Timings("BAML: ConverseOtherTopics")
        response = await b.ConverseOtherTopics(
            travel_context=travel_context_str,
            trip_memories=state.get("trip_memories", []),
            messages=message_buffer_strs,
            current_date=get_current_date_string(self.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            user_name=self.user.name,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("yellow")
        logger.log_baml()

        new_message = AIMessage(content="")
        new_message.additional_kwargs = {
            "function_call": {
                "arguments": response.model_dump_json(),
                "name": "OtherConversationStateSchema",
            }
        }

        return {
            "messages": [new_message],
            "current_topic": "Other",
            "model": response,
        }
