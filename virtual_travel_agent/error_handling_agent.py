from collections import defaultdict
from typing import Literal

from langchain_core.messages import AIMessage, BaseMessage
from langgraph.graph import E<PERSON>

from baml_client import b
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.common_models import <PERSON><PERSON><PERSON>r, AgentErrorCode, AgentState
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import Timings


class ErrorHandlingAgent:
    """
    This agent is responsible for handling errors that occur in Agents.
    """

    def __init__(
        self,
        user_facing_agent,
        messages: list[BaseMessage],
        memory: PostgresChatMessageHistory,
        timezone: str | None,
        max_retry: int = 0,
    ):
        self.user_facing_agent = user_facing_agent
        self.retry_map = defaultdict(int)
        self.max_retry = max_retry
        self.memory = memory
        self.messages = messages
        self.timezone = timezone

    async def __call__(self, state: AgentState):
        errors = state["errors"]
        state["next"] = None

        while errors:
            error = errors.pop(0)

            if isinstance(error, AgentError):
                if self.retry_map[error.from_ + error.code.name] > self.max_retry:
                    state["next"] = END
                    self.retry_map[error.from_ + error.code.name] = 0
                    return state

                # search again for the user
                if error.code in {AgentErrorCode.ITINERARY_FARE_EXPIRED}:
                    # reset the previous state to re-do the search.

                    msg = "It looks like there's been a change with the flight you picked. Let me search again for you!"
                    ai_message = await self._send_message_to_user(msg, error)
                    state["messages"] = [ai_message]
                    error.handled = True
                # {'errorMessages': [{'errorCode': 'API_VALIDATION_FAILED', 'message': 'Validation failed for field: itineraryId. Value received:  null. Validation error: must not be null'}]}
                # {"errorMessages":[{"errorCode":"GATEWAY_TIMEOUT","message":"Request timed out. Servers did not respond in time.","errorParameters":[],"errorDetail":"Supplier timed out. Do not retry."}],"timestamp":1739298346670,"status":999,"error":"None","exception":"io.grpc.StatusRuntimeException","message":"Request timed out. Servers did not respond in time.","errorCode":"GATEWAY_TIMEOUT","errorComponent":"supplier","appName":"supplier","errorSource":"SABRE","dedupId":""}
                # "errorMessages":[{"errorCode":"INVALID_SEAT_OPTION","message":"Invalid seat option selected ({{P1}})","errorParameters":[],"errorDetail":"Duplicate seats in request"}]
                # {'errorMessages': [{'errorCode': 'java.lang.NullPointerException', 'message': 'No message available', 'errorParameters': [], 'errorDetail': ''}], 'timestamp': 1739570529994, 'status': 'INTERNAL_SERVER_ERROR', 'error': 'None', 'exception': 'java.lang.NullPointerException', 'message': 'No message available', 'errorCode': 'java.lang.NullPointerException', 'appName': 'master'}
            if isinstance(error, AgentError) and error.handled:
                self.retry_map[error.from_ + error.code.name] += 1
            else:
                error_message = "An error occurred. Please try again later."
                if error.args:
                    error_message = str(error.args[0])
                messages = state["messages"]
                message_buffer_strs = get_message_buffer_as_strings(messages[-5:])
                travel_context_str = state["travel_context"].model_dump_json()
                t = Timings("BAML: GenerateErrorMessage")
                response = await b.GenerateErrorMessage(
                    travel_context=travel_context_str,
                    messages=message_buffer_strs,
                    error_message=error_message,
                    current_date=get_current_date_string(self.timezone),
                    baml_options={"collector": logger.collector},
                )
                t.print_timing("yellow")
                logger.log_baml()

                # ai_message = await self._send_message_to_user(response.agent_response)
                ai_message = AIMessage(
                    content=response.agent_response,
                    additional_kwargs={
                        "agent_classification": "Error",
                        "rawToolOutput": {"error": str(error)},
                        "function_call": {
                            "name": "ErrorHandlingAgent",
                            "arguments": {"error_response": response.agent_response},
                        },
                    },
                )
                state["messages"] = [ai_message]

            logger.error(str(error))
        return state

    def handle_next_step(self, state: AgentState) -> Literal["exchangeflightsearch", "flightsearch", "__end__"]:
        match state["next"]:
            case "flightsearch" | "exchangeflightsearch":
                return state["next"]
            case _:
                return "__end__"

    async def _send_message_to_user(self, msg: str, error: AgentError) -> BaseMessage:
        ai_msg = AIMessage(content=msg)
        self.memory.add_pending_message(ai_msg)
        self.messages.append(ai_msg)

        message = {
            "type": "prompt",
            "text": ai_msg.content,
            "isBotMessage": True,
            "rawToolOutput": str(error),
            "expectResponse": False,
            "textColor": settings.AGENT_MESSAGE_COLOR_MAP[AgentTypes.ERROR],
        }
        await self.user_facing_agent.websocket_send_message(message=message)
        return ai_msg
