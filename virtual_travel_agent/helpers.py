import json
import time
from datetime import datetime
from functools import wraps
from typing import Any, Dict

from dateutil.tz import gettz
from langchain_core.messages import AIMessage, BaseMessage, FunctionMessage

from baml_client.types import ResponseAllPreferences, TravelContext
from server.utils.logger import logger

console_masks = {
    "flight": "\033[92m {}\033[00m",
    "hotel": "\033[94m {}\033[00m",
    "booking.com": "\033[91m {}\033[00m",
    "post-booking": "\033[95m {}\033[00m",
    "supervisor": "\033[96m {}\033[00m",
    "other": "\033[93m {}\033[00m",
}


def get_current_date_string(timezone: str | None = None):
    # Get the current date
    if timezone is None:
        now = datetime.now()
    else:
        now = datetime.now(tz=gettz(timezone))

    # Format the date string with ordinal suffix
    return now.strftime(f"%B {now.day}{get_ordinal_suffix(now.day)} %Y")


def get_current_datetime_string(timezone: str | None = None):
    # Get the current datetime
    if timezone is None:
        now = datetime.now()
    else:
        now = datetime.now(tz=gettz(timezone))

    # Format the datetime string with ordinal suffix for day
    date_part = now.strftime(f"%B {now.day}{get_ordinal_suffix(now.day)} %Y")
    time_part = now.strftime("%H:%M")  # 24-hour format

    if timezone is None:
        return f"{date_part} at {time_part}"
    else:
        return f"{date_part} at {time_part} timezone {timezone}"


def get_ordinal_suffix(day):
    if 4 <= day <= 20 or 24 <= day <= 30:
        return "th"
    else:
        return ["st", "nd", "rd"][day % 10 - 1]


def prRed(skk):
    logger.info(skk, mask="\033[91m {}\033[00m")


def prGreen(skk):
    logger.info(skk, mask="\033[92m {}\033[00m")


def prYellow(skk):
    logger.info(skk, mask="\033[93m {}\033[00m")


def prLightPurple(skk):
    logger.info(skk, mask="\033[94m {}\033[00m")


def prPurple(skk):
    logger.info(skk, mask="\033[95m {}\033[00m")


def prCyan(skk):
    logger.info(skk, mask="\033[96m {}\033[00m")


def prLightGray(skk):
    logger.info(skk, mask="\033[97m {}\033[00m")


def prBlack(skk):
    logger.info(skk, mask="\033[98m {}\033[00m")


def time_function(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter_ns()
        result = func(*args, **kwargs)
        end_time = time.perf_counter_ns()
        duration_ns = end_time - start_time
        duration_s = duration_ns / 1_000_000_000  # Convert nanoseconds to seconds
        prYellow(f"{func.__name__} took {duration_s} seconds")
        return result

    return wrapper


def atime_function(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.perf_counter_ns()
        result = await func(*args, **kwargs)
        end_time = time.perf_counter_ns()
        duration_ns = end_time - start_time
        duration_s = duration_ns / 1_000_000_000  # Convert nanoseconds to seconds
        prYellow(f"{func.__name__} took {duration_s} seconds")
        return result

    return wrapper


def ensure_message_agent_name(message: BaseMessage, current_topic: str):
    has_agent = message.additional_kwargs.get("agent_classification", False)
    if has_agent:
        return

    needs_agent = isinstance(message, (AIMessage, FunctionMessage)) and not message.additional_kwargs.get(
        "timestamp", False
    )
    if not needs_agent:
        return

    message.additional_kwargs["agent_classification"] = current_topic


def base_prompt_structure_node(state, agent, name, topic):
    messages = state["messages"]

    # we process the whole of messages for contextual needs
    prYellow(f"invoking agent: {name}")
    prRed(messages[-1])

    inputs = {
        "travel_context": state["travel_context"],
        "messages": state["messages"],
    }

    start_time = time.perf_counter_ns()
    response = agent.invoke(inputs)
    end_time = time.perf_counter_ns()
    duration_ns = end_time - start_time
    duration_s = duration_ns / 1_000_000_000  # Convert nanoseconds to seconds
    prYellow(f"analysis took {duration_s} seconds")

    arguments_string = response.additional_kwargs["function_call"]["arguments"]
    arguments_obj = json.loads(arguments_string)

    prGreen(f"base_prompt_structure_node() arguments_string: {arguments_string}")

    # When topic is none it is a supervisor message
    # (and not put into the message buffer so that the next agent can process it as if it came in directly to them)
    # note too that since the supervisor processes every message it's the only one that needs to determine the
    # travel context and save it in the state, other agents make use of the travel context but only the supervisor
    # updates it
    if topic is None:
        prLightGray(f"base_prompt_structure_node() Supervisor travel context: {arguments_obj['travel_context']}")
        return {
            "supervisor_message_classification": arguments_obj["supervisor_message_classification"],
            "current_topic": topic,
            "travel_context": arguments_obj["travel_context"],
        }
    else:
        # JTB - 2024-07-17 - Note:  response.content is empty - This is OK,
        # as it gets copied later in the agent conditional edge functions
        return {"messages": [response], "current_topic": topic}


def is_flat_json_like(input_string: str) -> bool:
    """A flat JSON-like string should not have nested braces or brackets beyond arrays."""
    return input_string.count("{") == 1 and input_string.count("}") == 1


def convert_flat_json_like(input_str: str) -> str:
    """Convert a JSON-like string to a properly formatted JSON string.
    Args:
        input_str: A string that looks like JSON but may not be properly formatted
                  Example: {key: value, array: [item1, item2]}
    Returns:
        A properly formatted JSON string that can be parsed by json.loads()
    """
    if not is_flat_json_like(input_str):
        raise ValueError("Input string is not a flat object. Nested structures or literals are not supported.")

    # Remove curly braces from start and end if they exist
    content = input_str.strip()
    if content.startswith("{"):
        content = content[1:]
    if content.endswith("}"):
        content = content[:-1]

    # Split into key-value pairs
    pairs: list[list[str]] = []
    current_pair = []
    bracket_count = 0
    current_item = ""

    for char in content + ",":  # Add comma to handle last item
        if char == "[":
            bracket_count += 1
        elif char == "]":
            bracket_count -= 1

        if char == "," and bracket_count == 0:
            if current_item.strip():
                current_pair.append(current_item.strip())
            if current_pair:
                pairs.append(current_pair)
            current_pair = []
            current_item = ""
        elif char == ":" and bracket_count == 0:
            if current_item.strip():
                current_pair.append(current_item.strip())
            current_item = ""
        else:
            current_item += char

    # Process each key-value pair
    result: Dict[str, Any] = {}
    for pair in pairs:
        if len(pair) != 2:
            continue

        key, value = pair
        # Clean and quote the key
        key = f'"{key.strip()}"'

        # Process the value
        value = value.strip()
        if value.startswith("[") and value.endswith("]"):
            # Handle arrays
            array_content = value[1:-1].strip()
            if not array_content:
                value = "[]"
            else:
                items = [item.strip() for item in array_content.split(",")]
                items = [f'"{item}"' for item in items if item]
                value = f"[{', '.join(items)}]"
        elif value.lower() == "null":
            value = "null"
        elif not value.startswith('"'):
            # Quote non-array, non-null values that aren't already quoted
            value = f'"{value}"'

        result[json.loads(key)] = json.loads(value)

    # Convert to properly formatted JSON string
    return json.dumps(result, indent=4)


def new_travel_context(initial_preferences: ResponseAllPreferences) -> TravelContext:
    travel_context = TravelContext(
        # overarching_travel_preferences=initial_preferences,
        trip_airline_brands=[],
        trip_cabin=[],
        trip_seats=[],
        trip_hotel_brands=[],
        trip_travel_misc=[],
    )
    return travel_context
