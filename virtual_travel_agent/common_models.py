import operator
import traceback
from enum import Enum
from typing import Annotated, Any, List, Optional, Sequence, TypedDict

from fastapi import HTT<PERSON>Exception
from langchain_core.messages import BaseMessage
from langchain_core.pydantic_v1 import BaseModel, Field

from baml_client.types import (
    ResponseAllPreferences,
    SupervisorClassificationLabel,
    TravelContext,
)


class ConversationBaseSchema(BaseModel):
    # no field descriptors here because it appears this may conflict guidance
    # from prompt
    last_message_topic: str = Field(description="the topic that best matches the explanation of the input")
    message_explanation: str = Field(description="the explanation of the last message")
    agent_response: str = Field(
        description="provide a helpful and relevant response to the explanation of the input back to the traveler that helps them achieve their goal"
    )
    # agent_action: str = Field(description="the action the agent will take based on the explanation of the input")


class AgentErrorCode(Enum):
    UNKNOWN = "UNKNOWN"

    # Spotnana API Errors
    ITINERARY_FARE_EXPIRED = "ITINERARY_FARE_EXPIRED"
    RETRYABLE_THIRDPARTY_ERROR = "RETRYABLE_THIRDPARTY_ERROR"
    INVALID_AIR_ID = "INVALID_AIR_ID"
    API_VALIDATION_FAILED = "API_VALIDATION_FAILED"
    BOOKING_FAILED = "BOOKING_FAILED"

    # General Exceptions
    EXCEPTION = "EXCEPTION"

    @classmethod
    def _missing_(cls, value):
        return AgentErrorCode.UNKNOWN


class AgentError(Exception):
    from_: str
    code: AgentErrorCode
    message: str = ""
    details: Any = None
    handled: bool = False

    def __init__(self, code: AgentErrorCode | str | int, message: str = "", from_: str = "", details: Any = None):
        self.from_ = from_
        self.code = AgentErrorCode(str(code)) if not isinstance(code, AgentErrorCode) else code  # convert to ErrorCode
        self.message = message
        self.details = details
        self.trace = traceback.format_exc()
        super().__init__(self.message)

    @classmethod
    def from_exception(cls, from_: str, exception: Exception) -> "AgentError":
        return AgentError(from_=from_, code=AgentErrorCode.EXCEPTION, message=str(exception), details=exception)

    @classmethod
    def from_spotnana_error(cls, from_: str, error_response: dict) -> "AgentError":
        """Spotnana error response:
        {
            "debugIdentifier": "string",
            "errorMessages": [
                {
                    "errorCode": "string",
                    "message": "string",
                    "errorParameters": [{"name": "string", "value": "string"}],
                    "errorDetail": "string",
                }
            ],
        }
        """

        error: List[dict[str, str]] = error_response.get("errorMessages", [])
        if not error:
            error = [
                {
                    "errorCode": "UNKNOWN",
                    "message": f"Couldn't conver the Spotnana response, please check: {error_response}",
                }
            ]
        return AgentError(
            from_=from_,
            code=error[0].get("errorCode", AgentErrorCode.UNKNOWN),
            message=error[0].get("message", ""),
            details=error_response,
        )

    @classmethod
    def from_http_exception(cls, from_: str, e: HTTPException) -> "AgentError":
        return AgentError(from_=from_, code=AgentErrorCode.UNKNOWN, message=str(e), details=None)

    def __str__(self):
        return f"AgentError(from_={self.from_}, code={self.code}, message={self.message}, details={self.details}, handled={self.handled}\n\n {self.trace})"


class AgentState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], operator.add]
    output: str
    current: str  # current topic
    next: Optional[str]
    type: str
    current_topic: str  # current agent
    supervisor_message_classification: SupervisorClassificationLabel  # next agent
    trip_memories: List[str]
    # switch_agent: bool

    travel_preference: ResponseAllPreferences
    travel_context: TravelContext
    search_id: Optional[str]
    change_flight_search_id: Optional[str]
    seat_selection: Optional[dict[str, str]]
    trip_id: Optional[str]
    confirmation_id: Optional[str]  # same as pnrId
    flight_credits_ticket_numbers: Optional[list[str]]
    airline_confirmation_number: Optional[str]
    model: Any  # the pydantic model returned by baml, use it to check for the type of response and act accordingly using the model's fields
    start_path: str

    extra: Optional[dict[str, Any]]

    errors: Annotated[List[Exception], operator.add]
