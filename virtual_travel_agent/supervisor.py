from typing import Any

import yaml
from langchain_core.messages import AIMessage

from baml_client import b
from baml_client.types import (
    SupervisorClassificationLabel,
    SupervisorRouting,
    TravelContext,
)
from llm_utils.llm_utils import correct_invalid_date, get_message_buffer_as_strings, pydantic_delta_update
from server.schemas.authenticate.user import User
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.common_models import AgentState
from virtual_travel_agent.helpers import (
    atime_function,
    console_masks,
    get_current_date_string,
)
from virtual_travel_agent.timings import Timings

supervisor_log_mask = console_masks["supervisor"]


class SupervisorHelper:
    def __init__(self, user: User, timezone: str | None = None):
        self.input_tokens_counter = 0
        self.output_tokens_counter = 0

        self.user = user
        self.timezone = timezone

    @atime_function
    async def supervisor_runnable_function(self, state: AgentState):
        messages = state["messages"]
        message_buffer_strs = get_message_buffer_as_strings(messages)

        current_travel_context: TravelContext = state["travel_context"]
        travel_context_str = current_travel_context.model_dump_json()
        travel_preference_str = state["travel_preference"].model_dump_json()
        trip_memories = state["trip_memories"]

        should_use_claude_supervisor = await is_feature_flag_enabled(
            self.user.id, FeatureFlags.ENABLE_CLAUDE_SUPERVISOR
        )

        if should_use_claude_supervisor:
            t = Timings("BAML: SupervisorMessageClassification_Claude")
            response: SupervisorRouting = await b.SupervisorMessageClassification_Claude(
                travel_preference=travel_preference_str,
                travel_context=travel_context_str,
                messages=message_buffer_strs,
                current_date=get_current_date_string(self.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                trip_memories=trip_memories,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("purple")
            logger.log_baml()
        else:
            t = Timings("BAML: SupervisorMessageClassification")
            response: SupervisorRouting = await b.SupervisorMessageClassification(
                travel_preference=travel_preference_str,
                travel_context=travel_context_str,
                messages=message_buffer_strs,
                current_date=get_current_date_string(self.timezone),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                trip_memories=trip_memories,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("purple")
            logger.log_baml()

        if (
            response.supervisor_message_classification.name == "CancelFlights"
            or response.supervisor_message_classification.name == "ExchangeFlights"
        ):
            if not state["confirmation_id"] and not state["airline_confirmation_number"]:
                logger.error(
                    "We can't cancel or exchange flights without a confirmation_id or airline_confirmation_number, falling back to Flights",
                    mask=supervisor_log_mask,
                )
                response.supervisor_message_classification = SupervisorClassificationLabel.Flights

        logger.info(response.model_dump_json(), mask=supervisor_log_mask)
        if response.travel_context_json:
            travel_context_update: dict[str, Any] = {}
            # 1. Parse the JSON string manually and update fields selectively
            try:
                travel_context_json = response.travel_context_json
                travel_context_update = yaml.load(travel_context_json, yaml.SafeLoader)
            except Exception as e:
                logger.error(f"Error decoding JSON: {e}", mask=supervisor_log_mask)

            if travel_context_update:
                if response.should_context_reset:
                    # logger.warn(
                    #     f"Resetting travel context. Reason: {response.reason_on_should_context_reset}",
                    #     mask=supervisor_log_mask,
                    # )
                    current_travel_context.trip_airline_brands = None
                    current_travel_context.trip_cabin = None
                    current_travel_context.trip_seats = None
                    current_travel_context.trip_outbound_departure_time = None
                    current_travel_context.trip_outbound_arrival_time = None
                    current_travel_context.trip_return_departure_time = None
                    current_travel_context.trip_return_arrival_time = None
                    current_travel_context.hotel_location_preference = None
                    current_travel_context.trip_hotel_brands = None
                    current_travel_context.hotel_room_preference = None
                    current_travel_context.trip_travel_misc = None
                    current_travel_context.trip_flight_type = None
                current_travel_context = pydantic_delta_update(current_travel_context, travel_context_update)
                logger.info(f"New travel_context: {current_travel_context.model_dump_json()}", mask=supervisor_log_mask)
                state["travel_context"] = current_travel_context
            else:
                logger.error(
                    f"Failed to get travel context update as we can't parse the model output: {response.travel_context_json}",
                    mask=supervisor_log_mask,
                )
        else:
            logger.info("No travel context update", mask=supervisor_log_mask)

        corrected_start_date = await correct_invalid_date(state["travel_context"].trip_start_date)
        corrected_end_date = await correct_invalid_date(state["travel_context"].trip_end_date)
        invalid_date_message = []
        if corrected_start_date != state["travel_context"].trip_start_date:
            invalid_date_message.append(
                f"The trip start date {state['travel_context'].trip_start_date} appears to be invalid. Should it be {corrected_start_date} instead?"
            )
        if corrected_end_date != state["travel_context"].trip_end_date:
            invalid_date_message.append(
                f"The trip end date {state['travel_context'].trip_end_date} appears to be invalid. Should it be {corrected_end_date} instead?"
            )
        if invalid_date_message:
            t = Timings("BAML: CombineAllSentences")
            invalid_date_response = await b.CombineAllSentences(
                sentences=[
                    *invalid_date_message,
                    "Could you please double-check and confirm the dates for your upcoming trip?",
                ],
                baml_options={"collector": logger.collector},
            )
            t.print_timing("purple")
            logger.log_baml()
            message = {
                "type": "prompt",
                "text": invalid_date_response.combinedText,
                "isBotMessage": True,
                "expectResponse": True,
                "textColor": settings.AGENT_MESSAGE_COLOR_MAP[AgentTypes.OTHER],
            }
            return {
                "supervisor_message_classification": SupervisorClassificationLabel.Other,
                "messages": [AIMessage(content=message["text"])],
                "current_topic": "Other",
                "travel_context": current_travel_context,
                "model": invalid_date_response,
            }

        last_topic = state["current_topic"]

        return {
            "supervisor_message_classification": response.supervisor_message_classification,
            "current_topic": None,
            "last_topic": last_topic,
            "travel_context": current_travel_context,
            "model": response,
        }
