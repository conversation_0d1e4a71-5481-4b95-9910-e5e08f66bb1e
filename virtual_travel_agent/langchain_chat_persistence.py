import json
from datetime import datetime, timezone
from typing import List, Sequence, Tuple

from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.messages import BaseMessage, message_to_dict, messages_from_dict
from pydantic import BaseModel
from sqlalchemy.engine.row import Row

from server.database.models.checkpoint import Checkpoint


class PendingMessage(BaseModel):
    message: BaseMessage
    input_tokens: int | None
    output_tokens: int | None


class PostgresChatMessageHistory(BaseChatMessageHistory):
    """Chat message history stored in a Postgres database."""

    def __init__(
        self,
        thread_id: int,
        table_name: str = "checkpoints",
    ):
        self.thread_id = thread_id
        self.table_name = table_name
        self.max_created_date: datetime = datetime.min.replace(tzinfo=timezone.utc)
        self.pending: List[PendingMessage] = []
        self.async_db_task = None
        super().__init__()  # Ensure any necessary base class initialization
        self.messages: List[BaseMessage] = []  # Initialize as an instance attribute

    @property
    async def persisted_messages(self) -> List[BaseMessage]:
        """Retrieve the messages from PostgreSQL"""
        items: Sequence[Row[Tuple[Checkpoint]]] = await Checkpoint.from_thread_id(thread_id=self.thread_id) or []
        messages = []
        for item in items:
            message = messages_from_dict(
                [json.loads(item.Checkpoint.data) if isinstance(item.Checkpoint.data, str) else item.Checkpoint.data]
            )[0]
            message.id = item.Checkpoint.id
            messages.append(message)
        if len(items) > 0:
            self.max_created_date = items[-1].Checkpoint.created_date.replace(tzinfo=timezone.utc)
        return messages

    async def apersist(self):
        if len(self.pending) == 0:
            return
        pending_messages = self.pending.copy()
        self.pending = []
        checkpoints: list[Checkpoint] = []
        for msg in pending_messages:
            checkpoints.append(
                Checkpoint(
                    thread_id=self.thread_id,
                    data=message_to_dict(msg.message),
                    input_tokens=msg.input_tokens,
                    output_tokens=msg.output_tokens,
                )
            )
        await Checkpoint.new_checkpoint_batch(checkpoints)
        self.async_db_task = None

    def add_pending_message(
        self, message: BaseMessage, input_tokens: int | None = None, output_tokens: int | None = None
    ):
        pending_message = PendingMessage.model_construct(
            message=message,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
        )
        self.pending.append(pending_message)

    async def aupdate_message(self, message: BaseMessage, message_id: int) -> None:
        """Update the message record in PostgreSQL"""
        await Checkpoint.update_field(message_id, json.dumps(message_to_dict(message)))

    async def aclear(self) -> None:
        """Clear session memory from PostgreSQL"""
        await Checkpoint.delete_threads([self.thread_id])

    def clear(self) -> None:
        """Clear session memory from PostgreSQL"""
        pass
