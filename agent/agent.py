import asyncio
import itertools
import json
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timezone
from functools import partial
from typing import Any, Callable, Coroutine

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
)

from front_of_house_agent.back_of_house_executor.executor import Agent<PERSON>xecutor
from server.schemas.authenticate.user import User
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.memory.trips.retriever import TripMemoryRetriever
from server.services.task_manager.task_manager import GlobalTaskManager
from server.utils.logger import logger
from server.utils.settings import settings
from virtual_travel_agent.helpers import ensure_message_agent_name
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import LatencyTimer

STEAMING_BUFFER_SIZE = 8


@dataclass
class StopResponse:
    last_text: str | None = None


class Agent(ABC):
    def __init__(
        self,
        user: User,
        history: PostgresChatMessageHistory,
        mem_loader: TripMemoryRetriever,
        websocket_send_message: partial[Coroutine[Any, Any, None]],
    ):
        self.user = user
        self.history = history
        self.mem_loader = mem_loader
        self.websocket_send_message = websocket_send_message
        self.messages: list[BaseMessage] = []
        self.current_task = None
        self.executors: list[AgentExecutor] = []

    async def get_history_messages(self, adapter: Callable[[BaseMessage], Any]):
        db_messages = await self.history.persisted_messages
        self.messages.clear()
        self.messages.extend(db_messages)

        flight_found = False
        hotel_found = False

        history: list[list[dict[str, str | bool | list[dict[str, Any]] | None]]] = []
        for idx, message in reversed(list(enumerate(self.messages))):
            if message.additional_kwargs.get("is_card_update", False):
                continue
            elif message.additional_kwargs.get("timestamp", False):
                history.append(
                    [
                        {
                            "type": "prompt",
                            "timestamp": message.additional_kwargs.get("timestamp"),
                        }
                    ]
                )
                del self.messages[idx]
            else:
                history_messages = await adapter(message)

                if (
                    len(history_messages) == 1
                    and history_messages[0].get("flights", None) is not None
                    and not flight_found
                ):
                    history_messages[0]["isLastSearch"] = True
                    flight_found = True

                if (
                    len(history_messages) == 1
                    and history_messages[0].get("accommodations", None) is not None
                    and not hotel_found
                ):
                    history_messages[0]["isLastSearch"] = True
                    hotel_found = True

                history.append(history_messages)
        history.reverse()
        to_send = list(itertools.chain.from_iterable(history))

        # This is history, do not wait for user input
        for msg in to_send:
            msg["expectResponse"] = False

        return to_send

    def generate_stop_message(self) -> dict[str, Any]:
        message = AIMessage(content="", additional_kwargs={"is_stopped": True})
        self.history.add_pending_message(message)
        return {
            "type": "prompt",
            "text": "",
            "expectResponse": True,
            "isBotMessage": True,
            "isStopped": True,
        }

    async def add_timestamp_message(self, new_timestamp: str | None = None, send_ws_message: bool = False):
        if new_timestamp is None:
            new_timestamp = datetime.now(timezone.utc).isoformat()

        if datetime.fromisoformat(new_timestamp) - self.history.max_created_date > settings.MIN_MESSAGE_TIMESTAMP_DELTA:
            timestamp_message = AIMessage(content="", additional_kwargs={"timestamp": new_timestamp})
            self.history.add_pending_message(timestamp_message)

            if send_ws_message:
                await self.websocket_send_message(
                    message={
                        "type": "prompt",
                        "timestamp": timestamp_message.additional_kwargs.get("timestamp"),
                    },
                    broadcast_message=False,
                )

    def add_history_message(
        self,
        message: BaseMessage,
        input_tokens: int | None = None,
        output_tokens: int | None = None,
        current_topic: str | None = None,
    ):
        if current_topic is not None:
            ensure_message_agent_name(message, current_topic)
        self.history.add_pending_message(message, input_tokens=input_tokens, output_tokens=output_tokens)

    async def persist_messages(self, messages: list[BaseMessage]):
        self.messages.extend(messages)
        for message in messages:
            self.add_history_message(message)
        await self.history.apersist()

    async def get_messages(self) -> tuple[list[BaseMessage], list[str]]:
        """
        Gets chat messages passed to the agent.
        If memory feature flag is enabled for the user, enhance the messages with relevant context
        and limit the number of messages. If not, returns raw messages.

        Returns:
            Tuple of list of chat messages and list of trip memories
        """
        # Check if memory feature is enabled for this user
        is_memory_enabled = await is_feature_flag_enabled(self.user.id, FeatureFlags.ENABLE_MEMORY_RETRIEVAL)

        # If memory not enabled, return raw messages
        if not is_memory_enabled:
            logger.info(f"Memory feature is not enabled for this user: {self.user.id}")
            return self.messages, []

        logger.info(f"Memory feature is enabled for this user: {self.user.id}")
        # Process messages for enabled users
        memories = await self.get_trip_memories(self.messages)

        # Return last 10 messages
        return list(self.messages)[-10:], memories

    async def get_trip_memories(self, messages: list[BaseMessage]) -> list[str]:
        """
        Retrieves relevant context from memory layer.

        Args:
            messages: List of messages to inject context into

        Returns:
            List of trip memories
        """
        relevant_context = await self.mem_loader.search(messages=messages)
        logger.info(f"relevant_context: {relevant_context}")
        return relevant_context

    def message_raw_tool_output(self, message: BaseMessage):
        try:
            return json.loads(message.additional_kwargs.get("raw_tool_output", "{}"))
        except BaseException:
            return None

    @abstractmethod
    async def run(self, message=None, message_type="text", extra_payload=None) -> list[Any]:
        pass

    async def run_with_task(self, message=None, message_type="text", extra_payload=None) -> list[Any]:
        task = asyncio.create_task(self.run(message, message_type, extra_payload))
        self.current_task = task
        GlobalTaskManager.register_task(task)
        return await task

    def stop(self):
        if self.current_task is not None and not self.current_task.done():
            self.current_task.cancel()
        for executor in self.executors:
            executor.stop_all_task()

    async def process_streaming(
        self,
        llm_call: Callable[[], Any],
        response_checker: Callable,
        response_getter: Callable,
        pre_streaming_check: Callable[[Any], bool] | None = None,
    ) -> Any:
        """
        Process streaming response from LLM call.
        Args:
            llm_call: BAML streaming function
            response_checker: Function to check if the response is ready to stream
            response_getter: Function to extract the response from the LLM call to be streamed
            pre_streaming_check: Function to check if the streaming should continue. True allows streaming to continue, False stops it.
        Returns:
            Final response from the BAML function call
        """
        response_to_stream = None
        try:
            assert llm_call is not None, "llm_call is required for process_streaming call."
            if isinstance(llm_call, partial):
                callable_name = (
                    llm_call.func.__name__ if hasattr(llm_call.func, "__name__") else llm_call.func.__class__.__name__
                )

                # Add collector to baml_options
                if hasattr(llm_call, "keywords") and "baml_options" in llm_call.keywords:
                    llm_call.keywords["baml_options"]["collector"] = logger.collector
                else:
                    llm_call = partial(
                        llm_call.func,
                        *llm_call.args,
                        **{**llm_call.keywords, "baml_options": {"collector": logger.collector}},
                    )
            else:
                callable_name = llm_call.__name__ if hasattr(llm_call, "__name__") else llm_call.__class__.__name__
            t = LatencyTimer(f"BamlStream:{callable_name}")
            stream = llm_call()
            is_first_token_responsed = False
            buffer = []
            async for partial_response in stream:
                if response_checker(partial_response):
                    should_continue = True
                    if not is_first_token_responsed and pre_streaming_check:
                        # If streaming hasn't started yet, check if it should continue
                        # Otherwise, don't break the streaming while it's in progress
                        try:
                            should_continue = pre_streaming_check(partial_response)
                        except Exception as e:
                            logger.warning(f"Ignore the exception raised by pre_streaming_check: {e}")
                    if not should_continue:
                        break
                    response_to_stream = response_getter(partial_response)
                    if response_to_stream is not None:
                        buffer.append(response_to_stream)
                    if not is_first_token_responsed:
                        t.end(tag="FirstTokenResponsed")
                        is_first_token_responsed = True
                    if len(buffer) >= STEAMING_BUFFER_SIZE:
                        await self.websocket_send_message(
                            message={
                                "type": "prompt",
                                "partial": True,
                                "isBotMessage": True,
                                "expectResponse": False,
                                "text": response_to_stream,
                            }
                        )
                        buffer.clear()
            if buffer:
                await self.websocket_send_message(
                    message={
                        "type": "prompt",
                        "partial": True,
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": buffer[-1],
                    }
                )

            final_response = await stream.get_final_response()
            t.end(tag="FinalResponse")
            logger.log_baml()
            return final_response
        except asyncio.exceptions.CancelledError:
            return StopResponse(last_text=response_to_stream)
