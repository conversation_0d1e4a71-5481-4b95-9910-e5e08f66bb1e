#!/usr/bin/env python3

import os
import sys

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from in_trip.in_trip import InTripAgent


def test_parse_flight_legs():
    """Test the parse_flight_legs function with the provided JSON example."""

    example_json = {
        "legs": [
            {
                "id": None,
                "img": {"alt": "DL", "src": None},
                "type": None,
                "cabin": "ECONOMY",
                "price": {"amount": "521.96", "currency": "USD"},
                "action": "For my return I choose flight id: None.",
                "source": None,
                "credits": {"amount": "0.00", "currency": "USD"},
                "keyword": None,
                "cancelled": False,
                "highlight": None,
                "net_price": {"amount": "0.00", "currency": "USD"},
                "is_red_eye": None,
                "booking_code": "Q",
                "within_policy": None,
                "total_duration": None,
                "boarding_policy": None,
                "exchange_policy": None,
                "flight_segments": [
                    {
                        "origin_code": "SEA",
                        "origin_name": "Seattle–Tacoma International Airport",
                        "flight_stops": [
                            {
                                "seat": "15C",
                                "cabin": "ECONOMY",
                                "arrival": "2025-05-23T15:55:00",
                                "duration": "PT2H21M",
                                "departure": "2025-05-23T13:40:00",
                                "origin_code": "SEA",
                                "origin_name": "Seattle–Tacoma International Airport",
                                "airline_code": "DL",
                                "airline_name": "Delta Air Lines",
                                "booking_code": "Q",
                                "confirmation": "GEYSCO",
                                "aircraft_name": "Embraer 175 (long wing)",
                                "flight_number": "3707",
                                "arrival_timezone": "America/Los_Angeles",
                                "destination_code": "SFO",
                                "destination_name": "San Francisco International Airport",
                                "aircraft_iata_code": "E7W",
                                "departure_timezone": "America/Los_Angeles",
                                "operating_airline_code": "DL",
                                "operating_flight_number": "3707",
                            }
                        ],
                        "destination_code": "SFO",
                        "destination_name": "San Francisco International Airport",
                    }
                ],
                "fare_option_name": "Delta Main",
                "cancellation_policy": None,
                "total_distance_miles": None,
                "recommendationReasons": [""],
                "seat_selection_policy": None,
                "operating_airline_code": None,
                "operating_flight_number": None,
                "within_or_out_policy_reason": None,
            },
            {
                "id": None,
                "img": {"alt": "DL", "src": None},
                "type": None,
                "cabin": "ECONOMY",
                "price": {"amount": "521.96", "currency": "USD"},
                "action": "For my return I choose flight id: None.",
                "source": None,
                "credits": {"amount": "0.00", "currency": "USD"},
                "keyword": None,
                "cancelled": False,
                "highlight": None,
                "net_price": {"amount": "0.00", "currency": "USD"},
                "is_red_eye": None,
                "booking_code": "X",
                "within_policy": None,
                "total_duration": None,
                "boarding_policy": None,
                "exchange_policy": None,
                "flight_segments": [
                    {
                        "origin_code": "SFO",
                        "origin_name": "San Francisco International Airport",
                        "flight_stops": [
                            {
                                "seat": "21C",
                                "cabin": "ECONOMY",
                                "arrival": "2025-06-23T08:14:00",
                                "duration": "PT2H15M",
                                "departure": "2025-06-23T05:55:00",
                                "origin_code": "SFO",
                                "origin_name": "San Francisco International Airport",
                                "airline_code": "DL",
                                "airline_name": "Delta Air Lines",
                                "booking_code": "X",
                                "confirmation": "GEYSCO",
                                "aircraft_name": "Boeing 737-800",
                                "flight_number": "2721",
                                "arrival_timezone": "America/Los_Angeles",
                                "destination_code": "SEA",
                                "destination_name": "Seattle–Tacoma International Airport",
                                "aircraft_iata_code": "738",
                                "departure_timezone": "America/Los_Angeles",
                                "operating_airline_code": "DL",
                                "operating_flight_number": "2721",
                            }
                        ],
                        "destination_code": "SEA",
                        "destination_name": "Seattle–Tacoma International Airport",
                    }
                ],
                "fare_option_name": "Delta Main",
                "cancellation_policy": None,
                "total_distance_miles": None,
                "recommendationReasons": [""],
                "seat_selection_policy": None,
                "operating_airline_code": None,
                "operating_flight_number": None,
                "within_or_out_policy_reason": None,
            },
        ],
        "return": {
            "id": None,
            "img": {"alt": "DL", "src": None},
            "type": None,
            "cabin": "ECONOMY",
            "price": {"amount": "521.96", "currency": "USD"},
            "action": "For my return I choose flight id: None.",
            "source": None,
            "credits": {"amount": "0.00", "currency": "USD"},
            "keyword": None,
            "cancelled": False,
            "highlight": None,
            "net_price": {"amount": "0.00", "currency": "USD"},
            "is_red_eye": None,
            "booking_code": "X",
            "within_policy": None,
            "total_duration": None,
            "boarding_policy": None,
            "exchange_policy": None,
            "flight_segments": [
                {
                    "origin_code": "SFO",
                    "origin_name": "San Francisco International Airport",
                    "flight_stops": [
                        {
                            "seat": "21C",
                            "cabin": "ECONOMY",
                            "arrival": "2025-06-23T08:14:00",
                            "duration": "PT2H15M",
                            "departure": "2025-06-23T05:55:00",
                            "origin_code": "SFO",
                            "origin_name": "San Francisco International Airport",
                            "airline_code": "DL",
                            "airline_name": "Delta Air Lines",
                            "booking_code": "X",
                            "confirmation": "GEYSCO",
                            "aircraft_name": "Boeing 737-800",
                            "flight_number": "2721",
                            "arrival_timezone": "America/Los_Angeles",
                            "destination_code": "SEA",
                            "destination_name": "Seattle–Tacoma International Airport",
                            "aircraft_iata_code": "738",
                            "departure_timezone": "America/Los_Angeles",
                            "operating_airline_code": "DL",
                            "operating_flight_number": "2721",
                        }
                    ],
                    "destination_code": "SEA",
                    "destination_name": "Seattle–Tacoma International Airport",
                }
            ],
            "fare_option_name": "Delta Main",
            "cancellation_policy": None,
            "total_distance_miles": None,
            "recommendationReasons": [""],
            "seat_selection_policy": None,
            "operating_airline_code": None,
            "operating_flight_number": None,
            "within_or_out_policy_reason": None,
        },
        "status": "booked",
        "trip_id": "1548188379",
        "invoices": ["https://otto-trip-invoices.s3.amazonaws.com/1548188379/SPOT-US-002176.pdf"],
        "outbound": {
            "id": None,
            "img": {"alt": "DL", "src": None},
            "type": None,
            "cabin": "ECONOMY",
            "price": {"amount": "521.96", "currency": "USD"},
            "action": "For my return I choose flight id: None.",
            "source": None,
            "credits": {"amount": "0.00", "currency": "USD"},
            "keyword": None,
            "cancelled": False,
            "highlight": None,
            "net_price": {"amount": "0.00", "currency": "USD"},
            "is_red_eye": None,
            "booking_code": "Q",
            "within_policy": None,
            "total_duration": None,
            "boarding_policy": None,
            "exchange_policy": None,
            "flight_segments": [
                {
                    "origin_code": "SEA",
                    "origin_name": "Seattle–Tacoma International Airport",
                    "flight_stops": [
                        {
                            "seat": "15C",
                            "cabin": "ECONOMY",
                            "arrival": "2025-05-23T15:55:00",
                            "duration": "PT2H21M",
                            "departure": "2025-05-23T13:40:00",
                            "origin_code": "SEA",
                            "origin_name": "Seattle–Tacoma International Airport",
                            "airline_code": "DL",
                            "airline_name": "Delta Air Lines",
                            "booking_code": "Q",
                            "confirmation": "GEYSCO",
                            "aircraft_name": "Embraer 175 (long wing)",
                            "flight_number": "3707",
                            "arrival_timezone": "America/Los_Angeles",
                            "destination_code": "SFO",
                            "destination_name": "San Francisco International Airport",
                            "aircraft_iata_code": "E7W",
                            "departure_timezone": "America/Los_Angeles",
                            "operating_airline_code": "DL",
                            "operating_flight_number": "3707",
                        }
                    ],
                    "destination_code": "SFO",
                    "destination_name": "San Francisco International Airport",
                }
            ],
            "fare_option_name": "Delta Main",
            "cancellation_policy": None,
            "total_distance_miles": None,
            "recommendationReasons": [""],
            "seat_selection_policy": None,
            "operating_airline_code": None,
            "operating_flight_number": None,
            "within_or_out_policy_reason": None,
        },
        "price_summary": {
            "tax": {"amount": 64.88, "currency": "USD"},
            "base": {"amount": 457.08, "currency": "USD"},
            "total": {"amount": 521.96, "currency": "USD"},
            "total_seat_price": {"amount": None, "currency": "USD"},
        },
        "confirmation_id": "8363545474",
        "manageBookingUrl": "https://sboxmeta-app.partners.spotnana.com/trips/1548188379",
        "airline_confirmation_number": "GEYSCO",
    }

    print("Testing parse_flight_legs function...")
    print("=" * 50)

    legs = InTripAgent.parse_flight_legs(example_json)

    print(f"Number of legs parsed: {len(legs)}")
    print()

    for i, leg in enumerate(legs):
        print(f"Leg {i + 1}:")
        print(f"  Leg Number: {leg.leg_index}")
        print(f"  Origin: {leg.origin_code}")
        print(f"  Destination: {leg.destination_code}")
        print(f"  Airline: {leg.airline}")
        print(f"  Flight Numbers: {', '.join(leg.flight_numbers)}")
        print(f"  Departure Time: {leg.departure_time}")
        print(f"  Arrival Time: {leg.arrival_time}")
        print(f"  Seat: {leg.seat}")
        print(f"  Confirmation: {leg.confirmation_number}")
        print(f"  Boarding Time: {leg.boarding_time}")
        print(f"  Gate: {leg.gate}")
        print(f"  Terminal: {leg.terminal}")
        print(f"  Type: {type(leg).__name__}")
        print()

    print("Data availability analysis:")
    print("✅ Available: departure_time, arrival_time, seat, leg_number, confirmation_number")
    print("✅ Added fields: boarding_time, gate, terminal (set to None for now)")
    print("✅ Return type: InTripFlightInfo Pydantic model objects (extends FlightInfo)")

    print("\n" + "=" * 50)
    print("Testing parse_flight_legs_from_trip_details function...")
    print("=" * 50)

    try:
        print("Function parse_flight_legs_from_trip_details exists and is callable")
        print("Note: Full testing requires a valid trip_id and API access")
        print("The function is async and calls FlightSearchTools.get_trip_details_spotnana()")
    except Exception as e:
        print(f"Error testing API function: {e}")

    print("\nBoth functions are now available:")
    print("1. parse_flight_legs() - parses from booking.content")
    print("2. parse_flight_legs_from_trip_details() - calls trip details API")

    return legs


if __name__ == "__main__":
    test_parse_flight_legs()
