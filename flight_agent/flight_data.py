from typing import Any, Optional

from pydantic import BaseModel


class FlightInfo(BaseModel):
    departure_time: str
    departure_timezone: str
    arrival_time: str
    arrival_timezone: str
    airline: str
    flight_numbers: list[str]
    origin_name: str
    origin_code: str
    destination_name: str
    destination_code: str
    price_string: str
    seat: Optional[str] = None
    cabin: Optional[str] = None
    location: Optional[str] = None

    @classmethod
    def from_flight_data(cls, flight: dict[str, Any]) -> "FlightInfo":
        segments = flight.get("flight_segments", [{}])[0]
        flight_stops = segments.get("flight_stops", [{}])
        first_stop = segments.get("flight_stops", [{}])[0]
        last_stop = segments.get("flight_stops", [{}])[-1]
        price = flight.get("price", {})
        price_string = ""
        if price:
            price_string = f"{price.get('amount')} {price.get('currency')}"

        flight_numbers = []
        for flight_stop in flight_stops:
            flight_numbers.append(f"{flight_stop.get('airline_code')} {flight_stop.get('flight_number')}")

        return cls(
            departure_time=first_stop.get("departure"),
            departure_timezone=first_stop.get("departure_timezone"),
            arrival_time=last_stop.get("arrival"),
            arrival_timezone=last_stop.get("arrival_timezone"),
            airline=first_stop.get("airline_name"),
            flight_numbers=flight_numbers,
            origin_name=first_stop.get("origin_name"),
            origin_code=first_stop.get("origin_code"),
            destination_name=last_stop.get("destination_name"),
            destination_code=last_stop.get("destination_code"),
            price_string=price_string,
            seat=last_stop.get("seat", "-"),
            cabin=last_stop.get("cabin", "-"),
            location=first_stop.get("origin_name"),
        )
