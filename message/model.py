from enum import Enum

from langchain_core.messages import HumanMessage


class MessageType(str, Enum):
    PROMPT = "prompt"
    SILENT_PROMPT = "silent_prompt"
    HISTORY = "history"
    ITINERARY_UPDATE = "itinerary_update"
    ERROR = "error"
    UPDATE = "update"
    AGENT_RESUME = "agent_resume"
    STOP = "stop"
    TRIP_INIT = "trip_init"
    PREFERENCES_INIT = "preferences_init"
    TRAVEL_POLICY_INIT = "travel_policy_init"
    ONBOARDING_INIT = "onboarding_init"
    FUTURE_TRIPS_INIT = "future_trips_init"
    SUGGESTED_CAPABILITY = "suggested_capability"
    AI_HUMAN = "ai_human"
    FORK_NEW_TRIP = "fork_new_trip"


class AIHumanMessage(HumanMessage):
    def __init__(self, content: str):
        super().__init__(content=content)
        if not self.additional_kwargs:
            self.additional_kwargs = {}
        self.additional_kwargs["message_type"] = MessageType.AI_HUMAN.value
