name: Production Trigger
run-name: Production Deployment - Commit ${{ github.sha }}

on:
  workflow_dispatch:
    inputs:
      appversion:
        description: Version bump
        required: true
        type: choice
        default: patch
        options:
          - patch
          - minor
          - major

permissions:
  packages: write
  contents: write

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  increment-version:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.WORKFLOW_PAT }}

      - name: Set up Git
        run: |
          git config --global user.name '${{ github.actor }}'
          git config --global user.email '${{ github.actor }}@users.noreply.github.com'

      - name: Increment prod version and tag
        run: |
          PROD_TAGGING_PATTERN='v[0-9]+\.[0-9]+\.[0-9]+$'
          LATEST_PROD_TAG=$(git tag --list | grep -E $PROD_TAGGING_PATTERN | sort -V | tail -n1)

          if [[ $LATEST_PROD_TAG =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+)$ ]]; then
            PROD_MAJOR=${BASH_REMATCH[1]}
            PROD_MINOR=${BASH_REMATCH[2]}
            PROD_PATCH=${BASH_REMATCH[3]}
          fi

          case "${{ inputs.appversion }}" in
            major)
              ((PROD_MAJOR+=1))
              NEW_TAG="v$PROD_MAJOR.0.0"
              ;;
            minor)
              ((PROD_MINOR+=1))
              NEW_TAG="v$PROD_MAJOR.$PROD_MINOR.0"
              ;;
            patch)
              ((PROD_PATCH+=1))
              NEW_TAG="v$PROD_MAJOR.$PROD_MINOR.$PROD_PATCH"
              ;;
            *)
              echo "Invalid argument: '${{ inputs.appversion }}'. Please specify 'major', 'minor', or 'patch'."
              exit 2
              ;;
          esac

          echo "New tag: $NEW_TAG"
          git tag $NEW_TAG
          git push origin $NEW_TAG
