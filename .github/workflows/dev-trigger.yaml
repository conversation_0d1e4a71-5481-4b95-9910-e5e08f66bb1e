name: Development Trigger
run-name: Development Deployment - Commit ${{ github.sha }}

on:
  push:
    branches:
      - development

permissions:
  packages: write
  contents: write

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  increment-version:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.WORKFLOW_PAT }}

      - name: Set up Git
        run: |
          git config --global user.name '${{ github.actor }}'
          git config --global user.email '${{ github.actor }}@users.noreply.github.com'

      - name: Increment dev version and tag
        run: |
          DEV_TAGGING_PATTERN='v[0-9]+\.[0-9]+\.[0-9]+-rc[0-9]+$'
          PROD_TAGGING_PATTERN='v[0-9]+\.[0-9]+\.[0-9]+$'
          LATEST_PROD_TAG=$(git tag --list | grep -E $PROD_TAGGING_PATTERN | sort -V | tail -n1)
          LATEST_DEV_TAG=$(git tag --list | grep -E $DEV_TAGGING_PATTERN | sort -V | tail -n1)

          if [[ $LATEST_PROD_TAG =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+)$ ]]; then
            PROD_MAJOR=${BASH_REMATCH[1]}
            PROD_MINOR=${BASH_REMATCH[2]}
            PROD_PATCH=${BASH_REMATCH[3]}
          fi

          if [[ $LATEST_DEV_TAG =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+)-rc([0-9]+)$ ]]; then
            DEV_MAJOR=${BASH_REMATCH[1]}
            DEV_MINOR=${BASH_REMATCH[2]}
            DEV_PATCH=${BASH_REMATCH[3]}
            RC=${BASH_REMATCH[4]}
          fi

          if [[ $PROD_MAJOR -gt $DEV_MAJOR || $PROD_MINOR -gt $DEV_MINOR || $PROD_PATCH -gt $DEV_PATCH ]]; then
            NEW_TAG="v$PROD_MAJOR.$PROD_MINOR.$PROD_PATCH-rc0"
          else
            ((RC+=1))
            NEW_TAG="v$DEV_MAJOR.$DEV_MINOR.$DEV_PATCH-rc$RC"
          fi

          echo "New tag: $NEW_TAG"
          git tag $NEW_TAG
          git push origin $NEW_TAG
