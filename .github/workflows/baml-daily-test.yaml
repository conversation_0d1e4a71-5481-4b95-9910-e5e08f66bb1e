name: Daily BAML Tests
run-name: Daily BAML Tests

on:
  schedule:
    - cron: '0 13 * * *'  # Runs every day at 13:00 UTC (6 AM PT)
  workflow_dispatch:  # Allows manual triggering of the workflow

jobs:
  baml-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: development
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Setup environment
        run: |
          uv sync

      - name: Generate BAML client
        run: |
          uv run baml-cli generate
      - name: Write environment variables to .env
        run: |
          echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
          echo "AZURE_OPENAI_API_KEY=${{ secrets.AZURE_OPENAI_API_KEY }}" >> .env
          echo "BAML_LOG=off" >> .env
      - name: Run BAML tests
        id: baml-tests
        run: |
          uv run baml-cli test -x "DoFlightConversation::" -x "ConverseHotelHITL::" --dotenv-path .env 2>&1 | tee baml-test-results.txt || true

          if grep -q "FAILED" baml-test-results.txt; then
            echo "Tests failed!"
            exit 1
          fi

          if grep -q "ERROR" baml-test-results.txt; then
            echo "Tests failed!"
            exit 1
          fi
