name: Development Deploy Backend
run-name: Backend - Development Build & Deploy - Version ${{ github.ref_name }}

on:
  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+-rc[0-9]+"
  workflow_dispatch: # Added manual trigger for deployments
    inputs:
      branch: # Input for specifying the branch to deploy when manually triggered
        description: "Branch to deploy (if triggered manually)"
        required: true

permissions:
  id-token: write
  packages: write
  contents: read

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  build-backend:
    name: Build and Push Backend
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::891377138838:role/GithubActionsOttoBackend-dev
          aws-region: us-east-2

      - name: Login to Amazon ECR
        id: ecr-login
        uses: aws-actions/amazon-ecr-login@v1

      - name: Get Tags for Image
        id: metadata
        uses: docker/metadata-action@v3
        with:
          images: ${{ steps.ecr-login.outputs.registry }}/otto-backend-dev
          tags: |
            # Check if the workflow is triggered by a push to a branch (not a tag)
            ${{ github.event_name == 'workflow_dispatch' && startsWith(github.ref, 'refs/heads/') && format('type=raw,value={0}', github.sha) || '' }}
            ${{ github.event_name == 'push' && startsWith(github.ref, 'refs/tags/') && format('type=raw,value={0}', github.ref_name) || '' }}
            ${{ github.event_name == 'push' && startsWith(github.ref, 'refs/tags/') && 'type=raw,value=latest' || '' }}

      - name: Build and push
        id: build-image
        uses: docker/build-push-action@4f58ea79222b3b9dc2c8bbdd6debcef730109a75
        with:
          context: .
          push: true
          file: "Dockerfile"
          tags: ${{ steps.metadata.outputs.tags }}
          outputs: type=image,oci-mediatypes=true,compression=zstd,compression-level=3,force-compression=true
          cache-from: type=registry,ref=${{ steps.ecr-login.outputs.registry }}/otto-backend-dev:buildcache
          cache-to: type=registry,ref=${{ steps.ecr-login.outputs.registry }}/otto-backend-dev:buildcache,mode=max
          platforms: linux/amd64
          provenance: false

  run-migrations:
    name: Run Database Migrations
    runs-on: ubuntu-latest
    needs: [build-backend]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true

      - name: Cache uv dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/uv
          key: ${{ runner.os }}-uv-${{ hashFiles('uv.lock') }}
          restore-keys: |
            ${{ runner.os }}-uv-

      - name: Install dependencies
        run: uv sync

      - name: Generate BAML client
        run: OTTO_ENV=dev uv run baml-cli generate

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::891377138838:role/GithubActionsOttoBackend-dev
          aws-region: us-east-2

      - name: Run database migrations
        run: |
          OTTO_ENV=dev uv run alembic upgrade head

  deploy-apps:
    name: Deploy Backend
    runs-on: ubuntu-latest
    needs: [build-backend, run-migrations]

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::891377138838:role/GithubActionsOttoBackend-dev
          aws-region: us-east-2

      - name: Login to Amazon ECR
        id: ecr-login
        uses: aws-actions/amazon-ecr-login@v1

      - name: Get Tags for Backend
        run: |
          echo "Setting image tag for ECS deployment"
          # Check if the workflow was triggered by a tag push
          if [[ "${{ github.event_name }}" == "push" && "${{ github.ref }}" == refs/tags/* ]]; then
            echo "Triggered by tag push, using tag name"
            echo "tags=${{ github.ref_name }}" >> $GITHUB_OUTPUT
          else
            echo "Triggered by branch push or manual deployment, using commit SHA"
            echo "tags=${{ github.sha }}" >> $GITHUB_OUTPUT
          fi
        id: set-tags

      - name: Get task definition
        id: get-task-definition
        run: |
          aws ecs describe-task-definition --task-definition otto-backend-dev --query taskDefinition > task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: otto-backend-dev
          image: ${{ steps.ecr-login.outputs.registry }}/otto-backend-dev:${{ steps.set-tags.outputs.tags }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: otto-backend-dev
          cluster: otto-apps-dev
          wait-for-service-stability: true
