import asyncio
import itertools
import json
from datetime import datetime, timezone
from functools import partial
from typing import Any, Coroutine, List

from langchain_core.messages import AIMessage, BaseMessage, FunctionMessage, HumanMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.graph.graph import CompiledGraph

from baml_client import b
from baml_client.types import PreferencesConversation
from llm_utils.llm_utils import get_message_buffer_as_strings, load_past_calendar_events
from preferences_agent.models import AgentState
from preferences_agent.prompts import (
    cal_analysis_failed_string,
    cal_analysis_opening_string,
    no_cal_analysis_opening_string,
)
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.user.user_preferences import (
    get_user_preferences,
    save_user_preferences,
)
from server.utils.logger import logger
from server.utils.message_constants import CA<PERSON><PERSON>AR_MESSAGES
from server.utils.settings import settings
from server.utils.websocket_no_op import partial_no_op
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import Timings

config: RunnableConfig = {"configurable": {"recursion_limit": 10}}

# This will load and use local test data vs use the data that comes
# directly from the user's calendar
# should_load_test_data = False
# other_log_mask = console_masks["other"]


class TravelAgentPreferences:
    def __init__(
        self,
        user: User,
        thread_id: int,
        websocket_send_message: partial[Coroutine[Any, Any, None]] | None,
    ):
        self.websocket_send_message = websocket_send_message if websocket_send_message is not None else partial_no_op

        self.user: User = user
        self.thread_id = thread_id
        # self.should_load_test_data = should_load_test_data
        self.do_historical_calendar_analysis = False
        # Add memory
        self.history = PostgresChatMessageHistory(thread_id=thread_id)

        self.messages: list[BaseMessage] = []

        # Define a new graph
        self.workflow: StateGraph = StateGraph(AgentState)

        self.workflow.add_node("preferences_convo_agent", self.preferences_convo_agent_model_function)
        self.workflow.add_edge("preferences_convo_agent", END)

        self.workflow.add_node("preferences_cal_analysis_agent", self.preferences_cal_analysis_agent_model_function)
        self.workflow.add_edge("preferences_cal_analysis_agent", END)

        self.workflow.set_conditional_entry_point(self.supervisor_handle_next_message)

        self.graph: CompiledGraph = self.workflow.compile()

    async def supervisor_handle_next_message(self, state):
        # the state will be set in run() based on the fact if we have access to the calendar or not
        if self.do_historical_calendar_analysis:
            return "preferences_cal_analysis_agent"
        else:
            return "preferences_convo_agent"

    async def preferences_convo_agent_model_function(self, state):
        messages = []
        for idx, message in enumerate(reversed(state["messages"])):
            if isinstance(message, HumanMessage):
                messages.append(message)  # only respond to user's last message
                break

        base_user_preference = await get_user_preferences(self.user.id)
        message_buffer_strs = get_message_buffer_as_strings(messages)
        pref_dict = base_user_preference.model_dump()
        missing_preferences = [k for k in pref_dict.keys() if not pref_dict[k]]
        t = Timings("BAML: ConversePreferencesWithoutAnalysis")
        response = await b.ConversePreferencesWithoutAnalysis(
            existing_preference=base_user_preference.model_dump_json(exclude_none=True),
            missing_preferences=missing_preferences,
            messages=message_buffer_strs,
            user_name=self.user.name,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("purple")
        logger.log_baml()

        response.agent_response = response.agent_response.replace("\n\n", "&NewLine; &NewLine;")
        new_message = AIMessage(content="")
        new_message.additional_kwargs = {"function_call": {"arguments": response.model_dump_json()}}
        new_message.content = response.agent_response
        user_preferences = response.response_all_preferences
        asyncio.create_task(save_user_preferences(self.user.id, user_preferences))
        await self.add_timestamp_message()
        return {"messages": [new_message]}

    async def streaming_preference(
        self, existing_preference: str, message_buffer_strs: List[str], events: Any
    ) -> PreferencesConversation:
        t = Timings("BAML: ConversePreferences")
        response = b.stream.ConversePreferences(
            existing_preference=existing_preference,
            cal_events=json.dumps(events),
            messages=message_buffer_strs,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("purple")

        async for partial_response in response:
            if partial_response.agent_response is not None:
                await self.websocket_send_message(
                    message={
                        "type": "prompt",
                        "partial": True,
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": partial_response.agent_response.replace("\n\n", "&NewLine; &NewLine;"),
                    }
                )

        final_response = await response.get_final_response()
        final_response.agent_response = final_response.agent_response.replace("\n\n", "&NewLine; &NewLine;")
        logger.log_baml()
        return final_response

    async def preferences_cal_analysis_agent_model_function(self, state):
        try:
            await self.websocket_send_message(
                message={
                    "type": "search_update",
                    "text": CALENDAR_MESSAGES["CALENDAR_ANALYSIS_LOADING"],
                    "isBotMessage": True,
                    "expectResponse": False,
                }
            )
            events = await load_past_calendar_events(self.user.id)
            logger.info(f"Loaded {len(events)} calendar events for user {self.user.id}")

            await self.websocket_send_message(
                message={
                    "type": "search_update",
                    "text": CALENDAR_MESSAGES["CALENDAR_ANALYSIS_PROCESSING"].format(count=len(events)),
                    "isBotMessage": True,
                    "expectResponse": False,
                }
            )
            base_user_preference = await get_user_preferences(self.user.id)
            response = await self.streaming_preference(
                existing_preference=base_user_preference.model_dump_json(exclude_none=True),
                message_buffer_strs=[],  # Not passing any messages to avoid hallucination
                events=events,
            )

            response.agent_response = response.agent_response.replace("\n\n", "&NewLine; &NewLine;")
            new_message = AIMessage(content="")
            new_message.additional_kwargs = {"function_call": {"arguments": response.model_dump_json()}}
            new_message.content = response.agent_response
            user_preferences = response.response_all_preferences
            self.do_historical_calendar_analysis = False
            asyncio.create_task(save_user_preferences(self.user.id, user_preferences))
            await self.add_timestamp_message()
            return {"messages": [new_message]}
        except Exception as e:
            logger.error(f"Error in preferences_cal_analysis_agent_model_function: {e}")
            return {
                "messages": [
                    AIMessage(
                        content=cal_analysis_failed_string.replace("\n\n", "&NewLine; &NewLine;").format(
                            user_name=self.user.name
                        ),
                        additional_kwargs={"message_type": "error", "agent_classification": "error"},
                    )
                ]
            }

    async def run(self, message=None, message_type="text", extra_payload=None):
        to_send: list[dict[str, str | bool | None]] = []

        if message is None:
            to_send = await self.get_history_messages()

            if len(self.messages) == 0:
                # load user profile to check if we have access to their calendar
                user_profile: UserProfile | None = await UserProfile.from_user_id(self.user.id)

                has_calendar_access = False
                if user_profile is not None:
                    calendar_api = CalendarProviderManager(user_profile=user_profile)
                    has_calendar_access = calendar_api.has_calendar_access()
                if has_calendar_access:
                    self.do_historical_calendar_analysis = True
                    targeted_opening_string = cal_analysis_opening_string.replace("\n\n", "&NewLine; &NewLine;")
                else:
                    targeted_opening_string = no_cal_analysis_opening_string.replace("\n\n", "&NewLine; &NewLine;")

                # Send opening message over websocket first
                agent_response = AIMessage(content=targeted_opening_string.format(user_name=self.user.name).strip())
                self.history.add_pending_message(agent_response)
                self.messages.append(agent_response)
                await self.websocket_send_message(
                    message={
                        **self.map_websocket_message(agent_response)[0],
                        "expectResponse": False,
                    }
                )

            elif isinstance(self.messages[-1], AIMessage):
                # In the previous session we were waiting for user input,
                # return the entire history from previous session
                to_send[-1]["expectResponse"] = True
                return to_send
            else:
                # In the previous session we were waiting for LLM, wait for LLM
                # new message then return it with the entire history from
                # previous session
                agent_response = self.messages.pop()

                history_message = {
                    "type": "history",
                    "messages": [{**message, "is_history": True} for message in to_send],
                }

                await self.websocket_send_message(message=history_message)
                to_send = []
        else:
            if message_type == "update":
                agent_response = HumanMessage(
                    content=", ".join(map(str, message)),
                    additional_kwargs={"is_card_update": True},
                )
            elif message_type == "silent_prompt":
                agent_response = HumanMessage(content=message)
                # TODO:(chengxuan.wang) maybe front end should send flag instead of comparing message content
                if message == "Calendar permission has been granted":
                    self.do_historical_calendar_analysis = True
                agent_response.additional_kwargs["message_type"] = message_type
            else:
                agent_response = HumanMessage(content=message)
            self.history.add_pending_message(agent_response)
            self.messages.append(agent_response)

        response = await self.graph.ainvoke({"messages": list(self.messages), "input": agent_response}, config=config)

        lastest_model_message = response["messages"][-1]
        self.history.add_pending_message(lastest_model_message)
        self.messages.append(lastest_model_message)

        to_send += self.map_websocket_message(lastest_model_message)

        return to_send

    def map_websocket_message(self, message: BaseMessage) -> list[dict[str, str | bool | None]]:
        is_bot_message: bool = isinstance(message, AIMessage) or isinstance(message, FunctionMessage)

        messages: list[dict[str, Any]] = []
        try:
            message_dict: dict[str, str] = json.loads(
                message.additional_kwargs.get("function_call", {}).get("arguments", "{}")
            )
            # message_dict: dict[str, str] = json.loads(str(message.content)) if "{" in str(message.content) else {}
            messages.append(
                {
                    "type": message_dict.get("message_type", "prompt"),
                    "text": message_dict.get("agent_response") or str(message.content),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""), None
                    ),
                }
            )

        except ValueError:
            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": str(message.content),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""), None
                    ),
                }
            )

        except Exception as e:
            logger.error(f"Error parsing message: {e}")

            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": message.content,
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""), None
                    ),
                }
            )

        return messages

    async def add_timestamp_message(self, new_timestamp: str | None = None, send_ws_message: bool = True):
        if new_timestamp is None:
            new_timestamp = datetime.now(timezone.utc).isoformat()

        # if datetime.fromisoformat(new_timestamp) - self.history.max_created_date > settings.MIN_MESSAGE_TIMESTAMP_DELTA:
        timestamp_message = AIMessage(content="", additional_kwargs={"timestamp": new_timestamp})
        self.history.add_pending_message(timestamp_message)

        if send_ws_message:
            await self.websocket_send_message(
                message={"type": "prompt", "timestamp": timestamp_message.additional_kwargs.get("timestamp")}
            )

    async def get_history_messages(self):
        self.messages = await self.history.persisted_messages

        history: list[list[dict[str, str | bool | None]]] = []
        for message in self.messages:
            if message.additional_kwargs.get("is_card_update", False):
                continue
            elif message.additional_kwargs.get("timestamp", False):
                history.append([{"type": "prompt", "timestamp": message.additional_kwargs.get("timestamp")}])
            else:
                history.append(self.map_websocket_message(message))

        # Filter out timestamp messages
        self.messages = [m for m in self.messages if not m.additional_kwargs.get("timestamp", False)]

        # Flatten the nested list of history messages into a single list for easier processing
        to_send = list(itertools.chain.from_iterable(history))

        # This is history, do not wait for user input
        for msg in to_send:
            msg["expectResponse"] = False

        return to_send
