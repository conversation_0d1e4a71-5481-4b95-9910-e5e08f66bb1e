cal_analysis_opening_string = """
    Hi {user_name}, it's great to meet you! Thanks for letting me access your calendar. Give me a moment to analyze your travel preferences based on your past events.
"""

no_cal_analysis_opening_string = """
    Hi {user_name}! Let's start building your travel preferences. I can do this by asking you serveral questions.\n\nCan you share your key preferences, like your home airport, preferred airlines, class of service, and seat preferences (aisle/window, front/back)?\nFor hotels, do you have favorite brands, styles, or must-have amenities?\n\nThis will help me find the best options for you. If I have any follow-ups, I’ll check in — no need to worry about frequent flyer details just yet!.
"""

cal_analysis_failed_string = """
    There seems to be an issue with accessing your calendar. No worries, I can still do this by asking you serveral questions.\n\nCan you share your key preferences, like your home airport, preferred airlines, class of service, and seat preferences (aisle/window, front/back)?\nFor hotels, do you have favorite brands, styles, or must-have amenities?\n\nThis will help me find the best options for you. If I have any follow-ups, I’ll check in — no need to worry about frequent flyer details just yet!.
"""

preferences_exit_string = """
    Thanks, {user_name}! I’ll use these travel preferences to find the best accommodations tailored to your needs. You can always update your preferences anytime as your travel plans and priorities evolve.
"""

preferences_traveler_prod_message = "Great! What did you learn about me from looking at my calendar event history?"

preferences_pre_analysis_message = "Please give me a moment to build a summary of your travel preferences."
