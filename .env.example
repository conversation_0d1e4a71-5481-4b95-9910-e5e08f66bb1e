OTTO_ENV=DEV | STG | LIVE
IS_LOCAL=true
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
AWS_DEFAULT_REGION=...

# OPTIONAL - these variables will default to Secrets Manager if not set here
# OTTO_APP_SECRET=...
SERVER_DNS="https://api.local.dev.otto-demo.com:8000" # used to generate the path to static assets (images)
# OPENAI_API_KEY=...
# ALLOWED_DOMAINS=... # an array of strings

# PG_HOST=...
# PG_PORT=5432
# PG_USER=...
# PG_PASSWORD=...
# PG_DATABASE=otto_data

# GOOGLE_CLIENT_ID=...
# GOOGLE_CLIENT_SECRET=...

# MONGO_USER=...
# MONGO_PASSWORD=...
MONGO_HOST=...
# MONGO_PORT=...

RUN_CONTEXT=console # make logger console friendly
