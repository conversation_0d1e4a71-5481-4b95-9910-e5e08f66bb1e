import asyncio
import importlib
import inspect
from functools import partial
from typing import Any

import boto3
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel

from server.utils.logger import logger
from server.utils.settings import settings

CloudwatchMetricNames: dict[str, str] = {
    "WSConnectionManager.handle_onboarding_init": "WebSocketRequestHistory",
    "WSConnectionManager.handle_trip_init": "WebSocketRequestHistory",
    "WSConnectionManager.handle_trip_message": "WebSocketRequest",
    CompiledStateGraph.ainvoke.__qualname__: "LLMInvoke",
    "get_microsoft_auth_url": "get_microsoft_auth_url",
    "exchange_code_for_token": "exchange_code_for_token",
    # flight_executor_functions
    "search_flights": "search_flights",
    "validate_flight_search": "validate_flight_search",
    "flight_matcher_spotnana": "flight_matcher_spotnana",
    "validate_flight_v2": "validate_flight_v2",
    "seatselection": "seatselection",
    "flight_credits_check": "flight_credits_check",
    "flight_booking_v2": "flight_booking_v2",
    "save_flight_booking": "save_flight_booking",
    # hotel_executor_functions
    "search_hotels": "search_hotels",
    "validate_hotel": "validate_hotel",
    "hotel_booking": "hotel_booking",
}


def add_class_functions_to_metrics(module_name: str, class_name: str, prefix: str | None = None):
    module = importlib.import_module(module_name)
    cls = getattr(module, class_name)
    count = 0
    for name, _ in inspect.getmembers(cls, predicate=inspect.isfunction):
        if name in {"__init__"}:
            continue
        qualname = f"{class_name}.{name}"
        metric_name = f"{prefix or class_name}:{name}"
        CloudwatchMetricNames[qualname] = metric_name
        count += 1
    if count == 0:
        logger.warning(f"No functions found in {module_name}.{class_name}")


def load_all_class_functions_to_metrics():
    add_class_functions_to_metrics("baml_client.async_client", "BamlAsyncClient")
    add_class_functions_to_metrics("server.services.calendar_api.microsoft_calendar_events", "MicrosoftCalendarEvents")
    add_class_functions_to_metrics("server.utils.spotnana_api", "SpotnanaApi")
    add_class_functions_to_metrics("hotel_agent.booking_dot_com_tools", "BookingTools")
    add_class_functions_to_metrics("server.utils.google_serp_api", "GoogleSerpApi")
    add_class_functions_to_metrics("server.services.authenticate.google", "GoogleAuthenticate")
    add_class_functions_to_metrics("server.services.calendar_api.google_calendar_events", "GoogleCalendarEvents")
    add_class_functions_to_metrics("server.services.calendar_api.microsoft_calendar_events", "MicrosoftCalendarEvents")
    add_class_functions_to_metrics("front_of_house_agent.serp_flight_helper", "SerpFlightSearchHelper")
    add_class_functions_to_metrics(
        "front_of_house_agent.back_of_house_executor.flight_and_hotel_executor", "TripPlanExecutor"
    )


class MetricTag(BaseModel):
    tag_name: str
    tag_value: str


class CloudwatchMetrics:
    def __init__(self) -> None:
        self.cloudwatch_service = boto3.client(service_name="cloudwatch")

    def aws_send_metric_data(self, data: dict[str, dict[str, Any]]):
        loop = asyncio.get_running_loop()
        loop.run_in_executor(None, partial(self.__aws_put_metric_data, data=data))

    def count(self, metric_tags: list[MetricTag], metric_name: str, value: int = 1):
        loop = asyncio.get_running_loop()
        loop.run_in_executor(None, partial(self._count, metric_tags=metric_tags, metric_name=metric_name, value=value))

    def _count(self, metric_tags: list[MetricTag], metric_name: str, value: int = 1):
        try:
            self.cloudwatch_service.put_metric_data(
                Namespace=settings.CLOUDWATCH_METRICS_NAMESPACE,
                MetricData=[
                    {
                        "MetricName": metric_name,
                        "Unit": "Count",
                        "Value": value,
                        "Dimensions": [{"Name": tag.tag_name, "Value": tag.tag_value} for tag in metric_tags],
                    }
                ],
            )
        except Exception as e:
            logger.error(f"Error sending metrics to Cloudwatch: {e}")

    def latency(self, metric_tags: list[MetricTag], metric_name: str, value: float):
        loop = asyncio.get_running_loop()
        loop.run_in_executor(
            None, partial(self._latency, metric_tags=metric_tags, metric_name=metric_name, value=value)
        )

    def _latency(self, metric_tags: list[MetricTag], metric_name: str, value: float):
        try:
            self.cloudwatch_service.put_metric_data(
                Namespace=settings.CLOUDWATCH_METRICS_NAMESPACE,
                MetricData=[
                    {
                        "MetricName": metric_name,
                        "Unit": "Seconds",
                        "Value": value,
                        "Dimensions": [{"Name": tag.tag_name, "Value": tag.tag_value} for tag in metric_tags],
                    }
                ],
            )
        except Exception as e:
            logger.error(f"Error sending metrics to Cloudwatch: {e}")

    def __aws_put_metric_data(self, data: dict[str, dict[str, Any]]):
        try:
            metric_data = []
            for metric_key, metric_dict in data.items():
                for value in metric_dict.values():
                    tag = f"_{value['tag']}" if value["tag"] != "" else ""
                    # Map from the metric key to the metric name, or use the key if no mapping exists
                    metric_name = (
                        CloudwatchMetricNames[metric_key] if metric_key in CloudwatchMetricNames else metric_key
                    )
                    metric_data.append(
                        {
                            "MetricName": metric_name + tag,
                            "Unit": "Seconds",
                            "Value": value["elapsed_time"],
                        }
                    )

            self.cloudwatch_service.put_metric_data(
                Namespace=settings.CLOUDWATCH_METRICS_NAMESPACE, MetricData=metric_data
            )

        except Exception as e:
            logger.error(f"Error sending metrics to Cloudwatch: {e}")


metrics_service = CloudwatchMetrics()
