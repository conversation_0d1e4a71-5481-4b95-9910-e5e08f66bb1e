import re
from urllib.parse import urlparse, urlunparse

from prometheus_client import Counter, Gauge, Histogram

from server.utils.settings import settings

# Define metrics
REQUEST_DURATION = Histogram(
    "outbound_http_request_duration_seconds",
    "Duration of outbound HTTP requests in seconds",
    ["method", "url", "status_code", "environment"],
)

REQUEST_COUNT = Counter(
    "outbound_http_request_total",
    "Total number of outbound HTTP requests",
    ["method", "url", "status_code", "environment"],
)

REQUEST_SIZE = Histogram(
    "outbound_http_request_size_bytes",
    "Size of outbound HTTP requests in bytes",
    ["method", "url", "environment"],
)

RESPONSE_SIZE = Histogram(
    "outbound_http_response_size_bytes",
    "Size of outbound HTTP responses in bytes",
    ["method", "url", "status_code", "environment"],
)

ACTIVE_REQUESTS = Gauge(
    "outbound_http_active_requests",
    "Number of active outbound HTTP requests",
    ["method", "url", "environment"],
)

ipv4_pattern = re.compile(r"^(?:\d{1,3}\.){3}\d{1,3}$")
ipv6_pattern = re.compile(r"^([0-9a-fA-F]{0,4}:){2,7}[0-9a-fA-F]{0,4}$")


def track_active_requests(method: str, url: str, should_decrement: bool = False):
    environment = settings.OTTO_ENV.upper()
    processed_url = process_url(url)
    if should_decrement:
        ACTIVE_REQUESTS.labels(method=method, url=processed_url, environment=environment).dec()
    else:
        ACTIVE_REQUESTS.labels(method=method, url=processed_url, environment=environment).inc()


def process_url(url: str) -> str:
    """
    Process URL to reduce cardinality in metrics by:
    1. Removing query parameters
    2. Removing fragments
    3. Normalizing paths with IDs to use placeholders
    4. Removing trailing slashes
    """
    try:
        # Parse the URL
        parsed = urlparse(url)

        # Process the path to replace IDs with placeholders
        path_parts = parsed.path.strip("/").split("/")
        processed_parts = []

        for part in path_parts:
            # Replace UUID-like strings with {id}
            if re.match(r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", part, re.IGNORECASE):
                processed_parts.append("{id}")
            # Replace numeric IDs with {id}
            elif part.isdigit():
                processed_parts.append("{id}")
            # Replace other common ID patterns
            elif re.match(r"^[A-Z0-9]{6,}$", part):  # Common ID patterns like booking codes
                processed_parts.append("{id}")
            # Replace IP addresses with {ip_address}
            elif ipv4_pattern.match(part) or ipv6_pattern.match(part):
                processed_parts.append("{ip_address}")
            else:
                processed_parts.append(part)

        # Reconstruct the URL without query and fragment
        processed_path = "/".join(processed_parts)
        if processed_path:
            processed_path = "/" + processed_path

        # Reconstruct the URL
        processed = urlunparse(
            (
                parsed.scheme,
                parsed.netloc,
                processed_path,
                "",  # params
                "",  # query
                "",  # fragment
            )
        )

        return processed
    except Exception:
        # If URL processing fails, return a simplified version
        return url.split("?")[0].split("#")[0]


# Helper function to track request metrics
async def track_request_metrics(
    method: str, url: str, status_code: int, duration: float, request_size: int, response_size: int
):
    # Process the URL to reduce cardinality
    processed_url = process_url(url)
    environment = settings.OTTO_ENV.upper()

    # Record request duration
    REQUEST_DURATION.labels(
        method=method, url=processed_url, status_code=str(status_code), environment=environment
    ).observe(duration)

    # Increment request count
    REQUEST_COUNT.labels(method=method, url=processed_url, status_code=str(status_code), environment=environment).inc()

    # Record request size
    REQUEST_SIZE.labels(method=method, url=processed_url, environment=environment).observe(request_size)

    # Record response size
    RESPONSE_SIZE.labels(
        method=method, url=processed_url, status_code=str(status_code), environment=environment
    ).observe(response_size)
