import json
from typing import List

from google_auth_oauthlib.flow import Flow

from server.utils.logger import logger
from server.utils.settings import settings

GOOGLE_CLIENT_CONFIG = {
    "web": {
        "client_id": settings.GOOGLE_CLIENT_ID,
        "client_secret": settings.GOOGLE_CLIENT_SECRET,
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "redirect_uris": ["postmessage"],
    }
}

IDENTITY_SCOPES = [
    "openid",
    "profile",
    "email",
]

# Updated scopes to match exactly what's expected
CALENDAR_SCOPES = [
    "https://www.googleapis.com/auth/calendar.events",
]


def get_google_auth_urls_by_scope(login_hint: str, scopes: List[str]):
    flow = Flow.from_client_config(client_config=GOOGLE_CLIENT_CONFIG, scopes=scopes)

    flow.redirect_uri = f"{settings.CLIENT_DOMAIN}onboarding"

    auth_url, _ = flow.authorization_url(access_type="offline", include_granted_scopes="true", login_hint=login_hint)
    logger.info(f"Backend Generated by-scope Auth URL: {auth_url}")
    return auth_url


def get_google_auth_urls(login_hint: str):
    try:
        auth_urls: List[str] = []
        auth_url_login = get_google_auth_urls_by_scope(login_hint=login_hint, scopes=IDENTITY_SCOPES)
        auth_urls.append(auth_url_login)
        accumulated_calendar_scopes = []
        for scope in CALENDAR_SCOPES:
            accumulated_calendar_scopes.append(scope)
            auth_url = get_google_auth_urls_by_scope(
                login_hint=login_hint,
                scopes=IDENTITY_SCOPES + accumulated_calendar_scopes,
            )
            auth_urls.append(auth_url)
        logger.info(f"Backend Generated Auth URLs: \n{json.dumps(auth_urls)}")
        return auth_urls

    except Exception:
        return ""


def get_google_auth_url(login_hint: str, redirect_uri="onboarding"):
    try:
        flow = Flow.from_client_config(client_config=GOOGLE_CLIENT_CONFIG, scopes=CALENDAR_SCOPES)

        flow.redirect_uri = f"{settings.CLIENT_DOMAIN}{redirect_uri}"

        auth_url, _ = flow.authorization_url(
            access_type="offline",
            include_granted_scopes="true",
            login_hint=login_hint,
            prompt="consent",  # This forces the consent screen every time
        )
        logger.info(f"Backend Generated All-in-one Auth URL: {auth_url}")

        return auth_url

    except Exception:
        return ""
