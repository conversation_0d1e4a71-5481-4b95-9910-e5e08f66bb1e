"""
Centralized constants for WebSocket message strings used across the application.
"""

SEARCH_UPDATE_MESSAGES = {
    "TRIP_PAUSING": "Saving your trip so that you can pick back up later.",
    "FLIGHT_STATUS_CHECK": "Let me check the flight status for you. Please wait.",
    "FLIGHT_EXCHANGE_CHECK": "I'm checking if your flight can be changed. One moment.",
    "FLIGHT_EXCHANGE_OUTBOUND": "I'm looking at outbound flights for you. This will take a minute.",
    "FLIGHT_EXCHANGE_RETURN": "I'm looking at return flights for you now. Just a moment longer.",
    "FLIGHT_EXCHANGE_CONFIRM": "I'm confirming your flights. Please hold a moment.",
    "FLIGHT_EXCHANGE_BOOKING": "I'm booking your updated flight now. Hold for a moment, please.",
    "FLIGHT_BOOKING_WAIT": "I'm waiting to get the flight booking confirmation. It shouldn't be too long.",
    "HOTEL_BOOKING_WAIT": "I'm waiting to get the hotel reservation confirmation. It shouldn't be too long.",
    "FLIGHT_VALIDATION_LOCKING": "Great, I'm locking in your flights now. It will just take a minute while I confirm everything — thanks for your patience.",
    "FLIGHT_VALIDATION_LOCKING_ALT": "Great, I'm locking in your flights now. It will just take a minute while I confirm everything  -  thanks for your patience.",
    "FLIGHT_BOOKING_PROCESS": "I am booking your flight for you now. This process can take up to two minutes.",
    "FLIGHT_ITINERARY_RECREATE": "Unfortunately, too much time has passed since we looked at this itinerary. Let me see if I can recreate it for you.",
    "FLIGHT_BOOKING_FINALIZE": "I'm finalizing the booking now. One moment and I'll have your flight confirmation booking code for you.",
    "SEAT_SELECTION_LOYALTY": "I'm checking on available seats, keeping in mind your loyalty status with {airlines}.",
    "SEAT_SELECTION_GENERAL": "I'm checking on available seats, and will pick the best one for you.",
    "FLIGHT_UNAVAILABLE": "Unfortunately, these flights are no longer available. Let me help you find new options.",
    "HOTEL_VALIDATION_RETRY": "I need to try that again. The system came back with a validation error. Retrying now.",
    "FLIGHT_VALIDATION_SEAT_NOT_AVAILABLE": "I'm sorry, seat ({original_seat}) is no longer available{flight_number_phrase}. Don't worry - you'll get a seat assignment at check-in. I'll continue with the reservation.",
    "FLIGHT_CHECKOUT_NO_SEAT": "I'm sorry, I can't find any available seats{loyalty_msg}. Don't worry - you'll get a seat assignment at check-in. I'll continue with the reservation.",
    "FLIGHT_CHECKOUT_SEAT_W_LOYALTY": "Great news - based on your airline status, I was able to find you some upgraded seats, like {seat_number}.",
    "FLIGHT_CHECKOUT_SEAT_WO_LOYALTY": "I was able to secure you seat {seat_number}, which matches your preference for {preferred_seat_types}.",
}

FLIGHT_SKELETON_MESSAGES = {
    "INITIAL_SEARCH": "I'm looking through real-time flight options for you. I estimate this will take ~{seconds} seconds.",
    "MULTI_CITY_SEARCH": "I'm searching for the best options for this multi-leg trip. This particular search may require extra time. Please give me 1-2 minutes.",
    "OUTBOUND_FOUND": "I found {count} outbound flights for us to consider. I'm grabbing the return flights for you now and then we can look at pricing for the whole trip.",
    "DEEPER_SEARCH": "I found several options for your flight, but none that fit your preferences, so I'm searching a bit deeper for some more flight options.",
    "NARROWING_DOWN": "I found {count} options for your flight. I'm now narrowing them down to the ones that best match your preferences.",
    "NO_FLIGHTS": "Unfortunately I can't find any flights that match your search dates and destination. Can you adjust the dates or cities you're travelling between?",
    "SEARCH_FALLBACK": "This is taking a bit longer than it usually does. I'm still checking for available flights. Please hold on.",
}

HOTEL_SKELETON_MESSAGES = {
    "INITIAL_SEARCH": "Searching for available hotels now. I'll come back shortly with the best options for you.",
    "HOTEL_UNAVAILABLE": "Sorry about this, but the selected hotel isn't available for your dates. I'm running a quick search for similar options.",
    "RADIUS_EXPAND": "I don't see any available rooms within your {radius:.2f} miles radius filter. I'm going to search a bit farther out for you.",
    "NARROWING_DOWN": "I found {count} hotels. I'm now narrowing them down to the ones that best match your preferences.",
    "ROOM_DETAILS": "Almost done. I'm now fetching the room details like rate, bed size, view for you.",
}

SEAT_CHANGE_MESSAGES = {
    "SEAT_CHANGE_START": "I'm starting to change your seat now. Give me about a minute while I confirm the details for you.",
    "SEAT_AVAILABILITY_CHECK": "One moment while I check available seats for you. It can take me up to a minute.",
}

CANCELLATION_MESSAGES = {
    "FLIGHT_CANCELLATION_CHECK": "Let me check to see if the flight can be canceled. This can take a moment. Thanks for your patience.",
    "FLIGHT_CANCELLATION_SUBMIT": "Canceling your flight booking  -  this will just take a minute.",
    "HOTEL_CANCELLATION_CHECK": "I'm checking to see if there are any penalties for cancelling the hotel. Please hold a moment.",
    "HOTEL_CANCELLATION_SUBMIT": "Canceling your hotel reservation  -  this will just take a minute.",
}

CALENDAR_MESSAGES = {
    "CALENDAR_ANALYSIS_LOADING": "I am loading your calendar events to analyze your travel preferences.",
    "CALENDAR_ANALYSIS_PROCESSING": "I have loaded your {count} calendar events. Analyzing them now...",
    "CALENDAR_ACCESS_EXISTS": "I already have your calendar access. analyzing your calendar, please wait for a moment.",
}

TRIP_MESSAGES = {
    "REDIRECT_TO_TRIP": "Auto redirecting you to the new trip...",
    "FORK_NEW_TRIP_OPENING": "Let’s start your next trip. Can you tell me the dates, destination, or any other details you’d like me to plan around?",
    "FORK_NEW_TRIP_RESPONSE": "Looks like you're planning a new trip. Let's move the conversation to the new trip I created for you.",
}

SKELETON_MESSAGE_TYPES = ["search_update", "flights_skeleton_async", "hotels_skeleton_async"]
