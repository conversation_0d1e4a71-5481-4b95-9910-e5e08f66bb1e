import json
from typing import Any, Dict

import backoff
import boto3
from botocore.exceptions import BotoCoreError, ClientError

from server.utils.logger import logger

# Firehose delivery stream name, TODO: move to aws secrets manager
FIREHOSE_STREAM_NAME = "PUT-ICE-YDJOR"


class FirehoseClient:
    _instance = None

    def __new__(cls):
        """Implement singleton pattern."""
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.region = "us-east-2"
        self.firehose_client = boto3.client("firehose", region_name=self.region)

    @staticmethod
    def is_retriable_boto_exception(exc: Exception) -> bool:
        # Some exceptions in botocore have a 'retriable' property
        if hasattr(exc, "retriable"):
            return getattr(exc, "retriable")

        # Handle ClientError manually
        if isinstance(exc, ClientError):
            error_code = exc.response.get("Error", {}).get("Code", "")
            return error_code in {
                "ProvisionedThroughputExceededException",
                "ThrottlingException",
                "ServiceUnavailableException",
                "InternalFailure",
                "RequestLimitExceeded",
            }

        return False

    def __create_record_entry(self, data: dict) -> dict:
        return {"Data": json.dumps(data)}

    @backoff.on_exception(
        backoff.constant,
        (BotoCoreError, ClientError),
        max_tries=2,
        jitter=backoff.full_jitter,
        giveup=lambda e: not FirehoseClient.is_retriable_boto_exception(e),
    )
    async def put_record_to_firehose(self, data: dict[str, Any]):
        try:
            self.firehose_client.put_record(
                DeliveryStreamName=FIREHOSE_STREAM_NAME,
                Record=self.__create_record_entry(data),
            )
        except Exception as e:
            if FirehoseClient.is_retriable_boto_exception(e):
                logger.warning(f"Retrying record: {data}. Error: {e}")
            else:
                logger.error(f"Fail record: {data}. Error: {e}")
            raise

    @backoff.on_exception(
        backoff.expo,
        (BotoCoreError, ClientError),
        max_tries=2,
        jitter=backoff.full_jitter,
        giveup=lambda e: not FirehoseClient.is_retriable_boto_exception(e),
    )
    async def send_batch_to_firehose(self, records):
        self.firehose_client.put_record_batch(
            DeliveryStreamName=FIREHOSE_STREAM_NAME, Records=[{"Data": json.dumps(record) + "\n"} for record in records]
        )


firehose = FirehoseClient()
