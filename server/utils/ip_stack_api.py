from typing import Optional

from fastapi import HTTPException, Request
from pydantic import BaseModel

from baml_client.async_client import b
from server.services.user.user_preferences import get_user_preferences, save_user_preferences
from server.utils.async_requests import make_get_request
from server.utils.logger import logger
from server.utils.settings import settings


class IpStackServiceError(Exception):
    """Custom exception for IpStack service errors."""

    def __init__(self, message: str):
        super().__init__(message)
        self.message = message


class IpInfo(BaseModel):
    ip: str
    continent_code: Optional[str]
    continent_name: Optional[str]
    country_code: Optional[str]
    country_name: Optional[str]
    region_code: Optional[str]
    region_name: Optional[str]
    city: Optional[str]
    zip: Optional[str]


class IpStackApi:
    """
    API client for interacting with the IpStack API.
    """

    def __init__(self) -> None:
        self.base_url = "http://api.ipstack.com"
        self.api_key = settings.IPSTACK_ACCESS_KEY

    async def get_ip_info(self, ip_address: str) -> IpInfo:
        """
        Get IP information from IpStack API.
        """
        params = {"access_key": self.api_key, "output": "json"}
        url = f"{self.base_url}/{ip_address}"
        try:
            result = await make_get_request(url, headers={"Content-Type": "application/json"}, params=params)
            return IpInfo(**result)
        except Exception as e:
            logger.error(f"Error resolving ip address with IpStack: {str(e)}")
            if isinstance(e, HTTPException):
                if e.status_code >= 500:
                    raise IpStackServiceError("IpStack service error. Please try again later.")
            raise


def get_client_ip(request: Request) -> str:
    """
    Get the client IP address from the request.
    """
    forwarded_for_ips = request.headers.get("x-forwarded-for", "")
    logger.info(
        f"Client Forwarded For IPs: {forwarded_for_ips}, Client Host: {request.client.host if request.client else 'unknown'}"
    )
    client_ip = ""
    if forwarded_for_ips:
        client_ip = forwarded_for_ips.split(",")[0].strip()
    elif request.client:
        client_ip = request.client.host

    return client_ip


async def guess_and_update_home_airport(client_ip: str, user_id: int):
    if not client_ip:
        logger.warning(f"Client IP is empty. Cannot determine home airport for user {user_id}.")
        return
    try:
        ip_info = await ip_stack_api.get_ip_info(client_ip)
        logger.info(f"IP Info retrieved for determining home airport for user {user_id}: {ip_info}")
        if ip_info.city:
            home_airport = await b.DetermineHomeAirportFromIpInfo(
                ip_info.model_dump_json(), baml_options={"collector": logger.collector}
            )
            logger.log_baml()
            logger.info(f"Determined the home airport for user {user_id}: {home_airport.model_dump_json()}")
            if home_airport.airport:
                final_code = home_airport.airport
                metro_area_code = settings.AIRPORT_TO_METRO_MAP.get(home_airport.airport.upper(), None)
                if metro_area_code:
                    final_code = metro_area_code

                user_preferences = await get_user_preferences(user_id)
                copied_user_preferences = user_preferences.model_copy()
                copied_user_preferences.preferred_home_airport = final_code
                await save_user_preferences(user_id, copied_user_preferences)
                logger.info(f"Preferred Home Airport updated to {final_code}, for user {user_id}")
        else:
            logger.warning(f"City information is not available in the IP info for user {user_id}.")
    except Exception as e:
        logger.error(f"Error determining home airport for user {user_id}: {e}")


ip_stack_api = IpStackApi()
