import os

from server.utils.async_requests import make_get_request
from server.utils.logger import logger
from server.utils.mongo_connector import app_config_collection
from server.utils.settings import settings


async def update_cron_container_id():
    container_id: str | None = await get_container_id()
    if container_id is not None:
        await app_config_collection.update_one(
            {"env": settings.OTTO_ENV}, {"$set": {"cron_container_id": container_id}}, upsert=True
        )


async def should_cron_run():
    container_id: str | None = await get_container_id()
    cron_container_id: str | None = await get_config_container_id()

    if container_id is not None and cron_container_id is not None and container_id == cron_container_id:
        return True

    return False


async def get_container_id():
    metadata_url = os.getenv("ECS_CONTAINER_METADATA_URI_V4")

    if metadata_url:
        metadata = await make_get_request(metadata_url)
        container_id = metadata.get("DockerId")
        logger.info(f"Container ID: {container_id}")
        return container_id
    else:
        logger.info("ECS_CONTAINER_METADATA_URI_V4 is not available.")
        return None


async def get_config_container_id():
    response: dict[str, str] | None = await app_config_collection.find_one({"env": settings.OTTO_ENV})

    if response is not None:
        return response.get("cron_container_id")

    return None
