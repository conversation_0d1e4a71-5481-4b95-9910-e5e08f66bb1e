import json
from functools import partial
from typing import Any, Coroutine

from fastapi import WebSocket

from server.utils.logger import logger


async def no_op(websocket: WebSocket | None, message: dict[str, Any]) -> None:
    logger.warning(f"No websocket_send_message function provided. Dropping message: {json.dumps(message)}")


partial_no_op: partial[Coroutine[Any, Any, None]] = partial(no_op, None)


async def no_op_with_no_logger(websocket: WebSocket | None, message: dict[str, Any]) -> None:
    pass


partial_no_op_with_without_logger: partial[Coroutine[Any, Any, None]] = partial(no_op_with_no_logger, None)
