import asyncio
from datetime import timed<PERSON><PERSON>
from functools import wraps

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request

from server.utils.remote_cache import RemoteCache


class RateLimiter:
    """
    Rate limiter implementation for distributed system.

    Using Redis as the underlying cache implementation.
    """

    def __init__(self):
        self.cache = RemoteCache()

    def create_cache_key(self, endpoint: str, suffix: str):
        return f"rate_limit:{endpoint}:{suffix}"

    async def is_allowed(self, request: Request, limit: int, window: timedelta, key_func):
        """Check if the request is within the allowed rate limit."""
        key = await key_func(request) if asyncio.iscoroutinefunction(key_func) else key_func(request)
        endpoint = request.url.path

        full_key = self.create_cache_key(endpoint, key)
        cache_value = await self.cache.incr(full_key, expire=window)

        if cache_value > limit:
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

    def limit_request(self, limit: int, window: timedelta, key_func=None):
        """Decorator to apply rate limiting with custom limits, window (as timedelta), and key."""
        key_func = key_func or (lambda request: request.client.host if request.client is not None else "")

        def decorator(func):
            @wraps(func)
            async def wrapper(request: Request, *args, **kwargs):
                await self.is_allowed(request, limit, window, key_func)
                return await func(request, *args, **kwargs)

            return wrapper

        return decorator

    async def reset(self, full_key: str):
        await self.cache.delete(full_key)
