from datetime import datetime, timedelta, timezone
from functools import partial
from typing import Any, <PERSON>wai<PERSON>, Callable, Dict, Union

import jwt
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, WebSocket, WebSocketException
from fastapi.security import OAuth2PasswordBearer
from starlette.status import HTTP_401_UNAUTHORIZED, HTTP_403_FORBIDDEN

from server.services.user.user_activity import update_otto_ruid
from server.utils.logger import Logger, logger
from server.utils.settings import settings

SECRET_TYPE = Union[str, bytes]

InvalidCredentialsException = HTTPException(
    status_code=HTTP_401_UNAUTHORIZED,
    detail="Invalid credentials",
    headers={"WWW-Authenticate": "Bearer"},
)

PermissionDeniedException = HTTPException(
    status_code=HTTP_403_FORBIDDEN,
    detail="Permission Denied",
    headers={"WWW-Authenticate": "Bearer"},
)

InvalidCredentialsExceptionWS = WebSocketException(
    code=HTTP_401_UNAUTHORIZED,
    reason="Invalid credentials",
)


class TokenType:
    accessToken = "access_token"
    refreshToken = "refresh_token"


class CustomLoginManager(OAuth2PasswordBearer):
    def __init__(
        self,
        secret: Union[SECRET_TYPE, Dict[str, SECRET_TYPE]],
        token_url: str,
        scopes: Dict[str, str] | None = None,
        algorithm="HS256",
        cookie_name: str = "access_token",
        refresh_cookie_name: str = "refresh_token",
    ):
        self.secret = secret
        self.cookie_name = cookie_name
        self.refresh_cookie_name = refresh_cookie_name
        self.algorithm = algorithm
        self._user_callback: partial[Awaitable[Any]] | None = None

        super().__init__(tokenUrl=token_url, auto_error=False, scopes=scopes)

    async def __call__(self, request: Request | WebSocket) -> Any:
        try:
            token = await self._get_token(request)
            payload = self.decode_jwt(token, self.cookie_name)
        except jwt.exceptions.ExpiredSignatureError:
            token = self._refresh_token(request)
            payload = self.decode_jwt(token, self.cookie_name)
        except Exception as e:
            # Log the exception
            logger.error(f"An error occurred: {e}")
            self.raise_invalid_credentials_exception(request)

        current_user = await self._get_current_user(payload, request)
        otto_ruid = request.cookies.get("Otto")

        if otto_ruid is not None:
            await update_otto_ruid(str(current_user.id), otto_ruid)

        Logger.bind_log_context(user_id=current_user.id)

        return current_user

    async def _get_token(self, request: Request | WebSocket):
        if isinstance(request, WebSocket):
            sec_websocket_protocol = request.headers.get("sec-websocket-protocol")
            assert sec_websocket_protocol is not None, "Missing sec-websocket-protocol header"
            header_values = sec_websocket_protocol.split(", ")
            it = iter(header_values)
            header_values_tuples = [*zip(it, it)]
            header_values_dict = dict(header_values_tuples)
            token = header_values_dict.get("Authorization")
        else:
            token = request.cookies.get(self.cookie_name)

        if not token:
            self.raise_invalid_credentials_exception(request)

        return token

    async def _get_current_user(self, payload: Dict[str, Any], request: Request | WebSocket):
        # the identifier should be stored under the sub (subject) key
        user_identifier = payload.get("sub")
        if user_identifier is None:
            self.raise_invalid_credentials_exception(request)

        user = await self._load_user(user_identifier)
        if user is None:
            self.raise_invalid_credentials_exception(request)

        return user

    async def _load_user(self, identifier: Any):
        if self._user_callback is None:
            raise Exception("Missing user_loader callback")

        user = await self._user_callback(identifier)

        return user

    def _get_refresh_token(self, request: Request | WebSocket):
        return request.cookies.get(self.refresh_cookie_name)

    def _refresh_token(self, request: Request | WebSocket):
        try:
            old_refresh_token = self._get_refresh_token(request)
            assert old_refresh_token is not None, "Missing refresh token when refreshing access token"
            payload = self.decode_jwt(old_refresh_token, self.refresh_cookie_name)
            access_token, refresh_token = self.create_custom_jwt(payload)

            request.state.access_token = access_token
            request.state.refresh_token = refresh_token

            return access_token
        except BaseException:
            self.raise_invalid_credentials_exception(request)

    def encode_jwt(self, payload: dict[str, Any], expires: timedelta, cookie_name: str) -> str:
        payload.update({"exp": datetime.now(timezone.utc) + expires})
        return jwt.encode(payload, cookie_name, algorithm=self.algorithm)

    def decode_jwt(self, token: str, cookie_name: str) -> dict[str, Any]:
        return jwt.decode(token, cookie_name, algorithms=[self.algorithm])

    def create_custom_jwt(self, payload: dict[str, str]) -> tuple[str, str]:
        """
        Create the access token and refresh token based on the user payload.
        """

        payload["token_type"] = TokenType.accessToken
        access_token = self.encode_jwt(payload=payload, expires=timedelta(days=1), cookie_name=self.cookie_name)

        payload["token_type"] = TokenType.refreshToken
        refresh_token = self.encode_jwt(
            payload=payload,
            expires=settings.REFRESH_TOKEN_LIFESPAN,
            cookie_name=self.refresh_cookie_name,
        )

        return access_token, refresh_token

    def user_loader(self, *args, **kwargs) -> Union[Callable, Callable[..., Awaitable]]:
        def decorator(callback: Union[Callable, Callable[..., Awaitable]]):
            """
            The actual setter of the load_user callback
            Args:
                callback (Callable or Awaitable): The callback which returns the user

            Returns:
                Partial of the callback with given args and keyword arguments already set
            """
            self._user_callback = partial(callback, *args, **kwargs)
            return callback

        return decorator

    def raise_invalid_credentials_exception(self, request: Request | WebSocket):
        if isinstance(request, WebSocket):
            raise InvalidCredentialsExceptionWS

        raise InvalidCredentialsException


class CustomLoginManagerWS(CustomLoginManager):
    async def __call__(self, request: Request = None, websocket: WebSocket = None):  # type: ignore
        return await super().__call__(request or websocket)


class AdminLoginManager(CustomLoginManager):
    async def __call__(self, request: Request = None, websocket: WebSocket = None):  # type: ignore
        return await super().__call__(request or websocket)


class OrganizationLoginManager(CustomLoginManager):
    async def __call__(self, request: Request = None, websocket: WebSocket = None):  # type: ignore
        return await super().__call__(request or websocket)


class CustomLoginManagerWSWithHeaderTokens(CustomLoginManagerWS):
    async def _get_token(self, request: Request | WebSocket):
        token = request.headers.get("X-Access-Token")

        if not token:
            self.raise_invalid_credentials_exception(request)

        return token

    def _get_refresh_token(self, request: Request | WebSocket):
        return request.headers.get("X-Refresh-Token")
