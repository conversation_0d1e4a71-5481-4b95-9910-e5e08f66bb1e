from prometheus_client import Histogram

from server.utils.cloudwatch_metrics import CloudwatchMetricNames
from server.utils.logger import logger

profiler_histogram = Histogram("custom_profiler_duration_seconds", "Duration tracked by custom profiler", ["tag"])


def prometheus_record_profiler_metric(profiler_data: dict):
    try:
        for metric_key, metric_dict in profiler_data.items():
            for value in metric_dict.values():
                tag = f"_{value['tag']}" if value["tag"] != "" else ""

                # Map from the metric key to the metric name, or use the key if no mapping exists
                metric_name = CloudwatchMetricNames[metric_key] if metric_key in CloudwatchMetricNames else metric_key

                profiler_histogram.labels(tag=metric_name + tag).observe(value["elapsed_time"])
                logger.info(f"Prometheus metrics logged, tag {tag}")

    except Exception as e:
        logger.error(f"Error sending metrics to Prometheus: {e}")
