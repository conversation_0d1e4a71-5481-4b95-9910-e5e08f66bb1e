import math
import re
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

from server.utils.logger import logger


def phase_shift_dates(base_date: datetime, req: str) -> str:
    # Determine today's date in Pacific Time (midnight)
    current_date = datetime.now(ZoneInfo("America/Los_Angeles")).replace(hour=0, minute=0, second=0, microsecond=0)

    # Compute the day-offset from base_date to current_date,
    # rounded up to the next multiple of 7
    delta_days = (current_date - base_date).days
    weeks_offset = math.ceil(delta_days / 7)
    rounded_offset = weeks_offset * 7

    # Pattern to match month abbreviations and day numbers with optional ordinal suffixes
    pattern = r"\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{1,2})(st|nd|rd|th)?\b"

    out = req
    # Find all matches in the request
    matches = list(re.finditer(pattern, out))

    # Process matches in reverse order to avoid offset issues when replacing text
    for match in reversed(matches):
        try:
            mon_abbr, day = match.group(1), match.group(2)
            orig = datetime.strptime(f"{mon_abbr} {day} 2025", "%b %d %Y")
            orig = orig.replace(tzinfo=ZoneInfo("America/Los_Angeles"))

            new_dt = orig + timedelta(days=rounded_offset)

            day_num = new_dt.day
            if 11 <= day_num % 100 <= 13:
                suffix = "th"
            else:
                suffix = {1: "st", 2: "nd", 3: "rd"}.get(day_num % 10, "th")
            new_text = new_dt.strftime(f"%b {day_num}{suffix}")

            out = out[: match.start()] + new_text + out[match.end() :]
        except Exception as e:
            print(f"Error processing date in '{req}': {e}")
            logger.error(f"Error processing date in '{req}': {e}")
            return ""

    return out
