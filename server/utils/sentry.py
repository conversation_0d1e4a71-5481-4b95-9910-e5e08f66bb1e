import asyncio
import datetime
import ssl
import uuid

import aiohttp
import certifi

from server.utils.logger import logger
from server.utils.settings import settings


def not_invited_login_to_sentry(email: str, method: str) -> None:
    """
    Log non-invited user login attempt
    """
    asyncio.create_task(send_event_to_sentry(f"Non-invited user {method} login attempt: {email}", email))


def beta_login_to_sentry(email: str, method: str) -> None:
    """
    Log beta user login attempt
    """
    asyncio.create_task(send_event_to_sentry(f"Beta user logged in via {method}: {email}", email))


async def send_event_to_sentry(message: str, user_email: str | None = None) -> None:
    endpoint = f"https://sentry.io/api/{settings.SENTRY_PROJECT_ID}/store/"
    public_key = settings.SENTRY_KEY

    headers = {
        "Content-Type": "application/json",
        "X-Sentry-Auth": (f"Sentry sentry_version=7, sentry_client=raven-python/1.0, sentry_key={public_key}"),
    }

    event_data = {
        "event_id": str(uuid.uuid4().hex),
        "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat(),
        "level": "info",
        "logger": "",
        "platform": "python",
        "environment": settings.OTTO_ENV,
        "sdk": {"name": "custom-integration", "version": "1.0"},
        "message": message,
        "user": {"email": user_email or "anonymous"},
    }

    ssl_context = ssl.create_default_context(cafile=certifi.where())

    async with aiohttp.ClientSession() as session:
        async with session.post(endpoint, headers=headers, json=event_data, ssl=ssl_context) as response:
            if response.status == 200:
                logger.info("Event sent to Sentry successfully.")
            else:
                logger.error(f"Failed to send event to Sentry: {response.reason}")
