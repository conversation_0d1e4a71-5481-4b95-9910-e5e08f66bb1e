from datetime import datetime, timedelta
from pathlib import Path
from typing import Any

from server.schemas.spotnana.credit_card import CreditCard
from server.schemas.spotnana.user import CreateSpotnanaUser, MembershipInfos
from server.schemas.user_profile.payment_information import PaymentSource
from server.utils.async_requests import (
    make_delete_request,
    make_get_request,
    make_post_request,
    make_put_request,
)
from server.utils.settings import settings


class SpotnanaApi:
    def __init__(self) -> None:
        self.token_info = {}

    async def authorize(self):
        if self.is_token_expired():
            res = await make_post_request(
                f"{settings.SPOTNANA_HOST}/get-auth-token",
                headers={"Content-Type": "application/json"},
                data={
                    "clientId": settings.SPOTNANA_CLIENT_ID,
                    "clientSecret": settings.SPOTNANA_CLIENT_SECRET,
                },
            )
            self.token_info = {
                "token": res["token"],
                "expire": (datetime.now() + timedelta(seconds=int(res["expiryTimeInSeconds"]))).timestamp(),
            }

    def is_token_expired(self):
        if datetime.now().timestamp() >= self.token_info.get("expire", 0):
            return True

        return False

    async def get_user_by_email(self, user_email: str):
        await self.authorize()

        res = await make_get_request(
            f"{settings.SPOTNANA_HOST}/v2/users",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
            },
            params={"companyId": settings.SPOTNANA_COMPANY_GUID, "email": user_email},
        )
        return res

    async def get_user_details(self, user_id: str):
        await self.authorize()

        res = await make_get_request(
            f"{settings.SPOTNANA_HOST}/v2/users/{user_id}",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
            },
        )
        return res

    async def create_user(self, spotnana_user: CreateSpotnanaUser):
        await self.authorize()

        return await make_post_request(
            f"{settings.SPOTNANA_HOST}/v2/users",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=spotnana_user.model_dump(),
        )

    async def update_user(self, user_id: str, spotnana_user: CreateSpotnanaUser):
        await self.authorize()

        await make_put_request(
            f"{settings.SPOTNANA_HOST}/v2/users/{user_id}",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=spotnana_user.model_dump(),
        )

    async def update_user_roles(self, user_id: str, user_roles_request: dict[str, Any]):
        await self.authorize()

        await make_put_request(
            f"{settings.SPOTNANA_HOST}/v2/users/{user_id}/roles",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=user_roles_request,
        )

    async def get_user_roles(self, user_id: str):
        await self.authorize()

        res = await make_get_request(
            f"{settings.SPOTNANA_HOST}/v2/users/{user_id}/roles",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
            },
        )
        return res

    async def delete_user(self, user_id: str):
        await self.authorize()

        await make_delete_request(
            f"{settings.SPOTNANA_HOST}/v2/users/{user_id}",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
            },
        )

    async def get_traveler_by_email(self, user_email: str):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v2/traveler/search",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data={
                "organizationId": {"id": settings.SPOTNANA_COMPANY_GUID},
                "email": user_email,
            },
        )
        return res

    async def traveler_read(self, user_org_id):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v1/traveler/read",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data={"userOrgId": user_org_id},
        )
        return res

    async def create_credit_card(self, card_info: CreditCard):
        await self.authorize()
        ssl_certificate_filename = (
            "sandbox.pem"
            if settings.OTTO_VGS_OUTBOUND_HOST and "sandbox" in settings.OTTO_VGS_OUTBOUND_HOST
            else "live.pem"
        )

        res = await make_post_request(
            f"{settings.SPOTNANA_VGS_HOST}/v1/credit-card-create",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=card_info.model_dump(),
            proxy=f"{settings.OTTO_VGS_OUTBOUND_HOST}",
            ssl_cert_path=str(
                Path(__file__).parent.parent.parent.joinpath("vgs_cert").joinpath(ssl_certificate_filename)
            ),
        )

        return res

    async def confirm_credit_card(self, payload):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v1/confirm-card",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=payload,
        )
        return res

    async def delete_credit_card(self, card_id, user_org_id):
        await self.authorize()

        await make_post_request(
            f"{settings.SPOTNANA_HOST}/v1/credit-card-delete",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data={"id": card_id, "userOrgId": user_org_id},
        )

    async def get_trip_details(self, trip_id: str):
        await self.authorize()

        res = await make_get_request(
            f"{settings.SPOTNANA_HOST}/v3/trips/{trip_id}/detail",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
            },
        )
        return res

    async def update_user_membership_info(self, user_id: str, membership_info: MembershipInfos):
        await self.authorize()

        await make_put_request(
            f"{settings.SPOTNANA_HOST}/v2/users/{user_id}/membership-info",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=membership_info.model_dump(),
        )

    async def search_hotels(self, search_hotel_params: dict[str, Any]):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v2/hotel/search",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=search_hotel_params,
        )
        return res

    async def get_hotel_details(self, hotel_details_params: dict[str, Any]):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v2/hotel/details",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=hotel_details_params,
        )
        return res

    async def validate_hotel_price(self, hotel_price_validate_params: dict[str, Any]):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v2/hotel/price-check",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=hotel_price_validate_params,
        )
        return res

    async def create_hotel_booking(self, hotel_booking_params: dict[str, Any]):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v2/hotel/create-pnr",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=hotel_booking_params,
        )
        return res

    async def cancel_hotel_booking(self, pnrId: str):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v2/hotel/pnrs/{pnrId}/cancel-pnr",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
        )
        return res

    async def create_trip(self, trip_params: dict[str, Any]):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v2/trips",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=trip_params,
        )
        return res

    async def get_user_unused_credits(self, unused_credits_params: dict[str, Any]):
        await self.authorize()

        res = await make_post_request(
            f"{settings.SPOTNANA_HOST}/v3/air/fetch-traveler-unused-credits",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=unused_credits_params,
        )
        return res

    async def create_payment_source(self, user_id: str, payment_source: PaymentSource):
        await self.authorize()
        ssl_certificate_filename = (
            "sandbox.pem"
            if settings.OTTO_VGS_OUTBOUND_HOST and "sandbox" in settings.OTTO_VGS_OUTBOUND_HOST
            else "live.pem"
        )

        res = await make_post_request(
            f"{settings.SPOTNANA_VGS_HOST}/v2/payment/users/{user_id}/payment-sources",
            headers={
                "Authorization": f"Bearer {self.token_info.get('token')}",
                "Content-Type": "application/json",
            },
            data=payment_source.model_dump(),
            proxy=f"{settings.OTTO_VGS_OUTBOUND_HOST}",
            ssl_cert_path=str(
                Path(__file__).parent.parent.parent.joinpath("vgs_cert").joinpath(ssl_certificate_filename)
            ),
        )

        return res

    async def post(self, url, headers=None, *args, **kwargs):
        await self.authorize()
        if headers is None:
            headers = {}
        headers["Authorization"] = f"Bearer {self.token_info.get('token')}"

        res = await make_post_request(url, headers=headers, *args, **kwargs)
        return res

    async def get(self, url, headers=None, *args, **kwargs):
        await self.authorize()
        if headers is None:
            headers = {}
        headers["Authorization"] = f"Bearer {self.token_info.get('token')}"

        res = await make_get_request(url, headers=headers, *args, **kwargs)
        return res


spotnana_api = SpotnanaApi()


class SpotnanaHelper:
    @staticmethod
    def get_payment_source_id(traveler_read: dict[str, Any], admin_traveler_read: dict[str, Any] | None = None):
        traveler = traveler_read.get("traveler", {})

        payment_methods = traveler.get("user", {}).get("paymentInfos", [])
        payment_source_id = payment_methods[0].get("card", {}).get("id") if payment_methods else None

        # If the user is part of an organization, use company card
        if admin_traveler_read:
            admin_traveler = admin_traveler_read.get("traveler", {})
            admin_payment_methods = admin_traveler.get("user", {}).get("paymentInfos", [])
            payment_source_id = (
                admin_payment_methods[0].get("card", {}).get("id") if admin_payment_methods else payment_source_id
            )

        return payment_source_id
