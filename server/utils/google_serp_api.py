from typing import Any, Dict

from fastapi import HTTPException

from server.utils.async_requests import make_get_request
from server.utils.logger import logger
from server.utils.settings import settings


class SerpServiceError(Exception):
    """Custom exception for SerpAPI service errors."""

    def __init__(self, message: str):
        super().__init__(message)
        self.message = message


class GoogleSerpApi:
    """
    API client for interacting with the SerpAPI Google Flights endpoint.
    """

    def __init__(self) -> None:
        self.base_url = "https://serpapi.com"
        self.api_key = settings.SERP_API_KEY

    async def search_flights(self, params: Dict[str, Any]) -> Dict[str, Any]:
        # Ensure the engine parameter is set to google_flights
        params["engine"] = "google_flights"

        # Add the API key to the parameters
        params["api_key"] = self.api_key

        url = f"{self.base_url}/search"

        try:
            result = await make_get_request(url, headers={"Content-Type": "application/json"}, params=params)
            return result
        except Exception as e:
            logger.error(f"Error searching flights with SerpAPI: {str(e)}")
            if isinstance(e, HTTPException):
                if e.status_code >= 500:
                    raise SerpServiceError("SerpAPI service error. Please try again later.")
            raise

    async def search_archived(self, search_id: str) -> Dict[str, Any]:
        """Retrieve search result from the Search Archive API
        Parameters:
            search_id (int): unique identifier for the search provided by metadata.id
        Returns:
            dict: search result from the archive
        """
        url = f"{self.base_url}/searches/{search_id}"
        params = {"api_key": self.api_key}
        try:
            result = await make_get_request(url, headers={"Content-Type": "application/json"}, params=params)
            return result
        except Exception as e:
            logger.error(f"Error searching flights with SerpAPI: {str(e)}")
            raise


# Create a singleton instance
google_serp_api = GoogleSerpApi()
