import sys
import threading
import time
from collections import defaultdict
from contextvars import <PERSON><PERSON><PERSON><PERSON>, Token

from langgraph.graph.state import CompiledStateGraph

from server.utils.cloudwatch_metrics import CloudwatchMetricNames
from server.utils.prometheus_metrics import prometheus_record_profiler_metric

PROFILER_CONTEXT_ID: ContextVar[str] = ContextVar("profiler_context_id", default="")


class CustomProfiler:
    DEFAULT_STAT_VALUE = {"start": None, "end": None, "elapsed_time": None, "tag": ""}

    def __init__(self) -> None:
        # Dictionary to store start times of functions
        self.profile_data = {}
        self.calls_to_track = list(CloudwatchMetricNames.keys())

    @staticmethod
    def get_profiler_context_id():
        return PROFILER_CONTEXT_ID.get()

    @staticmethod
    def set_profiler_context_id(ctx_id: str):
        token = PROFILER_CONTEXT_ID.set(ctx_id)
        return token

    @staticmethod
    def reset_profiler_context_id(token: Token[str]):
        if token is not None:
            PROFILER_CONTEXT_ID.reset(token)

    def profile(self, frame, event, arg):
        ctx_id = CustomProfiler.get_profiler_context_id()
        frame_qualname = frame.f_code.co_qualname
        frame_id = id(frame.f_locals)

        if ctx_id is not None:
            if event == "call":
                # Record the start time when the function is called
                if frame_qualname in self.calls_to_track and not self.profile_data.get(ctx_id, {}).get(
                    frame_qualname, {}
                ).get(frame_id, None):
                    if self.profile_data.get(ctx_id, None) is None:
                        self.profile_data[ctx_id] = {}

                    if self.profile_data[ctx_id].get(frame_qualname, None) is None:
                        self.profile_data[ctx_id][frame_qualname] = defaultdict(lambda: self.DEFAULT_STAT_VALUE.copy())

                    self.profile_data[ctx_id][frame_qualname][frame_id]["start"] = time.perf_counter()

            elif event == "return":
                # Calculate and print the elapsed time when the function
                # returns
                if frame_qualname in self.calls_to_track:
                    start_time = self.profile_data.get(ctx_id, {}).get(frame_qualname, {}).get(frame_id, None)
                    if start_time:
                        self.profile_data[ctx_id][frame_qualname][frame_id]["end"] = time.perf_counter()
                        elapsed_time = (
                            self.profile_data[ctx_id][frame_qualname][frame_id]["end"]
                            - self.profile_data[ctx_id][frame_qualname][frame_id]["start"]
                        )
                        self.profile_data[ctx_id][frame_qualname][frame_id]["elapsed_time"] = elapsed_time

                        # Update for flights and hotels requests
                        if (
                            frame_qualname == CompiledStateGraph.ainvoke.__qualname__
                            and isinstance(arg, dict)
                            and "messages" in arg.keys()
                        ):
                            try:
                                if "hotel_options" in arg["messages"][-1].content:
                                    self.profile_data[ctx_id][frame_qualname][frame_id]["tag"] = "hotels"
                                    last_ws_request_frame_id = next(
                                        reversed(self.profile_data[ctx_id]["WSConnectionManager.handle_trip_message"])
                                    )
                                    self.profile_data[ctx_id]["WSConnectionManager.handle_trip_message"][
                                        last_ws_request_frame_id
                                    ]["tag"] = "hotels"
                                elif (
                                    "flight_choices" in arg["messages"][-1].content
                                    or "booking_options" in arg["messages"][-1].content
                                ):
                                    self.profile_data[ctx_id][frame_qualname][frame_id]["tag"] = "flights"
                                    last_ws_request_frame_id = next(
                                        reversed(self.profile_data[ctx_id]["WSConnectionManager.handle_trip_message"])
                                    )
                                    self.profile_data[ctx_id]["WSConnectionManager.handle_trip_message"][
                                        last_ws_request_frame_id
                                    ]["tag"] = "flights"
                            except BaseException:
                                pass

    def save(self):
        ctx_id = CustomProfiler.get_profiler_context_id()
        if ctx_id is not None and ctx_id in self.profile_data.keys():
            prometheus_record_profiler_metric(self.profile_data.get(ctx_id, {}))
            del self.profile_data[ctx_id]


# Add this at the top of the file
def is_pytest_running():
    return "pytest" in sys.modules


# Set the profiler
profiler = CustomProfiler()
if not is_pytest_running():
    sys.setprofile(profiler.profile)
    threading.setprofile(profiler.profile)
