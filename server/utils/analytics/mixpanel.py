import datetime
from typing import Any, Dict, Optional

from server.utils.analytics.analytics import EventPayload, TrackingClient, TrackingEvent
from server.utils.logger import logger
from server.utils.settings import settings


class MixpanelClient(TrackingClient):
    _instance: Optional["MixpanelClient"] = None

    def __init__(self):
        if not hasattr(self, "mp_instance"):  # Prevent reinitialization
            self.api_token: Optional[str] = settings.MIXPANEL_TOKEN
            self.mp_instance = None
            self._initialized = False

    @classmethod
    async def get_instance(cls) -> "MixpanelClient":
        async with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
        return cls._instance

    async def initialize(self) -> None:
        async with self._lock:
            if self.api_token is None or len(self.api_token) == 0:
                logger.error("Mixpanel API token not set")
                return

            if not self._initialized:
                try:
                    from mixpanel import Mixpanel

                    # Initialize the SDK with the project token
                    self.mp_instance = Mixpanel(self.api_token)
                    self._initialized = True
                    logger.info("Mixpanel SDK initialized")
                except ImportError:
                    logger.error("Mixpanel SDK not installed. Please run: pip install mixpanel")
                except Exception as e:
                    logger.error(f"Failed to initialize Mixpanel SDK: {e}")

    @staticmethod
    def _format_iso_timestamp() -> str:
        return datetime.datetime.utcnow().isoformat() + "Z"

    async def log_event(self, payload: EventPayload) -> Dict[str, Any]:
        if not await self.should_track_in_environment(payload.environment):
            logger.debug(
                f"Skipping event {payload.event_type} for user {payload.user_id} in {payload.environment} environment."
            )
            return {"skipped": True, "provider": "mixpanel"}

        if self.api_token is None or len(self.api_token) == 0:
            logger.error("Mixpanel API token not set")
            return {"success": False, "error": "Mixpanel API token not set", "provider": "mixpanel"}

        try:
            if not self._initialized:
                await self.initialize()

            if not self._initialized or self.mp_instance is None:
                raise Exception("Mixpanel client initialization failed")

            # Create the standard properties required by the spec
            event_properties = {
                "timestamp": self._format_iso_timestamp(),
                "event_id": payload.event_id,
                "environment": payload.environment,
                "event_type": payload.event_type,
                "user_id": payload.user_id,
                "user_email": payload.user_email,
            }

            if payload.extra and payload.extra.isdigit():
                event_properties["trip_id"] = payload.extra

            if payload.event_properties:
                for key, value in payload.event_properties.items():
                    event_properties[key] = value

            event_name = self._map_event_to_mixpanel_name(payload.event_type)

            logger.info(
                f"[Mixpanel] Sending event: {event_name}, user_id: {payload.user_id}, env: {payload.environment}"
            )

            if self.mp_instance is not None:
                mp = self.mp_instance
                mp.track(payload.user_id, event_name, event_properties)

            logger.info(f"[Mixpanel] Event sent: {event_name}, user_id: {payload.user_id}, env: {payload.environment}")

            return {"success": True, "event_id": payload.event_id, "event_name": event_name, "provider": "mixpanel"}

        except Exception as e:
            logger.error(f"[Mixpanel] Error sending event {payload.event_type}, user_id: {payload.user_id}, error: {e}")
            return {"success": False, "event_id": payload.event_id, "error": str(e), "provider": "mixpanel"}

    @staticmethod
    def _map_event_to_mixpanel_name(event_type: str) -> str:
        event_mapping = {
            # Onboarding events
            TrackingEvent.CALENDAR_ACCESS_GRANTED.value: "Onboarding - Calendar Connected",
            TrackingEvent.MANUAL_PREFERENCE_COLLECTED.value: "Onboarding - Preferences Updated",
            TrackingEvent.PREFERENCE_COLLECTION_COMPLETED.value: "Onboarding - Preferences Updated",
            TrackingEvent.COMPANY_POLICY_COLLECTED.value: "Onboarding - Travel Policy Updated",
            # User events
            TrackingEvent.FIRST_LOGIN.value: "Login",
            # Flight events
            TrackingEvent.FLIGHT_SEARCHED.value: "Flight Search",
            TrackingEvent.FLIGHT_BOOKED.value: "Flight Purchase",
            # Hotel events
            TrackingEvent.HOTEL_SEARCHED.value: "Hotel Search",
            TrackingEvent.HOTEL_BOOKED.value: "Hotel Purchase",
            # Error events
            TrackingEvent.ERROR_ENCOUNTERED.value: "Error Encountered",
            # Itinerary events
            TrackingEvent.ITINERARY_VIEWED.value: "Itinerary Page Viewed",
            TrackingEvent.TRIP_CREATED.value: "Future Trip Created by Otto",
        }

        return event_mapping.get(event_type, event_type)
