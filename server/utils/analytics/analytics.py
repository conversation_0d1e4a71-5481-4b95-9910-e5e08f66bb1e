import asyncio
import datetime
import json
import uuid
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Type

import pytz

from server.utils.logger import logger
from server.utils.settings import settings


class TrackingEvent(str, Enum):
    # Onboarding events
    CALENDAR_ACCESS_GRANTED = "onboarding_calendar_access_granted"
    CALENDAR_ACCESS_REJECTED = "onboarding_calendar_access_rejected"
    MANUAL_PREFERENCE_COLLECTED = "onboarding_manual_perference_collected"
    PREFERENCE_COLLECTION_COMPLETED = "onboarding_perference_collection_completed"
    COMPANY_POLICY_COLLECTED = "onboarding_company_policy_collected"
    COMPANY_POLICY_SKIPPED = "onboarding_company_policy_skipped"
    ONBOARDING_COMPLETED = "onboarding_completed"
    ONBOARDING_SKIPPED = "onboarding_skipped"
    TUTORIAL_COMPLETED = "tutorial_completed"

    # User events
    FIRST_LOGIN = "first_log_in"
    LAST_MESSAGE_SENT = "last_user_message_sent"

    # Flight events
    FLIGHT_SEARCHED = "last_flight_searched"
    FLIGHT_SELECTED = "last_flight_selected"
    FLIGHT_VALIDATED = "last_flight_validated"
    FLIGHT_BOOKED = "last_flight_booked"

    # Hotel events
    HOTEL_SEARCHED = "last_hotel_searched"
    HOTEL_SELECTED = "last_hotel_selected"
    HOTEL_VALIDATED = "last_hotel_validated"
    HOTEL_BOOKED = "last_hotel_booked"

    # Error events
    ERROR_ENCOUNTERED = "error_encountered"

    # Itinerary events
    ITINERARY_VIEWED = "itinerary_viewed"
    TRIP_CREATED = "trip_created"

    def __str__(self) -> str:
        return self.value


class EventPayload:
    """Standard event payload structure for all tracking providers"""

    def __init__(
        self,
        event_type: str,
        user_id: str,
        user_email: str,
        environment: str,
        event_id: Optional[str] = None,
        extra: Optional[str] = None,
        timestamp: Optional[str] = None,
        user_properties: Optional[Dict[str, Any]] = None,
        event_properties: Optional[Dict[str, Any]] = None,
    ):
        self.event_id = event_id or str(uuid.uuid4())
        self.event_type = event_type
        self.user_id = user_id
        self.user_email = user_email
        self.environment = environment
        self.extra = extra
        self.user_properties = user_properties or {}
        self.event_properties = event_properties or {}

        # Use Pacific timezone for consistency as specified in the requirements
        pacific_tz = pytz.timezone("America/Los_Angeles")
        self.timestamp = timestamp or datetime.datetime.now(tz=datetime.UTC).astimezone(pacific_tz).strftime(
            "%Y-%m-%d %H:%M:%S"
        )

    def to_dict(self):
        payload = {
            "event_id": self.event_id,
            "event_type": self.event_type,
            "user_id": self.user_id,
            "user_email": self.user_email,
            "timestamp": self.timestamp,
            "environment": self.environment,
        }

        if self.extra:
            payload["extra"] = self.extra

        if self.event_properties:
            payload["event_properties"] = json.dumps(self.event_properties)

        return payload


class TrackingClient(ABC):
    """Abstract base class for all tracking service clients"""

    _lock = asyncio.Lock()  # Shared lock for concurrency safety

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the tracking client"""
        pass

    @abstractmethod
    async def log_event(self, payload: EventPayload) -> Dict[str, Any]:
        """Send an event to the tracking service"""
        pass

    @classmethod
    @abstractmethod
    async def get_instance(cls) -> "TrackingClient":
        """Get or create singleton instance of the client"""
        pass

    @classmethod
    async def should_track_in_environment(cls, environment: str) -> bool:
        """Determine if tracking should occur in the current environment"""
        # By default, only track in LIVE environment

        # TODO If you want to trak just on production.
        # return environment.upper() == "LIVE"
        return True


class TrackingManager:
    """Manager class for handling all tracking clients"""

    _instance: Optional["TrackingManager"] = None
    _lock = asyncio.Lock()

    def __init__(self):
        self.tracking_clients: List[Type[TrackingClient]] = []
        self._register_clients()

    @classmethod
    async def get_instance(cls) -> "TrackingManager":
        async with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
        return cls._instance

    def _register_clients(self) -> None:
        from server.utils.analytics.mixpanel import MixpanelClient
        from server.utils.analytics.zapier import ZapierClient

        self.tracking_clients.append(ZapierClient)
        self.tracking_clients.append(MixpanelClient)

    async def initialize_all_clients(self) -> None:
        for client_class in self.tracking_clients:
            client = await client_class.get_instance()
            await client.initialize()

    async def log_event_to_all(
        self,
        event_type: str,
        user_id: str,
        user_email: str,
        extra: Optional[str] = None,
        user_properties: Optional[Dict[str, Any]] = None,
        event_properties: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        env = settings.OTTO_ENV.upper()
        event_id = str(uuid.uuid4())  # Use same event ID across all providers

        logger.info(
            f"[Tracking] Received event: {event_type}, user_id: {user_id}, "
            f"user_email: {user_email}, extra: {extra}, env: {env}"
        )

        payload = EventPayload(
            event_id=event_id,
            event_type=event_type,
            user_id=user_id,
            user_email=user_email,
            environment=env,
            extra=extra,
            user_properties=user_properties,
            event_properties=event_properties,
        )

        tasks = []
        for client_class in self.tracking_clients:
            client = await client_class.get_instance()
            tasks.append(client.log_event(payload))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Error tracking event: {result}")
                processed_results.append({"success": False, "error": str(result), "event_id": event_id})
            else:
                processed_results.append(result)

        return processed_results

    @classmethod
    async def log_event_in_background(
        cls,
        event_type: str,
        user_id: str,
        user_email: str,
        extra: Optional[str] = None,
        user_properties: Optional[Dict[str, Any]] = None,
        event_properties: Optional[Dict[str, Any]] = None,
    ) -> None:
        manager = await TrackingManager.get_instance()
        asyncio.create_task(
            manager.log_event_to_all(
                event_type=event_type,
                user_id=user_id,
                user_email=user_email,
                extra=extra,
                user_properties=user_properties,
                event_properties=event_properties,
            )
        )
