import asyncio
import uuid
from typing import Any, Dict, Optional

import aiohttp

from server.services.user.user_activity import get_user_activity
from server.utils.analytics.analytics import EventPayload, TrackingClient
from server.utils.logger import logger
from server.utils.settings import settings


class ZapierClient(TrackingClient):
    _instance: Optional["ZapierClient"] = None

    def __init__(self):
        if not hasattr(self, "webhook_url"):  # Prevent reinitialization
            self.webhook_url: Optional[str] = settings.ZAPIER_WEBHOOK
            self.session: Optional[aiohttp.ClientSession] = None

    @classmethod
    async def get_instance(cls) -> "ZapierClient":
        """Returns the singleton instance of ZapierClient (async-safe)"""
        async with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
        return cls._instance

    async def initialize(self) -> None:
        """Initialize the aiohttp session"""
        async with self._lock:
            if self.webhook_url is None or len(self.webhook_url) == 0:
                logger.error("Zapier webhook URL not set")
                return

            if self.session is None:
                self.session = aiohttp.ClientSession()
                logger.info("Zapier logger initialized")

    async def log_event(self, payload: EventPayload) -> Dict[str, Any]:
        if not await self.should_track_in_environment(payload.environment):
            logger.debug(
                f"Skipping event {payload.event_type} for user {payload.user_id} in {payload.environment} environment."
            )
            return {"skipped": True, "provider": "zapier"}

        if self.webhook_url is None or len(self.webhook_url) == 0:
            logger.error("Zapier webhook URL not set")
            return {"success": False, "error": "Zapier webhook URL not set", "provider": "zapier"}

        try:
            if self.session is None:
                await self.initialize()

            assert self.session is not None

            # Add Otto RUID to user properties if available
            user_activity = await get_user_activity(payload.user_id)
            otto_ruid = user_activity.get("otto_ruid", {}).get("RUID", "")

            # Create Zapier-specific payload
            zapier_payload = payload.to_dict()
            zapier_payload["otto_ruid"] = otto_ruid

            # Add event properties to the root level for Zapier
            if payload.event_properties:
                for key, value in payload.event_properties.items():
                    zapier_payload[key] = value

            logger.info(
                f"[Zapier] Sending event: {payload.event_type}, user_id: {payload.user_id}, "
                f"user_email: {payload.user_email}, extra: {payload.extra}, env: {payload.environment}"
            )

            async with self.session.post(self.webhook_url, json=zapier_payload) as response:
                status = response.status

                if 200 <= status < 300:
                    resp_text = await response.text()
                    logger.info(
                        f"[Zapier] Event sent: {payload.event_type}, user_id: {payload.user_id}, "
                        f"user_email: {payload.user_email}, extra: {payload.extra}, env: {payload.environment}, Status: {status}"
                    )
                    return {
                        "success": True,
                        "event_id": payload.event_id,
                        "status": status,
                        "response": resp_text,
                        "provider": "zapier",
                    }
                else:
                    error_text = await response.text()
                    logger.error(
                        f"[Zapier] Failed to send event: {payload.event_type}, user_id: {payload.user_id}, "
                        f"user_email: {payload.user_email}, extra: {payload.extra}, env: {payload.environment}, "
                        f"Status: {status}, Error: {error_text}"
                    )
                    return {
                        "success": False,
                        "event_id": payload.event_id,
                        "status": status,
                        "error": error_text,
                        "provider": "zapier",
                    }

        except Exception as e:
            logger.error(
                f"[Zapier] Error sending event {payload.event_type}, user_id: {payload.user_id}, "
                f"user_email: {payload.user_email}, extra: {payload.extra}, env: {payload.environment}, error: {e}"
            )
            return {"success": False, "event_id": payload.event_id, "error": str(e), "provider": "zapier"}

    async def prepare_to_log_events(
        self,
        event_type: str,
        user_id: str,
        user_email: str,
        extra: Optional[str] = None,
        user_properties: Optional[Dict[str, Any]] = None,
        event_properties: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        env = settings.OTTO_ENV.upper()
        event_id = str(uuid.uuid4())  # Use same event ID across all providers

        logger.info(
            f"[Tracking] Received event: {event_type}, user_id: {user_id}, "
            f"user_email: {user_email}, extra: {extra}, env: {env}"
        )

        payload = EventPayload(
            event_id=event_id,
            event_type=event_type,
            user_id=user_id,
            user_email=user_email,
            environment=env,
            extra=extra,
            user_properties=user_properties,
            event_properties=event_properties,
        )

        result = await self.log_event(payload)
        return result

    @classmethod
    async def log_event_in_background(
        cls,
        event_type: str,
        user_id: str,
        user_email: str,
        extra: Optional[str] = None,
        user_properties: Optional[Dict[str, Any]] = None,
        event_properties: Optional[Dict[str, Any]] = None,
    ) -> None:
        client = await cls.get_instance()
        asyncio.create_task(
            client.prepare_to_log_events(
                event_type=event_type,
                user_id=user_id,
                user_email=user_email,
                extra=extra,
                user_properties=user_properties,
                event_properties=event_properties,
            )
        )
