from datetime import datetime, timezone
from typing import Any

import jwt
from fastapi import Depends, HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from starlette.status import HTTP_401_UNAUTHORIZED

from server.utils.logger import logger
from server.utils.settings import settings

InvalidCredentialsException = HTTPException(
    status_code=HTTP_401_UNAUTHORIZED,
    detail="Invalid credentials",
    headers={"WWW-Authenticate": "Bearer"},
)

security = HTTPBearer()


async def partner_spotnana_access_manager(
    credentials: HTTPAuthorizationCredentials = Depends(security),
):
    try:
        payload = decode_jwt(credentials.credentials)
        return payload
    except Exception as e:
        logger.error(f"[PartnerLoginManager] An error occurred: {e}")
        raise InvalidCredentialsException


async def partner_prometheus_access_manager(
    credentials: HTTPAuthorizationCredentials = Depends(security),
):
    try:
        assert credentials.credentials == settings.PARTNER_PROMETHEUS_TOKEN
    except Exception as e:
        logger.error(f"[PrometheusPartnerLoginManager] An error occurred: {e}")
        raise InvalidCredentialsException


def encode_jwt(payload: dict[str, Any]) -> str:
    payload.update({"exp": datetime.now(timezone.utc) + settings.PARTNER_SPOTNANA_TOKEN_LIFESPAN})
    assert settings.PARTNER_SPOTNANA_TOKEN_SIGN_SECRET is not None, "Spotnana token sign secret is not set"
    return jwt.encode(payload, settings.PARTNER_SPOTNANA_TOKEN_SIGN_SECRET, algorithm="HS256")


def decode_jwt(token: str) -> dict[str, Any]:
    assert settings.PARTNER_SPOTNANA_TOKEN_SIGN_SECRET is not None, "Spotnana token sign secret is not set"
    return jwt.decode(token, settings.PARTNER_SPOTNANA_TOKEN_SIGN_SECRET, algorithms=["HS256"])


def create_custom_jwt(client_id: str) -> str:
    """
    Create the access token based on client_id.
    """

    payload = {
        "client_id": client_id,
        "exp": datetime.now(timezone.utc) + settings.PARTNER_SPOTNANA_TOKEN_LIFESPAN,
    }
    access_token = encode_jwt(payload)

    return access_token
