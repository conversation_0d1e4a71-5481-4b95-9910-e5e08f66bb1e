import base64
import json
import smtplib
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText

from google.oauth2 import service_account
from googleapiclient.discovery import build

from server.utils.logger import logger
from server.utils.settings import settings

SCOPES = ["https://www.googleapis.com/auth/gmail.send"]
DELEGATED_USER = "<EMAIL>"
SEND_AS_USER = "<EMAIL>"


def get_service_account_credentials(delegated_user: str):
    assert settings.GMAIL_SENDER_SERVICE_ACCOUNT_JSON is not None, "GMAIL_SENDER_SERVICE_ACCOUNT_JSON is not set"
    service_account_info = json.loads(settings.GMAIL_SENDER_SERVICE_ACCOUNT_JSON)

    credentials = service_account.Credentials.from_service_account_info(
        service_account_info, scopes=["https://www.googleapis.com/auth/gmail.send"]
    ).with_subject(delegated_user)

    return credentials


def gmail_create_message(to, subject, content, mime_type, from_name=None):
    message = MIMEText(content, mime_type)
    message["to"] = to
    message["subject"] = subject
    from_address = f"{from_name} <{SEND_AS_USER}>" if from_name else SEND_AS_USER
    message["from"] = from_address
    message["reply-to"] = SEND_AS_USER
    encoded_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
    return {"raw": encoded_message}


def gmail_send_message(to, subject, content, mime_type="plain", from_name=None):
    message = gmail_create_message(to, subject, content, mime_type, from_name)
    credentials = get_service_account_credentials(DELEGATED_USER)
    service = build("gmail", "v1", credentials=credentials)

    try:
        result = service.users().messages().send(userId="me", body=message).execute()
        logger.info(f"Sent email to {to}")
        return result
    except Exception as e:
        logger.error(f"Failed to send email via Gmail API: {str(e)}")
        raise e


async def send_booking_email(booking_type: str, user_email: str, thread_id: str | int, extra_info: str | None = None):
    # Validate booking type
    if booking_type not in ["flight", "hotel"]:
        raise ValueError("Invalid booking type. Must be 'flight' or 'hotel'.")

    extra_info = extra_info.replace("\n", "<br>") if extra_info else ""

    # Create the email subject and body
    subject = f"[{settings.OTTO_ENV.upper()}] New {booking_type} booking made by {user_email}"
    body = f"""
    <!DOCTYPE html>
    <html>
    <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Otto Booking - internal</title>
    </head>
    <body style="margin:0; padding:0; background-color:#f9f9f9;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
        <td align="center">
            <!-- Inner container with max-width 400px -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="400" style="max-width:600px; width:100%; background-color:#ffffff;">
            <tr>
                <td align="center" style="padding:20px 0;">
                <!-- Logo -->
                <img src="https://otto-web.s3.us-east-2.amazonaws.com/otto-logo/otto-logo-banner.png" alt="Otto Logo" style="display:block; width:150px; height:auto;">
                </td>
            </tr>
            <tr>
                <td align="center" style="padding:20px;">
                <!-- Heading -->
                <h1 style="margin:0; font-size:24px; font-family:Arial, sans-serif;">
                    {user_email} Booked a {booking_type.title()}! 🎉
                </h1>
                </td>
            </tr>
            <tr>
                <td style="padding:20px; font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                User {user_email} just booked a {booking_type} for trip {thread_id} in Otto {settings.OTTO_ENV.upper()} environment.<br>
                {extra_info}
                </td>
            </tr>
            <tr>
                <td align="center" style="padding:20px; font-family:Arial, sans-serif; font-size:12px; color:#777777;">
                <!-- Footer with support -->
                <p style="margin:0;">
                    Reach <NAME_EMAIL> or #otto-operations on slack.
                </p>
                </td>
            </tr>
            </table>
        </td>
        </tr>
    </table>
    </body>
    </html>
    """

    assert settings.RECEIVER_EMAIL is not None, "RECEIVER_EMAIL is not set"
    gmail_send_message(
        to=settings.RECEIVER_EMAIL, subject=subject, content=body, mime_type="html", from_name="Otto Booking"
    )
    logger.info(f"Successfully sent email notification for {booking_type} booking.")


async def send_otp_email(user_email: str, otp_code: str):
    # Create the email subject and body
    subject = f"{otp_code} is your Otto verification code"
    body = f"""
    <!DOCTYPE html>
    <html>
    <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Otto Verification Code</title>
    </head>
    <body style="margin:0; padding:0; background-color:#f9f9f9;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
        <td align="center">
            <!-- Inner container with max-width 400px -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="400" style="max-width:600px; width:100%; background-color:#ffffff;">
            <tr>
                <td align="center" style="padding:20px 0;">
                <!-- Logo -->
                <img src="https://otto-web.s3.us-east-2.amazonaws.com/otto-logo/otto-logo-banner.png" alt="Otto Logo" style="display:block; width:150px; height:auto;">
                </td>
            </tr>
            <tr>
                <td align="center" style="padding:20px;">
                <!-- Heading -->
                <h1 style="margin:0; font-size:24px; font-family:Arial, sans-serif;">
                    Your Otto Login Verification Code
                </h1>
                </td>
            </tr>
            <tr>
                <td style="padding:20px; font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                <!-- Greeting and verification code details -->
                <p style="margin:0;">Hi,</p>
                <p style="margin:10px 0 0;">Here is your verification code:</p>
                <p style="margin:10px 0 20px; text-align:center; font-size:18px; font-weight:bold;">{otp_code}</p>
                <p style="margin:10px 0;">
                    This verification code can only be used once and will expire in 10 minutes. Enter it on our website or app and you'll be on your way.
                </p>
                <p style="margin:10px 0;">
                    If you didn't request to have your identity confirmed, ignore this email.
                </p>
                </td>
            </tr>
            <tr>
                <td align="center" style="padding:20px; font-family:Arial, sans-serif; font-size:12px; color:#777777;">
                <!-- Footer with Privacy and Terms links -->
                <p style="margin:0;">
                    Read our 
                    <a href="https://app.ottotheagent.com/privacy" style="color:#777777; text-decoration:underline;">Privacy Policy</a> 
                    and 
                    <a href="https://app.ottotheagent.com/terms" style="color:#777777; text-decoration:underline;">Terms of Service</a>.
                </p>
                </td>
            </tr>
            </table>
        </td>
        </tr>
    </table>
    </body>
    </html>
    """

    gmail_send_message(to=user_email, subject=subject, content=body, mime_type="html", from_name="Otto Login")
    logger.info(f"Successfully sent OTP email to {user_email}.")


async def smtp_send_email(
    sender_email: str, sender_label: str, receiver: str, subject: str, body: str, mime_type: str = "plain"
):
    # Define SMTP email server details
    smtp_server = "smtp.gmail.com"
    smtp_port = 587

    assert settings.SMTP_USERNAME is not None, "SMTP_USERNAME is not set"
    assert settings.SMTP_PASSWORD is not None, "SMTP_PASSWORD is not set"
    assert settings.RECEIVER_EMAIL is not None, "RECEIVER_EMAIL is not set"

    # Use environment variables for sensitive information
    smtp_username = settings.SMTP_USERNAME
    smtp_password = settings.SMTP_PASSWORD

    # Create the email message
    msg = MIMEMultipart()
    msg["From"] = sender_label
    msg["To"] = receiver
    msg["Subject"] = subject
    msg.attach(MIMEText(body, mime_type))

    # Send the email
    try:
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.sendmail(sender_email, receiver, msg.as_string())
    except smtplib.SMTPException as e:
        logger.error(f"Failed to send email notification. SMTP error: {str(e)}")
    except Exception as e:
        logger.error(f"Failed to send email notification. Error: {str(e)}")
