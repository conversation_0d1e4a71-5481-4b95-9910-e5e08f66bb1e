from typing import Optional

import redis.asyncio as redis

from server.utils.logger import logger
from server.utils.settings import settings


class RedisConnector:
    """
    Redis connection manager class, providing the creation and management of Redis connection instances.
    """

    _instance: Optional["RedisConnector"] = None
    _connection_pool: Optional[redis.ConnectionPool] = None
    _is_available: bool = True

    @classmethod
    def _get_instance(cls):
        """Get or create the singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    async def _initialize_connection_pool(self):
        """Initialize the Redis connection pool"""
        assert isinstance(settings.REDIS_URL, str), "REDIS_URL must be a string"
        redis_client = None
        try:
            self._connection_pool = redis.BlockingConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=150,  # Set the maximum number of connections
                timeout=3,  # Throw exception after 3 seconds if no connection is available
                decode_responses=False,  # Keep raw binary responses, let each service decode
            )

            redis_client = redis.Redis(connection_pool=self._connection_pool)
            response = await redis_client.ping()

            self._is_available = response is True
        except Exception as e:
            logger.error(f"Error initializing Redis connection pool: {e}")
            self._is_available = False
        finally:
            if redis_client:
                await redis_client.aclose()

    @classmethod
    async def get_connection(cls, raise_on_error: bool = False) -> Optional[redis.Redis]:
        """
        Get the Redis connection instance

        Args:
            raise_on_error: If True, raises exception when connection fails
                          If False, returns None when connection fails

        Returns:
            Optional[redis.Redis]: The Redis connection instance or None if connection failed
        """
        instance = cls._get_instance()
        if instance._connection_pool is None:
            await instance._initialize_connection_pool()

        if not instance._is_available:
            if raise_on_error:
                raise redis.ConnectionError("Redis connection is not available")
            return None

        try:
            return redis.Redis(connection_pool=instance._connection_pool)
        except Exception as e:
            logger.error(f"Failed to create Redis connection: {e}")
            if raise_on_error:
                raise
            return None

    @classmethod
    async def close(cls):
        """Close all connections in the connection pool"""
        logger.info("Closing Redis connection pool")
        try:
            instance = cls._get_instance()
            if instance._connection_pool:
                await instance._connection_pool.disconnect()
            instance._connection_pool = None
            instance._is_available = False
            logger.info("Redis connection pool closed successfully")
        except Exception as e:
            logger.error(f"Error closing Redis connection pool: {e}")
            raise e
