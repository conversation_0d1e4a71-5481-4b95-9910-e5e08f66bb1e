<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>{{ title }}</title>
  </head>

  <body class="text-gray-900 text-xs">
    <img class="logo" src="logo.svg" />

    <h1 class="mb-2 mt-5 text-xl">Receipt</h1>
    <div>Confirmation Number: {{ confirmation_number }}</div>

    <h2 class="mt-8 text-sm">Booking details</h2>
    <div class="font-bold italic mt-2 text-neutral-500">Hotel Stay</div>

    <div class="mt-2">
      <div class="bg-gray-100 font-bold p-2">{{ hotel.name }}</div>
      <div class="mt-2 pl-5">
        <div>Address: {{ hotel.address }}</div>
        <div class="mt-1">Check In: {{ hotel.check_in }}</div>
        <div class="mt-1">Check Out: {{ hotel.check_out }}</div>
        <div class="mt-1">Room Type: {{ hotel.room_type }}</div>
      </div>
    </div>

    <div>
      <div class="font-bold italic mt-4 text-neutral-500">Traveler info</div>
      <div class="mt-2 pl-5">
        <div>Room 1 primary guest: {{ traveler.name }}</div>
      </div>
    </div>

    <h2 class="mt-8 text-sm">Payment Details</h2>
    <div class="mt-2 pl-5">
      <div>Payment Method: {{ payment.method }}</div>
      <div class="mt-1">Transaction date: {{ payment.transaction_date }}</div>
      <div class="mt-1">Status: {{ payment.status }}</div>
    </div>

    <div class="bg-neutral-250 h-px my-4"></div>

    <div class="flex justify-between">
      <div>Average rate per night</div>
      <div>{{ payment.per_night }}</div>
    </div>
    <div class="flex justify-between">
      <div>
        {{ payment.nights }} night{% if payment.nights > 1 %}s{% endif %} stay
      </div>
      <div>{{ payment.subtotal }}</div>
    </div>
    <div class="flex justify-between">
      <div>Taxes & Fees</div>
      <div>{{ payment.taxes_fees }}</div>
    </div>

    <div class="bg-gray-900 h-px my-4"></div>

    <h2 class="flex justify-between mt-4 text-sm">
      <div>Total</div>
      <div>{{ payment.total }}</div>
    </h2>
  </body>
</html>
