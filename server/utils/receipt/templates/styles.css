@page {
  size: 216mm 279mm;
  margin: 11mm;
}

body {
  font-family: "Arial", sans-serif;
}

.logo {
  display: block;
  margin-left: auto;
  width: 21.88mm;

  svg {
    width: 100%;
  }
}

h1,
h2 {
  margin: 0;
}

/* Tailwind class names */

.bg-gray-100 {
  background-color: #f3f5f5;
}
.bg-gray-900 {
  background-color: #1d1c1d;
}

.bg-neutral-250 {
  background-color: #e1e1e1;
}

.flex {
  display: flex;
}

.font-bold {
  font-weight: bold;
}

.h-px {
  height: 1px;
}

.justify-between {
  justify-content: space-between;
}

.mb-2 {
  /* margin-bottom: 8px; */
  margin-bottom: 2.82mm;
}

.mt-1 {
  /* margin-top: 4px; */
  margin-top: 1.41mm;
}

.mt-2 {
  /* margin-bottom: 8px; */
  margin-top: 2.82mm;
}

.mt-4 {
  /* margin-top: 16px; */
  margin-top: 5.64mm;
}

.mt-5 {
  /* margin-top: 20px; */
  margin-top: 7.05mm;
}

.mt-8 {
  /* margin-top: 32px; */
  margin-top: 11.28mm;
}

.mt-13 {
  /* margin-top: 52px; */
  margin-top: 18.33mm;
}

.my-4 {
  /* margin: 16px 0; */
  margin: 5.64mm 0;
}

.p-2 {
  /* padding: 8px; */
  padding: 2.82mm;
}

.pl-5 {
  /* padding-left: 20px; */
  padding-left: 7.05mm;
}

.text-xs {
  /* font-size: 12px; */
  /* line-height: 16px; */
  font-size: 4.23mm;
  line-height: 5.64mm;
}

.text-sm {
  /* font-size: 14px;
        line-height: 20px; */
  font-size: 4.935mm;
  line-height: 7.05mm;
}

.text-base {
  /* font-size: 16px; */
  /* line-height: 24px; */
  font-size: 5.64mm;
  line-height: 8.46mm;
}

.text-xl {
  /* font-size: 20px; */
  /* line-height: 28px; */
  font-size: 7.05mm;
  line-height: 9.87mm;
}

.italic {
  font-style: italic;
}

.text-gray-900 {
  color: #1d1c1d;
}

.text-neutral-500 {
  color: #777;
}
