from jinja2 import Environment, FileSystemLoader
from weasyprint import CSS, HTML

from server.schemas.receipt.receipt_flight_data import ReceiptFlightData
from server.schemas.receipt.receipt_hotel_data import ReceiptHotelData
from server.utils.settings import settings


class ReceiptGenerator:
    BASE_URL: str = settings.SERVER_DNS or ""
    TEMPLATE_PATH: str = "server/utils/receipt/templates"
    # TEMPLATE_PATH: str = "templates"
    CSS_FILE_PATH: str = f"{TEMPLATE_PATH}/styles.css"

    def generate_hotel_receipt(self, hotel_data: ReceiptHotelData):
        css = CSS(filename=self.CSS_FILE_PATH)
        env = Environment(loader=FileSystemLoader(self.TEMPLATE_PATH))
        template = env.get_template("hotel-template.html")

        # Generate PDF
        return HTML(string=template.render(**hotel_data.model_dump()), base_url=self.TEMPLATE_PATH).write_pdf(
            stylesheets=[css]
        )

    def generate_flight_receipt(self, flight_data: ReceiptFlightData):
        css = CSS(filename=self.CSS_FILE_PATH)
        env = Environment(loader=FileSystemLoader(self.TEMPLATE_PATH))
        template = env.get_template("flight-template.html")

        # Generate PDF
        return HTML(string=template.render(**flight_data.model_dump()), base_url=self.TEMPLATE_PATH).write_pdf(
            stylesheets=[css]
        )
