import json
from typing import Any

from server.utils.logger import logger
from server.utils.redis_connector import RedisConnector


class RedisStorage:
    """
    Redis implementation for caching / key-value storage.
    """

    def __init__(self):
        """
        Initialize the Redis Storage.
        """
        self.connection = None

    async def _get_connection(self):
        """Get the Redis connection from the connector."""
        if self.connection is None:
            self.connection = await RedisConnector.get_connection()

        if self.connection is None:
            raise ConnectionError("Redis connection is not available")

        return self.connection

    async def save_to_redis(self, key: str, data: dict[str, Any], expire_timedelta):  # Changed type hint
        try:
            connection = await self._get_connection()

            await connection.set(key, json.dumps(data), ex=expire_timedelta)
        except Exception as e:
            logger.error(f"Failed to save value to redis, error: {e}")
            raise e

    async def retrieve_from_redis(self, key: str):
        try:
            connection = await self._get_connection()

            data = await connection.get(key)
            if data is None:
                return None

            return json.loads(data)
        except Exception as e:
            logger.error(f"Failed to retrieve value from redis, error: {e}")
            return None

    async def remove_from_redis(self, key: str):
        try:
            connection = await self._get_connection()

            await connection.delete(key)
        except Exception as e:
            logger.error(f"Failed to remove value from redis, error: {e}")
            return None

    async def close(self) -> None:
        """
        Close the message broker connection and release resources.
        """
        # Connection pool is managed by RedisConnector, so we don't need to close it here
        # Just reset our local reference
        self.connection = None
