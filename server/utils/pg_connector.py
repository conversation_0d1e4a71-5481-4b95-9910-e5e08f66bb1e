from sqlalchemy import MetaData
from sqlalchemy.ext.asyncio import Async<PERSON>tt<PERSON>, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase

from server.utils.settings import settings

connection_string = f"postgresql+asyncpg://{settings.PG_USER}:{settings.PG_PASSWORD}@{settings.PG_HOST}:{settings.PG_PORT}/{settings.PG_DATABASE}"

pg_engine = create_async_engine(connection_string, pool_size=10, pool_recycle=600, pool_timeout=30)
async_session = async_sessionmaker(pg_engine, expire_on_commit=False)


class Base(AsyncAttrs, DeclarativeBase):
    metadata = MetaData(schema="otto", naming_convention={"fk": "%(table_name)s_%(column_0_name)s_fkey"})
    pass
