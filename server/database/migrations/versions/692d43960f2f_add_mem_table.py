"""add_mem_table

Revision ID: 692d43960f2f
Revises: eb1d86cf6d1c
Create Date: 2025-01-10 11:51:09.366984

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "692d43960f2f"
down_revision: Union[str, None] = "eb1d86cf6d1c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Enable pgvector extension
    op.execute("CREATE EXTENSION IF NOT EXISTS vector;")

    op.execute(
        "CREATE TABLE IF NOT EXISTS otto.user_memories (id UUID PRIMARY KEY, vector vector(1536), payload JSONB);"
    )
    op.execute(
        "CREATE INDEX IF NOT EXISTS user_memories_hnsw_idx ON otto.user_memories USING hnsw (vector vector_cosine_ops);"
    )


def downgrade() -> None:
    op.execute("DROP TABLE IF EXISTS otto.user_memories;")
