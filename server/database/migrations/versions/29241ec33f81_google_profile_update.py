"""Google profile update

Revision ID: 29241ec33f81
Revises: 29c9ff1d9259
Create Date: 2024-09-17 14:29:11.480779

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "29241ec33f81"
down_revision: Union[str, None] = "29c9ff1d9259"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("google_profile", sa.Column("resource_id", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("google_profile", "resource_id")
