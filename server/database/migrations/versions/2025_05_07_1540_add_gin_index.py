"""add gin index

Revision ID: 72a73d76bbb3
Revises: e9cf1e636b9b
Create Date: 2025-05-07 15:40:16.150927

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "72a73d76bbb3"
down_revision: Union[str, None] = "e9cf1e636b9b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_index(
        "ix_booking_content_gin", "bookings", ["content"], unique=False, schema="otto", postgresql_using="gin"
    )


def downgrade() -> None:
    op.drop_index("ix_booking_content_gin", table_name="bookings", schema="otto", postgresql_using="gin")
