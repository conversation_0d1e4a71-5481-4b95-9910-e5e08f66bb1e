"""Add columns to bookings table

Revision ID: d9863411e152
Revises: b1e9ab52e5eb
Create Date: 2025-05-14 16:48:16.798962

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "d9863411e152"
down_revision: Union[str, None] = "b1e9ab52e5eb"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("bookings", sa.Column("start_date", sa.DateTime(), nullable=True))
    op.add_column("bookings", sa.Column("end_date", sa.DateTime(), nullable=True))
    op.add_column("bookings", sa.Column("status", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("bookings", "status")
    op.drop_column("bookings", "end_date")
    op.drop_column("bookings", "start_date")
