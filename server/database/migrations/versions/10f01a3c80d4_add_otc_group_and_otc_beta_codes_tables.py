"""Add otc_group and otc_beta_codes tables

Revision ID: 10f01a3c80d4
Revises: 29ba866d519d
Create Date: 2025-04-08 11:19:16.626180

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "10f01a3c80d4"
down_revision: Union[str, None] = "29ba866d519d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "otc_groups",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        schema="otto",
    )
    op.create_table(
        "otc_beta_codes",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("group_id", sa.Integer(), nullable=False),
        sa.Column("code", sa.String(), nullable=False),
        sa.Column(
            "status",
            sa.Enum("generated", "allocated", "used", name="otc_beta_codes_status", create_constraint=True),
            nullable=True,
        ),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("used_at", sa.DateTime(), nullable=True),
        sa.Column("used_by", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["group_id"],
            ["otto.otc_groups.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="otto",
    )


def downgrade() -> None:
    op.drop_table("otc_beta_codes", schema="otto")
    op.drop_table("otc_groups", schema="otto")
    sa.Enum(name="otc_beta_codes_status").drop(op.get_bind(), checkfirst=False)
