"""Create google profile table

Revision ID: c3f8144194cf
Revises: fcb32163b700
Create Date: 2024-03-11 09:56:33.515066

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c3f8144194cf"
down_revision: Union[str, None] = "fcb32163b700"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "google_profile",
        sa.Column("sub", sa.String(), nullable=False),
        sa.Column("users_id", sa.Integer(), nullable=False),
        sa.Column("refresh_token", sa.String(), nullable=False),
        sa.Column("created_date", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["users_id"],
            ["otto.users.id"],
        ),
        sa.PrimaryKeyConstraint("sub"),
        schema="otto",
    )


def downgrade() -> None:
    op.drop_table("google_profile", schema="otto")
