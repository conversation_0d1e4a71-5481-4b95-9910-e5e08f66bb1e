"""Remove microsoft table

Revision ID: 2c94e494ebd0
Revises: 150fee0a9a05
Create Date: 2024-12-19 14:36:10.630074

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "2c94e494ebd0"
down_revision: Union[str, None] = "150fee0a9a05"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.drop_table("microsoft_profile")
    op.add_column("google_profile", sa.Column("last_login_method", sa.String(), nullable=True))
    op.drop_constraint("google_profile_users_id_fkey", "google_profile", type_="foreignkey")
    op.create_foreign_key(
        None, "google_profile", "users", ["users_id"], ["id"], source_schema="otto", referent_schema="otto"
    )
    op.drop_column("users", "last_login_method")


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.Column("last_login_method", sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint(None, "google_profile", schema="otto", type_="foreignkey")
    op.create_foreign_key("google_profile_users_id_fkey", "google_profile", "users", ["users_id"], ["id"])
    op.drop_column("google_profile", "last_login_method")

    op.create_table(
        "microsoft_profile",
        sa.Column("sub", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("users_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("refresh_token", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("tenant_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "created_date",
            postgresql.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["users_id"], ["users.id"], name="microsoft_profile_users_id_fkey"),
        sa.PrimaryKeyConstraint("sub", name="microsoft_profile_pkey"),
    )
