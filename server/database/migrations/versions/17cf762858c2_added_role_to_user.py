"""Added role to user

Revision ID: 17cf762858c2
Revises: 29241ec33f81
Create Date: 2024-09-24 10:19:16.040937

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "17cf762858c2"
down_revision: Union[str, None] = "29241ec33f81"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("users", sa.Column("role", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("users", "role")
