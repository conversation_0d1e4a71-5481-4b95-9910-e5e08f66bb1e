"""Create organization tables

Revision ID: 490a46581ee4
Revises: f928d19a20b8
Create Date: 2025-06-02 16:53:45.765310

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "490a46581ee4"
down_revision: Union[str, None] = "f928d19a20b8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "organizations",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("domain", sa.String(), nullable=False),
        sa.Column("created_date", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("updated_date", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("auto_approve_domain_users", sa.Boolean(), nullable=False),
        sa.Column("require_otp_login", sa.Boolean(), nullable=False),
        sa.Column("travel_policy", sa.JSON(), nullable=True),
        sa.Column("preferred_airlines", postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column("preferred_hotels", postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column("default_payment_method_id", sa.String(), nullable=True),
        sa.Column("billing_contact_email", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="otto",
    )
    op.create_index(op.f("ix_otto_organizations_domain"), "organizations", ["domain"], unique=True, schema="otto")
    op.create_table(
        "organization_domains",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.Column("domain", sa.String(), nullable=False),
        sa.Column("is_validated", sa.Boolean(), nullable=False),
        sa.Column("created_date", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["organization_id"], ["otto.organizations.id"], name=op.f("organization_domains_organization_id_fkey")
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="otto",
    )
    op.create_index(
        op.f("ix_otto_organization_domains_domain"), "organization_domains", ["domain"], unique=False, schema="otto"
    )
    op.add_column("users", sa.Column("organization_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        op.f("users_organization_id_fkey"),
        "users",
        "organizations",
        ["organization_id"],
        ["id"],
        source_schema="otto",
        referent_schema="otto",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("users_organization_id_fkey"), "users", schema="otto", type_="foreignkey")
    op.drop_column("users", "organization_id")
    op.drop_index(op.f("ix_otto_organization_domains_domain"), table_name="organization_domains", schema="otto")
    op.drop_table("organization_domains", schema="otto")
    op.drop_index(op.f("ix_otto_organizations_domain"), table_name="organizations", schema="otto")
    op.drop_table("organizations", schema="otto")
