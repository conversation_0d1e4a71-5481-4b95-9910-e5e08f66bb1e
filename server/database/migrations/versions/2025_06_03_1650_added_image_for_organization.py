"""Added image for organization

Revision ID: aa317f9cc1cc
Revises: 490a46581ee4
Create Date: 2025-06-03 16:50:19.494296

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "aa317f9cc1cc"
down_revision: Union[str, None] = "490a46581ee4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("organizations", sa.Column("image", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("organizations", "image")
