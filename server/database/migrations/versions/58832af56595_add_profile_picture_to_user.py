"""Add profile picture to user

Revision ID: 58832af56595
Revises: 7d6a498a985a
Create Date: 2024-04-05 14:51:56.348479

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "58832af56595"
down_revision: Union[str, None] = "7d6a498a985a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("users", sa.Column("profile_picture", sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column("users", "profile_picture")
