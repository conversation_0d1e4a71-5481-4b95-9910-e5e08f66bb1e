"""Create chat threads table

Revision ID: cfe4f97c7a78
Revises: c3f8144194cf
Create Date: 2024-04-02 15:57:34.458255

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "cfe4f97c7a78"
down_revision: Union[str, None] = "c3f8144194cf"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "chat_threads",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("users_id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(), nullable=False),
        sa.Column("created_date", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["users_id"],
            ["otto.users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="otto",
    )


def downgrade() -> None:
    op.drop_table("chat_threads", schema="otto")
