"""Add last_login_method to User and other changes

Revision ID: 5ae0637e82d0
Revises: 24f2a6d42ac2
Create Date: 2024-12-18 17:41:07.654074

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5ae0637e82d0"
down_revision: Union[str, None] = "24f2a6d42ac2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "microsoft_profile",
        sa.Column("sub", sa.String(), nullable=False),
        sa.Column("users_id", sa.Integer(), nullable=False),
        sa.Column("refresh_token", sa.String(), nullable=False),
        sa.Column("tenant_id", sa.String(), nullable=True),
        sa.Column("created_date", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["users_id"],
            ["otto.users.id"],
        ),
        sa.PrimaryKeyConstraint("sub"),
        schema="otto",
    )

    op.drop_constraint("google_profile_users_id_fkey", "google_profile", type_="foreignkey")
    op.create_foreign_key(
        None, "google_profile", "users", ["users_id"], ["id"], source_schema="otto", referent_schema="otto"
    )
    op.add_column("users", sa.Column("last_login_method", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    op.drop_column("users", "last_login_method")
    op.drop_constraint(None, "google_profile", schema="otto", type_="foreignkey")
    op.create_foreign_key("google_profile_users_id_fkey", "google_profile", "users", ["users_id"], ["id"])

    op.drop_table("microsoft_profile", schema="otto")
