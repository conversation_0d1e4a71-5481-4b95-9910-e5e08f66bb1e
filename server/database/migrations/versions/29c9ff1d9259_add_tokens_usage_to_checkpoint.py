"""Add tokens usage to checkpoint

Revision ID: 29c9ff1d9259
Revises: 5aa31f4dbcff
Create Date: 2024-05-14 10:28:34.955277

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "29c9ff1d9259"
down_revision: Union[str, None] = "5aa31f4dbcff"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("checkpoints", sa.Column("input_tokens", sa.Integer(), nullable=True))
    op.add_column("checkpoints", sa.Column("output_tokens", sa.Integer(), nullable=True))


def downgrade() -> None:
    op.drop_column("checkpoints", "output_tokens")
    op.drop_column("checkpoints", "input_tokens")
