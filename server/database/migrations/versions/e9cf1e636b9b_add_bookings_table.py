"""Add bookings table

Revision ID: e9cf1e636b9b
Revises: 544ab5c03013
Create Date: 2025-04-30 14:36:07.334808

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "e9cf1e636b9b"
down_revision: Union[str, None] = "544ab5c03013"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "bookings",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("thread_id", sa.Integer(), nullable=False),
        sa.Column(
            "type", sa.Enum("accommodations", "flight", name="booking_type", create_constraint=True), nullable=True
        ),
        sa.Column("content", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("flight_exchanges", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.ForeignKeyConstraint(
            ["thread_id"],
            ["otto.chat_threads.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="otto",
    )


def downgrade() -> None:
    op.drop_table("bookings", schema="otto")
    sa.Enum(name="booking_type").drop(op.get_bind(), checkfirst=False)
