"""Add extra to chat_thread

Revision ID: b1e9ab52e5eb
Revises: 72a73d76bbb3
Create Date: 2025-05-08 13:32:16.315934

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "b1e9ab52e5eb"
down_revision: Union[str, None] = "72a73d76bbb3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("chat_threads", sa.Column("extra", postgresql.JSONB(astext_type=sa.Text()), nullable=True))


def downgrade() -> None:
    op.drop_column("chat_threads", "extra")
