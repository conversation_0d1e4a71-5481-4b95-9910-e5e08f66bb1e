"""add_company_policy

Revision ID: eb1d86cf6d1c
Revises: 2c94e494ebd0
Create Date: 2025-01-10 17:50:50.782953

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "eb1d86cf6d1c"
down_revision: Union[str, None] = "2c94e494ebd0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "user_company_travel_policies",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("parsed_travel_policy", sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["otto.users.id"],
        ),
        sa.PrimaryKeyConstraint("user_id"),
        schema="otto",
    )


def downgrade() -> None:
    op.drop_table("user_company_travel_policies", schema="otto")
    # ### end Alembic commands ###
