"""add preferred_name to users table

Revision ID: 29ba866d519d
Revises: 09973bcf2324
Create Date: 2025-04-04 14:39:33.407448

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "29ba866d519d"
down_revision: Union[str, None] = "09973bcf2324"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.Column("preferred_name", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "preferred_name")
    # ### end Alembic commands ###
