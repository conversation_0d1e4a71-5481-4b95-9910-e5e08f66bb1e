"""Add is_sintetic and is_mined fields

Revision ID: 87e554b8b046
Revises: 47980a29d2b0
Create Date: 2024-04-18 18:36:22.119572

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "87e554b8b046"
down_revision: Union[str, None] = "47980a29d2b0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("chat_threads", sa.Column("is_mined", sa.<PERSON>(), nullable=True))
    op.add_column("users", sa.Column("is_sintetic", sa.<PERSON>(), nullable=True))


def downgrade() -> None:
    op.drop_column("users", "is_sintetic")
    op.drop_column("chat_threads", "is_mined")
