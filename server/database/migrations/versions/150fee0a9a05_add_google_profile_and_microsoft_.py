"""Add google_profile and microsoft_profile to user

Revision ID: 150fee0a9a05
Revises: 5ae0637e82d0
Create Date: 2024-12-18 17:47:37.040205

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "150fee0a9a05"
down_revision: Union[str, None] = "5ae0637e82d0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.drop_constraint("google_profile_users_id_fkey", "google_profile", type_="foreignkey")
    op.create_foreign_key(
        None, "google_profile", "users", ["users_id"], ["id"], source_schema="otto", referent_schema="otto"
    )
    op.drop_constraint("microsoft_profile_users_id_fkey", "microsoft_profile", type_="foreignkey")
    op.create_foreign_key(
        None, "microsoft_profile", "users", ["users_id"], ["id"], source_schema="otto", referent_schema="otto"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "microsoft_profile", schema="otto", type_="foreignkey")
    op.create_foreign_key("microsoft_profile_users_id_fkey", "microsoft_profile", "users", ["users_id"], ["id"])
    op.drop_constraint(None, "google_profile", schema="otto", type_="foreignkey")
    op.create_foreign_key("google_profile_users_id_fkey", "google_profile", "users", ["users_id"], ["id"])
