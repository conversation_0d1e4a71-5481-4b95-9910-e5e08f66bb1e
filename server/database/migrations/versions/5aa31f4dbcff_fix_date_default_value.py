"""Fix date default value

Revision ID: 5aa31f4dbcff
Revises: 87e554b8b046
Create Date: 2024-05-09 11:15:45.562429

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5aa31f4dbcff"
down_revision: Union[str, None] = "87e554b8b046"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column(
        "users",
        "created_date",
        server_default=sa.func.current_timestamp(),
        schema="otto",
    )
    op.alter_column(
        "google_profile",
        "created_date",
        server_default=sa.func.current_timestamp(),
        schema="otto",
    )
    op.alter_column(
        "chat_threads",
        "created_date",
        server_default=sa.func.current_timestamp(),
        schema="otto",
    )
    op.alter_column(
        "checkpoints",
        "created_date",
        server_default=sa.func.current_timestamp(),
        schema="otto",
    )


def downgrade() -> None:
    op.alter_column("users", "created_date", server_default=None, schema="otto")
    op.alter_column("google_profile", "created_date", server_default=None, schema="otto")
    op.alter_column("chat_threads", "created_date", server_default=None, schema="otto")
    op.alter_column("checkpoints", "created_date", server_default=None, schema="otto")
