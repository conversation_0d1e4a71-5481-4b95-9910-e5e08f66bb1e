"""add calendar connected in user profile

Revision ID: f928d19a20b8
Revises: d9863411e152
Create Date: 2025-05-29 16:26:48.968233

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f928d19a20b8"
down_revision: Union[str, None] = "d9863411e152"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("google_profile", sa.Column("calendar_enabled", sa.<PERSON>(), nullable=True))


def downgrade() -> None:
    op.drop_column("google_profile", "calendar_enabled")
