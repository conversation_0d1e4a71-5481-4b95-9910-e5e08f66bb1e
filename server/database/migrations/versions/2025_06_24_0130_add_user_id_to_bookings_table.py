"""add user_id to bookings table

Revision ID: 0e27567912f3
Revises: aa317f9cc1cc
Create Date: 2025-06-24 01:30:12.901279

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0e27567912f3"
down_revision: Union[str, None] = "aa317f9cc1cc"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("bookings", sa.Column("user_id", sa.Integer(), nullable=True))

    op.create_foreign_key(
        op.f("bookings_user_id_fkey"),
        "bookings",
        "users",
        ["user_id"],
        ["id"],
        source_schema="otto",
        referent_schema="otto",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("bookings_user_id_fkey"), "bookings", schema="otto", type_="foreignkey")
    op.drop_column("bookings", "user_id")
    # ### end Alembic commands ###
