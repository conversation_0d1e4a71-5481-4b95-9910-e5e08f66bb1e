"""Add is_deleted column to chat threads table

Revision ID: b2561ef7f779
Revises: 17cf762858c2
Create Date: 2024-11-11 10:14:44.143990

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b2561ef7f779"
down_revision: Union[str, None] = "17cf762858c2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("chat_threads", sa.Column("is_deleted", sa.<PERSON>(), nullable=True))


def downgrade() -> None:
    op.drop_column("chat_threads", "is_deleted")
