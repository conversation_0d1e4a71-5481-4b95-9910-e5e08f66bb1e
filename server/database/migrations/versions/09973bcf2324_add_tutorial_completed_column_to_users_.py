"""Add tutorial_completed column to users table

Revision ID: 09973bcf2324
Revises: 692d43960f2f
Create Date: 2025-01-21 13:33:34.223969

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "09973bcf2324"
down_revision: Union[str, None] = "692d43960f2f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("users", sa.Column("tutorial_completed", sa.<PERSON>(), nullable=True))


def downgrade() -> None:
    op.drop_column("users", "tutorial_completed")
