"""Add frequent_flyer_attempted column to chat threads table

Revision ID: 24f2a6d42ac2
Revises: b2561ef7f779
Create Date: 2024-11-21 17:15:34.530972

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "24f2a6d42ac2"
down_revision: Union[str, None] = "b2561ef7f779"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "chat_threads",
        sa.Column("frequent_flyer_attempted_flight", sa.<PERSON>(), nullable=True),
    )
    op.add_column(
        "chat_threads",
        sa.Column("frequent_flyer_attempted_hotel", sa.<PERSON>(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("chat_threads", "frequent_flyer_attempted_hotel")
    op.drop_column("chat_threads", "frequent_flyer_attempted_flight")
