"""Create checkpoints table

Revision ID: 7d6a498a985a
Revises: cfe4f97c7a78
Create Date: 2024-04-04 14:19:41.082807

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "7d6a498a985a"
down_revision: Union[str, None] = "cfe4f97c7a78"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "checkpoints",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("thread_id", sa.Integer(), nullable=False),
        sa.Column("created_date", sa.DateTime(), nullable=False),
        sa.Column("data", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.ForeignKeyConstraint(
            ["thread_id"],
            ["otto.chat_threads.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="otto",
    )


def downgrade() -> None:
    op.drop_table("checkpoints", schema="otto")
