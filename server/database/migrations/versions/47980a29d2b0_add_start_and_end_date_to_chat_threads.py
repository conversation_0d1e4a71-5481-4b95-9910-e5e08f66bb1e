"""Add start and end date to chat threads

Revision ID: 47980a29d2b0
Revises: 58832af56595
Create Date: 2024-04-08 14:13:38.814979

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "47980a29d2b0"
down_revision: Union[str, None] = "58832af56595"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("chat_threads", sa.Column("date_start", sa.DateTime(), nullable=True))
    op.add_column("chat_threads", sa.Column("date_end", sa.DateTime(), nullable=True))


def downgrade() -> None:
    op.drop_column("chat_threads", "date_end")
    op.drop_column("chat_threads", "date_start")
