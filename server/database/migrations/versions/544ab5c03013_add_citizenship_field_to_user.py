"""add_citizenship_field_to_user

Revision ID: 544ab5c03013
Revises: 10f01a3c80d4
Create Date: 2025-04-09 20:23:12.827922

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import ARRAY

# revision identifiers, used by Alembic.
revision: str = "544ab5c03013"
down_revision: Union[str, None] = "10f01a3c80d4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("users", sa.Column("citizenship", ARRAY(sa.String()), nullable=True), schema="otto")


def downgrade() -> None:
    op.drop_column("users", "citizenship", schema="otto")
