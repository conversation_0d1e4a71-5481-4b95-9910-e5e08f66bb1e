import asyncio
from logging.config import fileConfig

from alembic import context
from sqlalchemy.engine import Connection

# Load all files that have database models
# Model files not loaded here won't be taken into consideration for
# migration by alembic
from server.database.models import (  # noqa
    bookings,
    chat_thread,
    checkpoint,
    otc_beta_codes,
    otc_group,
    # one_time_password,
    user,
    user_company_travel_policy,
    user_memories,
    user_profile,
)
from server.utils.pg_connector import Base, connection_string, pg_engine

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

NON_ORM_TABLES = (
    "alembic_version",
    "booking_dot_com_hotel_rooms",
    "booking_dot_com_hotel_rooms_small",
    "spatial_ref_sys",
)


def include_name(name, type_, parent_names):
    if type_ == "schema" and name == "public":
        return False

    if type_ == "table" and name in NON_ORM_TABLES:
        return False

    return True


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    context.configure(
        url=connection_string,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_schemas=True,
        version_table_schema=target_metadata.schema,
        include_name=include_name,
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        include_schemas=True,
        version_table_schema=target_metadata.schema,
        include_name=include_name,
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """In this scenario we need to create an Engine
    and associate a connection with the context.

    """

    async with pg_engine.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await pg_engine.dispose()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""

    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
