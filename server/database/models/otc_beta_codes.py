from __future__ import annotations

from datetime import datetime
from typing import Any, Literal, get_args

from sqlalchemy import Enum, ForeignKey, func, select, update
from sqlalchemy.orm import Mapped, mapped_column

from server.database.models.otc_group import OTCGroup
from server.utils.pg_connector import Base, async_session

OTCBetaCodeStatus = Literal["generated", "allocated", "used"]


class OTCBetaCode(Base):
    __tablename__ = "otc_beta_codes"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    group_id: Mapped[int] = mapped_column(ForeignKey(OTCGroup.id))
    code: Mapped[str]
    status: Mapped[OTCBetaCodeStatus] = mapped_column(
        Enum(
            *get_args(OTCBetaCodeStatus),
            name="otc_beta_codes_status",
            create_constraint=True,
            validate_strings=True,
        ),
        default=get_args(OTCBetaCodeStatus)[0],
        nullable=True,
    )
    created_at: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)
    used_at: Mapped[datetime] = mapped_column(nullable=True)
    used_by: Mapped[str] = mapped_column(nullable=True)

    def __init__(self, group_id: int, code: str, status: OTCBetaCodeStatus = get_args(OTCBetaCodeStatus)[0]):
        self.group_id = group_id
        self.code = code
        self.status = status

    @staticmethod
    async def from_id(id: int) -> OTCBetaCode | None:
        async with async_session() as session:
            async with session.begin():
                query = select(OTCBetaCode).where(OTCBetaCode.id == id)
                result = (await session.execute(query)).fetchone()

                return None if result is None else result[0]

    @staticmethod
    async def from_code(code: str) -> OTCBetaCode | None:
        async with async_session() as session:
            async with session.begin():
                query = select(OTCBetaCode).where(OTCBetaCode.code == code)
                result = (await session.execute(query)).fetchone()

                return None if result is None else result[0]

    @staticmethod
    async def new_otc_beta_code(otc_beta_code: OTCBetaCode):
        async with async_session() as session:
            async with session.begin():
                session.add(otc_beta_code)

    @staticmethod
    async def new_otc_beta_code_batch(otc_beta_codes: list[OTCBetaCode]):
        async with async_session() as session:
            async with session.begin():
                session.add_all(otc_beta_codes)

    async def update_fields(self, fields: dict[str, Any]):
        if len(fields) == 0:
            return

        async with async_session() as session:
            async with session.begin():
                for key, value in fields.items():
                    if key not in self.__table__.columns:
                        raise Exception(f"Could not update OTCBetaCode: key {key} not found in the table columns")

                    query = update(OTCBetaCode).where(OTCBetaCode.id == self.id).values({key: value})
                    await session.execute(query)

                    setattr(self, key, value)
