from __future__ import annotations

from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, ForeignKey, String, delete, func, select, update
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship

from server.database.models.chat_thread import ChatThread
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.utils.mongo_connector import user_preferences_collection
from server.utils.pg_connector import Base, async_session
from server.utils.settings import settings


class UserRole:
    user = "user"
    admin = "admin"
    company_admin = "company_admin"


class OrganizationStatus:
    active = "active"
    inactive = "inactive"
    suspended = "suspended"


class Organization(Base):
    __tablename__ = "organizations"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(nullable=False)
    domain: Mapped[str] = mapped_column(nullable=False, unique=True, index=True)
    image: Mapped[Optional[str]] = mapped_column(nullable=True)
    # status: Mapped[str] = mapped_column(default=OrganizationStatus.active, nullable=False)

    created_date: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)
    updated_date: Mapped[datetime] = mapped_column(
        server_default=func.current_timestamp(), onupdate=func.current_timestamp(), nullable=False
    )

    # Organization settings, TODO might be removed.
    auto_approve_domain_users: Mapped[bool] = mapped_column(default=True, nullable=False)
    require_otp_login: Mapped[bool] = mapped_column(default=True, nullable=False)

    # Travel policy and preferences (stored as JSON for flexibility), TODO might be removed.
    travel_policy: Mapped[Optional[Dict[str, Any]]] = mapped_column(type_=JSON, nullable=True)
    preferred_airlines: Mapped[Optional[List[str]]] = mapped_column(type_=ARRAY(String), nullable=True)
    preferred_hotels: Mapped[Optional[List[str]]] = mapped_column(type_=ARRAY(String), nullable=True)

    # Company payment settings, TODO might be removed.
    default_payment_method_id: Mapped[Optional[str]] = mapped_column(nullable=True)
    billing_contact_email: Mapped[Optional[str]] = mapped_column(nullable=True)

    # Relationships
    users: Mapped[List["User"]] = relationship("User", back_populates="organization")
    validated_domains: Mapped[List["OrganizationDomain"]] = relationship(
        "OrganizationDomain", back_populates="organization"
    )


class OrganizationDomain(Base):
    __tablename__ = "organization_domains"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    organization_id: Mapped[int] = mapped_column(ForeignKey("organizations.id"), nullable=False)
    domain: Mapped[str] = mapped_column(nullable=False, index=True)
    is_validated: Mapped[bool] = mapped_column(default=False, nullable=False)
    created_date: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)

    # Relationships
    organization: Mapped["Organization"] = relationship("Organization", back_populates="validated_domains")


class User(Base):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    email: Mapped[str]
    first_name: Mapped[str]
    last_name: Mapped[str]
    profile_picture: Mapped[str | None] = mapped_column(nullable=True)
    created_date: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)
    is_sintetic: Mapped[bool] = mapped_column(default=False, nullable=True)
    tutorial_completed: Mapped[bool] = mapped_column(default=False, nullable=True)

    role: Mapped[str] = mapped_column(default=UserRole.user, nullable=True)
    organization_id: Mapped[Optional[int]] = mapped_column(ForeignKey("organizations.id"), nullable=True)

    preferred_name: Mapped[str | None] = mapped_column(nullable=True)
    citizenship: Mapped[list[str]] = mapped_column(type_=ARRAY(String), nullable=True)

    organization: Mapped[Optional["Organization"]] = relationship("Organization", back_populates="users")

    @property
    def name(self):
        if self.preferred_name:
            return self.preferred_name
        return self.first_name.capitalize() if self.first_name else ""

    def __init__(self, email: str, first_name: str, last_name: str, profile_picture: str | None):
        self.email = email
        self.first_name = first_name
        self.last_name = last_name
        self.profile_picture = profile_picture

    def as_dict(self):
        json_data = {c.name: getattr(self, c.name) for c in self.__table__.columns}
        for k, v in json_data.items():
            if isinstance(v, datetime):
                json_data[k] = v.isoformat()

        return json_data

    async def refresh_fields(self, fields: dict[str, Any]):
        if len(fields) == 0:
            return

        async with async_session() as session:
            async with session.begin():
                for key, value in fields.items():
                    if key not in self.__table__.columns:
                        raise Exception(f"Could not refresh User: key {key} not found in the table columns")
                    setattr(self, key, value)

                session.add(self)

    async def delete(self):
        async with async_session() as session:
            async with session.begin():
                query = delete(User).where(User.id == self.id)
                await session.execute(query)

    async def set_tutorial_completed(self):
        async with async_session() as session:
            async with session.begin():
                self.tutorial_completed = True
                session.add(self)

    async def reset_tutorial(self):
        async with async_session() as session:
            async with session.begin():
                self.tutorial_completed = False
                session.add(self)

    @staticmethod
    async def from_id(id: int) -> User | None:
        async with async_session() as session:
            async with session.begin():
                query = select(User).where(User.id == id)
                result = (await session.execute(query)).fetchone()
                return None if result is None else result[0]

    @staticmethod
    async def from_email(email: str):
        async with async_session() as session:
            async with session.begin():
                query = select(User).where(func.lower(User.email) == func.lower(email))
                result = (await session.execute(query)).fetchone()
                return None if result is None else result[0]

    @staticmethod
    async def from_organization_id_and_role(organization_id: int, role: str):
        async with async_session() as session:
            async with session.begin():
                query = select(User).where((User.organization_id == organization_id) & (User.role == role))
                result = (await session.execute(query)).fetchone()
                return None if result is None else result[0]

    @staticmethod
    async def new_user(user: User):
        async with async_session() as session:
            async with session.begin():
                session.add(user)

    @staticmethod
    async def update_citizenship(user_id: int, citizenship: list[str]):
        async with async_session() as session:
            async with session.begin():
                stmt = update(User).where(User.id == user_id).values(citizenship=citizenship)
                await session.execute(stmt)

    @staticmethod
    async def update_names_from_profile(user_id: int, first_name: str, last_name: str):
        user = await User.from_id(user_id)
        if user is None:
            return

        fields_to_update = {}
        if first_name.strip():
            fields_to_update["first_name"] = first_name.strip()
        if last_name.strip():
            fields_to_update["last_name"] = last_name.strip()

        if fields_to_update:
            await user.refresh_fields(fields_to_update)


async def get_users_by_emails(emails):
    async with async_session() as session:
        async with session.begin():
            query = select(User).where(func.lower(User.email).in_(map(str.lower, emails)))
            result = (await session.execute(query)).scalars().all()
            return result


async def get_all_users():
    async with async_session() as session:
        async with session.begin():
            query = select(User)
            result = (await session.execute(query)).scalars().all()
            return result


async def get_all_users_session():
    async with async_session() as session:
        async with session.begin():
            # Perform a single join query to get users with session history
            query = (
                select(User, func.count(ChatThread.id).label("session_count"))
                .outerjoin(
                    ChatThread,
                    (ChatThread.users_id == User.id)
                    & (
                        ChatThread.title.not_in(
                            [
                                settings.ONBOARDING_THREAD_TITLE,
                                settings.PREFERENCES_THREAD_TITLE,
                                settings.FUTURE_TRIPS_THREAD_TITLE,
                                settings.TRAVEL_POLICY_THREAD_TITLE,
                                settings.ONBOARDING_PAGE_TITLE,
                            ]
                        )
                    ),
                )
                .group_by(User.id)
                .order_by(User.id.desc())
            )

            # Execute the query and process results
            results = (await session.execute(query)).all()
            users = [user for user, session_count in results]
            user_ids = [user.id for user in users]

            # Batch query for company travel policies
            policies_query = select(UserCompanyTravelPolicy.user_id).where(
                UserCompanyTravelPolicy.user_id.in_(user_ids)
            )
            policy_user_ids = [row[0] for row in (await session.execute(policies_query)).all()]

            # Batch query for user preferences from MongoDB
            user_preferences_cursor = user_preferences_collection.find(
                {"users_id": {"$in": user_ids}}, {"_id": 0, "users_id": 1}
            )
            pref_user_ids = [doc["users_id"] for doc in await user_preferences_cursor.to_list(length=None)]

            # Add properties to each user
            for i, (user, session_count) in enumerate(results):
                setattr(user, "is_onboarding_completed", session_count > 0)
                setattr(user, "has_user_preferences", user.id in pref_user_ids)
                setattr(user, "has_company_travel_policy", user.id in policy_user_ids)

            return users
