from __future__ import annotations

from datetime import datetime
from typing import Any, Sequence, Tuple

from sqlalchemy import Foreign<PERSON>ey, Row, cast, func, select, update
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column

from server.utils.pg_connector import Base, async_session
from server.utils.settings import settings


class ChatThread(Base):
    __tablename__ = "chat_threads"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    users_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    title: Mapped[str]
    date_start: Mapped[datetime | None] = mapped_column(nullable=True)
    date_end: Mapped[datetime | None] = mapped_column(nullable=True)
    created_date: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)
    is_mined: Mapped[bool] = mapped_column(default=False, nullable=True)
    is_deleted: Mapped[bool] = mapped_column(default=False, nullable=True)
    frequent_flyer_attempted_flight: Mapped[bool] = mapped_column(default=False, nullable=True)
    frequent_flyer_attempted_hotel: Mapped[bool] = mapped_column(default=False, nullable=True)
    extra: Mapped[dict] = mapped_column(JSONB, default={}, nullable=True)

    def __init__(self, users_id: int, title: str, extra: dict = {}, frequent_flyer_attempted_flight: bool = False):
        self.users_id = users_id
        self.title = title
        self.extra = extra
        self.frequent_flyer_attempted_flight = frequent_flyer_attempted_flight

    @staticmethod
    async def from_id(id: int, include_deleted: bool = False):
        async with async_session() as session:
            async with session.begin():
                cond = ChatThread.id == id
                if not include_deleted:
                    cond &= (ChatThread.is_deleted == False) | (
                        ChatThread.is_deleted == None  # noqa
                    )

                query = select(ChatThread).where(cond)
                result = (await session.execute(query)).fetchone()

                if result is None:
                    return None

                return result[0]

    @staticmethod
    async def from_user_id(user_id: int, filter_only_upcoming: bool = False, include_deleted: bool = False):
        async with async_session() as session:
            async with session.begin():
                cond = ChatThread.users_id == user_id
                if filter_only_upcoming:
                    cond &= (
                        (ChatThread.date_end >= func.current_timestamp())
                        | (ChatThread.date_end == None)  # noqa
                        | (
                            (ChatThread.date_end == None)  # noqa
                            & (ChatThread.date_start >= func.current_timestamp())
                        )
                    )

                if not include_deleted:
                    cond &= (ChatThread.is_deleted == False) | (
                        ChatThread.is_deleted == None  # noqa
                    )

                query = select(ChatThread).where(cond).order_by(ChatThread.id.asc())
                results = (await session.execute(query)).fetchall()

                return results

    @staticmethod
    async def from_user_id_last_entry(user_id: int, include_deleted: bool = False) -> ChatThread | None:
        async with async_session() as session:
            async with session.begin():
                cond = ChatThread.users_id == user_id
                if not include_deleted:
                    cond &= (ChatThread.is_deleted == False) | (
                        ChatThread.is_deleted == None  # noqa
                    )

                query = select(ChatThread).where(cond).order_by(ChatThread.id.desc()).limit(1)
                results: Row[Tuple[ChatThread]] | None = (await session.execute(query)).fetchone()

                if results is None:
                    return None

                return results[0]

    @staticmethod
    async def from_user_id_and_title(
        user_id: int, title: str, include_deleted: bool = False
    ) -> Sequence[Row[Tuple[ChatThread]]]:
        async with async_session() as session:
            async with session.begin():
                cond = (ChatThread.users_id == user_id) & (ChatThread.title == title)

                if not include_deleted:
                    cond &= (ChatThread.is_deleted == False) | (
                        ChatThread.is_deleted == None  # noqa
                    )

                query = select(ChatThread).where(cond).order_by(ChatThread.id.asc())
                results = (await session.execute(query)).fetchall()

                return results

    @staticmethod
    async def create_initial_chat_threads(user_id: int) -> list[ChatThread]:
        """
        Create multiple initial chat threads for a user in a single database transaction.

        Args:
            user_id: The ID of the user for whom to create chat threads

        Returns:
            List of created ChatThread objects
        """
        thread_titles = [
            settings.ONBOARDING_PAGE_TITLE,
            settings.PREFERENCES_THREAD_TITLE,
            settings.TRAVEL_POLICY_THREAD_TITLE,
        ]

        chat_threads = [ChatThread(users_id=user_id, title=title) for title in thread_titles]

        async with async_session() as session:
            async with session.begin():
                session.add_all(chat_threads)

        return chat_threads

    @staticmethod
    async def new_chat_thread(chat_thread: ChatThread):
        async with async_session() as session:
            async with session.begin():
                session.add(chat_thread)
            await session.refresh(chat_thread)
            return chat_thread

    async def update_fields(self, fields: dict[str, Any]):
        if len(fields) == 0:
            return

        async with async_session() as session:
            async with session.begin():
                update_values = {}

                for key, value in fields.items():
                    if key == "extra":
                        # Handle the extra field specially for JSONB operations
                        if not isinstance(value, dict):
                            raise TypeError("The 'extra' field must be a dictionary")
                        # Merge the new extra values with existing ones
                        # Cast empty dict to JSONB to avoid encoding issues
                        update_values["extra"] = func.coalesce(ChatThread.extra, cast({}, JSONB)).op("||")(
                            cast(value, JSONB)
                        )
                    else:
                        if key not in self.__table__.columns:
                            raise Exception(f"Could not refresh User: key {key} not found in the table columns")
                        update_values[key] = value

                # Perform a single update with all the values
                query = update(ChatThread).where(ChatThread.id == self.id).values(update_values)
                await session.execute(query)

                # Update the instance attributes
                for key, value in fields.items():
                    setattr(self, key, value)

    @staticmethod
    async def all_upcoming():
        async with async_session() as session:
            async with session.begin():
                query = (
                    select(ChatThread.id)
                    .where(
                        (ChatThread.date_start >= func.current_timestamp())
                        & ((ChatThread.is_deleted == False) | (ChatThread.is_deleted == None))  # noqa
                    )
                    .order_by(ChatThread.id.asc())
                )

                results = (await session.execute(query)).fetchall()
                return results
