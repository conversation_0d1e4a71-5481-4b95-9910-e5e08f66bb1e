from __future__ import annotations

from uuid import UUID

from sqlalchemy import select
from sqlalchemy.dialects.postgresql import <PERSON><PERSON>N<PERSON>
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import Mapped, mapped_column

from server.utils.pg_connector import Base, async_session


class UserMemories(Base):
    __tablename__ = "user_memories"
    __table_args__ = {"schema": "otto"}

    id: Mapped[UUID] = mapped_column(PG_UUID, primary_key=True)
    payload: Mapped[dict] = mapped_column(JSONB)

    def __init__(
        self,
        id: UUID,
        payload: dict,
    ):
        self.id = id
        self.payload = payload

    @staticmethod
    async def query_by_payload_field(field: str, value: str) -> list[UserMemories]:
        """
        Query memories by a specific payload field
        Example: query_by_payload_field("memory_isolation_key", "TRIP_PLAN_Delaware_2025-03-05")
                query_by_payload_field("type", "TRIP_PLAN")
        """
        async with async_session() as session:
            async with session.begin():
                query = select(UserMemories).where(UserMemories.payload[field].astext == value)
                result = await session.execute(query)
                return [row[0] for row in result.fetchall()]
