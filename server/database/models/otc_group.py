from __future__ import annotations

from datetime import datetime

from sqlalchemy import func, select
from sqlalchemy.orm import Mapped, mapped_column

from server.utils.pg_connector import Base, async_session


class OTCGroup(Base):
    __tablename__ = "otc_groups"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    name: Mapped[str]
    created_at: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)

    def __init__(self, name: str):
        self.name = name

    @staticmethod
    async def from_id(id: int) -> OTCGroup | None:
        async with async_session() as session:
            async with session.begin():
                query = select(OTCGroup).where(OTCGroup.id == id)
                result = (await session.execute(query)).fetchone()

                return None if result is None else result[0]

    @staticmethod
    async def from_name(name: str) -> OTCGroup | None:
        async with async_session() as session:
            async with session.begin():
                query = select(OTCGroup).where(OTCGroup.name == name)
                result = (await session.execute(query)).fetchone()

                return None if result is None else result[0]

    @staticmethod
    async def new_otc_group(otc_group: OTCGroup):
        async with async_session() as session:
            async with session.begin():
                session.add(otc_group)
