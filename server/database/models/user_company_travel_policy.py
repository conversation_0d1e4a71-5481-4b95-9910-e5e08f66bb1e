from datetime import datetime
from typing import Any, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, delete, select
from sqlalchemy.orm import Mapped, mapped_column

from baml_client.types import CompanyPolicy
from server.utils.pg_connector import Base, async_session


class UserCompanyTravelPolicy(Base):
    __tablename__ = "user_company_travel_policies"

    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), primary_key=True)
    parsed_travel_policy: Mapped[dict | None] = mapped_column(type_=JSON, nullable=True)

    def __init__(self, user_id: int, parsed_travel_policy: dict | None):
        self.user_id = user_id
        self.parsed_travel_policy = parsed_travel_policy

    def as_dict(self):
        json_data = {c.name: getattr(self, c.name) for c in self.__table__.columns}
        for k, v in json_data.items():
            if isinstance(v, datetime):
                json_data[k] = v.isoformat()

        return json_data

    async def refresh_fields(self, fields: dict[str, Any]):
        if len(fields) == 0:
            return

        async with async_session() as session:
            async with session.begin():
                for key, value in fields.items():
                    if key not in self.__table__.columns:
                        raise Exception(f"Could not refresh User: key {key} not found in the table columns")
                    setattr(self, key, value)

                session.add(self)

    async def delete(self):
        async with async_session() as session:
            async with session.begin():
                query = delete(UserCompanyTravelPolicy).where(UserCompanyTravelPolicy.user_id == self.user_id)
                await session.execute(query)

    @staticmethod
    async def from_user_id(user_id: int) -> Optional["UserCompanyTravelPolicy"]:
        async with async_session() as session:
            async with session.begin():
                query = select(UserCompanyTravelPolicy).where(UserCompanyTravelPolicy.user_id == user_id)
                result = (await session.execute(query)).fetchone()
                return None if result is None else result[0]

    @staticmethod
    async def update_company_travel_policy(user_id: int, parsed_travel_policy: CompanyPolicy | None):
        async with async_session() as session:
            async with session.begin():
                query = select(UserCompanyTravelPolicy).where(UserCompanyTravelPolicy.user_id == user_id)
                result = (await session.execute(query)).fetchone()
                if result is None:
                    new_policy = UserCompanyTravelPolicy(
                        user_id, None if parsed_travel_policy is None else parsed_travel_policy.model_dump()
                    )
                    session.add(new_policy)
                else:
                    result[0].parsed_travel_policy = (
                        None if parsed_travel_policy is None else parsed_travel_policy.model_dump()
                    )
                    session.add(result[0])
