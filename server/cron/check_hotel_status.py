from datetime import datetime
from typing import Any

from hotel_agent.booking_dot_com_models import BookingStatus
from in_trip.in_trip import InTripAgent
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User
from server.schemas.authenticate.user import User as UserSchema
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.utils.async_requests import make_post_request
from server.utils.logger import logger
from server.utils.settings import settings


class CheckHotelStatus:
    MAX_RESERVATION_BATCH_SIZE = 100
    HEADERS = {
        "Content-Type": "application/json",
        "X-Affiliate-Id": settings.BOOKING_DOT_COM_AFFILIATE_ID,
        "Authorization": f"Bearer {settings.BOOKING_DOT_COM_API_KEY}",
    }
    HOTEL_ORDER_DETAILS_URL = "https://demandapi.booking.com/3.1/orders/details/accommodations"
    CANCELLED_STATUSES = ["cancelled", "cancelled_by_accommodation", "cancelled_by_guest"]

    async def sync_hotels_status(self):
        hotels_bookings = await self.get_all_upcoming_hotel_ids()

        reservations: list[int] = [
            int(item.content["reservation_number"])
            for item in hotels_bookings
            if item.content.get("reservation_number") is not None
        ]

        logger.info(
            f"[CheckHotelStatus] Found {len(reservations)} hotels that should have their status checked: {reservations}."
        )

        chunks = self.divide_chunks(reservations, self.MAX_RESERVATION_BATCH_SIZE)
        for chunk in chunks:
            hotel_orders_details: list[dict[str, Any]] = await self.get_hotel_order_details(chunk)
            await self.process_status(hotel_orders_details)

    async def notify_in_trip_hotel_changes(self):
        hotels_bookings = await self.get_all_in_trip_hotel_ids()

        reservations: list[int] = []
        hotel_booking_dict = {}
        for item in hotels_bookings:
            if reservation_number := item.content.get("reservation_number"):
                res_num = int(reservation_number)
                hotel_booking_dict[res_num] = item
                reservations.append(res_num)

        logger.info(
            f"[IN_TRIP] Found {len(reservations)} in-trip hotels that should have their status checked: {reservations}."
        )

        chunks = self.divide_chunks(reservations, self.MAX_RESERVATION_BATCH_SIZE)
        for chunk in chunks:
            hotel_orders_details: list[dict[str, Any]] = await self.get_hotel_order_details(chunk)
            await self.process_hotel_changes(hotel_orders_details, hotel_booking_dict)
            await self.process_status(hotel_orders_details)

    async def get_hotel_order_details(self, chunk):
        payload = {"reservations": chunk, "currency": "USD"}

        response = await make_post_request(self.HOTEL_ORDER_DETAILS_URL, data=payload, headers=self.HEADERS)
        logger.info(f"[CheckHotelStatus] Hotel order details response: {response}")
        return response.get("data", [])

    async def get_all_upcoming_hotel_ids(self):
        """
        Fetch upcoming accommodation bookings starting today or later,
        excluding those marked as 'cancelled'.
        """
        today_str = datetime.now().date()  # assuming UTC
        query = {"type": "accommodations", "start_date__gt": today_str, "content.status__ne": "cancelled"}

        hotels_bookings: list[Booking] = await Booking.from_query_batch(query)

        return hotels_bookings

    @staticmethod
    async def get_all_in_trip_hotel_ids():
        today_str = datetime.now().date()  # assuming UTC
        query = {
            "type": "accommodations",
            "start_date__lte": today_str,
            "end_date__gte": today_str,
            "content.status__ne": "cancelled",
        }

        hotels_bookings: list[Booking] = await Booking.from_query_batch(query)
        return hotels_bookings

    async def process_status(self, hotels: list[dict[str, Any]]):
        cancelled_hotels: list[str] = []

        for hotel in hotels:
            status = hotel.get("status", "")
            if status in self.CANCELLED_STATUSES:
                cancelled_hotels.append(str(hotel.get("reservation", "")))

        logger.info(
            f"[CheckHotelStatus] Processed {len(hotels)} hotels, found {len(cancelled_hotels)} with status cancelled."
        )

        if len(cancelled_hotels) > 0:
            await self.save_cancelled_status_to_mongo(cancelled_hotels)

    async def save_cancelled_status_to_mongo(self, cancelled_hotel_reservations: list[str]):
        await Booking.update_fields(
            {"content.reservation_number": cancelled_hotel_reservations},
            {"content.status": "cancelled", "status": BookingStatus.CANCELLED.name},
        )

    async def process_hotel_changes(self, changes: list[dict[str, Any]], booking_dict: dict[int, Booking]):
        for change in changes:
            try:
                reservation_number = change["reservation"]
                status = change.get("status", "")
                if status not in ["cancelled_by_accommodation", "cancelled_by_guest", "no_show"]:
                    continue

                hotel_booking = booking_dict.get(reservation_number)
                if not hotel_booking:
                    logger.warning(f"[IN_TRIP] Hotel booking not found for reservation {reservation_number}")
                    continue

                thread_info: ChatThread | None = await ChatThread.from_id(hotel_booking.thread_id)
                if not thread_info:
                    logger.warning(f"[IN_TRIP] Thread not found for booking {hotel_booking.id}")
                    continue

                user = await User.from_id(thread_info.users_id)
                if not user:
                    logger.warning(f"[IN_TRIP] User not found for thread {thread_info.id}")
                    continue

                in_trip_enabled = await is_feature_flag_enabled(user.id, FeatureFlags.ENABLE_IN_TRIP)

                if not in_trip_enabled:
                    logger.info(
                        f"[IN_TRIP] In trip not enabled for user {user.id} or booking {hotel_booking.id} is not in trip, skipping."
                    )
                    continue

                user_schema = UserSchema.from_orm_user(user)
                in_trip_agent = InTripAgent(user_schema, thread_info)
                await in_trip_agent.handle_hotel_change_for_in_trip(hotel_booking, change, user_schema)

                logger.info(f"[IN_TRIP] Processed in-trip hotel change for reservation {reservation_number}")

            except Exception as e:
                logger.error(f"[IN_TRIP] Error processing in-trip hotel change: {str(e)}")

    @staticmethod
    def divide_chunks(list_to_divide, chunk_size):
        for i in range(0, len(list_to_divide), chunk_size):
            yield list_to_divide[i : i + chunk_size]
