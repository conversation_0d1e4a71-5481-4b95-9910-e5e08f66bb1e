import asyncio
from typing import Any

from pymongo import UpdateOne
from temporalio import activity

from server.utils.async_requests import make_post_request
from server.utils.logger import logger
from server.utils.mongo_connector import (
    hotels_data_cache_collection,
    hotels_data_cache_dev_collection,
    hotels_data_cache_stg_collection,
)
from server.utils.settings import settings


class HotelDataCache:
    HEADERS = {
        "Content-Type": "application/json",
        "X-Affiliate-Id": settings.BOOKING_DOT_COM_AFFILIATE_ID,
        "Authorization": f"Bearer {settings.BOOKING_DOT_COM_API_KEY}",
    }

    HOTEL_DETAILS_URL = "https://demandapi.booking.com/3.1/accommodations/details"

    COUNTRIES_ENDPOINT = "https://demandapi.booking.com/3.1/common/locations/countries"

    async def get_all_supported_countries(self):
        response = await make_post_request(self.COUNTRIES_ENDPOINT, headers=self.HEADERS, data={"rows": 250})

        countries_data = response.get("data", [])

        if not countries_data:
            logger.error("No countries data found in the response.")
            return []
        else:
            return [country["id"] for country in countries_data if country.get("id")]

    async def sync_all(self, country_code: str):
        payload = {
            "country": country_code,
            "extras": [
                "description",
                "facilities",
                "payment",
                "photos",
                "policies",
                "rooms",
            ],
            "rows": 1000,
            # "accommodation_types": [204],  # Just hotels
            "languages": [
                "en-us",
            ],
        }

        inserted_count = 0
        updated_count = 0

        try:
            keep_paging = True
            while keep_paging:
                response = await make_post_request(self.HOTEL_DETAILS_URL, data=payload, headers=self.HEADERS)

                hotels_data = response.get("data", [])
                res = await self.save_to_mongo(hotels_data)
                inserted_count += res.get("inserted_count", 0)
                updated_count += res.get("updated_count", 0)
                activity.heartbeat(f"Synced {inserted_count + updated_count} hotels into data cache.")

                next_page = response.get("next_page", None)
                logger.info(
                    f"Synced {inserted_count + updated_count} hotels into data cache. Next page token: {next_page}"
                )

                if next_page:
                    payload = {"page": next_page}
                else:
                    logger.info(f"No more pages to sync, last page response: {response}")
                    keep_paging = False

            logger.info(
                f"Synced all booking.com hotels into data cache, inserted {inserted_count} docs and updated {updated_count} docs."
            )
            return {
                "inserted_count": inserted_count,
                "updated_count": updated_count,
            }

        except Exception as e:
            logger.error(f"Error syncing all booking.com hotels into data cache, failed with error {str(e)}")
            raise e

    async def save_to_mongo(self, hotels_data: list[dict[str, Any]]):
        async def __update_other_env(updates: Any):
            if hotels_data_cache_dev_collection is not None:
                logger.info("Updating hotels data cache in dev environment")
                await hotels_data_cache_dev_collection.bulk_write(updates)
            if hotels_data_cache_stg_collection is not None:
                logger.info("Updating hotels data cache in stg environment")
                await hotels_data_cache_stg_collection.bulk_write(updates)

        batch_updates = []
        for hotel_data in hotels_data:
            self.update_geo_location_field(hotel_data)
            batch_updates.append(UpdateOne({"id": hotel_data["id"]}, {"$set": hotel_data}, upsert=True))

        if len(batch_updates) > 0:
            results = await hotels_data_cache_collection.bulk_write(batch_updates)

            asyncio.create_task(__update_other_env(batch_updates))

            ids = []
            if results.upserted_ids:
                for index, doc_id in results.upserted_ids.items():
                    ids.append(str(doc_id))
            else:
                logger.info("No documents were upserted in this batch")

            logger.info(
                f"Hotels data cache updated, {results.inserted_count} documents inserted, {results.matched_count} documents matched, {results.modified_count} documents updated, {results.upserted_count} documents upserted, {results.deleted_count} documents deleted, {len(ids)} documents upserted with IDs: {ids}"
            )

            return {
                "inserted_count": results.upserted_count,
                "updated_count": results.modified_count,
            }
        return {
            "inserted_count": 0,
            "updated_count": 0,
        }

    def update_geo_location_field(self, hotel_data: dict[str, Any]):
        if "location" in hotel_data and "coordinates" in hotel_data["location"]:
            coords = hotel_data["location"]["coordinates"]
            hotel_data["geo_location"] = {
                "type": "Point",
                "coordinates": [coords["longitude"], coords["latitude"]],
            }
