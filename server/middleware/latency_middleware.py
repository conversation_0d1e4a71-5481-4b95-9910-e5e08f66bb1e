import time

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

from server.utils.cloudwatch_metrics import metrics_service
from server.utils.logger import logger


class LatencyMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.perf_counter_ns()
        response = await call_next(request)
        latency_ms = (time.perf_counter_ns() - start_time) / 1_000_000
        metrics_service.aws_send_metric_data(
            {
                self._normalize_path(request.url.path): {
                    "data": {"elapsed_time": latency_ms / 1_000, "tag": request.method}
                }
            }
        )
        logger.info(f"Latency for {request.method} {request.url.path}: {latency_ms:.2f} ms.")
        return response

    def _normalize_path(self, path: str) -> str:
        # Replace any numeric segments with a placeholder
        return "/".join(["{id}" if segment.isdigit() else segment for segment in path.strip("/").split("/")])
