from fastapi import APIRouter

from server.api.v1.endpoints import websocket
from server.api.v1.endpoints.admin import (
    ai_tester,
    feature_flags,
    flight_search_evaluation,
    manage_otc,
    message,
    onboarding_conversation,
    organization,
    thread_import,
    trip_conversation,
    trip_itinerary,
    trips,
    user,
    user_whitelist,
)
from server.api.v1.endpoints.authenticate import authenticate
from server.api.v1.endpoints.deepgram import transcribe
from server.api.v1.endpoints.documents import receipts
from server.api.v1.endpoints.email_template import jinjia_preview_router
from server.api.v1.endpoints.google_api import get_calendar_events
from server.api.v1.endpoints.partners.spotnana import (
    auth as partners_spotnana_auth,
)
from server.api.v1.endpoints.partners.spotnana import (
    webhook_travel_delivery,
)
from server.api.v1.endpoints.spotnana import (
    create_spotnana_user_profile,
    get_spotnana_user,
    update_frequent_flyer_number,
)
from server.api.v1.endpoints.test import sse
from server.api.v1.endpoints.trips import (
    create_trip,
    delete_trip,
    get_messages,
    get_onboarding_details,
    get_trip_details,
    list_trips,
    post_message,
    sample_trip,
)
from server.api.v1.endpoints.user import (
    user_account_delete,
    user_logs,
    user_profile,
    user_profile_remove_preference,
    user_session_history,
)
from server.api.v1.endpoints.user_profile import (
    airline_credits,
    loyalty_programs,
    payment_information,
    personal_information,
)
from server.api.v1.endpoints.vgs import test
from server.api.v1.endpoints.webhooks import mailgun_router

prefix = "/api"

api_router = APIRouter()
api_router.include_router(authenticate.router, prefix=prefix, tags=["auth"])
api_router.include_router(websocket.router, prefix=prefix, tags=["websocket"])
api_router.include_router(user_profile.router, prefix=prefix, tags=["user"])
api_router.include_router(user_logs.router, prefix=prefix, tags=["user"])
api_router.include_router(create_trip.router, prefix=prefix, tags=["trips"])
api_router.include_router(list_trips.router, prefix=prefix, tags=["trips"])
api_router.include_router(delete_trip.router, prefix=prefix, tags=["trips"])
api_router.include_router(get_onboarding_details.router, prefix=prefix, tags=["trips"])
api_router.include_router(get_trip_details.router, prefix=prefix, tags=["trips"])
api_router.include_router(post_message.router, prefix=prefix, tags=["trips"])
api_router.include_router(get_messages.router, prefix=prefix, tags=["trips"])
api_router.include_router(user_session_history.router, prefix=prefix, tags=["user"])
api_router.include_router(get_calendar_events.router, prefix=prefix, tags=["calendar_api"])
api_router.include_router(user_account_delete.router, prefix=prefix, tags=["user"])
api_router.include_router(user_profile_remove_preference.router, prefix=prefix, tags=["user"])
api_router.include_router(feature_flags.router, prefix=prefix, tags=["user"])
api_router.include_router(test.router, prefix=prefix, tags=["vgs"])
api_router.include_router(create_spotnana_user_profile.router, prefix=prefix, tags=["spotnana"])
api_router.include_router(get_spotnana_user.router, prefix=prefix, tags=["spotnana"])
api_router.include_router(update_frequent_flyer_number.router, prefix=prefix, tags=["spotnana"])
api_router.include_router(transcribe.router, tags=["Deepgram"])

# Admin endpoints
api_router.include_router(sample_trip.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(user.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(trips.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(trip_conversation.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(onboarding_conversation.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(trip_itinerary.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(user_whitelist.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(ai_tester.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(message.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(manage_otc.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(thread_import.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(flight_search_evaluation.router, prefix=prefix, tags=["Admin user"])
api_router.include_router(organization.router, prefix=prefix, tags=["Admin - Organizations"])

# PARTNERS SPOTNANA endpoints
api_router.include_router(partners_spotnana_auth.router, prefix=prefix, tags=["Partners Spotnana"])
api_router.include_router(webhook_travel_delivery.router, prefix=prefix, tags=["Partners Spotnana"])

# WEBHOOKS endpoints
api_router.include_router(mailgun_router, prefix=prefix, tags=["Webhooks"])

# TEST endpoints
api_router.include_router(sse.router, prefix=prefix, tags=["Test"])
api_router.include_router(jinjia_preview_router, prefix=prefix, tags=["Test"])

# User Profile
api_router.include_router(personal_information.router, prefix=f"{prefix}/user-profile", tags=["User Profile"])
api_router.include_router(payment_information.router, prefix=f"{prefix}/user-profile", tags=["User Profile"])
api_router.include_router(airline_credits.router, prefix=f"{prefix}/user-profile", tags=["User Profile"])
api_router.include_router(loyalty_programs.router, prefix=f"{prefix}/user-profile", tags=["User Profile"])

# Stream download documents
api_router.include_router(receipts.router, prefix=prefix, tags=["Documents"])
