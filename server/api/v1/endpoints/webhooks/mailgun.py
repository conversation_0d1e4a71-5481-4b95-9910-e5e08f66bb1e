import hashlib
import hmac
import json
import time
from datetime import timed<PERSON><PERSON>
from typing import Optional

import markdown2
from fastapi import APIRouter, Depends, Form, HTTPException, Request, status
from jinja2 import Environment, FileSystemLoader
from starlette.responses import JSONResponse
from temporalio.common import WorkflowIDReusePolicy

from server.database.models.chat_thread import ChatThread
from server.database.models.user import User as UserDB
from server.dependencies import get_remote_cache
from server.schemas.authenticate.user import User
from server.utils.async_requests import make_post_request_with_any_data
from server.utils.logger import Logger, logger
from server.utils.remote_cache import RemoteCache
from server.utils.settings import settings
from workflows.email_proces_workflow import (
    MailgunEmailWorkflow,
    MailgunWorkflowInput,
    insert_original_message,
    style_html_table,
)
from workflows.workflow import get_temporal_client

router = APIRouter()

OTTO_DOMAIN = "@ottotheagent.com"
env = Environment(loader=FileSystemLoader("email_templates"))


def get_email_message_key(message_id: str):
    return f"mailgun_message_id:{message_id}"


def validate_mailgun_signature(timestamp: str, token: str, signature: str) -> bool:
    assert settings.MAILGUN_WEBHOOK_SIGNING_KEY is not None
    signed = hmac.new(
        key=settings.MAILGUN_WEBHOOK_SIGNING_KEY.encode(),
        msg=f"{timestamp}{token}".encode(),
        digestmod=hashlib.sha256,
    ).hexdigest()
    return hmac.compare_digest(signed, signature)


async def send_user_not_found_email(sender: str, subject: str, message_id: Optional[str], stripped_text: str) -> None:
    message_id = message_id or f"otto-rejection-{sender}-{int(time.time())}"
    not_found_message = (
        "Thanks for reaching out to Otto. "
        "Unfortunately, email booking is only available for existing Otto users. "
        f"Please visit our website : {settings.CLIENT_DOMAIN} to sign up first and start planning your travels with us!"
    )

    summary_as_html = markdown2.markdown(not_found_message)
    summary_as_html = style_html_table(summary_as_html)

    context = {"trip_title": "User Not Found", "base_url": settings.CLIENT_DOMAIN, "summary_html": summary_as_html}
    template = env.get_template("response_base.html")
    response_body_as_html = template.render(context)
    insert_original_message(response_body_as_html, stripped_text)

    try:
        await make_post_request_with_any_data(
            f"https://api.mailgun.net/v3/{settings.MAILGUN_REPLYTO_EMAIL_DOMAIN}/messages",
            data={
                "from": f"Otto <noreply@{settings.MAILGUN_REPLYTO_EMAIL_DOMAIN}>",
                "to": [sender],
                "subject": f"Re: {subject}",
                "html": response_body_as_html,
                "h:In-Reply-To": message_id,
                "h:References": message_id,
            },
            auth=("api", settings.MAIL_GUN_API_KEY or ""),
        )
        logger.info(f"[MAILGUN WEBHOOK] Sent user not found email to: {sender}")
    except Exception as e:
        logger.error(f"[MAILGUN WEBHOOK] Failed to send user not found email to {sender}: {e}")


@router.post("/webhooks/mailgun", response_class=JSONResponse)
async def mailgun_webhook(
    request: Request,
    signature: Optional[str] = Form(None),
    timestamp: Optional[str] = Form(None),
    token: Optional[str] = Form(None),
    recipient: Optional[str] = Form(None),
    sender: Optional[str] = Form(None),
    subject: Optional[str] = Form(None),
    from_field: Optional[str] = Form(None, alias="from"),
    to: Optional[str] = Form(None),
    body_plain: Optional[str] = Form(None, alias="body-plain"),
    body_html: Optional[str] = Form(None, alias="body-html"),
    stripped_text: Optional[str] = Form(None, alias="stripped-text"),
    stripped_html: Optional[str] = Form(None),
    message_headers: Optional[str] = Form(None, alias="message-headers"),
    remote_cache: RemoteCache = Depends(get_remote_cache),
):
    if not (signature and timestamp and token):
        logger.error("[MAILGUN WEBHOOK] Missing Mailgun signature fields")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing Mailgun signature fields",
        )

    if not validate_mailgun_signature(timestamp, token, signature):
        logger.error("[MAILGUN WEBHOOK] Invalid Mailgun signature")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid Mailgun signature",
        )

    assert sender, "Sender is required"
    trip_title = subject or "(no subject)"

    message_headers_list = json.loads(message_headers) if message_headers else []
    logger.info(f"[MAILGUN WEBHOOK] parsed_message_headers_list: {message_headers}")
    message_id = next((header[1][1:-1] for header in message_headers_list if header[0].lower() == "message-id"), None)

    user_in_db = await UserDB.from_email(sender)
    error_message = None

    # Special logic for demo accounts
    if user_in_db is None and OTTO_DOMAIN in sender:
        if sender and from_field and sender not in from_field:
            alternative_sender = from_field[from_field.index("<") + 1 : from_field.index(">")]
            if "@" in alternative_sender and "." in alternative_sender:
                user_in_db = await UserDB.from_email(alternative_sender)
                if user_in_db is None:
                    error_message = (
                        f"[MAILGUN WEBHOOK] User not found for alternative email sender either: {alternative_sender}"
                    )
            else:
                error_message = f"[MAILGUN WEBHOOK] Invalid alternative sender email format: {alternative_sender}"
        else:
            error_message = f"[MAILGUN WEBHOOK] User not found for email sender: {sender}"

    if not user_in_db:
        logger.error(
            error_message
            or f"[MAILGUN WEBHOOK] User not found for email sender: {sender}, sending friendly rejection email"
        )
        await send_user_not_found_email(sender, subject or "(no subject)", message_id, stripped_text or "")
        raise HTTPException(
            status_code=status.HTTP_406_NOT_ACCEPTABLE,
            detail=error_message or f"User not found for email sender: {sender}",
        )

    # dedup message_id by using redis
    if message_id:
        exists = await remote_cache.exists(get_email_message_key(message_id))
        if exists:
            logger.info(f"[MAILGUN WEBHOOK] Duplicate message_id found: {message_id}, skipping processing")
            return JSONResponse(content={"success": True}, status_code=200)

    trip_user = User.from_orm_user(user_in_db)
    assert recipient, "Recipient is required"
    existing_thread_id_list = recipient.split("+")
    existing_thread_id = None
    if len(existing_thread_id_list) == 2:
        try:
            existing_thread_id = int(
                existing_thread_id_list[1].split("@")[0][len(settings.EMAIL_ADDRESS_AFTER_PLUS_SIGN_PREFIX) :]
            )
        except Exception as e:
            logger.error(f"[MAILGUN WEBHOOK] Error parsing thread ID: {e}")

    if existing_thread_id is not None:
        thread = await ChatThread.from_id(existing_thread_id, include_deleted=True)
    else:
        thread = await ChatThread.new_chat_thread(
            ChatThread(
                title=trip_title,
                users_id=trip_user.id,
                extra={
                    "source": "EMAIL",
                },
                frequent_flyer_attempted_flight=True,
            )
        )
    assert thread is not None, "Thread not found"
    Logger.bind_log_context(trip_id=thread.id, user_id=trip_user.id)
    if not stripped_text or not message_id:
        logger.error(
            f"[MAILGUN WEBHOOK] Missing required fields in the request: stripped_text={stripped_text}, message_id={message_id}"
        )
        return JSONResponse(content={"success": True}, status_code=200)

    logger.info(f"[MAILGUN WEBHOOK] Using thread: {thread.id} for user: {trip_user.email}")

    try:
        client = await get_temporal_client()
        workflow_id = f"email_chat_{trip_user.id}_{thread.id}"

        await client.start_workflow(
            MailgunEmailWorkflow.run,
            MailgunWorkflowInput(
                user_id=trip_user.id,
                thread_id=thread.id,
                sender=sender,
                recipient=recipient,
                subject=subject or "",
                stripped_text=stripped_text,
                body_plain=body_plain,
                message_id=message_id,
                thread_title=thread.title,
            ),
            id=workflow_id,
            task_queue="otto-worker-queue",
            id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
        )

        # mailgun retry maximum is 4 hours: https://help.mailgun.com/hc/en-us/articles/202236504-Webhooks
        await remote_cache.set(get_email_message_key(message_id), "1", expire=timedelta(hours=5))
        logger.info(f"[MAILGUN WEBHOOK] Started Temporal workflow {workflow_id}")
    except Exception as e:
        logger.error(f"[MAILGUN WEBHOOK] Error starting Temporal workflow: {e}")
        return JSONResponse(content={"success": False, "error": str(e)}, status_code=500)

    return JSONResponse(content={"success": True}, status_code=200)
