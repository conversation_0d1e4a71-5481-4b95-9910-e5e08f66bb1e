import asyncio
import json
import time
import traceback
from enum import Enum
from functools import partial
from typing import Any, Optional, Sequence, Set, Tuple

from fastapi import (
    APIRouter,
    Depends,
    WebSocket,
    WebSocketDisconnect,
    WebSocketException,
    status,
)
from prometheus_client import Histogram
from sqlalchemy.engine.row import Row
from starlette.websockets import WebSocketState

from baml_client.types import CompanyPolicy
from company_travel_policy_agent.company_travel_policy_agent import CompanyTravelPolicyAgent
from front_of_house_agent.front_of_house_agent import FrontOfHouseAgent
from future_trips_agent.future_trips_agent import TravelAgentFutureTrips
from onboarding_agent.onboarding_agent import OnboardingAgent
from preferences_agent.preferences_agent import TravelAgentPreferences
from server.api.v1.endpoints.admin.user_whitelist import whitelist_users_remove
from server.database.models.chat_thread import ChatThread
from server.database.models.user import UserRole
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.connectivity.message_broker import MessageBroker
from server.services.connectivity.redis_message_broker import RedisMessageBroker
from server.services.trips.clear_onboarding import clear_state_data
from server.services.user.user_account_delete import clear_user_state
from server.services.user.user_preferences import get_user_preferences
from server.utils.custom_profiler import profiler
from server.utils.logger import Logger, logger
from server.utils.message_constants import SKELETON_MESSAGE_TYPES
from server.utils.mongo_connector import (
    trip_context_v2_collection,
    user_preferences_collection,
)
from server.utils.no_op_websocket import NoOpWebSocket
from server.utils.settings import AgentTypes, settings

router = APIRouter()


REDIS_PUBSUB_CONSUME_LAG = Histogram(
    "redis_pubsub_latency_seconds",
    "Latency from publish to consume",
    labelnames=["environment"],
    buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5],
)


class ControlSignals(Enum):
    stop = "stop"


class WSContexts(Enum):
    none = 0
    onboarding = 1
    trip = 2
    travel_preferences = 3
    future_trips = 4
    travel_policy = 5


class GlobalWSManager:
    _instance = None
    _active_connections: Set[WebSocket] = set()
    _active_connection_managers: Set["WSConnectionManager"] = set()
    _is_shutting_down: bool = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    async def add_connection(cls, websocket: WebSocket, connection_manager: "WSConnectionManager"):
        if cls._is_shutting_down:
            await websocket.close(
                code=status.WS_1012_SERVICE_RESTART, reason="Server is shutting down, not accepting new connections"
            )
            return False

        cls._active_connections.add(websocket)
        cls._active_connection_managers.add(connection_manager)
        return True

    @classmethod
    async def remove_connection(cls, websocket: WebSocket, connection_manager: "WSConnectionManager"):
        cls._active_connections.discard(websocket)
        cls._active_connection_managers.discard(connection_manager)
        await connection_manager.on_disconnect()

    @classmethod
    async def close_all_connections(cls):
        logger.info("Closing all WebSocket connections")
        cls._is_shutting_down = True

        for connection_manager in list(cls._active_connection_managers):
            await connection_manager.on_disconnect()

        for ws in list(cls._active_connections):
            try:
                await ws.close(code=status.WS_1012_SERVICE_RESTART, reason="Server shutdown")
            except Exception as e:
                logger.error(f"Error closing WebSocket connection: {e}")

        cls._active_connections.clear()
        cls._active_connection_managers.clear()
        logger.info("All WebSocket connections closed")


class WSConnectionManager:
    def __init__(self, user: User) -> None:
        self.user = user
        self.context = WSContexts.none
        self.thread: Optional[ChatThread] = None
        self.agent = None
        self.logger_token = None
        self.message_broker: MessageBroker = RedisMessageBroker(default_channel=str(user.id))
        self._subscription_task: asyncio.Task | None = None

    async def connect(self, websocket: WebSocket):
        await websocket.accept("Authorization")
        if await GlobalWSManager.add_connection(websocket, self):
            await self._register_message_handler(websocket)

    async def _register_message_handler(self, websocket: WebSocket):
        profiler_token = None
        try:
            profiler_token = profiler.set_profiler_context_id(str(id(websocket)))
            while True:
                data = await websocket.receive_text()

                if data == "ping":
                    await websocket.send_text("pong")
                    continue

                data = json.loads(data)
                await self.on_receive(websocket, data)
                if self.agent is not None:
                    await self.agent.history.apersist()

        except WebSocketDisconnect:
            logger.info("websocket client disconnected")
        except Exception as e:
            logger.warn(f"Error when receiving websocket messages: {e}")
            logger.warn(traceback.format_exc())

            ask_support_msg = f"Please contact support and provide the trip details (e.g. trip id, URL, etc.). current trip id: {self.thread.id if self.thread is not None else None}"
            await self.send_message(
                websocket,
                {
                    "status": "error",
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP[AgentTypes.ERROR],
                    "reason": (
                        traceback.format_exc().split("\n") + [ask_support_msg]
                        if settings.OTTO_ENV.upper() in ("DEV", "STG")
                        else ask_support_msg
                    ),
                },
            )

        finally:
            if self.agent is not None:
                await self.agent.history.apersist()
            profiler.save()
            if profiler_token is not None:
                profiler.reset_profiler_context_id(profiler_token)
            await GlobalWSManager.remove_connection(websocket, self)

    async def on_receive(self, websocket: WebSocket | NoOpWebSocket, message: dict[str, Any]):
        Logger.bind_log_context(
            user_id=self.user.id,
        )
        self.validate_message_format(message)

        match message["type"]:
            case "trip_init":
                await self.handle_trip_init(websocket, message)

            case "prompt" | "update" | "silent_prompt" | "agent_resume":
                if (
                    settings.OTTO_ENV.upper() in ("DEV", "STG")
                    and isinstance(
                        self.agent,
                        (
                            TravelAgentPreferences,
                            TravelAgentFutureTrips,
                            OnboardingAgent,
                            CompanyTravelPolicyAgent,
                            FrontOfHouseAgent,
                        ),
                    )
                    and message.get("text") == "/clear"
                ):
                    assert self.thread is not None

                    if isinstance(
                        self.agent,
                        (TravelAgentPreferences, TravelAgentFutureTrips, OnboardingAgent, CompanyTravelPolicyAgent),
                    ):

                        async def __delete_preferences():
                            assert self.thread is not None
                            await user_preferences_collection.delete_one({"users_id": self.user.id})

                        await clear_state_data(self.thread.id, __delete_preferences)
                    else:  # FrontOfHouseAgent

                        async def __delete_trip_context_v2():
                            assert self.thread is not None
                            await trip_context_v2_collection.delete_one({"thread_id": self.thread.id})

                        await clear_state_data(self.thread.id, __delete_trip_context_v2)

                    message = {
                        "type": "prompt",
                        "text": "state data cleared, refreshing the page...",
                        "isBotMessage": True,
                        "expectResponse": True,
                        "refreshPage": True,
                    }
                    await self.send_message(websocket, message)
                elif settings.OTTO_ENV.upper() in ("DEV", "STG") and message.get("text") == "/reset":
                    assert self.thread is not None
                    assert self.user is not None

                    # Call the clear_user_state function with user id and email
                    await clear_user_state(self.user.id, self.user.email)

                    # Send response to user and refresh the page
                    response_message = {
                        "type": "prompt",
                        "text": "Your account has been reset. All your preferences, bookings, and other data have been cleared. Refreshing the page...",
                        "isBotMessage": True,
                        "expectResponse": True,
                        "refreshPage": True,
                    }
                    await self.send_message(websocket, response_message)
                elif settings.OTTO_ENV.upper() in ("DEV", "STG") and message.get("text") == "/remove-whitelist":
                    assert self.thread is not None
                    assert self.user is not None

                    await whitelist_users_remove(self.user.email)

                    # Send response to user and refresh the page
                    response_message = {
                        "type": "prompt",
                        "text": "Your account has been removed from whitelist. Refreshing the page...",
                        "isBotMessage": True,
                        "expectResponse": True,
                        "refreshPage": True,
                    }
                    await self.send_message(websocket, response_message)
                else:
                    if isinstance(self.agent, FrontOfHouseAgent):
                        asyncio.create_task(
                            self.handle_trip_message(websocket, message, self.thread.id if self.thread else None)
                        )
                    else:
                        await self.handle_trip_message(websocket, message)
            case "stop":
                if self.agent is not None and isinstance(self.agent, FrontOfHouseAgent):
                    await self._publish_signal(websocket, ControlSignals.stop)

            case "preferences_init":
                await self.handle_preferences_init(websocket, message)
            case "travel_policy_init":
                await self.handle_travel_policy_init(websocket, message)

            case "onboarding_init":
                await self.handle_onboarding_init(websocket, message)

            case "future_trips_init":
                await self.handle_future_trips_init(websocket, message)

    async def handle_trip_init(self, websocket: WebSocket | NoOpWebSocket, message: dict):
        self.context = WSContexts.trip
        Logger.bind_log_context(trip_id=message.get("tripId"))

        chat_thread: Optional[ChatThread] = await ChatThread.from_id(message["tripId"])
        if chat_thread is None or chat_thread.users_id != self.user.id:
            raise WebSocketException(code=status.WS_1003_UNSUPPORTED_DATA, reason="Trip id does not exist!")
        self.thread = chat_thread
        logger.info(f"Trip init success, trip_id = {message['tripId']}.")

        user_preferences, user_profile = await asyncio.gather(
            get_user_preferences(self.user.id),
            UserProfile.from_user_id(self.user.id),
        )

        self.agent = FrontOfHouseAgent(
            thread=self.thread,
            websocket_send_message=partial(
                self.send_message, websocket=websocket, broadcast_message=True, thread_id=self.thread.id
            ),
            user_preferences=user_preferences,
            user=self.user,
            user_profile=user_profile,
            timezone=message.get("clientTimezone", None),
            is_mobile=message.get("isMobile", False),
            trying_mode_enabled=(message.get("extra") or {}).get("tryingModeEnabled", False),
            enable_trip_planning=(message.get("extra") or {}).get("enableTripPlanning", False),
            custom_opening_message=message.get("openingMessage"),
        )

        if (message.get("extra") or {}).get("enableSpotnanaSearch", False):
            from front_of_house_agent.common_models import FlightSearchSource

            self.agent.travel_context.flight_select_result.search_source = FlightSearchSource.FALLBACK_SPOTNANA

        await self._subscribe_message_broker_with_thread(websocket, self.thread.id)

        to_send_arr = await self.agent.run()

        travel_context_dict = self.agent.travel_context.to_dict()

        history_message = {
            "type": "history",
            "lastTimestamp": self.agent.history.max_created_date.isoformat(),
            "minMessageTimestampSeconds": settings.MIN_MESSAGE_TIMESTAMP_DELTA.total_seconds(),
            "messages": [{**message, "is_history": True} for message in to_send_arr],
            "travel_context": travel_context_dict,
            "selected_flight_itinerary": self.agent.travel_context.to_selected_flights(),
            "selected_accomodation": await self.agent.travel_context.to_selected_accomodation(),
        }

        # Send history message to websocket directly, skip publishing to message broker
        await self.send_message(websocket, history_message)

    async def handle_trip_message(
        self, websocket: WebSocket | NoOpWebSocket, message: dict, thread_id: int | None = None
    ):
        to_send_arr = []
        new_timestamp = message.get("timestamp", None)
        assert self.agent is not None

        if (
            self.user.organization_id is not None
            and self.context == WSContexts.travel_policy
            and self.user.role != UserRole.company_admin
        ):
            await self.send_message(
                websocket,
                {
                    "type": "prompt",
                    "text": "Access denied. Only company administrators can manage travel policies. Please contact your admin for assistance.",
                    "expectResponse": True,
                    "isBotMessage": True,
                    "isStopped": True,
                },
                broadcast_message=True,
                thread_id=thread_id,
            )
            return

        try:
            if new_timestamp is not None:
                await self.agent.add_timestamp_message(new_timestamp)
            else:
                await self._sync_user_message(message)
                message_body: str | list[str] | None = (
                    message["cardIds"] if message["type"] == "update" else message.get("text")
                )
                if isinstance(self.agent, FrontOfHouseAgent):
                    to_send_arr = await self.agent.run_with_task(
                        message=message_body,
                        message_type=message["type"],
                        extra_payload=message.get("extra", None),
                    )
                else:
                    to_send_arr = await self.agent.run(
                        message=message_body, message_type=message["type"], extra_payload=message.get("extra", None)
                    )

                travel_context_dict = None
                selected_flight_itinerary = None
                selected_accomodation = None
                if isinstance(self.agent, FrontOfHouseAgent):
                    travel_context_dict = self.agent.travel_context.to_dict()
                    selected_flight_itinerary = self.agent.travel_context.to_selected_flights()
                    selected_accomodation = await self.agent.travel_context.to_selected_accomodation()

                for to_send in to_send_arr:
                    if hasattr(self.agent, "travel_context"):
                        to_send = {
                            **to_send,
                            "travel_context": travel_context_dict,
                            "selected_flight_itinerary": selected_flight_itinerary,
                            "selected_accomodation": selected_accomodation,
                        }

                    await self.send_message(
                        websocket,
                        to_send,
                        broadcast_message=isinstance(self.agent, FrontOfHouseAgent),
                        thread_id=thread_id,
                    )
        except asyncio.exceptions.CancelledError:
            logger.info("User cancelled the operation")
            if isinstance(self.agent, FrontOfHouseAgent):
                await self.send_message(
                    websocket, self.agent.generate_stop_message(), broadcast_message=True, thread_id=thread_id
                )
        finally:
            await self.agent.history.apersist()

    async def handle_onboarding_init(self, websocket: WebSocket | NoOpWebSocket, message: dict):
        self.context = WSContexts.onboarding
        Logger.bind_log_context(trip_id="onboarding")

        chat_threads: Sequence[Row[Tuple[ChatThread]]] = await ChatThread.from_user_id_and_title(
            self.user.id, settings.ONBOARDING_PAGE_TITLE
        )

        preference = await get_user_preferences(self.user.id)
        if len(chat_threads) == 0:
            new_chat_thread = ChatThread(users_id=self.user.id, title=settings.ONBOARDING_PAGE_TITLE)
            await ChatThread.new_chat_thread(new_chat_thread)

            # Get the new value for chat thread
            chat_threads = await ChatThread.from_user_id_and_title(self.user.id, settings.ONBOARDING_PAGE_TITLE)

        self.thread = chat_threads[0].ChatThread
        assert self.thread is not None, "Onboarding Thread is always created at this point"

        self.agent = OnboardingAgent(
            self.user,
            self.thread.id,
            partial(self.send_message, websocket=websocket),
            preference,
        )

        to_send_arr = await self.agent.run()

        history_message = {
            "type": "history",
            "messages": [{**message, "is_history": True} for message in to_send_arr],
        }
        await self.send_message(websocket, history_message)

    async def handle_travel_policy_init(self, websocket: WebSocket | NoOpWebSocket, message: dict):
        self.context = WSContexts.travel_policy
        Logger.bind_log_context(trip_id="travel_policy")

        if self.user.organization_id is not None and self.user.role != UserRole.company_admin:
            await self.send_message(
                websocket,
                {
                    "type": "prompt",
                    "text": "Access denied. Only company administrators can manage travel policies. Please contact your admin for assistance.",
                    "expectResponse": True,
                    "isBotMessage": True,
                    "isStopped": True,
                },
                broadcast_message=True,
            )
            return

        # Try to get existing thread
        chat_threads = await ChatThread.from_user_id_and_title(self.user.id, settings.TRAVEL_POLICY_THREAD_TITLE)

        # Create thread only if none exists and no company admin (individual user)
        no_threads = len(chat_threads) == 0
        if no_threads:
            new_chat_thread = ChatThread(users_id=self.user.id, title=settings.TRAVEL_POLICY_THREAD_TITLE)
            await ChatThread.new_chat_thread(new_chat_thread)

            # Fetch the newly created thread
            chat_threads = await ChatThread.from_user_id_and_title(self.user.id, settings.TRAVEL_POLICY_THREAD_TITLE)

        self.thread = chat_threads[0].ChatThread
        assert self.thread is not None, "Travel Policy Thread is always created at this point"

        base_company_travel_policy = None
        policy_in_db = await UserCompanyTravelPolicy.from_user_id(self.user.id)
        if policy_in_db is not None:
            base_company_travel_policy = (
                CompanyPolicy(**policy_in_db.parsed_travel_policy)
                if policy_in_db.parsed_travel_policy is not None
                else None
            )

        self.agent = CompanyTravelPolicyAgent(
            self.user,
            self.thread.id,
            partial(self.send_message, websocket=websocket),
            base_company_travel_policy=base_company_travel_policy,
        )

        to_send_arr = await self.agent.run()

        history_message = {
            "type": "history",
            "lastTimestamp": self.agent.history.max_created_date.isoformat(),
            "minMessageTimestampSeconds": settings.MIN_MESSAGE_TIMESTAMP_DELTA.total_seconds(),
            "messages": [{**message, "is_history": True} for message in to_send_arr],
        }
        await self.send_message(websocket, history_message)

    async def handle_preferences_init(self, websocket: WebSocket | NoOpWebSocket, message: dict):
        self.context = WSContexts.travel_preferences
        Logger.bind_log_context(trip_id="preferences")

        chat_threads: Sequence[Row[Tuple[ChatThread]]] = await ChatThread.from_user_id_and_title(
            self.user.id, settings.PREFERENCES_THREAD_TITLE
        )
        if len(chat_threads) == 0:
            new_chat_thread = ChatThread(users_id=self.user.id, title=settings.PREFERENCES_THREAD_TITLE)
            await ChatThread.new_chat_thread(new_chat_thread)

            # Get the new value for chat thread
            chat_threads = await ChatThread.from_user_id_and_title(self.user.id, settings.PREFERENCES_THREAD_TITLE)

        self.thread = chat_threads[0].ChatThread
        assert self.thread is not None, "Preference Thread is always created at this point"

        self.agent = TravelAgentPreferences(
            user=self.user,
            thread_id=self.thread.id,
            websocket_send_message=partial(self.send_message, websocket=websocket),
        )

        to_send_arr = await self.agent.run()

        history_message = {
            "type": "history",
            "lastTimestamp": self.agent.history.max_created_date.isoformat(),
            "minMessageTimestampSeconds": settings.MIN_MESSAGE_TIMESTAMP_DELTA.total_seconds(),
            "messages": [{**message, "is_history": True} for message in to_send_arr],
        }
        await self.send_message(websocket, history_message)

    async def handle_future_trips_init(self, websocket: WebSocket | NoOpWebSocket, message: dict):
        self.context = WSContexts.future_trips
        Logger.bind_log_context(trip_id="future_trips")

        chat_threads: Sequence[Row[Tuple[ChatThread]]] = await ChatThread.from_user_id_and_title(
            self.user.id, settings.FUTURE_TRIPS_THREAD_TITLE
        )
        if len(chat_threads) == 0:
            new_chat_thread = ChatThread(users_id=self.user.id, title=settings.FUTURE_TRIPS_THREAD_TITLE)
            await ChatThread.new_chat_thread(new_chat_thread)

            # Get the new value for chat thread
            chat_threads = await ChatThread.from_user_id_and_title(self.user.id, settings.FUTURE_TRIPS_THREAD_TITLE)

        agents = [
            TravelAgentFutureTrips(
                self.user,
                thread.ChatThread.id,
                partial(self.send_message, websocket=websocket),
                is_onboarding=message.get("isOnboarding", False),
            )
            for thread in chat_threads
        ]

        agents_messages = await asyncio.gather(*[agent.get_history_messages() for agent in agents])

        # If last conversation is not completed, use last thread, otherwise
        # create new thread
        if agents[-1].selected_calendar_events is None:
            agents_messages.pop()
            self.agent = agents.pop()
            self.thread = chat_threads[-1].ChatThread
        else:
            new_chat_thread = ChatThread(users_id=self.user.id, title=settings.FUTURE_TRIPS_THREAD_TITLE)
            await ChatThread.new_chat_thread(new_chat_thread)

            # Get the new value for chat thread
            chat_threads = await ChatThread.from_user_id_and_title(self.user.id, settings.FUTURE_TRIPS_THREAD_TITLE)

            self.thread = chat_threads[-1].ChatThread
            assert self.thread is not None, "At least one Future Trips thread is always created at this point"

            self.agent = TravelAgentFutureTrips(
                self.user,
                self.thread.id,
                partial(self.send_message, websocket=websocket),
                is_onboarding=message.get("isOnboarding", False),
            )

        # Send history conversation from previous threads
        for agent_messages in agents_messages:
            history_message = {
                "type": "history",
                "messages": [{**message, "is_history": True} for message in agent_messages],
            }
            await self.send_message(websocket, history_message)

        to_send_arr = await self.agent.run()

        history_message = {
            "type": "history",
            "messages": [{**message, "is_history": True} for message in to_send_arr],
        }
        await self.send_message(websocket, history_message)

    def _channel_name(self, thread_id: int | None):
        if not thread_id:
            return f"{self.user.id}"

        return f"{self.user.id}:{thread_id}"

    async def _publish_thread_message_to_broker(
        self, websocket: WebSocket | NoOpWebSocket, thread_id: int, message: dict[str, Any]
    ):
        payload = {"status": "success", "thread_id": thread_id, **message}
        published = await self.message_broker.publish(
            is_user_chat_message=False, payload=payload, channel=self._channel_name(thread_id)
        )
        if not published:
            logger.warning(
                f"Failed to publish message to broker for thread {thread_id}, fallback to send to websocket directly"
            )
            await self._send_to_websocket(websocket, message)

    async def _on_signal(self, websocket: WebSocket | NoOpWebSocket, signal: ControlSignals):
        if signal == ControlSignals.stop:
            if self.agent is not None and isinstance(self.agent, FrontOfHouseAgent):
                self.agent.stop()

    async def _publish_signal(self, websocket: WebSocket | NoOpWebSocket, signal: ControlSignals):
        if self.thread is None:
            await self._on_signal(websocket, signal)
            return

        payload = {"signal": signal.value}
        published = await self.message_broker.publish(
            is_user_chat_message=False, payload=payload, channel=self._channel_name(self.thread.id)
        )
        if not published:
            logger.warning(
                f"Failed to publish control signal to broker for thread {self.thread.id}, fallback to execute locally"
            )
            await self._on_signal(websocket, signal)

    async def _stop_subscription(self):
        # if the task is already running, cancel it
        if self._subscription_task is not None:
            old_task = self._subscription_task
            if not old_task.done():
                old_task.cancel()
            try:
                await old_task
            except asyncio.CancelledError:
                pass

            self._subscription_task = None

    async def _subscribe_message_broker_with_thread(self, websocket: WebSocket | NoOpWebSocket, thread_id: int):
        await self._stop_subscription()

        # create new subscription task
        message_generator = await self.message_broker.subscribe(self._channel_name(thread_id))
        if message_generator is None:
            return

        async def subscription_handler():
            try:
                async for message in message_generator:
                    signal = message.get("signal", None)
                    if signal is not None:
                        await self._on_signal(websocket, ControlSignals(signal))
                        continue

                    await self._send_to_websocket(websocket, message)
                    if message.get("published_at_seconds") is not None:
                        REDIS_PUBSUB_CONSUME_LAG.labels(environment=settings.OTTO_ENV.upper()).observe(
                            time.time() - message["published_at_seconds"]
                        )

            except Exception as e:
                if websocket.client_state == WebSocketState.DISCONNECTED or isinstance(e, WebSocketDisconnect):
                    logger.warn("Sending message to a disconnected websocket, stopping subscription")
                    return
                logger.error(f"Error in subscription handler: {e}")

        # create and store new task
        task = asyncio.create_task(subscription_handler())
        self._subscription_task = task

    async def _send_to_websocket(self, websocket: WebSocket | NoOpWebSocket, message: dict[str, Any]):
        await websocket.send_json(
            {"status": "success", "thread_id": self.thread.id if self.thread is not None else None, **message}
        )

    # sync user message to other clients if there is any
    async def _sync_user_message(self, message: dict[str, Any]):
        if self.thread:
            thread_id = self.thread.id
            payload = {"status": "success", "thread_id": thread_id, **message}
            published = await self.message_broker.publish(
                is_user_chat_message=True, payload=payload, channel=self._channel_name(thread_id)
            )
            if not published:
                logger.warning(f"Failed to sync user message to other clients for thread {thread_id}")

    async def send_message(
        self,
        websocket: WebSocket | NoOpWebSocket,
        message: dict[str, Any],
        broadcast_message: bool = False,
        thread_id: int | None = None,
    ):
        # logging all non-persisted update messages
        if message.get("type", "") in SKELETON_MESSAGE_TYPES:
            logger.info(f"Otto updates [{message.get('type', '')}]: {message.get('text', '')}")

        if broadcast_message and thread_id:
            message["published_at_seconds"] = time.time()
            await self._publish_thread_message_to_broker(websocket, thread_id, message)
        else:
            await self._send_to_websocket(websocket, message)

    async def on_disconnect(self):
        logger.info(f"on websocket client disconnected: {self.user.id}")
        await self._stop_subscription()

    def validate_message_format(self, message: dict[str, Any] | Any):
        if not isinstance(message, dict):
            raise WebSocketException(
                code=status.WS_1003_UNSUPPORTED_DATA,
                reason="The message is not a json.",
            )

        if "type" not in message.keys():
            raise WebSocketException(
                code=status.WS_1003_UNSUPPORTED_DATA,
                reason="The message does not have the 'type' field.",
            )

        if message["type"] not in [
            "prompt",
            "silent_prompt",
            "update",
            "trip_init",
            "preferences_init",
            "travel_policy_init",
            "future_trips_init",
            "onboarding_init",
            "agent_resume",
            "stop",
        ]:
            raise WebSocketException(
                code=status.WS_1003_UNSUPPORTED_DATA,
                reason="The message type is unknown.",
            )

        if message["type"] == "update" and "cardIds" not in message.keys():
            raise WebSocketException(
                code=status.WS_1003_UNSUPPORTED_DATA,
                reason="The update message must have the keys ['cardIds'].",
            )

        if message["type"] == "prompt" and not ("text" in message.keys() or "timestamp" in message.keys()):
            raise WebSocketException(
                code=status.WS_1003_UNSUPPORTED_DATA,
                reason="The prompt message must have one of the keys ['text', 'timestamp'].",
            )

        if message["type"] == "silent_prompt" and "text" not in message.keys():
            raise WebSocketException(
                code=status.WS_1003_UNSUPPORTED_DATA,
                reason="The silent_prompt message must have one of the keys ['text'].",
            )

        if message["type"] == "trip_init" and "tripId" not in message.keys():
            raise WebSocketException(
                code=status.WS_1003_UNSUPPORTED_DATA,
                reason="The trip_init message must have one of the keys ['trip_id'].",
            )


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, user: User = Depends(manager)):
    ws_manager = WSConnectionManager(user)
    await ws_manager.connect(websocket)
