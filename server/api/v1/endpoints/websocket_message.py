from typing import Any, Union

from pydantic import BaseModel

from baml_client.types import ResponseAllPreferences
from message.model import MessageType


class LearnedPreferences(BaseModel):
    """
    This class is used to store the preferences that are learned from the new trip.
    """

    class Option(BaseModel):
        action: str = ""
        title: str = ""
        detail: str = ""
        selected: bool = False

    options: list[Option] = []
    submitted: bool = False

    @classmethod
    def from_Preferences(cls, preferences: ResponseAllPreferences):
        p = cls()
        for k, v in preferences.model_dump().items():
            title = k.replace("_", " ")
            if isinstance(v, list):
                p.options += [
                    cls.Option(
                        action=f"My {title}: {preference}.",
                        title=title.capitalize(),
                        detail=preference,
                    )
                    for preference in v
                ]
            elif isinstance(v, str):
                p.options.append(cls.Option(action=f"My {title}: {v}", title=title.capitalize(), detail=v))
        return p


class BaseMessage(BaseModel):
    type: MessageType
    textColor: str | None = None


# Sent Messages
class PromptMessage(BaseMessage):
    type: MessageType = MessageType.PROMPT
    text: str
    timestamp: str
    expectResponse: bool
    isBotMessage: bool
    refreshPage: bool = False
    suggested_preferences: LearnedPreferences | None = None


class SilentPromptMessage(BaseMessage):
    type: MessageType = MessageType.SILENT_PROMPT
    text: str


class HistoryMessage(BaseMessage):
    type: MessageType = MessageType.HISTORY
    messages: list[dict[str, Any]]
    travel_context: dict[str, Any]


class ItineraryUpdate(BaseMessage):
    type: MessageType = MessageType.ITINERARY_UPDATE


class ErrorMessage(BaseMessage):
    type: MessageType = MessageType.ERROR
    reason: str
    isBotMessage: bool = True


# Received Messages
class UpdateMessage(BaseMessage):
    type: MessageType = MessageType.UPDATE
    cardIds: list[str]


class ResumeMessage(BaseMessage):
    type: MessageType = MessageType.AGENT_RESUME


class StopMessage(BaseMessage):
    type: MessageType = MessageType.STOP


class TripInitMessage(BaseMessage):
    type: MessageType = MessageType.TRIP_INIT
    tripId: str


class PreferencesInit(BaseMessage):
    type: MessageType = MessageType.PREFERENCES_INIT
    isOnboarding: bool = False


class TravelPolicyInit(BaseMessage):
    type: MessageType = MessageType.TRAVEL_POLICY_INIT


class OnboardingInit(BaseMessage):
    type: MessageType = MessageType.ONBOARDING_INIT


class FutureTripsInit(BaseMessage):
    type: MessageType = MessageType.FUTURE_TRIPS_INIT
    isOnboarding: bool = False


class SuggestedCapabilityMessage(BaseMessage):
    type: MessageType = MessageType.SUGGESTED_CAPABILITY
    text: str
    isBotMessage: bool = True


# Discriminated Union
ReceivedMessage = Union[
    UpdateMessage,
    ResumeMessage,
    StopMessage,
    TripInitMessage,
    PreferencesInit,
    TravelPolicyInit,
    OnboardingInit,
    FutureTripsInit,
]

SentMessage = Union[
    PromptMessage,
    SilentPromptMessage,
    HistoryMessage,
    ItineraryUpdate,
    ErrorMessage,
    SuggestedCapabilityMessage,
]

WebSocketMessage = Union[ReceivedMessage, SentMessage]
