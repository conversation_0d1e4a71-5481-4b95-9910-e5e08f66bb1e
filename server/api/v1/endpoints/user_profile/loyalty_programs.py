import json
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Response, status

from server.schemas.authenticate.user import User
from server.schemas.spotnana.create_spotnana_profile import UpdateFrequentFlyerNumber
from server.schemas.user_profile.loyalty_programs import FlightsLoyaltyProgram
from server.services.authenticate.authenticate import manager
from server.services.payment_profile.spotnana_profile import get_spotnana_user_id, update_spotnana_membership_info
from server.services.user_profile.loyalty_programs import get_user_profile_flights_loyalty_programs
from server.utils.mongo_connector import user_profile_loyalty_programs_collection

router = APIRouter()


@router.get("/loyalty-programs")
async def get_user_loyalty_programs(user: User = Depends(manager)):
    user_flights_loyalty_programs = await get_user_profile_flights_loyalty_programs(user.id)
    user_hotels_loyalty_programs = []

    response = Response(
        content=json.dumps(
            {
                "flights_loyalty_programs": user_flights_loyalty_programs.get("loyaltyPrograms", [])
                if user_flights_loyalty_programs is not None
                else None,
                "hotels_loyalty_programs": user_hotels_loyalty_programs,
            }
        ),
        media_type="application/json",
    )

    return response


@router.post("/flights-loyalty-programs")
async def save_flights_loyalty_programs(flights_loyalty_program: FlightsLoyaltyProgram, user: User = Depends(manager)):
    if flights_loyalty_program.number is None:
        raise HTTPException(
            status_code=400,
            detail="The loyalty program number cannot be empty.",
        )

    # Get payment profile from db
    loyalty_programs_db = await user_profile_loyalty_programs_collection.find_one({"users_id": user.id})
    if loyalty_programs_db is None:
        loyalty_programs_db = {"loyaltyPrograms": []}

    loyalty_programs_dict: dict[str, Any] = {
        v.get("IATACode"): v for v in loyalty_programs_db.get("loyaltyPrograms", [])
    }
    loyalty_programs_dict[flights_loyalty_program.IATACode] = flights_loyalty_program.model_dump()

    # Save loyalty programs info to database
    query: dict[str, Any] = {"users_id": user.id}
    update: dict[str, Any] = {"$set": {"users_id": user.id, "loyaltyPrograms": list(loyalty_programs_dict.values())}}
    await user_profile_loyalty_programs_collection.update_one(query, update, upsert=True)

    # We need the spotnana user id to attach the membership info
    spotnana_user_id = await get_spotnana_user_id(user.email)
    if spotnana_user_id is None:
        raise HTTPException(
            status_code=400,
            detail="Spotnana payment profile is not complete. Please save your payment profile first.",
        )

    # Update Spotnana membership info
    await update_spotnana_membership_info(
        UpdateFrequentFlyerNumber(**{"frequentFlyerNumbers": list(loyalty_programs_dict.values())}),
        spotnana_user_id,
    )

    response = Response(content=json.dumps({"status": "success"}), media_type="application/json")

    return response


@router.delete("/flights-loyalty-programs")
async def delete_flights_loyalty_programs(IATACode: str, user: User = Depends(manager)):
    # Get payment profile from db
    loyalty_programs_db = await user_profile_loyalty_programs_collection.find_one({"users_id": user.id})
    if loyalty_programs_db is None:
        loyalty_programs_db = {"loyaltyPrograms": []}

    loyalty_programs_dict: dict[str, Any] = {
        v.get("IATACode"): v for v in loyalty_programs_db.get("loyaltyPrograms", [])
    }

    if IATACode not in loyalty_programs_dict.keys():
        return Response(
            media_type="application/json",
            status_code=status.HTTP_204_NO_CONTENT,
        )

    del loyalty_programs_dict[IATACode]

    # Save loyalty programs info to database
    query: dict[str, Any] = {"users_id": user.id}
    update: dict[str, Any] = {"$set": {"users_id": user.id, "loyaltyPrograms": list(loyalty_programs_dict.values())}}
    await user_profile_loyalty_programs_collection.update_one(query, update, upsert=True)

    # We need the spotnana user id to attach the membership info
    spotnana_user_id = await get_spotnana_user_id(user.email)
    if spotnana_user_id is None:
        raise HTTPException(
            status_code=400,
            detail="Spotnana payment profile is not complete. Please save your payment profile first.",
        )

    # Update Spotnana membership info
    await update_spotnana_membership_info(
        UpdateFrequentFlyerNumber(**{"frequentFlyerNumbers": list(loyalty_programs_dict.values())}),
        spotnana_user_id,
    )

    response = Response(content=json.dumps({"status": "success"}), media_type="application/json")

    return response
