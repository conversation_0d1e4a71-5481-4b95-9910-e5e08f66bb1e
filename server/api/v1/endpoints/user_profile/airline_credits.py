import json

from fastapi import APIRouter, Depends, Response

from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.trips.flight_credits_api import flight_credits_api

router = APIRouter()


@router.get("/airline-credits")
async def get_user_airline_credits(user: User = Depends(manager)):
    user_airline_credits = await flight_credits_api.get_user_unused_credits(user.email, ignore_cache=True)

    response = Response(
        content=json.dumps(
            {
                "airline_credits": flight_credits_api.map_user_profile_airline_credits(user_airline_credits),
            }
        ),
        media_type="application/json",
    )

    return response
