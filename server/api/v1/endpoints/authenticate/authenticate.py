from fastapi import APIRouter

from server.api.v1.endpoints.authenticate import apple, email, google, microsoft, one_time_code

router = APIRouter()
router.include_router(google.router, prefix="/google")
router.include_router(microsoft.router, prefix="/microsoft")
router.include_router(apple.router, prefix="/apple")
router.include_router(email.router, prefix="/email-login")
router.include_router(one_time_code.router, prefix="/beta")
