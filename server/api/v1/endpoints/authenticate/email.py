import json
import random
import string
from datetime import datetime, timedelta, timezone
from typing import <PERSON><PERSON>

from fastapi import APIRouter, Request, Response
from sqlalchemy import Row
from starlette.responses import JSONResponse

from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.email import GenerateOTPRequest, ValidateOTPRequest
from server.services.authenticate.email import EmailAuthenticate
from server.services.user.user_activity import update_user_activity
from server.services.user.user_session_history import get_user_last_session
from server.utils.rate_limiter import RateLimiter
from server.utils.remote_cache import RemoteCache
from server.utils.settings import settings
from server.utils.smtp import send_otp_email

router = APIRouter()

rate_limiter = RateLimiter()


async def rate_limiter_key_func(request: Request):
    body = await request.json() if request.method == "POST" else {}
    return body.get("email", request.client.host if request.client is not None else "")


@router.post("/generate-otp")
@rate_limiter.limit_request(limit=2, window=timedelta(minutes=1), key_func=rate_limiter_key_func)
async def generate_otp(request: Request, data: GenerateOTPRequest):
    """
    Initiate Personal Email login
    """

    data.email = data.email.strip()

    if data.email in settings.AUTOMATION_TESTS_OTC_EMAILS:
        otp_code = settings.AUTOMATION_TESTS_OTP
    else:
        otp_code = "".join(random.choices(string.digits, k=6))

    new_otp_entry: dict[str, str] = {
        "email": data.email,
        "code": otp_code,
        "created_date": datetime.now(tz=timezone.utc).isoformat(),
    }

    cache: RemoteCache = RemoteCache()
    await cache.set(
        key=EmailAuthenticate.otp_redis_key_format(otp_code),
        value=json.dumps(new_otp_entry),
        expire=settings.ONE_TIME_PASSWORD_LIFESPAN,
    )

    await send_otp_email(new_otp_entry.get("email", ""), new_otp_entry.get("code", ""))

    validate_otp_rate_limiter_key = rate_limiter.create_cache_key(
        "/api/email-login/validate-otp", await rate_limiter_key_func(request)
    )
    await rate_limiter.reset(validate_otp_rate_limiter_key)

    return JSONResponse({"status": "success"})


@router.post("/validate-otp")
@rate_limiter.limit_request(limit=10, window=timedelta(minutes=1), key_func=rate_limiter_key_func)
async def validate_otp(request: Request, data: ValidateOTPRequest):
    """
    Validate Personal Email login
    """

    data.email = data.email.strip()

    otc_code: str | None = request.cookies.get("otc", None)
    await EmailAuthenticate.validate_user_email(data.email, otc_code=otc_code, otp=data.code)

    authenticator = EmailAuthenticate(data.email, data.code)
    (access_token, refresh_token), user_id, user_email = await authenticator.authorize(request=request)

    await update_user_activity(user_id=str(user_id), activity_type="sign_in")

    session_history: Row[Tuple[ChatThread]] | None = await get_user_last_session(user_id)

    response = Response(
        content=json.dumps(
            {
                "accessToken": access_token,
                "refreshToken": refresh_token,
                "user_id": user_id,
                "user_email": user_email,
                "is_onboarding_completed": session_history is not None,
                "last_checkpoint_trip_id": (session_history.ChatThread.id if session_history is not None else None),
            }
        ),
        media_type="application/json",
    )

    response.set_cookie(
        key="access_token",
        value=access_token,
        samesite="none",
        secure=True,
        httponly=False,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + settings.ACCESS_TOKEN_LIFESPAN,
    )
    response.set_cookie(
        key="refresh_token",
        value=refresh_token,
        samesite="none",
        secure=True,
        httponly=True,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + settings.REFRESH_TOKEN_LIFESPAN,
    )

    if otc_code is not None:
        response.delete_cookie("otc", secure=True, domain=settings.COOKIES_DOMAIN)

    return response
