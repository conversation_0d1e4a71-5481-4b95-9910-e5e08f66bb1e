import asyncio
import json
from datetime import timedel<PERSON>
from typing import Any

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from starlette.responses import JSONResponse

from server.api.v1.endpoints.authenticate.apple import handle_auth_with_token as apple_login
from server.api.v1.endpoints.authenticate.email import validate_otp as email_login
from server.api.v1.endpoints.authenticate.google import login as google_login
from server.api.v1.endpoints.authenticate.microsoft import handle_auth_with_token as microsoft_login
from server.database.models.user_profile import LoginMethod
from server.schemas.authenticate.email import ValidateOTPRequest
from server.schemas.authenticate.google import GoogleAuthenticateModel
from server.services.one_time_codes.manage import redeem_otc
from server.utils.rate_limiter import RateLimiter
from server.utils.remote_cache import RemoteCache
from server.utils.settings import settings

router = APIRouter()

rate_limiter = RateLimiter()


def rate_limiter_key_func(request: Request):
    return request.cookies.get("otc_email", request.client.host if request.client is not None else "")


class ValidateOTCRequest(BaseModel):
    otc: str


@router.post("/redeem", response_class=JSONResponse)
@rate_limiter.limit_request(limit=10, window=timedelta(minutes=1), key_func=rate_limiter_key_func)
async def redeem(request: Request, data: ValidateOTCRequest):
    email: str | None = request.cookies.get("otc_email")
    assert email is not None, "Could not determine the email"

    is_valid: bool = await redeem_otc(data.otc, email)
    if not is_valid:
        raise HTTPException(status_code=403, detail="Invalid OTC.")

    cache: RemoteCache = RemoteCache()
    cache_key: str = f"OTTO_AUTH_STATE_{email}"
    state_json: str | None = await cache.get(key=cache_key)

    if state_json is None:
        raise HTTPException(status_code=400, detail="Login state could not be retrieved, please login again!")

    state: dict[str, Any] = json.loads(state_json)
    response = JSONResponse({"message": "Missing required data!"}, status_code=400)

    match state.get("auth_provider"):
        case LoginMethod.EMAIL:
            response = await email_login(request, ValidateOTPRequest(email=email, code=state.get("otp", "")))

        case LoginMethod.GOOGLE:
            response = await google_login(
                request,
                GoogleAuthenticateModel(token_id=state.get("id_token"), refresh_token=state.get("refresh_token")),
            )

        case LoginMethod.MICROSOFT:
            response = await microsoft_login(
                request,
                code=state.get("code", ""),
                token_response=state.get("token_response", {}),
                state=state.get("state"),
                redirect_response=False,
            )

        case LoginMethod.APPLE:
            response = await apple_login(
                request,
                code=state.get("code", ""),
                token_response=state.get("token_response", {}),
                user_data=state.get("user_data", {}),
                state=state.get("state"),
                redirect_response=False,
            )

    asyncio.create_task(cache.delete(cache_key))
    response.delete_cookie(
        "otc_email",
        secure=True,
        domain=settings.COOKIES_DOMAIN,
    )
    return response
