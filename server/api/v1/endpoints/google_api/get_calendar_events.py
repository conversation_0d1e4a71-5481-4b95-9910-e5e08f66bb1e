import json
from datetime import datetime
from typing import Any, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, Response
from starlette.responses import JSONResponse

from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.calendar_api.calendar_events_background_task import (
    background_get_google_calendar_events,
)
from server.services.calendar_api.calendar_provider import CalendarProviderManager

router = APIRouter()


@router.get("/google_calendar_events", response_class=JSONResponse)
async def google_calendar_events(
    start_date: datetime,
    end_date: datetime,
    keywords: Optional[str] = "",
    user: User = Depends(manager),
):
    user_profile = await UserProfile.from_user_id(user.id)
    assert user_profile is not None
    events_api = CalendarProviderManager(user_profile=user_profile)
    events = events_api.get_events(start_date, end_date, keywords=keywords)

    response = Response(content=json.dumps({"events": events}), media_type="application/json")

    return response


@router.get("/calendar_events_background_task", response_class=JSONResponse)
async def calendar_events_background_task(background_tasks: BackgroundTasks, user: User = Depends(manager)):
    background_tasks.add_task(background_get_google_calendar_events, user)

    response = Response(content=json.dumps({"status": "triggered"}), media_type="application/json")

    return response


@router.post("/calendar_events_update", response_class=JSONResponse)
async def calendar_events_update(event_id: str, key: str, value: Any, user: User = Depends(manager)):
    user_profile = await UserProfile.from_user_id(user.id)
    assert user_profile is not None
    events_api = CalendarProviderManager(user_profile=user_profile)

    events_api.update_event(event_id, {key: value})

    response = Response(content=json.dumps({"status": "updated"}), media_type="application/json")

    return response
