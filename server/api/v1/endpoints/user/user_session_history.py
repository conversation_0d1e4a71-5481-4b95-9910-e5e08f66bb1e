import json

from fastapi import APIRouter, Depends, Response
from starlette.responses import JSONResponse

from server.database.models.user import User as UserDB
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.user.user_session_history import get_user_current_trip, get_user_last_session
from server.utils.analytics.analytics import TrackingEvent
from server.utils.analytics.zapier import ZapierClient

router = APIRouter()


@router.get("/user_session_history", response_class=JSONResponse)
async def user_session_history(user: User = Depends(manager)):
    session_history = await get_user_last_session(user.id)
    in_trip_enabled = await is_feature_flag_enabled(user.id, FeatureFlags.ENABLE_IN_TRIP)
    current_trip = await get_user_current_trip(user.id) if in_trip_enabled else None

    tutorial_completed = False
    if user.tutorial_completed is not None:
        tutorial_completed = user.tutorial_completed

    response = Response(
        content=json.dumps(
            {
                "tutorial_completed": tutorial_completed,
                "is_onboarding_completed": session_history is not None,
                "last_checkpoint_trip_id": (session_history.ChatThread.id if session_history is not None else None),
                "current_trip_id": (current_trip.ChatThread.id if current_trip is not None else None),
            }
        ),
        media_type="application/json",
    )

    return response


@router.post("/tutorial-completed", response_class=JSONResponse)
async def tutorial_completed(user: User = Depends(manager)):
    user_data = await UserDB.from_id(user.id)
    if user_data is not None:
        await user_data.set_tutorial_completed()
        await ZapierClient.log_event_in_background(
            event_type=TrackingEvent.TUTORIAL_COMPLETED, user_id=str(user.id), user_email=user.email
        )
    return JSONResponse({"status": "OK"})
