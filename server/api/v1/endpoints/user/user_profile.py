import json
from typing import Any, Optional

from fastapi import APIRouter, Depends, Response
from pydantic import BaseModel
from starlette.responses import JSONResponse

from server.database.models.user import User, UserRole
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.database.models.user_profile import LoginMethod
from server.database.models.user_profile import UserProfile as UserProfileDb
from server.services.authenticate.authenticate import manager
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.user.user_preferences import (
    get_user_preferences_with_updated_time,
    set_hide_sample_trips,
)
from server.utils.mongo_connector import google_calendar_events_collection
from server.utils.settings import settings

router = APIRouter()


class UpdateHideSampleTripsRequest(BaseModel):
    hide_sample_trips: bool = True


class UserProfile(BaseModel):
    role: Optional[str]
    id: Optional[int]
    user_name: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    preferred_name: Optional[str]
    profile_picture: Optional[str]
    new_trip_count: Optional[int]
    citizenship: Optional[list[str]]
    user_email: Optional[str]
    organization_id: Optional[int]


class UserProfileResponse(BaseModel):
    userProfile: UserProfile
    userPreferences: Optional[dict[str, Any]]
    shouldShowSampleTrips: bool
    shouldShowDontShowAgain: bool
    userCompanyTravelPolicy: Optional[dict[str, Any]]
    microsoft_calendar_enabled: bool = False
    google_calendar_enabled: bool = False


@router.post("/user_profile/update_hide_sample_trips", response_class=JSONResponse)
async def update_hide_sample_trips(
    user: User = Depends(manager),
    request: UpdateHideSampleTripsRequest = UpdateHideSampleTripsRequest(hide_sample_trips=True),
):
    await set_hide_sample_trips(user.id, request.hide_sample_trips)
    user_profile_response = await _get_user_profile(user)
    return Response(
        content=json.dumps(
            user_profile_response.model_dump(),
        ),
        media_type="application/json",
    )


async def _get_user_profile(user: User) -> UserProfileResponse:
    user_travel_company_policy: dict[str, Any] | None = None

    company_admin = (
        await User.from_organization_id_and_role(user.organization_id, UserRole.company_admin)
        if user.organization_id
        else None
    )
    policy_user_id = company_admin.id if company_admin else user.id

    db_user_company_travel_policy = await UserCompanyTravelPolicy.from_user_id(policy_user_id)
    if db_user_company_travel_policy:
        user_travel_company_policy = db_user_company_travel_policy.parsed_travel_policy

    user_profile_db = await UserProfileDb.from_user_id(user.id)

    microsoft_calendar_enabled = False
    google_calendar_enabled = False

    if user_profile_db is not None:
        calendar_provider = CalendarProviderManager(user_profile=user_profile_db, user_email=user.email)

        has_calendar_access = calendar_provider.has_calendar_access()
        microsoft_calendar_enabled: bool = (
            user_profile_db.last_login_method == LoginMethod.MICROSOFT and has_calendar_access
        )
        google_calendar_enabled: bool = user_profile_db.last_login_method == LoginMethod.GOOGLE and has_calendar_access

    user_preferences = await get_user_preferences_with_updated_time(user.id)
    user_preferences_dict: dict[str, Any] | None = None
    if user_preferences is not None:
        user_preferences_dict = user_preferences.model_dump(
            exclude={"triggered_flight_search_amount", "triggered_hotel_search_amount", "hide_sample_trips"}
        )
        if isinstance(user_preferences.preferred_airline_brands, str):
            user_preferences_dict["preferred_airline_brands"] = [
                v.strip() for v in user_preferences.preferred_airline_brands.split(",")
            ]

        if isinstance(user_preferences.preferred_hotel_brands, str):
            user_preferences_dict["preferred_hotel_brands"] = [
                v.strip() for v in user_preferences.preferred_hotel_brands.split(",")
            ]
        if update_time := user_preferences.updated_at:
            user_preferences_dict["updated_at"] = update_time.isoformat()

    pipeline = [
        {"$match": {"user_id": user.email}},
        {"$unwind": "$events"},
        {"$match": {"events.is_new": True}},
        {"$group": {"_id": "$user_id", "newEventsCount": {"$sum": 1}}},
    ]
    cursor = google_calendar_events_collection.aggregate(pipeline)
    result = await cursor.to_list(length=None)
    default_name = (
        " ".join([user.first_name.capitalize() if user.first_name else "", user.last_name or ""]).strip()
        if user.first_name or user.last_name
        else ""
    )
    user_name = user.preferred_name if user.preferred_name else default_name

    hide_sample_trips = False if user_preferences is None else user_preferences.hide_sample_trips
    should_show_dont_show_again = (
        False
        if user_preferences is None
        else (
            user_preferences.triggered_flight_search_amount + user_preferences.triggered_hotel_search_amount
            >= settings.HIDE_SAMPLES_AFTER_N_SEARCHES
        )
    )

    return UserProfileResponse(
        userProfile=UserProfile(
            role=user.role,
            id=user.id,
            user_name=user_name,
            first_name=user.first_name,
            last_name=user.last_name,
            preferred_name=user.preferred_name,
            user_email=user.email,
            profile_picture=user.profile_picture or f"{settings.SERVER_DNS}/static/default-avatar-icon.jpg",
            new_trip_count=result[0].get("newEventsCount", 0) if result else 0,
            citizenship=user.citizenship,
            organization_id=user.organization_id,
        ),
        microsoft_calendar_enabled=microsoft_calendar_enabled,
        google_calendar_enabled=google_calendar_enabled,
        userPreferences=None
        if not user_preferences_dict or all(not v for v in user_preferences_dict.values())
        else user_preferences_dict,
        shouldShowSampleTrips=not hide_sample_trips,
        shouldShowDontShowAgain=should_show_dont_show_again,
        userCompanyTravelPolicy=user_travel_company_policy,
    )


@router.get("/user_profile", response_class=JSONResponse)
async def user_profile(user: User = Depends(manager)):
    user_profile_response = await _get_user_profile(user)
    return Response(
        content=json.dumps(
            user_profile_response.model_dump(),
        ),
        media_type="application/json",
    )


@router.post("/user_profile/preferred-name", response_class=JSONResponse)
async def update_preferred_name(request: dict, user: User = Depends(manager)):
    preferred_name = request.get("preferred_name", "").strip()
    if not preferred_name:
        return JSONResponse({"error": "Preferred name is required"}, status_code=400)

    user_data = await User.from_id(user.id)
    if user_data is not None:
        await user_data.refresh_fields({"preferred_name": preferred_name})

    return JSONResponse({"status": "success"})
