import json

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, Response, status
from starlette.responses import JSONResponse

from server.schemas.authenticate.user import User
from server.schemas.user.user_profile_remove_preference import RemovePreference
from server.services.authenticate.authenticate import manager
from server.services.user.user_preferences import get_user_preferences, save_user_preferences

router = APIRouter()


@router.post("/user_profile/remove_preference", response_class=JSONResponse)
async def user_profile_remove_preference(data: RemovePreference, user: User = Depends(manager)):
    key = data.key
    value = data.value

    user_preferences = await get_user_preferences(user.id)
    if not user_preferences:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User preferences not found!")

    if getattr(user_preferences, key, None) is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="The key does not exist!")

    if isinstance(getattr(user_preferences, key), list):
        if value not in getattr(user_preferences, key):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The value does not exist!",
            )

        setattr(
            user_preferences,
            key,
            [x for x in getattr(user_preferences, key) if x != value],
        )

    elif isinstance(getattr(user_preferences, key), str):
        if getattr(user_preferences, key) != value:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The value does not exist!",
            )

        setattr(user_preferences, key, "")

    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unknown data type!")

    await save_user_preferences(user.id, user_preferences)

    response = Response(content=json.dumps({"status": "updated"}), media_type="application/json")

    return response
