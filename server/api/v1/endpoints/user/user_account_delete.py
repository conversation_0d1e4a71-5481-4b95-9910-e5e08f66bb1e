import json

from fastapi import APIRouter, Depends, Response
from starlette.responses import JSONResponse

from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.authenticate.google_logout import GoogleLogout
from server.services.user.user_account_delete import delete_user_account
from server.utils.settings import settings

router = APIRouter()


@router.get("/user_account_delete", response_class=JSONResponse)
async def user_account_delete(user: User = Depends(manager)):
    # if settings.OTTO_ENV.upper() != "DEV":
    # return Response(status_code=403, content=json.dumps({"message": "Not
    # implemented."}), media_type="application/json")

    user_profile = await UserProfile.from_user_id(user.id)
    if user_profile:
        google_logout_api = GoogleLogout(user_profile.refresh_token)
        google_logout_api.logout()

    await delete_user_account(user.id, user.email)

    response = Response(content=json.dumps({"status": "success"}), media_type="application/json")
    response.delete_cookie(
        "access_token",
        secure=(settings.COOKIES_DOMAIN != "localhost"),
        domain=settings.COOKIES_DOMAIN,
    )
    response.delete_cookie(
        "refresh_token",
        secure=(settings.COOKIES_DOMAIN != "localhost"),
        domain=settings.COOKIES_DOMAIN,
    )

    return response
