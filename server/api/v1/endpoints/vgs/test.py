import json

from fastapi import APIRouter, Response

from server.schemas.vgs.test_vgs import TestVgs
from server.utils.mongo_connector import test_vgs_collection

router = APIRouter()


@router.post("/vgs/test")
async def test_vgs(data: TestVgs):
    await test_vgs_collection.insert_one({"card_cvc": data.card_cvc, "card_number": data.card_number})

    return Response(content=json.dumps({"status": "success"}), media_type="application/json")
