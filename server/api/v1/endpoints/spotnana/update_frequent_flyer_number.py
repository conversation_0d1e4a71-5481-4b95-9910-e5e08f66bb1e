import json

from fastapi import APIRouter, Depends, HTTPException, Response

from server.schemas.authenticate.user import User
from server.schemas.spotnana.create_spotnana_profile import UpdateFrequentFlyerNumber
from server.services.authenticate.authenticate import manager
from server.services.payment_profile.spotnana_profile import (
    get_spotnana_user_id,
    update_spotnana_membership_info,
)
from server.services.payment_profile.user_payment_profile import (
    update_user_payment_profile,
)
from server.utils.mongo_connector import payment_profile_collection

router = APIRouter()


@router.post("/spotnana/frequent-flyer-number")
async def update_frequent_flyer_number(data: UpdateFrequentFlyerNumber, user: User = Depends(manager)):
    # Get payment profile from db
    payment_profile_db = await payment_profile_collection.find_one({"users_id": user.id})

    if payment_profile_db is None:
        raise HTTPException(
            status_code=400,
            detail="Payment profile is not complete. Please save your payment profile first.",
        )

    # We need the spotnana user id to attach the membership info
    spotnana_user_id = await get_spotnana_user_id(user.email)
    if spotnana_user_id is None:
        raise HTTPException(
            status_code=400,
            detail="Payment profile is not complete. Please save your payment profile first.",
        )

    # Remove null frequent flyer numbers
    if isinstance(data.frequentFlyerNumbers, list):
        for idx in range(len(data.frequentFlyerNumbers) - 1, -1, -1):
            if data.frequentFlyerNumbers[idx].number is None:
                del data.frequentFlyerNumbers[idx]

    await update_spotnana_membership_info(data, spotnana_user_id)

    # Save user account info to database
    payment_profile_new_data = {**payment_profile_db, **data.model_dump()}

    await update_user_payment_profile(payment_profile_new_data, user.id)

    response = Response(content=json.dumps({"status": "updated"}), media_type="application/json")

    return response
