import json

import aiohttp
from fastapi import APIRouter, Depends, Response

from server.database.models.user import User as UserDbModel
from server.schemas.authenticate.user import User
from server.schemas.spotnana.create_spotnana_profile import (
    CreateSpotnanaProfile,
    FrequentFlyerNumber,
)
from server.services.authenticate.authenticate import manager
from server.services.payment_profile.spotnana_profile import (
    get_spotnana_user_by_email,
    update_spotnana_payment_profile,
    update_spotnana_user_profile,
)
from server.services.payment_profile.user_payment_profile import (
    get_missing_flight_payment_profile_fields,
    is_flight_payment_profile_complete,
    update_user_payment_profile,
)
from server.utils.mongo_connector import payment_profile_collection
from server.utils.settings import settings

router = APIRouter()


@router.post("/spotnana/create-profile")
async def create_spotnana_profile(data: CreateSpotnanaProfile, user: User = Depends(manager)):
    _, missing_fields = get_missing_flight_payment_profile_fields(data.model_dump())
    if len(missing_fields or []) > 0:
        return Response(
            content=f"The input is invalid, missing fields: {missing_fields}",
            media_type="application/json",
            status_code=400,
        )

    payment_profile_db = await payment_profile_collection.find_one({"users_id": user.id})
    payment_profile_db = payment_profile_db if payment_profile_db is not None else {}

    if data.citizenship is not None:
        await UserDbModel.update_citizenship(user.id, data.citizenship)

    await UserDbModel.update_names_from_profile(user.id, data.first_name, data.last_name)

    if isinstance(data.frequentFlyerNumbers, list) and len(data.frequentFlyerNumbers) == 0:
        data.frequentFlyerNumbers = [
            FrequentFlyerNumber(**frequent_flyer)
            for frequent_flyer in payment_profile_db.get("frequentFlyerNumbers", [])
        ]

    # Remove null frequent flyer numbers
    if isinstance(data.frequentFlyerNumbers, list):
        for idx in range(len(data.frequentFlyerNumbers) - 1, -1, -1):
            if data.frequentFlyerNumbers[idx].number is None:
                del data.frequentFlyerNumbers[idx]

    spotnana_user = await get_spotnana_user_by_email(user.email)
    spotnana_user_id = spotnana_user.get("id", None)
    spotnana_original_user_persona = spotnana_user.get("persona", "GUEST")

    if is_flight_payment_profile_complete(data.model_dump()):
        await update_spotnana_user_profile(data, user.email, spotnana_user_id, spotnana_original_user_persona)

    card_related_fields = ["cardholder_name", "exp_date", "address", "city", "zip_code"]
    is_card_details_updated = [
        getattr(data, field) != payment_profile_db.get(field, None) for field in card_related_fields
    ].count(True) > 0

    if is_flight_payment_profile_complete(data.model_dump()) and (
        is_card_details_updated or data.isCVCEdited or data.isCardNumberEdited
    ):
        await update_spotnana_payment_profile(data, user.email)

    # Save user account info to database
    payment_profile_new_data = data.model_dump()
    is_cvc_edited = payment_profile_new_data.pop("isCVCEdited", None)
    is_card_number_edited = payment_profile_new_data.pop("isCardNumberEdited", None)
    if not is_cvc_edited:
        payment_profile_new_data.pop("card_cvc", None)
    if not is_card_number_edited:
        payment_profile_new_data.pop("card_number", None)

    await update_user_payment_profile(payment_profile_new_data, user.id)

    response = Response(content=json.dumps({"status": "created"}), media_type="application/json")

    return response


@router.post("/spotnana/form-submit")
async def submit_spotnana_form(data: CreateSpotnanaProfile, user: User = Depends(manager)):
    async with aiohttp.ClientSession() as session:
        res = await session.post(
            f"{settings.OTTO_VGS_INBOUND_HOST}/api/spotnana/create-profile",
            headers={"Content-Type": "application/json"},
            data=data.model_dump_json(),
        )
        res.raise_for_status()
        return await res.json()
