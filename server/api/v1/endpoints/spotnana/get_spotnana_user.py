import json
from typing import Any, get_args

from fastapi import APIRouter, Depends, Response

from server.schemas.authenticate.user import User
from server.schemas.spotnana.user import PersonalInfo
from server.services.authenticate.authenticate import manager
from server.services.trips.flight_credits_api import flight_credits_api
from server.utils.mongo_connector import payment_profile_collection

router = APIRouter()


@router.get("/spotnana/get-profile")
async def get_spotnana_profile(user: User = Depends(manager)):
    payment_profile = await payment_profile_collection.find_one({"users_id": user.id}, {"_id": 0})

    airline_credits: list[dict[str, Any]] = []
    if payment_profile is not None:
        airline_credits = await flight_credits_api.get_user_unused_credits(user.email, ignore_cache=True)

    response = Response(
        content=json.dumps(
            {
                "payment_profile": payment_profile,
                "dropdown_values": {
                    "title": [
                        {
                            "value": v,
                            "text": v.capitalize(),
                        }
                        for v in get_args(get_args(PersonalInfo.__annotations__["title"])[0])
                        if v != "TITLE_UNKNOWN"  # Skip the TITLE_UNKNOWN value entirely
                    ],
                    "gender": [
                        {"value": v, "text": v.capitalize()}
                        for v in get_args(get_args(PersonalInfo.__annotations__["gender"])[0])
                    ],
                },
                "airline_credits": flight_credits_api.group_credits_by_airline(airline_credits),
            }
        ),
        media_type="application/json",
    )

    return response
