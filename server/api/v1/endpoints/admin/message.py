from typing import List

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from openai import BaseModel
from sqlalchemy import delete
from starlette.responses import JSONResponse
from starlette.status import HTTP_403_FORBIDDEN

from server.database.models.checkpoint import Checkpoint
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import admin_manager
from server.utils.pg_connector import async_session
from server.utils.settings import settings

router = APIRouter()


class CheckpointDeleteRequest(BaseModel):
    checkpoint_ids: List[int] = []


@router.delete("/admin/messages/batch")
async def delete_messages_batch(data: CheckpointDeleteRequest, user: User = Depends(admin_manager)) -> JSONResponse:
    """Delete multiple messages by checkpoint IDs."""
    if settings.OTTO_ENV.upper() not in ("DEV", "STG"):
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN,
            detail="This operation is only allowed in development and staging environments",
        )

    if not data.checkpoint_ids:
        return JSONResponse({"success": False, "message": "No message IDs provided"})

    async with async_session() as session:
        async with session.begin():
            query = delete(Checkpoint).where(Checkpoint.id.in_(data.checkpoint_ids))
            await session.execute(query)

    response = {
        "success": True,
        "message": f"Successfully deleted {len(data.checkpoint_ids)} messages",
        "deleted_ids": data.checkpoint_ids,
    }

    return JSONResponse(response)
