from zoneinfo import ZoneInfo

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, <PERSON><PERSON>
from starlette.responses import JSONResponse

from server.api.v1.endpoints.trips.list_trips import (
    categorize_and_sort_trips,
    format_trips_for_response,
    get_last_timestamps,
    get_trip_bookings_map,
)
from server.database.models.chat_thread import ChatThread
from server.dependencies import admin_manager
from server.schemas.authenticate.user import User
from server.utils.pg_connector import async_session

router = APIRouter()


@router.get("/admin/users/trip")
async def list_trips(
    user_id: int,
    user: User = Depends(admin_manager),
    x_timezone: str | None = Header(default=None),
) -> JSONResponse:
    trips = await ChatThread.from_user_id(user_id, filter_only_upcoming=False, include_deleted=True)
    chat_thread_ids = [trip.ChatThread.id for trip in trips]

    # Get bookings map
    thread_bookings_map = await get_trip_bookings_map(chat_thread_ids)

    # Get last timestamps
    async with async_session() as session:
        async with session.begin():
            trips_last_timestamps = await get_last_timestamps(chat_thread_ids, session)

    # Retrieve timezone information if provided
    try:
        tz = ZoneInfo(x_timezone) if x_timezone else None
    except Exception:
        tz = None

    # Categorize and sort trips
    (
        planned_trips,
        booked_trips,
        past_trips,
        saved_trips,
        preferences_thread_id,
        travel_policy_thread_id,
        onboarding_thread_id,
    ) = categorize_and_sort_trips(trips, thread_bookings_map, trips_last_timestamps, tz=tz)

    # Format for response
    planned, booked, past, saved = format_trips_for_response(
        planned_trips, booked_trips, past_trips, saved_trips, thread_bookings_map
    )

    return JSONResponse(
        content={
            "planned": planned,
            "booked": booked,
            "past": past,
            "saved": saved,
            "preferences_thread_id": preferences_thread_id,
            "travel_policy_thread_id": travel_policy_thread_id,
            "onboarding_thread_id": onboarding_thread_id,
        }
    )
