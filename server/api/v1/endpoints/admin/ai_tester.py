from datetime import datetime
from typing import List

from fastapi import APIRouter, Depends, Response
from pydantic import BaseModel

from baml_client import b
from baml_client.types import TestPlan, UserResponseMessage
from server.dependencies import admin_manager
from server.schemas.authenticate.user import User

router = APIRouter()


class GenerateTestPlanRequest(BaseModel):
    test_requirement: str


class GenerateTestUserResponseRequest(BaseModel):
    test_plan: TestPlan
    messages: List[str]


@router.post("/admin/ai_generated_test_plan")
async def ai_generated_test_plan(data: GenerateTestPlanRequest, user: User = Depends(admin_manager)):
    test_plan: TestPlan = await b.GenerateTestPlan(test_requirement=data.test_requirement)
    response = Response(content=test_plan.model_dump_json(), media_type="application/json")
    return response


@router.post("/admin/ai_generated_test_user_response")
async def ai_generated_test_user_response(data: GenerateTestUserResponseRequest, user: User = Depends(admin_manager)):
    current_date = datetime.now()
    test_user_response: UserResponseMessage = await b.GenerateTestUserResponse(
        test_plan=data.test_plan, messages=data.messages, current_date=current_date.strftime("%B %dth %Y")
    )
    response = Response(content=test_user_response.model_dump_json(), media_type="application/json")
    return response
