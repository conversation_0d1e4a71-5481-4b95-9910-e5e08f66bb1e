from fastapi import APIRouter, Depends
from starlette.responses import JSONResponse

from server.dependencies import admin_manager
from server.schemas.authenticate.user import User
from server.services.one_time_codes.manage import generate_otcs

router = APIRouter()


@router.get("/admin/otc/generate", response_class=JSONResponse)
async def generate_otc(group_name: str, n_codes: int, user: User = Depends(admin_manager)):
    codes: list[str] = await generate_otcs(group_name, n_codes)

    return {"status": "OK", "n_codes": len(codes), "codes": codes}
