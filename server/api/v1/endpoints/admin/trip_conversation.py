import asyncio
import itertools

from fastapi import APIRouter, Depends, HTTPException

from company_travel_policy_agent.company_travel_policy_agent import CompanyTravelPolicyAgent
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.front_of_house_agent import FrontOfHouseAgent
from future_trips_agent.future_trips_agent import TravelAgentFutureTrips
from preferences_agent.preferences_agent import TravelAgentPreferences
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User as UserDB
from server.dependencies import admin_manager, manager
from server.schemas.authenticate.user import User
from server.utils.logger import logger
from server.utils.settings import settings
from server.utils.websocket_no_op import partial_no_op

router = APIRouter()


@router.get("/admin/users/trip_conversation")
async def get_trip_conversation(trip_id: int, user: User = Depends(admin_manager)):
    thread = await ChatThread.from_id(trip_id, include_deleted=True)
    assert thread is not None
    user_db = await UserDB.from_id(thread.users_id)
    assert user_db is not None

    if thread.is_deleted:
        logger.info(f"Admin user (id: {user.id}) accessed deleted thread (id: {trip_id}) for user (id: {user_db.id})")

    trip_user = User(
        id=user_db.id,
        email=user_db.email,
        first_name=user_db.first_name,
        last_name=user_db.last_name,
        preferred_name=user_db.preferred_name,
        profile_picture=user_db.profile_picture or "",
        created_date=user_db.created_date,
        tutorial_completed=user_db.tutorial_completed,
        citizenship=user_db.citizenship,
    )

    agent = FrontOfHouseAgent(thread, partial_no_op, None, trip_user, None, None)
    conversation = await agent.get_history_messages(
        lambda message: map_websocket_message(
            message,
            agent.travel_context.hotel_search_core_criteria.check_in_date,
            agent.travel_context.hotel_search_core_criteria.check_in_date,
        ),
    )

    return conversation


@router.get("/admin/user-trip")
async def get_trip(trip_id: int, user: User = Depends(manager)):
    thread = await ChatThread.from_id(trip_id, include_deleted=True)
    assert thread is not None
    user_db = await UserDB.from_id(thread.users_id)
    assert user_db is not None
    return user_db


@router.get("/admin/users/future_trips_conversation")
async def get_future_trips_conversation(user_id: int, user: User = Depends(admin_manager)):
    threads = await ChatThread.from_user_id_and_title(user_id, settings.FUTURE_TRIPS_THREAD_TITLE, include_deleted=True)
    if len(threads) > 0:
        user_db = await UserDB.from_id(threads[0].ChatThread.users_id)
        assert user_db is not None

        for thread in threads:
            if thread.ChatThread.is_deleted:
                logger.info(
                    f"Admin user (id: {user.id}) accessed deleted future trips thread (id: {thread.ChatThread.id}) for user (id: {user_db.id})"
                )

        trip_user = User(
            id=user_db.id,
            email=user_db.email,
            first_name=user_db.first_name,
            last_name=user_db.last_name,
            preferred_name=user_db.preferred_name,
            profile_picture=user_db.profile_picture or "",
            created_date=user_db.created_date,
            tutorial_completed=user_db.tutorial_completed,
            citizenship=user_db.citizenship,
        )

        agents = [
            TravelAgentFutureTrips(trip_user, thread.ChatThread.id, None, is_onboarding=False) for thread in threads
        ]
        agents_messages = await asyncio.gather(*[agent.get_history_messages() for agent in agents])

        conversation = list(itertools.chain.from_iterable(agents_messages))
        return conversation

    return []


@router.get("/admin/users/travel_preferences_conversation")
async def get_travel_preferences_conversation(user_id: int, user: User = Depends(admin_manager)):
    threads = await ChatThread.from_user_id_and_title(user_id, settings.PREFERENCES_THREAD_TITLE)
    if not threads:
        return [
            {
                "type": "prompt",
                "isBotMessage": True,
                "expectResponse": False,
                "text": "This user has not initiated the travel preferences conversation.",
            }
        ]

    thread = threads[0]
    user_db = await UserDB.from_id(thread.ChatThread.users_id)
    assert user_db is not None
    trip_user = User(
        id=user_db.id,
        email=user_db.email,
        first_name=user_db.first_name,
        last_name=user_db.last_name,
        preferred_name=user_db.preferred_name,
        profile_picture=user_db.profile_picture or "",
        created_date=user_db.created_date,
        tutorial_completed=user_db.tutorial_completed,
        citizenship=user_db.citizenship,
    )

    agent = TravelAgentPreferences(trip_user, thread.ChatThread.id, None)
    conversation = await agent.get_history_messages()

    return conversation


@router.get("/admin/users/travel_policy_conversation")
async def get_travel_policy_conversation(user_id: int, user: User = Depends(admin_manager)):
    threads = await ChatThread.from_user_id_and_title(user_id, settings.TRAVEL_POLICY_THREAD_TITLE)

    if not threads:
        return [
            {
                "type": "prompt",
                "isBotMessage": True,
                "expectResponse": False,
                "text": "This user has not initiated the travel policy conversation.",
            }
        ]

    thread = threads[0]

    user_db = await UserDB.from_id(thread.ChatThread.users_id)
    if user_db is None:
        raise HTTPException(status_code=404, detail="User not found")

    trip_user = User(
        id=user_db.id,
        email=user_db.email,
        first_name=user_db.first_name,
        last_name=user_db.last_name,
        preferred_name=user_db.preferred_name,
        profile_picture=user_db.profile_picture or "",
        created_date=user_db.created_date,
        tutorial_completed=user_db.tutorial_completed,
        citizenship=user_db.citizenship,
    )

    agent = CompanyTravelPolicyAgent(
        trip_user,
        thread.ChatThread.id,
        None,
    )
    conversation = await agent.get_history_messages()

    return conversation
