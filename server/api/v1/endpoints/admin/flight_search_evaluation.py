import json
from typing import Any, Dict, List, Optional

from fastapi import API<PERSON><PERSON><PERSON>, Depends, Query
from langchain_core.messages import messages_from_dict
from starlette.responses import JSONResponse

from server.database.models.checkpoint import Checkpoint
from server.dependencies import admin_manager
from server.schemas.authenticate.user import User
from server.utils.mongo_connector import flight_search_evaluation_collection, trip_context_v2_collection

router = APIRouter()


async def get_human_messages_for_thread(thread_id: int) -> List[Dict[str, Any]]:
    """Extract human messages for a given thread_id."""
    items = await Checkpoint.from_thread_id(thread_id=thread_id)
    if not items:
        return []

    human_messages = []
    for item in items:
        message_data = item.Checkpoint.data
        if isinstance(message_data, str):
            message_data = json.loads(message_data)
        if message_data.get("type") == "human":
            messages = messages_from_dict([message_data])
            if messages:
                message = messages[0]
                human_messages.append(
                    {
                        "id": item.Checkpoint.id,
                        "created_date": item.Checkpoint.created_date.isoformat(),
                        "content": message.content,
                        "additional_kwargs": message.additional_kwargs,
                    }
                )
    return human_messages


async def get_travel_context_for_thread(thread_id: int) -> Dict[str, Any] | None:
    """Retrieve travel context for a given thread_id."""
    travel_context = await trip_context_v2_collection.find_one({"thread_id": thread_id})
    if travel_context:
        if "_id" in travel_context:
            travel_context.pop("_id")
        return travel_context
    return None


@router.get("/admin/flight_search_evaluation/data")
async def get_flight_search_evaluation_data(
    user: User = Depends(admin_manager),
    trip_id: Optional[int] = Query(None),
    user_email: Optional[str] = Query(None),
    environment: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    limit: int = Query(10, le=100),
):
    """Get flight search evaluation data with filters and pagination."""
    query = {}

    if trip_id:
        query["trip_id"] = trip_id
    if user_email:
        query["user_email"] = user_email
    if environment:
        query["environment"] = environment

    skip = (page - 1) * limit

    results = {}
    total = 0

    if flight_search_evaluation_collection is not None:
        total = await flight_search_evaluation_collection.count_documents(query)

        cursor = flight_search_evaluation_collection.find(query).sort("created_at", -1).skip(skip).limit(limit)
        results = await cursor.to_list(length=limit)

        for result in results:
            result["_id"] = str(result["_id"])

            thread_id = result.get("trip_id")
            if thread_id:
                result["human_messages"] = await get_human_messages_for_thread(thread_id)
                result["travel_context"] = await get_travel_context_for_thread(thread_id)

    return JSONResponse(
        content={"results": results, "total": total, "page": page, "total_pages": (total + limit - 1) // limit}
    )
