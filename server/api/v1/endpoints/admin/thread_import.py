from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from pydantic import BaseModel
from starlette.responses import JSONResponse

from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.database.models.user import User
from server.dependencies import admin_manager
from server.utils.mongo_connector import trip_context_v2_collection

router = APIRouter()


class ThreadImportRequest(BaseModel):
    source_thread_id: int


@router.post("/admin/import-thread", response_class=JSONResponse)
async def import_thread(data: ThreadImportRequest, user: User = Depends(admin_manager)):
    """
    Import a thread from one user to another.
    This will create a new thread for the current user with the same messages as the source thread.
    """
    source_thread = await ChatThread.from_id(data.source_thread_id, include_deleted=True)
    if not source_thread:
        raise HTTPException(status_code=404, detail="Source thread not found")

    target_user_id = user.id

    new_thread_title = f"[Imported] {source_thread.title}"
    new_thread = ChatThread(users_id=target_user_id, title=new_thread_title)
    await ChatThread.new_chat_thread(new_thread)

    created_threads = await ChatThread.from_user_id_and_title(target_user_id, new_thread_title)
    if not created_threads:
        raise HTTPException(status_code=500, detail="Failed to create new thread")

    new_thread_id = created_threads[-1].ChatThread.id

    checkpoints = await Checkpoint.from_thread_id(data.source_thread_id)
    if not checkpoints:
        return {
            "message": "Thread imported successfully, but no messages were found to import",
            "thread_id": new_thread_id,
        }

    new_checkpoints = []
    for checkpoint in checkpoints:
        checkpoint_data = checkpoint[0].data  # Extract data from the checkpoint row
        new_checkpoint = Checkpoint(
            thread_id=new_thread_id,
            data=checkpoint_data,
            input_tokens=checkpoint[0].input_tokens,
            output_tokens=checkpoint[0].output_tokens,
        )
        new_checkpoints.append(new_checkpoint)

    await Checkpoint.new_checkpoint_batch(new_checkpoints)

    travel_context = await trip_context_v2_collection.find_one({"thread_id": data.source_thread_id})
    if travel_context:
        if "_id" in travel_context:
            travel_context.pop("_id")

        travel_context["thread_id"] = new_thread_id

        await trip_context_v2_collection.update_one({"thread_id": new_thread_id}, {"$set": travel_context}, upsert=True)

    return {
        "message": f"Successfully imported {len(new_checkpoints)} messages from thread {data.source_thread_id} to thread {new_thread_id}",
        "thread_id": new_thread_id,
    }
