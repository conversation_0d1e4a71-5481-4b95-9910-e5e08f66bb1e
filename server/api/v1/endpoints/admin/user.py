import json
from typing import Any

from fastapi import APIRouter, Depends, Response
from starlette.responses import JSONResponse

from server.database.models.user import User, get_all_users_session
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.dependencies import admin_manager
from server.services.user.user_preferences import get_user_preferences_with_updated_time

router = APIRouter()


@router.get("/admin/users", response_class=JSONResponse)
async def get_users(user: User = Depends(admin_manager)):
    users = await get_all_users_session()

    return users


@router.get("/admin/users/profile", response_class=JSONResponse)
async def get_user_travel_profile(user_id: int, user: User = Depends(admin_manager)):
    user_travel_company_policy: dict[str, Any] | None = None
    db_user_company_travel_policy = await UserCompanyTravelPolicy.from_user_id(user_id)
    if db_user_company_travel_policy:
        user_travel_company_policy = db_user_company_travel_policy.parsed_travel_policy

    user_preferences = await get_user_preferences_with_updated_time(user_id)
    user_preferences_dict: dict[str, Any] | None = None
    if user_preferences is not None:
        user_preferences_dict = user_preferences.model_dump()
        if isinstance(user_preferences.preferred_airline_brands, str):
            user_preferences_dict["preferred_airline_brands"] = [
                v.strip() for v in user_preferences.preferred_airline_brands.split(",")
            ]

        if isinstance(user_preferences.preferred_hotel_brands, str):
            user_preferences_dict["preferred_hotel_brands"] = [
                v.strip() for v in user_preferences.preferred_hotel_brands.split(",")
            ]
        if update_time := user_preferences.updated_at:
            user_preferences_dict["updated_at"] = update_time.isoformat()

    response = Response(
        content=json.dumps(
            {
                "userPreferences": None
                if not user_preferences_dict or all(not v for v in user_preferences_dict.values())
                else user_preferences_dict,
                "userCompanyTravelPolicy": user_travel_company_policy,
            }
        ),
        media_type="application/json",
    )

    return response
