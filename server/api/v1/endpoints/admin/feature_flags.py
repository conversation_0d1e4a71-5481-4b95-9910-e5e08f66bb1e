from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from pydantic import BaseModel
from starlette.responses import JSONResponse

from server.dependencies import admin_manager
from server.schemas.authenticate.user import User
from server.services.feature_flags.feature_flag import (
    FeatureFlags,
    create_feature_flag,
    get_feature_flags_by_user_id,
    remove_feature_flag,
)

router = APIRouter()


@router.get("/admin/feature-flag", response_class=JSONResponse)
async def get_feature_flag(user: User = Depends(admin_manager)):
    feature_flag = await get_feature_flags_by_user_id(user.id)
    if feature_flag is None:
        raise HTTPException(status_code=404, detail="Feature flag not found.")

    return feature_flag


class FlagControlRequest(BaseModel):
    enabled: bool
    feature_flag: FeatureFlags


@router.post("/admin/feature-flag", response_class=JSONResponse)
async def enable_feature_flag(data: FlagControlRequest, user: User = Depends(admin_manager)):
    if data.enabled:
        result = await create_feature_flag(user.id, user.email, data.feature_flag)
    else:
        result = await remove_feature_flag(user.id, user.email, data.feature_flag)
    return result
