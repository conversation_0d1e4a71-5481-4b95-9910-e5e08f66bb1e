from fastapi import APIRouter, HTTPException, status
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import selectinload

from server.api.v1.endpoints.user_profile.personal_information import save_user_personal_information
from server.database.models.user import Organization, OrganizationDomain, UserRole
from server.database.models.user import User as UserDB
from server.schemas.authenticate.user import User
from server.schemas.user.organization import (
    OrganizationCreate,
    OrganizationDomainCreate,
    OrganizationResponse,
    OrganizationUpdate,
)
from server.schemas.user_profile.personal_information import PersonalInformationExtendedRequest
from server.services.user_profile.personal_information import update_spotnana_travel_arranger_role
from server.utils.pg_connector import async_session
from server.utils.spotnana_api import spotnana_api

router = APIRouter()


@router.post("/admin/organizations", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED)
async def create_organization(organization_data: OrganizationCreate):
    admin_user: UserDB | None = await UserDB.from_email(organization_data.admin_email)
    if admin_user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User with the specified email not found in database!"
        )

    async with async_session() as session:
        async with session.begin():
            try:
                # Check if primary domain already exists
                stmt = select(Organization).where(Organization.domain == organization_data.domain)
                result = await session.execute(stmt)
                existing_org = result.scalar_one_or_none()

                if existing_org:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"Organization with domain '{organization_data.domain}' already exists",
                    )

                # Check if any additional domains already exist
                if organization_data.additional_domains:
                    domain_list = [d.domain for d in organization_data.additional_domains]
                    stmt = select(OrganizationDomain).where(OrganizationDomain.domain.in_(domain_list))
                    result = await session.execute(stmt)
                    existing_domains = result.scalars().all()

                    if existing_domains:
                        conflicting_domains = [d.domain for d in existing_domains]
                        raise HTTPException(
                            status_code=status.HTTP_409_CONFLICT,
                            detail=f"The following domains are already registered: {', '.join(conflicting_domains)}",
                        )

                # Create organization
                org = Organization(
                    name=organization_data.name,
                    domain=organization_data.domain,
                    auto_approve_domain_users=organization_data.auto_approve_domain_users,
                    require_otp_login=organization_data.require_otp_login,
                    preferred_airlines=organization_data.preferred_airlines,
                    preferred_hotels=organization_data.preferred_hotels,
                    travel_policy=organization_data.travel_policy,
                    billing_contact_email=organization_data.billing_contact_email,
                )

                session.add(org)
                await session.flush()  # Get the org.id

                # Add primary domain to organization_domains
                primary_domain = OrganizationDomain(
                    organization_id=org.id, domain=organization_data.domain, is_validated=True
                )
                session.add(primary_domain)

                # Add additional domains
                if organization_data.additional_domains:
                    for domain_data in organization_data.additional_domains:
                        additional_domain = OrganizationDomain(
                            organization_id=org.id, domain=domain_data.domain, is_validated=domain_data.is_validated
                        )
                        session.add(additional_domain)

                # Collect domain information before committing
                validated_domains = [organization_data.domain]  # Primary domain is always validated
                if organization_data.additional_domains:
                    validated_domains.extend([d.domain for d in organization_data.additional_domains if d.is_validated])

                await session.commit()

                # Update the admin user
                await admin_user.refresh_fields({"role": UserRole.company_admin, "organization_id": org.id})

                # Create Spotnana user with mock values
                personal_information: PersonalInformationExtendedRequest = PersonalInformationExtendedRequest(
                    title="TITLE_UNKNOWN",
                    first_name="Travel Arranger",
                    last_name="Otto",
                    dob="01/01/1970",
                    gender="UNSPECIFIED",
                    phone="",
                )

                await save_user_personal_information(personal_information, User(**admin_user.as_dict()))

                # Update the Spotnana user role to TRAVEL_ARRANGER
                spotnana_user_id: str | None = (
                    (await spotnana_api.get_user_by_email(admin_user.email)).get("elements", [{}])[0].get("id")
                )
                assert spotnana_user_id is not None, "User should be created on spotnana."

                await update_spotnana_travel_arranger_role(spotnana_user_id)

                # Build response (org.users will be empty for new organization)
                return OrganizationResponse(
                    id=org.id,
                    name=org.name,
                    domain=org.domain,
                    auto_approve_domain_users=org.auto_approve_domain_users,
                    require_otp_login=org.require_otp_login,
                    preferred_airlines=org.preferred_airlines,
                    preferred_hotels=org.preferred_hotels,
                    travel_policy=org.travel_policy,
                    billing_contact_email=org.billing_contact_email,
                    created_date=org.created_date,
                    updated_date=org.updated_date,
                    user_count=1,  # New organization has no users
                    validated_domains=validated_domains,
                )

            except IntegrityError:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT, detail="Organization with this domain already exists"
                )
            except HTTPException:
                raise  # Re-raise HTTP exceptions
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to create organization: {str(e)}"
                )


@router.delete("/admin/organizations/{organization_id}/domains", status_code=status.HTTP_204_NO_CONTENT)
async def delete_organization_domain_by_name(organization_id: int, domain_name: str):
    async with async_session() as session:
        async with session.begin():
            stmt = select(Organization).where(Organization.id == organization_id)
            result = await session.execute(stmt)
            org = result.scalar_one_or_none()

            if not org:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

            stmt = select(OrganizationDomain).where(
                OrganizationDomain.domain == domain_name.lower(), OrganizationDomain.organization_id == organization_id
            )
            result = await session.execute(stmt)
            domain = result.scalar_one_or_none()

            if not domain:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Domain '{domain_name}' not found or doesn't belong to this organization",
                )

            # Prevent deletion of primary domain
            if domain.domain == org.domain:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Cannot delete the primary domain of an organization. Update the organization's primary domain first.",
                )

            try:
                await session.delete(domain)
                await session.commit()
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to delete domain: {str(e)}"
                )


@router.get("/admin/organizations/{organization_id}", response_model=OrganizationResponse)
async def get_organization(organization_id: int):
    """
    Get organization details by ID (Super Admin only)
    """
    async with async_session() as session:
        async with session.begin():
            stmt = (
                select(Organization)
                .options(selectinload(Organization.users), selectinload(Organization.validated_domains))
                .where(Organization.id == organization_id)
            )

            result = await session.execute(stmt)
            org = result.scalar_one_or_none()

            if not org:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

            validated_domains = [d.domain for d in org.validated_domains if d.is_validated]

            return OrganizationResponse(
                id=org.id,
                name=org.name,
                domain=org.domain,
                image=org.image,
                auto_approve_domain_users=org.auto_approve_domain_users,
                require_otp_login=org.require_otp_login,
                preferred_airlines=org.preferred_airlines,
                preferred_hotels=org.preferred_hotels,
                travel_policy=org.travel_policy,
                billing_contact_email=org.billing_contact_email,
                created_date=org.created_date,
                updated_date=org.updated_date,
                user_count=len(org.users),
                validated_domains=validated_domains,
            )


@router.put("/admin/organizations/{organization_id}", response_model=OrganizationResponse)
async def update_organization(
    organization_id: int,
    organization_data: OrganizationUpdate,
):
    async with async_session() as session:
        async with session.begin():
            stmt = (
                select(Organization)
                .options(selectinload(Organization.users), selectinload(Organization.validated_domains))
                .where(Organization.id == organization_id)
            )

            result = await session.execute(stmt)
            org = result.scalar_one_or_none()

            if not org:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

            try:
                # Update only provided fields
                update_data = organization_data.dict(exclude_unset=True)

                for field, value in update_data.items():
                    setattr(org, field, value)

                await session.commit()

                # Get the updated data in a separate query after commit
                stmt = (
                    select(Organization)
                    .options(selectinload(Organization.users), selectinload(Organization.validated_domains))
                    .where(Organization.id == org.id)
                )

                result = await session.execute(stmt)
                updated_org = result.scalar_one()

                validated_domains = [d.domain for d in updated_org.validated_domains if d.is_validated]

                return OrganizationResponse(
                    id=updated_org.id,
                    name=updated_org.name,
                    domain=updated_org.domain,
                    auto_approve_domain_users=updated_org.auto_approve_domain_users,
                    require_otp_login=updated_org.require_otp_login,
                    preferred_airlines=updated_org.preferred_airlines,
                    preferred_hotels=updated_org.preferred_hotels,
                    travel_policy=updated_org.travel_policy,
                    billing_contact_email=updated_org.billing_contact_email,
                    created_date=updated_org.created_date,
                    updated_date=updated_org.updated_date,
                    user_count=len(updated_org.users),
                    validated_domains=validated_domains,
                )

            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to update organization: {str(e)}"
                )


@router.post("/admin/organizations/{organization_id}/domains", status_code=status.HTTP_201_CREATED)
async def add_organization_domain(organization_id: int, domain_data: OrganizationDomainCreate):
    async with async_session() as session:
        async with session.begin():
            # Check if organization exists
            stmt = select(Organization).where(Organization.id == organization_id)
            result = await session.execute(stmt)
            org = result.scalar_one_or_none()

            if not org:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found")

            # Check if domain already exists
            stmt = select(OrganizationDomain).where(OrganizationDomain.domain == domain_data.domain)
            result = await session.execute(stmt)
            existing_domain = result.scalar_one_or_none()

            if existing_domain:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT, detail=f"Domain '{domain_data.domain}' is already registered"
                )

            try:
                new_domain = OrganizationDomain(
                    organization_id=organization_id, domain=domain_data.domain, is_validated=domain_data.is_validated
                )

                session.add(new_domain)
                await session.commit()

                return {"message": f"Domain '{domain_data.domain}' added successfully"}

            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to add domain: {str(e)}"
                )
