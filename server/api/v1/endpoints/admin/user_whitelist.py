import json
from datetime import datetime
from math import ceil
from typing import Any, List, Optional, Sequence, Tuple
from zoneinfo import ZoneInfo

from email_validator import EmailNotValidError, validate_email
from fastapi import APIRouter, Depends, HTTPException, Query, Response
from pydantic import BaseModel
from pymongo import ASCENDING, DESCENDING
from sqlalchemy import func, select
from sqlalchemy.engine.row import Row
from sqlalchemy.orm import aliased
from starlette.responses import JSONResponse

from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.database.models.user import User
from server.database.models.user_profile import UserProfile
from server.services.authenticate.authenticate import admin_manager
from server.services.user.user_activity import get_users_activities
from server.services.user.user_preferences import get_users_preferences
from server.utils.mongo_connector import users_whitelist_collection
from server.utils.pg_connector import async_session
from server.utils.settings import settings

pacific_tz = ZoneInfo("America/Los_Angeles")

router = APIRouter()


class EmailsItem(BaseModel):
    emails: list[str]


class UserRow(BaseModel):
    name: Optional[str]
    email: str
    profile_picture: Optional[str]
    identity_provider: Optional[str]
    # Activation stage
    is_tutorial_completed: Optional[bool]
    is_onboarding_completed: Optional[bool]
    is_company_policy_uploaded: Optional[bool]
    is_calendar_connected: Optional[bool]
    has_preferences: Optional[bool]
    # Activities
    sign_up_at: Optional[Any]
    first_trip_created_at: Optional[Any]
    last_trip_created_at: Optional[Any]
    first_hotel_searched_at: Optional[Any]
    last_hotel_searched_at: Optional[Any]
    first_flight_searched_at: Optional[Any]
    last_flight_searched_at: Optional[Any]
    otto_ruid: Optional[Any]
    last_sign_in: Optional[Any]


class WhitelistResponse(BaseModel):
    data: List[dict]
    total: int
    page: int
    total_pages: int


def readable_date(date: datetime) -> str:
    return date.replace(tzinfo=ZoneInfo("UTC")).astimezone(pacific_tz).strftime("%Y-%m-%d %I:%M:%S %p %Z")


async def get_users_by_emails_with_data(
    emails: List[str],
) -> Sequence[Row[Tuple[User, UserProfile, ChatThread, ChatThread]]]:
    """
    Get multiple users data by emails ordered by user creation date
    """
    async with async_session() as session:
        async with session.begin():
            excluded_titles = [
                settings.ONBOARDING_THREAD_TITLE,
                settings.PREFERENCES_THREAD_TITLE,
                settings.FUTURE_TRIPS_THREAD_TITLE,
                settings.TRAVEL_POLICY_THREAD_TITLE,
                settings.ONBOARDING_PAGE_TITLE,
            ]

            # First sessions subquery with ROW_NUMBER()
            first_sessions = (
                select(
                    ChatThread.users_id,
                    ChatThread.id.label("thread_id"),
                    func.row_number()
                    .over(partition_by=ChatThread.users_id, order_by=Checkpoint.created_date.asc())
                    .label("rn"),
                )
                .join(Checkpoint)
                .where(ChatThread.title.not_in(excluded_titles))
                .subquery()
            )

            # Last sessions subquery with ROW_NUMBER()
            last_sessions = (
                select(
                    ChatThread.users_id,
                    ChatThread.id.label("thread_id"),
                    func.row_number()
                    .over(partition_by=ChatThread.users_id, order_by=Checkpoint.created_date.desc())
                    .label("rn"),
                )
                .join(Checkpoint)
                .where(ChatThread.title.not_in(excluded_titles))
                .subquery()
            )

            # Filter only the first row from each partition
            first_sessions_filtered = (
                select(first_sessions.c.users_id, first_sessions.c.thread_id).where(first_sessions.c.rn == 1).subquery()
            )

            last_sessions_filtered = (
                select(last_sessions.c.users_id, last_sessions.c.thread_id).where(last_sessions.c.rn == 1).subquery()
            )

            # Aliases for the chat threads
            FirstSession = aliased(ChatThread)
            LastSession = aliased(ChatThread)

            # Main query with ordering by User.created_at
            query = (
                select(User, UserProfile, FirstSession, LastSession)
                .outerjoin(UserProfile, User.id == UserProfile.users_id)
                .outerjoin(first_sessions_filtered, User.id == first_sessions_filtered.c.users_id)
                .outerjoin(FirstSession, FirstSession.id == first_sessions_filtered.c.thread_id)
                .outerjoin(last_sessions_filtered, User.id == last_sessions_filtered.c.users_id)
                .outerjoin(LastSession, LastSession.id == last_sessions_filtered.c.thread_id)
                .where(func.lower(User.email).in_(map(str.lower, emails)))
                .order_by(User.created_date)
            )

            result = await session.execute(query)
            return result.all()


async def prepare_user_rows(
    users_data: Sequence[Row[Tuple[User, UserProfile, ChatThread, ChatThread]]],
) -> List[UserRow]:
    """
    Prepare user rows from fetched data with batch activity fetching.

    Args:
        users_data: List of user data tuples

    Returns:
        List of prepared UserRow objects
    """
    user_ids = [str(user.id) for user, *_ in users_data]

    activities = await get_users_activities(user_ids)

    user_preferences = await get_users_preferences(user_ids)
    user_rows = []
    for user, profile, first_session, last_session in users_data:
        activity_data = activities.get(str(user.id), {})

        has_preferences = user_preferences.get(user.id) is not None

        user_row = UserRow(
            name=f"{user.first_name} {user.last_name}",
            email=user.email,
            profile_picture=user.profile_picture,
            identity_provider=profile.last_login_method if profile else None,
            is_tutorial_completed=user.tutorial_completed,
            is_onboarding_completed=last_session is not None,
            is_company_policy_uploaded=activity_data.get("has_company_policy", False),
            is_calendar_connected=activity_data.get("metadata", {}).get("calendar_connected", False),
            sign_up_at=readable_date(user.created_date),
            first_trip_created_at=readable_date(first_session.created_date) if first_session else None,
            last_trip_created_at=readable_date(last_session.created_date) if last_session else None,
            first_hotel_searched_at=activity_data.get("first_hotel_search"),
            last_hotel_searched_at=activity_data.get("last_hotel_search"),
            first_flight_searched_at=activity_data.get("first_flight_search"),
            last_flight_searched_at=activity_data.get("last_flight_search"),
            has_preferences=has_preferences,
            otto_ruid=activity_data.get("otto_ruid", {}).get("RUID"),
            last_sign_in=activity_data.get("last_sign_in"),
        )
        user_rows.append(user_row)

    return user_rows


@router.get("/admin/whitelist-users/list", response_class=JSONResponse)
async def whitelist_users_list(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1),
    sortField: Optional[str] = None,
    sortOrder: Optional[str] = None,
    user: User = Depends(admin_manager),
):
    sort_direction = ASCENDING if sortOrder == "1" else DESCENDING if sortOrder == "-1" else None

    pipeline = [
        {
            "$project": {
                "emails": {
                    "$filter": {"input": "$whitelisted_emails", "as": "email", "cond": {"$ne": ["$$email", None]}}
                }
            }
        },
        {"$unwind": "$emails"},
        {"$group": {"_id": None, "all_emails": {"$addToSet": "$emails"}, "total": {"$sum": 1}}},
    ]

    result = await users_whitelist_collection.aggregate(pipeline).to_list(1)

    if not result:
        return WhitelistResponse(data=[], total=0, page=page, total_pages=0)

    all_emails = result[0]["all_emails"]
    total = len(all_emails)

    # Get sorted user data
    users_data = await get_users_by_emails_with_data(all_emails)
    user_rows = await prepare_user_rows(users_data)

    all_users = [row.model_dump() for row in user_rows]
    processed = frozenset(user["email"] for user in all_users)  # Immutable for slightly better performance
    all_users += [{"email": e, "name": None} for e in all_emails if e not in processed]

    if sortField and sort_direction and all_users:

        def sort_key(x):
            value = x.get(sortField)
            # Always put null values at the bottom regardless of sort direction
            if value is None:
                return (1, None) if sort_direction == ASCENDING else (-1, None)
            return (0, value) if isinstance(value, bool) else (0, str(value))

        all_users.sort(key=sort_key, reverse=sort_direction == DESCENDING)

    start_idx = (page - 1) * limit
    end_idx = min(start_idx + limit, total)
    paginated_users = all_users[start_idx:end_idx]
    total_pages = ceil(total / limit)

    await users_whitelist_collection.create_index("whitelisted_emails")

    return WhitelistResponse(data=paginated_users, total=total, page=page, total_pages=total_pages)


@router.post("/admin/whitelist-users/add", response_class=JSONResponse)
async def whitelist_users_add(email_input: str, user: User = Depends(admin_manager)):
    try:
        email = email_input.strip().lower()

        existing_doc = await users_whitelist_collection.find_one({"whitelisted_emails": email})

        if existing_doc:
            raise HTTPException(status_code=400, detail=f"Email {email} is already whitelisted")

        result = await users_whitelist_collection.update_one(
            {}, {"$addToSet": {"whitelisted_emails": email}}, upsert=True
        )

        if result.modified_count == 0 and result.upserted_id is None:
            raise HTTPException(status_code=400, detail="Failed to add email to whitelist")

        return Response(
            content=json.dumps({"status": "success", "message": f"Email {email} added to whitelist"}),
            media_type="application/json",
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error adding email to whitelist: {str(e)}")


@router.post("/admin/whitelist-users/add-batch", response_class=JSONResponse)
async def whitelist_users_add_batch(emails: str, user: User = Depends(admin_manager)):
    email_list = [email.strip() for email in emails.split(",")]

    response_data = {
        "success": False,
        "valid_emails_added": [],
        "warnings": {"invalid_format": [], "already_exists": []},
    }

    existing_whitelist = await users_whitelist_collection.find_one({})
    existing_emails = existing_whitelist.get("whitelisted_emails", []) if existing_whitelist else []

    valid_emails = []

    for email in email_list:
        try:
            validated_email = validate_email(email, check_deliverability=False)
            normalized_email = validated_email.normalized

            if normalized_email in existing_emails:
                response_data["warnings"]["already_exists"].append(email)
                continue

            valid_emails.append(normalized_email)
            response_data["valid_emails_added"].append(normalized_email)

        except EmailNotValidError:
            response_data["warnings"]["invalid_format"].append(email)

    if valid_emails:
        await users_whitelist_collection.update_one(
            {}, {"$addToSet": {"whitelisted_emails": {"$each": valid_emails}}}, upsert=True
        )
        response_data["success"] = True

    status_code = 200 if valid_emails else 400

    return JSONResponse(content=response_data, status_code=status_code)


@router.post("/admin/whitelist-users/remove", response_class=JSONResponse)
async def whitelist_users_remove(email: str, user: User = Depends(admin_manager)):
    await users_whitelist_collection.update_one({}, {"$pull": {"whitelisted_emails": email}})

    response = Response(content=json.dumps({"status": "success"}), media_type="application/json")

    return response


@router.post("/admin/whitelist-users/remove-batch", response_class=JSONResponse)
async def whitelist_users_remove_batch(data: EmailsItem, user: User = Depends(admin_manager)):
    await users_whitelist_collection.update_one({}, {"$pull": {"whitelisted_emails": {"$in": data.emails}}})

    response = Response(content=json.dumps({"status": "success"}), media_type="application/json")

    return response
