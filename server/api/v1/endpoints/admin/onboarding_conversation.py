from fastapi import (
    APIRouter,
    Depends,
)

from onboarding_agent.onboarding_agent import OnboardingAgent
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User as UserDB
from server.dependencies import admin_manager
from server.schemas.authenticate.user import User
from server.utils.settings import settings

router = APIRouter()


@router.get("/admin/users/onboarding")
async def get_trip_conversation(user_id: int, user: User = Depends(admin_manager)):
    user_db = await UserDB.from_id(user_id)
    assert user_db is not None
    trip_user = User(
        id=user_db.id,
        email=user_db.email,
        first_name=user_db.first_name,
        last_name=user_db.last_name,
        preferred_name=user_db.preferred_name,
        profile_picture=user_db.profile_picture or "",
        created_date=user_db.created_date,
        tutorial_completed=user_db.tutorial_completed,
        citizenship=user_db.citizenship,
    )

    chat_threads = await ChatThread.from_user_id_and_title(user_id, settings.ONBOARDING_PAGE_TITLE)
    if len(chat_threads) == 0:
        return []

    thread = chat_threads[0].ChatThread

    agent = OnboardingAgent(trip_user, thread.id, None)
    conversation = await agent.get_history_messages()

    return conversation
