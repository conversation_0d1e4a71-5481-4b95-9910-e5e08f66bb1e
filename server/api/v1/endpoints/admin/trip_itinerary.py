import asyncio
import json
from datetime import datetime
from math import ceil
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from pydantic import BaseModel
from sqlalchemy import Float, cast, func, or_, select
from starlette.responses import JSONResponse

from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User as UserDb
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import admin_manager
from server.services.trips.bookings import get_bookings
from server.utils.pg_connector import async_session
from server.utils.settings import settings

router = APIRouter()


class BookingsResponse(BaseModel):
    data: list
    total: int
    page: int
    total_pages: int


@router.get("/admin/trips/{trip_id}", response_class=JSONResponse)
async def get_admin_trip(trip_id: int, user: User = Depends(admin_manager)):
    chat_thread: Optional[ChatThread] = await ChatThread.from_id(trip_id)

    if chat_thread is None:
        return Response(content="{}", media_type="application/json")

    response = Response(
        content=json.dumps(
            {
                "itinerary": await get_bookings(chat_thread),
            }
        ),
        media_type="application/json",
    )

    return response


@router.get("/admin/trips", response_class=JSONResponse)
async def get_admin_trips(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    search: str = Query(None),
    start: str = Query(None, description="Format: dd-mm-yyyy"),
    end: str = Query(None, description="Format: dd-mm-yyyy"),
    booking_type=Query(None),
    user: User = Depends(admin_manager),
):
    skip = (page - 1) * limit
    db_query = {}

    async with async_session() as session:
        # Combine search and date filters into a single query if both are present
        if search or start or end:
            base_query = select(ChatThread.id)

            if search:
                base_query = base_query.join(UserDb, ChatThread.users_id == UserDb.id).where(
                    or_(
                        UserDb.email.ilike(f"%{search}%"),
                        UserDb.first_name.ilike(f"%{search}%"),
                        UserDb.last_name.ilike(f"%{search}%"),
                    )
                )

            if start or end:
                if start:
                    try:
                        start_date = datetime.strptime(start, "%d-%m-%Y")
                        # Set time to beginning of day (00:00:00)
                        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
                        base_query = base_query.where(ChatThread.created_date >= start_date)
                    except ValueError:
                        raise HTTPException(status_code=400, detail="Invalid start date format. Use dd-mm-yyyy")

                if end:
                    try:
                        end_date = datetime.strptime(end, "%d-%m-%Y")
                        # Set time to end of day (23:59:59)
                        end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
                        base_query = base_query.where(ChatThread.created_date <= end_date)
                    except ValueError:
                        raise HTTPException(status_code=400, detail="Invalid end date format. Use dd-mm-yyyy")

            result = await session.execute(base_query)
            thread_ids = [r[0] for r in result]

            if not thread_ids:
                return BookingsResponse(data=[], total=0, page=page, total_pages=0)

            db_query["thread_id"] = thread_ids
            if booking_type:
                db_query["type"] = booking_type

        total, bookings = await asyncio.gather(
            Booking.from_query_count(db_query), Booking.from_query_batch(db_query, offset=skip, limit=limit)
        )

        if not bookings:
            return BookingsResponse(data=[], total=0, page=page, total_pages=0)

        # Fetch all thread and user data in a single query
        booking_thread_ids = [booking.thread_id for booking in bookings]
        if booking_thread_ids:
            thread_query = (
                select(ChatThread, UserDb.email, UserDb.first_name, UserDb.last_name)
                .outerjoin(UserDb, ChatThread.users_id == UserDb.id)
                .where(ChatThread.id.in_(booking_thread_ids))
            )
            result = await session.execute(thread_query)
            threads = {
                thread.id: {"thread": thread, "email": email, "first_name": first_name, "last_name": last_name}
                for thread, email, first_name, last_name in result
            }
        else:
            threads = {}

        # Format bookings with thread data
        formatted_bookings = []
        for booking in bookings:
            if formatted_booking := await format_booking_response(booking):
                thread_id = booking.thread_id
                if thread_data := threads.get(thread_id):
                    formatted_booking.update(
                        {
                            "thread_title": getattr(thread_data["thread"], "title", None),
                            "user_id": getattr(thread_data["thread"], "users_id", None),
                            "user_email": thread_data["email"],
                            "name": f"{thread_data['first_name']} {thread_data['last_name']}"
                            if thread_data["first_name"] and thread_data["last_name"]
                            else None,
                        }
                    )
                formatted_bookings.append(formatted_booking)

        return BookingsResponse(data=formatted_bookings, total=total, page=page, total_pages=ceil(total / limit))


@router.get("/admin/statistics", response_class=JSONResponse)
async def get_booking_statistics(
    start: str = Query(None, description="Format: dd-mm-yyyy"),
    end: str = Query(None, description="Format: dd-mm-yyyy"),
    user: User = Depends(admin_manager),
):
    async with async_session() as session_flights, async_session() as session_hotels:
        query_flights = (
            select(
                func.count().label("total_count"),
                func.coalesce(
                    func.sum(func.coalesce(cast(Booking.content["outbound"]["price"]["amount"].astext, Float), 0.0)),
                    0.0,
                ).label("total_amount"),
            )
            .select_from(Booking)
            .outerjoin(ChatThread, ChatThread.id == Booking.thread_id)
            .where(Booking.content["status"].astext == "booked", Booking.type == "flight")
        )

        query_hotels = (
            select(
                func.count().label("total_count"),
                func.coalesce(
                    func.sum(func.coalesce(cast(Booking.content["room"]["price"].astext, Float), 0.0)), 0.0
                ).label("total_amount"),
            )
            .select_from(Booking)
            .join(ChatThread, ChatThread.id == Booking.thread_id)
            .where(Booking.content["status"].astext == "booked", Booking.type == "accommodations")
        )

        if start:
            try:
                start_date = datetime.strptime(start, "%d-%m-%Y")
                # Set time to beginning of day (00:00:00)
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
                query_flights = query_flights.where(ChatThread.created_date >= start_date)
                query_hotels = query_hotels.where(ChatThread.created_date >= start_date)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid start date format. Use dd-mm-yyyy")

        if end:
            try:
                end_date = datetime.strptime(end, "%d-%m-%Y")
                # Set time to end of day (23:59:59)
                end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
                query_flights = query_flights.where(ChatThread.created_date <= end_date)
                query_hotels = query_hotels.where(ChatThread.created_date <= end_date)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid end date format. Use dd-mm-yyyy")

        result_flights, result_hotels = await asyncio.gather(
            session_flights.execute(query_flights), session_hotels.execute(query_hotels)
        )

    flight_stats = result_flights.fetchone()
    hotel_stats = result_hotels.fetchone()

    return {
        "total_flights_booked": getattr(flight_stats, "total_count") if flight_stats is not None else 0,
        "total_hotels_booked": getattr(hotel_stats, "total_count") if hotel_stats is not None else 0,
        "total_flights_amount": round(getattr(flight_stats, "total_amount") if flight_stats is not None else 0, 2),
        "total_hotels_amount": round(getattr(hotel_stats, "total_amount") if hotel_stats is not None else 0, 2),
        "date_range": {"start": start, "end": end},
        "currency": "USD",
    }


async def format_booking_response(booking: Booking):
    """Format a single booking for API response"""

    booking_type = booking.type
    thread_id = booking.thread_id

    content = booking.content
    if content is None:
        content = {}

    formatted_booking = {
        "booking_type": booking_type,
        "booking_link": content.get("manageBookingURL", None),
        "status": content.get("status", None),
        "thread_id": thread_id,
        "confirmation_number": None,
        "start_date": None,
        "end_date": None,
        "title": None,
    }

    if booking_type == "accommodations":
        hotel = content.get("hotel", None)
        room = content.get("room", {}).get("option_title", None)
        no_nights = content.get("room", {}).get("no_nights", None)
        title = f"{hotel} - {room} ({no_nights} nights)" if hotel and room and no_nights else None
        formatted_booking.update(
            {
                "confirmation_number": content.get("reservation_number", None),
                "start_date": content.get("check_in_date", None),
                "end_date": content.get("check_out_date", None),
                "title": title,
            }
        )

    elif booking_type == "flight":
        if "flight_segments" in content:
            # legacy format - should be removed soon
            flight_segments = content.get("flight_segments", [])
            formatted_booking.update(
                {
                    "confirmation_number": content.get("airline_confirmation_number"),
                    "start_date": flight_segments[0].get("departure") if flight_segments else None,
                    "end_date": flight_segments[0].get("arrival") if flight_segments else None,
                    "title": f"{flight_segments[0].get('origin_code')} {'<->' if len(flight_segments) > 1 else '->'} {flight_segments[-1].get('destination_code')}",
                }
            )
        elif "outbound" in content:
            outbound_flight_segments = content.get("outbound").get("flight_segments") or []  # type: ignore
            outbound_flight_stops = (
                (outbound_flight_segments[0].get("flight_stops") or []) if outbound_flight_segments else []
            )
            start_date = outbound_flight_stops[0].get("departure") if outbound_flight_stops else None
            return_flight_segments = (
                (content.get("return") or {}).get("flight_segments", []) if "return" in content else []
            )
            return_flight_stops = (
                (return_flight_segments[0].get("flight_stops") or []) if return_flight_segments else []
            )
            end_date = (
                return_flight_stops[-1].get("arrival")
                if return_flight_stops
                else outbound_flight_stops[-1].get("arrival")
                if outbound_flight_stops
                else None
            )
            confirmation_id = content.get("confirmation_id")
            if settings.OTTO_ENV.upper() in ["DEV", "STG"]:
                spotnana_url = f"https://sboxmeta-app.partners.spotnana.com/trips/{confirmation_id}"
            else:
                spotnana_url = f"https://app.spotnana.com/trips/{confirmation_id}"
            formatted_booking.update(
                {
                    "confirmation_number": content.get("airline_confirmation_number", None),
                    "start_date": start_date,
                    "end_date": end_date,
                    "title": f"{outbound_flight_stops[0].get('origin_code')} {'<->' if return_flight_segments else '->'} {outbound_flight_stops[-1].get('destination_code')}",
                    "booking_link": spotnana_url if confirmation_id else None,
                }
            )

    return formatted_booking
