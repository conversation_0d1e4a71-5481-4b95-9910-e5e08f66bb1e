import httpx
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from starlette.responses import JSONResponse

from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.utils.settings import settings

router = APIRouter()

OPENAI_PROXY_URL = "https://api.openai.com"


@router.post("/v1/audio/transcriptions", response_class=JSONResponse)
async def deepgram_proxy(request: Request, user: User = Depends(manager)):
    try:
        form_data = await request.form()
        file = form_data["file"]

        headers = dict(request.headers)

        if headers.get("authorization") and headers.get("authorization") == "Token proxy":
            headers["Authorization"] = f"Bearer {settings.OPENAI_API_KEY}"

        async with httpx.AsyncClient() as client:
            response = await client.request(
                method=request.method,
                url=OPENAI_PROXY_URL + request.url.path,
                headers={"Authorization": f"Bearer {settings.OPENAI_API_KEY}"},
                params=dict(request.query_params),
                data={k: form_data[k] for k in form_data.keys() if k != "file"},
                files={"file": (file.filename, file.file, file.content_type)},  # type: ignore
                timeout=60,
            )

            content_type = response.headers.get("content-type", "")
            if "Content-Encoding" in response.headers:
                del response.headers["Content-Encoding"]

            return Response(response.content, response.status_code, response.headers, content_type)
    except httpx.RequestError:
        raise HTTPException(status_code=status.HTTP_502_BAD_GATEWAY, detail="Bad Gateway")
