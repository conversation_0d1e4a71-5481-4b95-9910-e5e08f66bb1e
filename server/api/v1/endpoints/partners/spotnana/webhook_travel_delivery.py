import asyncio
import json
from typing import Any

from fastapi import APIRouter, Depends, Response
from starlette.responses import JSONResponse

from server.schemas.partners.spotnana.travel_delivery import SpotnanaTravelDelivery
from server.services.partners.spotnana.webhook_event_handler import handle_spotnana_webhook_event
from server.utils.async_requests import make_post_request_with_any_data
from server.utils.logger import logger
from server.utils.mongo_connector import spotnana_webhook_events_collection
from server.utils.partners_login_manager import partner_spotnana_access_manager
from server.utils.settings import settings

router = APIRouter()


@router.post("/partners/spotnana/travel-delivery", response_class=JSONResponse)
async def partners_spotnana_auth(
    data: SpotnanaTravelDelivery,
    credentials: Any = Depends(partner_spotnana_access_manager),
):
    logger.info(f"[SPOTNANA WEBHOOK] {data.model_dump()}")
    await spotnana_webhook_events_collection.insert_one(data.model_dump())

    asyncio.create_task(
        handle_spotnana_webhook_event(
            data,
            route_to_staging=route_to_staging,
        )
    )

    response = Response(
        content=json.dumps({"success": True}),
        media_type="application/json",
    )

    return response


async def route_to_staging(data: SpotnanaTravelDelivery):
    url = f"{settings.STAGING_SPOTNANA_WEBHOOK_URL}/auth"

    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    auth_input = {
        "client_id": settings.STAGING_SPOTNANA_AUTH_CLIENT_ID,
        "client_secret": settings.STAGING_SPOTNANA_AUTH_CLIENT_SECRET,
        "grant_type": "client_credentials",
    }

    response = await make_post_request_with_any_data(url, data=auth_input, headers=headers)
    access_token = response.get("access_token")
    if access_token is not None:
        data_url = f"{settings.STAGING_SPOTNANA_WEBHOOK_URL}/travel-delivery"
        await make_post_request_with_any_data(
            data_url,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            },
            data=data.model_dump_json(),
        )
