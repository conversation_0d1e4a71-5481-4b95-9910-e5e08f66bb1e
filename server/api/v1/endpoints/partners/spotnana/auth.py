import json
from typing import Annotated

from fastapi import APIRouter, Form, HTTPException, Response
from starlette.responses import JSONResponse

from server.utils.partners_login_manager import create_custom_jwt
from server.utils.settings import settings

router = APIRouter()


@router.post("/partners/spotnana/auth", response_class=JSONResponse)
async def partners_spotnana_auth(
    client_id: Annotated[str, Form()],
    client_secret: Annotated[str, Form()],
    grant_type: Annotated[str, Form()],
):
    if client_id != settings.PARTNER_SPOTNANA_CLIENT_ID or client_secret != settings.PARTNER_SPOTNANA_CLIENT_SECRET:
        raise HTTPException(status_code=401, detail="Wrong credentials!")

    access_token = create_custom_jwt(client_id)

    response = Response(
        content=json.dumps(
            {
                "access_token": access_token,
                "expires_in": settings.PARTNER_SPOTNANA_TOKEN_LIFESPAN.total_seconds(),
                "token_type": "Bearer",
            }
        ),
        media_type="application/json",
    )

    return response
