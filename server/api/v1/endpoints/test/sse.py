from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse

from server.database.models.user import User
from server.dependencies import admin_manager
from server.services.connectivity.redis_message_broker import RedisMessageBroker


class TestPublishData(BaseModel):
    message: str


router = APIRouter()


@router.get("/test/sse")
async def test_sse(user: User = Depends(admin_manager)):
    broker_service = RedisMessageBroker(default_channel="otto-test")
    # TODO: it seems wrong. consider to revemove it
    return EventSourceResponse(broker_service.subscribe(), media_type="text/event_stream")  # type: ignore


@router.post("/test/publish")
async def test_publisher(data: TestPublishData, user: User = Depends(admin_manager)):
    broker_service = RedisMessageBroker(default_channel="otto-test")
    # TODO: it seems wrong. consider to revemove it
    await broker_service.publish({"user_id": user.id, "message": data.message})  # type: ignore

    return JSONResponse({"status": "OK"})
