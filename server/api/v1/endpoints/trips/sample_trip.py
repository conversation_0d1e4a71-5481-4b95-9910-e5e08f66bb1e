from fastapi import APIRouter, Depends
from pydantic import BaseModel

from server.dependencies import admin_manager
from server.schemas.authenticate.user import User
from server.schemas.sample_trip.sample_trip import GeoLocationDB, SampleTripDB
from server.utils.mongo_connector import sample_trips_collection

router = APIRouter()


class GeoLocation(BaseModel):
    longitude: float
    latitude: float


class SampleTrip(BaseModel):
    title: str
    description: str
    prompt: str
    location: GeoLocation


class SampleTripResponses(BaseModel):
    trips: list[SampleTrip]


def _convert_to_sample_trip(trip: dict) -> SampleTrip:
    return SampleTrip(
        title=trip["title"],
        description=trip["description"],
        prompt=trip["prompt"],
        location=GeoLocation(longitude=trip["location"]["coordinates"][0], latitude=trip["location"]["coordinates"][1]),
    )


@router.get("/admin/sample_trips")
async def get_sample_trips(user: User = Depends(admin_manager)):
    all_sample_trips = await sample_trips_collection.find().to_list(length=None)
    return [_convert_to_sample_trip(trip) for trip in all_sample_trips]


@router.post("/admin/sample_trips")
async def upsert_sample_trips(request: list[SampleTrip], user: User = Depends(admin_manager)):
    for trip in request:
        trip_db = SampleTripDB(
            title=trip.title,
            description=trip.description,
            prompt=trip.prompt,
            location=GeoLocationDB(type="Point", coordinates=[trip.location.longitude, trip.location.latitude]),
        )

        # Upsert to MongoDB using title as the unique key
        await sample_trips_collection.update_one(
            {"title": trip.title},  # filter by title as unique key
            {"$set": trip_db.model_dump()},
            upsert=True,
        )

    result = []
    all_sample_trips = await sample_trips_collection.find().to_list(length=None)
    for sample_trip in all_sample_trips:
        result.append(_convert_to_sample_trip(sample_trip))

    return SampleTripResponses(trips=result)
