from fastapi import APIRouter, Depends, HTTPException

from server.api.v1.endpoints.websocket_message import MessageType
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager

router = APIRouter()


@router.get("/trips/{trip_id}/messages")
async def get_messages(trip_id: int, user: User = Depends(manager)):
    thread = await ChatThread.from_id(trip_id)
    if thread is None or thread.users_id != user.id:
        raise HTTPException(status_code=404, detail="Trip not found")

    checkpoints = await Checkpoint.from_thread_id(trip_id)
    if not checkpoints:
        return {"messages": []}

    messages = []
    for checkpoint in checkpoints:
        data = checkpoint.Checkpoint.data
        message_type = data.get("additional_kwargs", {}).get("message_type", MessageType.PROMPT)
        message = {
            "id": checkpoint.Checkpoint.id,
            "created_date": checkpoint.Checkpoint.created_date.isoformat(),
            "content": data.get("content", ""),
            "type": data.get("type", ""),
            "message_type": message_type,
            "extra": data.get("additional_kwargs", {}).get("extra", {}),
        }
        messages.append(message)

    return {"messages": messages}
