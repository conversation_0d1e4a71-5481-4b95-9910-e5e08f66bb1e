from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from pydantic import BaseModel

from server.api.v1.endpoints.websocket import WSConnectionManager
from server.api.v1.endpoints.websocket_message import MessageType
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.utils.no_op_websocket import NoOpWebSocket

router = APIRouter()


class PostMessageRequest(BaseModel):
    text: str
    type: MessageType = MessageType.PROMPT
    extra: dict = {}


@router.post("/trips/{trip_id}/messages")
async def post_message(trip_id: int, request: PostMessageRequest, user: User = Depends(manager)):
    thread = await ChatThread.from_id(trip_id)
    if thread is None or thread.users_id != user.id:
        raise HTTPException(status_code=404, detail="Trip not found")

    checkpoints = await Checkpoint.from_thread_id(trip_id)
    is_empty = len(checkpoints) == 0 if checkpoints else True

    websocket = NoOpWebSocket()
    ws_manager = WSConnectionManager(user=user)

    if is_empty:
        await ws_manager.on_receive(
            websocket=websocket,
            message={
                "type": MessageType.TRIP_INIT,
                "tripId": trip_id,
            },
        )

    await ws_manager.on_receive(
        websocket=websocket,
        message={
            "type": request.type,
            "tripId": trip_id,
            "text": request.text,
            "extra": request.extra,
        },
    )

    checkpoints = await Checkpoint.from_thread_id(trip_id)
    latest_checkpoint = checkpoints[-1].Checkpoint if checkpoints else None

    return {"status": "success", "message_id": latest_checkpoint.id if latest_checkpoint else None}
