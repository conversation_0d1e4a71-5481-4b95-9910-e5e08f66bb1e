import json
from datetime import timezone

from fastapi import APIRouter, Depends, HTTPException, Response, status
from starlette.responses import JSONResponse

from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.trips.bookings import add_booking_action_strings, get_bookings
from server.services.trips.hotel_itinerary import get_hotel_itinerary
from server.services.trips.spotnana_itinerary import get_spotnana_itinerary
from server.utils.settings import settings

router = APIRouter()


@router.get("/trips/{trip_id}", response_class=JSONResponse)
async def get_trip(trip_id: int, user: User = Depends(manager)):
    chat_thread = await ChatThread.from_id(trip_id)
    if chat_thread is None or chat_thread.users_id != user.id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Trip id does not exist!")

    checkpoints = await Checkpoint.from_thread_id(trip_id) or []
    last_timestamp: str | None = None
    if len(checkpoints) > 0:
        last_timestamp = checkpoints[-1].Checkpoint.created_date.replace(tzinfo=timezone.utc).isoformat()

    bookings = await get_bookings(chat_thread)

    if "flight" in bookings.keys():
        old_outbound = None
        old_return = None
        old_legs = []
        flight_status = bookings["flight"].get("status")
        receipts = bookings["flight"].get("receipts")
        if len(bookings["flight"].get("flight_exchanges", [])) > 0:
            old_outbound = bookings["flight"].get("flight_exchanges")[0].get("outbound")
            old_return = bookings["flight"].get("flight_exchanges")[0].get("return")
            old_legs = bookings["flight"].get("flight_exchanges")[0].get("legs", [])

        bookings["flight"] = await get_spotnana_itinerary(
            bookings["flight"].get("trip_id"),
            bookings["flight"].get("confirmation_id"),
        )
        bookings["flight"]["oldOutbound"] = old_outbound  # TODO: remove this
        bookings["flight"]["oldReturn"] = old_return  # TODO: remove this
        bookings["flight"]["old_legs"] = old_legs
        bookings["flight"]["status"] = flight_status
        bookings["flight"]["receipts"] = receipts

    if "accommodations" in bookings:
        for i, accommodation in enumerate(bookings["accommodations"]):
            if "website" in accommodation and user.role != "admin":
                del bookings["accommodations"][i]["website"]

            if accommodation.get("status", "") != "cancelled":
                bookings["accommodations"][i] = await get_hotel_itinerary(accommodation)

    response_dict = {
        "lastTimestamp": last_timestamp,
        "minMessageTimestampSeconds": settings.MIN_MESSAGE_TIMESTAMP_DELTA.total_seconds(),
        "itinerary": bookings,
    }

    add_booking_action_strings(response_dict)

    response = Response(
        content=json.dumps(response_dict),
        media_type="application/json",
    )

    return response
