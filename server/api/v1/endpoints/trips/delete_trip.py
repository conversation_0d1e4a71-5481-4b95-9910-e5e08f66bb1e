from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from starlette.responses import JSONResponse
from starlette.status import HTTP_403_FORBIDDEN

from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.trips.bookings import get_bookings

router = APIRouter()


@router.delete("/trips/{thread_id}")
async def delete_trip(thread_id: str, user: User = Depends(manager)) -> JSONResponse:
    chat_thread = await ChatThread.from_id(int(thread_id))
    if chat_thread is None or chat_thread.users_id != user.id:
        raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="The trip id does not exist!")

    bookings = await get_bookings(chat_thread)
    if len(bookings) > 0:
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN,
            detail="The trip has booked hotel or flight!",
        )

    # await Checkpoint.delete_threads([int(thread_id)])

    # Soft delete threads
    await chat_thread.update_fields({"is_deleted": True})
    return JSONResponse({"success": "deleted"})
