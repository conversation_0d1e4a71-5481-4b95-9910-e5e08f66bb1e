from datetime import timezone
from typing import Optional, Sequence, <PERSON><PERSON>

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy import Row

from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.schemas.authenticate.user import User
from server.schemas.trip import Trip
from server.services.authenticate.authenticate import manager
from server.utils.settings import settings

router = APIRouter()


class CreateTripRequest(BaseModel):
    tryingModeEnabled: Optional[bool] = False


@router.post("/trips/new")
async def create_trip(request: CreateTripRequest | None = None, user: User = Depends(manager)) -> Trip:
    last_trip: ChatThread | None = await ChatThread.from_user_id_last_entry(user.id)

    titles = [settings.NEW_TRIP_TITLE]
    isTryingModeEnabled = (request and request.tryingModeEnabled) or False
    if isTryingModeEnabled:
        titles.append(settings.GETTING_STARTED_TITLE)

    if last_trip is not None and last_trip.title in titles:
        last_trip_checkpoints: Sequence[Row[Tuple[Checkpoint]]] | None = await Checkpoint.from_thread_id(last_trip.id)

        if last_trip_checkpoints is not None and len(last_trip_checkpoints) <= 2:
            last_trip.created_date = last_trip.created_date.replace(tzinfo=timezone.utc)
            return Trip(
                id=last_trip.id,
                users_id=last_trip.users_id,
                title=last_trip.title,
                date_start=last_trip.date_start.isoformat() if last_trip.date_start else None,
                date_end=last_trip.date_end.isoformat() if last_trip.date_end else None,
                created_date=last_trip.created_date.isoformat(),
            )

    title = settings.NEW_TRIP_TITLE
    if isTryingModeEnabled and (
        last_trip is None
        or last_trip.title
        in [settings.ONBOARDING_PAGE_TITLE, settings.TRAVEL_POLICY_THREAD_TITLE, settings.PREFERENCES_THREAD_TITLE]
    ):
        title = settings.GETTING_STARTED_TITLE

    new_trip: ChatThread = ChatThread(user.id, title)
    await ChatThread.new_chat_thread(new_trip)

    new_trip.created_date = new_trip.created_date.replace(tzinfo=timezone.utc)

    return Trip(
        id=new_trip.id,
        users_id=new_trip.users_id,
        title=new_trip.title,
        date_start=new_trip.date_start.isoformat() if new_trip.date_start else None,
        date_end=new_trip.date_end.isoformat() if new_trip.date_end else None,
        created_date=new_trip.created_date.isoformat(),
    )
