import json
from datetime import timezone

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Response, status
from starlette.responses import JSONResponse

from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.calendar_api.calendar_events_background_task import (
    background_get_google_calendar_events,
)
from server.utils.settings import settings

router = APIRouter()


@router.get("/trips/onboarding", response_class=JSONResponse)
async def get_trip(background_tasks: BackgroundTasks, user: User = Depends(manager)):
    last_timestamp: str | None = None

    chat_threads = await ChatThread.from_user_id_and_title(user.id, settings.ONBOARDING_THREAD_TITLE)
    if len(chat_threads) == 0:
        background_tasks.add_task(background_get_google_calendar_events, user)
    else:
        chat_thread: ChatThread = chat_threads[0].ChatThread

        if chat_thread is None or chat_thread.users_id != user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Trip id does not exist!",
            )

        checkpoints = await Checkpoint.from_thread_id(chat_thread.id) or []
        if len(checkpoints) > 0:
            last_timestamp = checkpoints[-1].Checkpoint.created_date.replace(tzinfo=timezone.utc).isoformat()

    response = Response(
        content=json.dumps(
            {
                "lastTimestamp": last_timestamp,
                "minMessageTimestampSeconds": settings.MIN_MESSAGE_TIMESTAMP_DELTA.total_seconds(),
            }
        ),
        media_type="application/json",
    )

    return response
