import io
import zipfile

from botocore.exceptions import Client<PERSON>rror
from botocore.response import Streaming<PERSON>ody
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse

from server.database.models.bookings import Booking
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.utils.s3_utils import s3_utils
from server.utils.settings import settings

router = APIRouter()


@router.get("/documents/receipt")
async def stream_document_receipt(filename: str, user: User = Depends(manager)):
    try:
        booking: Booking | None = await Booking.from_query({"content.receipts__contains": filename})

        if booking is None:
            raise HTTPException(status_code=404, detail="File not found")
        elif booking.user_id != user.id:
            raise HTTPException(status_code=403, detail="Forbidden")

        stream: StreamingBody = await s3_utils.stream_single_file(settings.INVOICE_S3_BUCKET, filename)

        headers = {
            "Content-Disposition": "inline"  # open in browser
        }

        return StreamingResponse(
            stream,
            media_type="application/pdf",
            headers=headers,
        )

    except ClientError as e:
        if e.response["Error"]["Code"] == "NoSuchKey":
            raise HTTPException(status_code=404, detail="File not found")
        else:
            raise HTTPException(status_code=500, detail="S3 error")


@router.get("/documents/trip-receipts")
async def download_trip_receipts(trip_id: int, user: User = Depends(manager)):
    bookings: list[Booking] = await Booking.from_query_batch({"thread_id": trip_id, "user_id": user.id})

    if len(bookings) == 0:
        raise HTTPException(status_code=404, detail="No bookings found for this trip")

    receipts: list[str] = []
    for booking in bookings:
        if len(booking.content.get("receipts", [])) > 0:
            receipts.append(booking.content.get("receipts", [])[0])

    if len(receipts) == 0:
        raise HTTPException(status_code=404, detail="No receipts found for this trip")

    if len(receipts) == 1:
        stream: StreamingBody = await s3_utils.stream_single_file(settings.INVOICE_S3_BUCKET, receipts[0])

        headers = {
            "Content-Disposition": "inline"  # open in browser
        }

        return StreamingResponse(
            stream,
            media_type="application/pdf",
            headers=headers,
        )

    zip_buffer: io.BytesIO = io.BytesIO()
    folder_name: str = f"{user.last_name}_{trip_id}_receipts"
    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        for receipt in receipts:
            # Download PDF file from S3
            pdf_data: bytes | None = await s3_utils.download_single_file(settings.INVOICE_S3_BUCKET, receipt)
            if pdf_data is None:
                raise HTTPException(status_code=500, detail="Could not download receipt from S3")

            zip_file.writestr(f"{folder_name}/{receipt.split('/')[-1]}", pdf_data)

    # Move buffer pointer to start
    zip_buffer.seek(0)

    headers = {
        "Content-Disposition": f"attachment; filename={folder_name}.zip"  # download in browser
    }

    return StreamingResponse(
        zip_buffer,
        media_type="application/zip",
        headers=headers,
    )
