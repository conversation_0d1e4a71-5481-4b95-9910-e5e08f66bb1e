import markdown2
from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

from baml_client import b
from server.utils.settings import settings
from workflows.email_proces_workflow import style_html_table

router = APIRouter()


@router.get("/preview-email", response_class=HTMLResponse)
async def preview_email(request: Request):
    summary = """I provided a list of top business-friendly hotels in downtown San Diego's Gaslamp Quarter that comply with company policy and offer free breakfast. The options include:

1. **Granger Hotel Gaslamp Quarter, a Member of Design Hotels**
   - Rating: 9.0 (Excellent)
   - Amenities: Free Wifi, Concierge, Valet parking, Restaurant, etc.
   - Room options include Broadway King, Gaslamp Corner King, and Gaslamp Double, with prices ranging from $1364 to $2022 for a 6-night stay.

2. **Courtyard by Marriott San Diego Downtown**
   - Rating: 8.3 (Very Good)
   - Amenities: 24-hour fitness center, Business center, Free Wifi, etc.
   - Room options include King Room and Queen Room with City View, with prices from $1398 to $1688 for a 6-night stay.

Please let me know if you have a preferred brand or neighborhood for a more tailored shortlist.
"""
    agent_response = await b.SummaryResponseForEmail(
        original_input=summary,
        table_format=False,
        other_options="",
        self_intro=settings.OTTO_SELF_INTRO,
    )
    agent_response_html = markdown2.markdown(agent_response.summary, extras=["task_list", "tables"])

    context = {
        "request": request,  # required by Jinja2Templates
        "trip_title": "Trip to Localhost v8, June 14-16 2025",
        "trip_id": "12345",
        "base_url": "https://app.ottotheagent.com/",
        "agent_response": style_html_table(agent_response_html),
    }
    templates = Jinja2Templates(directory="email_templates")
    return templates.TemplateResponse("response_base.html", context)
