from server.utils.custom_login_manager import (
    AdminLoginManager,
    CustomLoginManagerWS,
    CustomLoginManagerWSWithHeaderTokens,
    OrganizationLoginManager,
)
from server.utils.remote_cache import RemoteCache
from server.utils.settings import settings

assert settings.OTTO_APP_SECRET is not None, "OTTO_APP_SECRET is not set"

manager = CustomLoginManagerWS(
    settings.OTTO_APP_SECRET,
    "api/google/login",
    cookie_name="access_token",
    refresh_cookie_name="refresh_token",
)
admin_manager = AdminLoginManager(
    settings.OTTO_APP_SECRET,
    "api/google/login",
    cookie_name="access_token",
    refresh_cookie_name="refresh_token",
)

organization_admin_manager = OrganizationLoginManager(
    settings.OTTO_APP_SECRET,
    "api/google/login",
    cookie_name="access_token",
    refresh_cookie_name="refresh_token",
)

manager_request_with_header_tokens = CustomLoginManagerWSWithHeaderTokens(
    settings.OTTO_APP_SECRET,
    "api/google/login",
    cookie_name="access_token",
    refresh_cookie_name="refresh_token",
)


def get_remote_cache():
    return RemoteCache()
