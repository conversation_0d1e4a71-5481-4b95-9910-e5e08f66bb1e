# don't move this line as BAML need to import settings before importing anything else
from server.utils.settings import settings  # noqa: I001

import asyncio
import signal
import traceback
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timezone

import sentry_sdk
from fastapi import Depends, FastAPI, Request, Response, status
from fastapi.exceptions import HTTPException, RequestValidationError
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from prometheus_client import CONTENT_TYPE_LATEST, generate_latest
from prometheus_fastapi_instrumentator import Instrumentator, metrics
from starlette.middleware.cors import CORSMiddleware

from server.api.v1.api import api_router
from server.api.v1.endpoints.websocket import GlobalWSManager
from server.middleware.latency_middleware import LatencyMiddleware
from server.schemas.errors.errors import ErrorDetail, StandardErrorResponse
from server.services.authenticate.authenticate import admin_manager
from server.services.task_manager.task_manager import GlobalTaskManager
from server.utils.cloudwatch_metrics import load_all_class_functions_to_metrics
from server.utils.logger import logger
from server.utils.redis_connector import RedisConnector
from workflows.workflow import create_temporal_worker


def custom_metrics_configuration(app: FastAPI) -> dict[str, str]:
    """Add custom labels to all metrics."""
    return {"app_name": "otto_server", "environment": settings.OTTO_ENV.upper()}


shutdown_started = False


@asynccontextmanager
async def lifespan(app: FastAPI):
    asyncio.create_task(create_temporal_worker())

    # Custom shutdown handler
    async def on_shutdown(signum: signal.Signals):
        global shutdown_started
        logger.info(f"Received signal {signum}")
        if shutdown_started:
            logger.info("Shutdown already started, skipping")
            return
        shutdown_started = True
        try:
            # Close all WebSocket connections and stop accepting new ones
            await GlobalWSManager.close_all_connections()

            # Close all tasks
            await GlobalTaskManager.shutdown()

            # Close Redis connection pool
            await RedisConnector.close()

            logger.info("Cleanup completed")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        finally:
            import os

            logger.info("Server shutdown")
            os._exit(0)

    # Register signal handlers
    loop = asyncio.get_event_loop()
    for sig in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(on_shutdown(s)))

    yield


if settings.SENTRY_DSN:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        environment=settings.OTTO_ENV,
        traces_sample_rate=0.0 if settings.is_dev_env else 1.0,
        sample_rate=0.0 if settings.is_dev_env else 1.0,
        attach_stacktrace=True,
    )

app = FastAPI(title="OTTO API", version="1.0.0", docs_url=None, redoc_url=None, openapi_url=None, lifespan=lifespan)

assert settings.ALLOWED_DOMAINS is not None, "ALLOWED_DOMAINS environment variable is not set"
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_DOMAINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(LatencyMiddleware)

app.mount("/static", StaticFiles(directory="server/static/images"), name="static")

load_all_class_functions_to_metrics()


@app.exception_handler(Exception)
async def generic_exception_handler(request, exc):
    logger.error(traceback.format_exc())
    # Try to get errorDetail attribute first, then fall back to string representation
    error_message = getattr(exc, "errorDetail", None) or str(exc) if str(exc) else exc.__class__.__name__

    # Get status code from exception if it has one, otherwise default to 500
    status_code = getattr(exc, "status_code", 500)

    error_response = StandardErrorResponse(detail=[ErrorDetail(error_detail=error_message)])
    return JSONResponse(status_code=status_code, content=error_response.model_dump())


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    error_details = [ErrorDetail(error_detail=str(error)) for error in exc.errors()]
    error_response = StandardErrorResponse(detail=error_details)
    return JSONResponse(status_code=422, content=error_response.model_dump())


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    # Handle detail as either string, dict, or list
    if isinstance(exc.detail, str):
        error_details = [ErrorDetail(error_detail=exc.detail)]
    elif isinstance(exc.detail, dict):
        # Extract errorDetail from single dict
        if "errorDetail" in exc.detail:
            error_details = [ErrorDetail(error_detail=exc.detail["errorDetail"])]
        else:
            error_details = [ErrorDetail(error_detail=str(exc.detail))]
    elif isinstance(exc.detail, list):
        error_details = []
        for detail in exc.detail:
            if isinstance(detail, dict) and "errorDetail" in detail:
                # Extract errorDetail from dict
                error_details.append(ErrorDetail(error_detail=detail["errorDetail"]))
            else:
                # Fall back to string representation
                error_details.append(ErrorDetail(error_detail=str(detail)))
    else:
        error_details = [ErrorDetail(error_detail=str(exc.detail))]

    error_response = StandardErrorResponse(detail=error_details)
    response = JSONResponse(status_code=exc.status_code, content=error_response.model_dump(), headers=exc.headers)

    if response.status_code == status.HTTP_401_UNAUTHORIZED:
        response.delete_cookie(
            "access_token",
            secure=(settings.COOKIES_DOMAIN != "localhost"),
            domain=settings.COOKIES_DOMAIN,
        )
        response.delete_cookie(
            "refresh_token",
            secure=(settings.COOKIES_DOMAIN != "localhost"),
            domain=settings.COOKIES_DOMAIN,
        )
    return response


@app.middleware("http")
async def set_auth_cookies_on_token_refresh(request: Request, call_next):
    response: Response = await call_next(request)

    if "access_token" in request.state._state and "refresh_token" in request.state._state:
        response.set_cookie(
            key="access_token",
            value=request.state.access_token,
            samesite="none",
            secure=True,
            httponly=False,
            domain=settings.COOKIES_DOMAIN,
            expires=datetime.now(tz=timezone.utc) + settings.ACCESS_TOKEN_LIFESPAN,
        )
        response.set_cookie(
            key="refresh_token",
            value=request.state.refresh_token,
            samesite="none",
            secure=True,
            httponly=True,
            domain=settings.COOKIES_DOMAIN,
            expires=datetime.now(tz=timezone.utc) + settings.REFRESH_TOKEN_LIFESPAN,
        )

    return response


@app.middleware("http")
async def logging_middleware(request: Request, call_next) -> Response:
    trace_id = uuid.uuid4().hex[:16].upper()

    logger.bind_log_context(trace_id=trace_id)

    response = await call_next(request)

    response.headers["X-Trace-ID"] = trace_id

    return response


@app.get("/health")
def health():
    return JSONResponse({"status": "OK"})


app.include_router(api_router)


@app.get("/docs", include_in_schema=False)
async def get_swagger_documentation(username: str = Depends(admin_manager)):
    return get_swagger_ui_html(openapi_url="/openapi.json", title="docs")


@app.get("/redoc", include_in_schema=False)
async def get_redoc_documentation(username: str = Depends(admin_manager)):
    return get_redoc_html(openapi_url="/openapi.json", title="docs")


@app.get("/openapi.json", include_in_schema=False)
async def openapi(username: str = Depends(admin_manager)):
    return get_openapi(title=app.title, version=app.version, routes=app.routes)


instrumentator = (
    Instrumentator(excluded_handlers=[".*admin.*", "/metrics"])
    .add(metrics.default(custom_labels=custom_metrics_configuration(app)))
    .instrument(app)
)


@app.get("/metrics")
def metrics_handler():
    # def metrics(credentials: Any = Depends(partner_prometheus_access_manager)):
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)
