from pydantic import BaseModel


class HotelDetails(BaseModel):
    name: str
    address: str
    check_in: str
    check_out: str
    room_type: str


class HotelTraveler(BaseModel):
    name: str


class HotelPayment(BaseModel):
    method: str
    nights: int
    per_night: str
    subtotal: str
    taxes_fees: str
    total: str
    transaction_date: str
    status: str


class ReceiptHotelData(BaseModel):
    title: str
    confirmation_number: str
    hotel: HotelDetails
    traveler: HotelTraveler
    payment: HotelPayment
