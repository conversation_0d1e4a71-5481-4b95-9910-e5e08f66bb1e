from typing import List, Literal

from pydantic import BaseModel


class FlightLeg(BaseModel):
    airline_name: str
    arrival_time: str
    cabin: str
    confirmation_number: str
    departure_time: str
    destination: str
    flight_number: str
    origin: str


class FlightTraveler(BaseModel):
    name: str
    ticket_number: str


class FlightPayment(BaseModel):
    base_fare: str
    method: str
    taxes_fees: str
    total: str
    transaction_date: str


class ReceiptFlightData(BaseModel):
    title: str
    pnr: str
    flight_type: Literal["One Way Flight", "Round Trip Flight"]
    legs: List[FlightLeg]
    traveler: FlightTraveler
    payment: FlightPayment
