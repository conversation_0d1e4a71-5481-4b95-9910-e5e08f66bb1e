from enum import Enum, IntEnum
from typing import Any, Optional

from pydantic import BaseModel


class CompanyEnum(IntEnum):
    NONE = 0
    VISA = 1
    MASTERCARD = 2
    AMEX = 3
    DISCOVER = 4
    AIR_TRAVEL_UATP = 5
    CARTE_BLANCHE = 6
    DINERS_CLUB = 7
    JCB = 8
    EURO_CARD = 9
    ACCESS_CARD = 10
    BREX = 11
    UNION_PAY = 12
    UNRECOGNIZED = 13

    @classmethod
    def get(cls, key):
        return cls.__members__.get(key, cls.UNRECOGNIZED)


class TypeEnum(int, Enum):
    UNKNOWN = 0
    CREDIT = 1
    DEBIT = 2
    UNRECOGNIZED = 3


class ExpiryInt(BaseModel):
    year: int
    month: int


class ExpiryStr(BaseModel):
    year: str
    month: str


class CardExpiry(BaseModel):
    nonTokenized: Optional[ExpiryInt] = None
    tokenized: Optional[ExpiryStr] = None


class Address(BaseModel):
    regionCode: str
    postalCode: str
    administrativeArea: str
    locality: str
    sublocality: str = ""
    addressLines: list[str]
    description: str


class Card(BaseModel):
    company: CompanyEnum
    type: TypeEnum
    address: Address
    label: str
    name: str
    number: str
    # expiry: Optional[CardExpiry] = None
    expiryMonth: Optional[str] = None
    expiryYear: Optional[str] = None
    cvv: str


class ApplicableToEnum(int, Enum):
    UNKNOWN_APPLICABLE_TO = 0
    AIR = 1
    HOTEL = 2
    RAIL = 3
    CAR = 4
    SERVICE_FEE = 5
    UNRECOGNIZED = 6


class CreditCard(BaseModel):
    card: Card
    userOrgId: Any
    applicableTo: list[ApplicableToEnum]
