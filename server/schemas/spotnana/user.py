from typing import Literal, Optional

from pydantic import BaseModel


class Dob(BaseModel):
    iso8601: str


class Name(BaseModel):
    family1: str
    family2: Optional[str] = None
    given: str
    middle: Optional[str] = None


class PhoneNumber(BaseModel):
    countryCode: int
    countryCodeSource: Literal[
        "UNSPECIFIED",
        "FROM_NUMBER_WITH_PLUS_SIGN",
        "FROM_NUMBER_WITH_IDD",
        "FROM_NUMBER_WITHOUT_PLUS_SIGN",
        "FROM_DEFAULT_COUNTRY",
    ]
    isoCountryCode: str
    rawInput: str
    type: Literal["UNKNOWN_TYPE", "MOBI<PERSON>", "LANDLINE"]
    extension: str = ""
    italianLeadingZero: bool = False
    nationalNumber: int = 0
    numberOfLeadingZeros: int = 0


class KTNDict(BaseModel):
    number: str
    issueCountry: str


class KTNBase(BaseModel):
    ktn: KTNDict


class RedressDict(BaseModel):
    number: str
    issueCountry: str


class RedressBase(BaseModel):
    redress: RedressDict


class PersonalInfo(BaseModel):
    dob: Optional[Dob] = None
    name: Name
    gender: Optional[Literal["MALE", "FEMALE", "UNSPECIFIED", "UNDISCLOSED"]] = None
    email: str
    title: Optional[
        Literal[
            "MR",
            "MS",
            "MRS",
            "MX",
            "MASTER",
            "MISS",
            "DR",
            "PROFESSOR",
            "CAPTAIN",
            "REVEREND",
            "HONOURABLE",
            "SIR",
            "LADY",
            "AMBASSADOR",
            "LORD",
            "BRIGADIER",
            "SENATOR",
            "DAME",
            "JUSTICE",
            "UK",
            "TITLE_UNKNOWN",
        ]
    ] = None
    travelerName: Name
    phoneNumbers: list[PhoneNumber]
    identityDocs: Optional[list[KTNBase | RedressBase]] = None


class EntityRef(BaseModel):
    id: str


class BusinessInfo(BaseModel):
    email: str
    legalEntityRef: EntityRef
    organizationRef: EntityRef


class MembershipInfo(BaseModel):
    appliedTo: list[str]
    id: str
    issuedBy: str
    type: Literal["UNKNOWN_TYPE", "AIR", "HOTEL", "CAR", "RAIL"]


class MembershipInfos(BaseModel):
    membershipInfos: list[MembershipInfo]


class CreateSpotnanaUser(BaseModel):
    personalInfo: PersonalInfo
    persona: Literal["UNKNOWN_PERSONA", "EMPLOYEE", "GUEST", "PERSONAL", "RELATIVE", "ADHOC"]
    businessInfo: BusinessInfo
    membershipInfo: MembershipInfos


class SpotnanaUser(BaseModel):
    id: str
    personalInfo: PersonalInfo
    persona: Literal["UNKNOWN_PERSONA", "EMPLOYEE", "GUEST", "PERSONAL", "RELATIVE", "ADHOC"]
    businessInfo: BusinessInfo
    isActive: bool
    tier: Literal["BASIC", "SEAT1A"]
