from typing import Literal, Optional

from pydantic import BaseModel


class FrequentFlyerNumber(BaseModel):
    IATACode: str
    number: Optional[str] = None


class CreateSpotnanaProfile(BaseModel):
    title: Optional[
        Literal[
            "TITLE_UNKNOWN",
            "MR",
            "<PERSON>",
            "MRS",
            "MX",
            "<PERSON><PERSON><PERSON>",
            "MI<PERSON>",
            "DR",
            "PROFESSOR",
            "CA<PERSON><PERSON><PERSON>",
            "RE<PERSON><PERSON><PERSON>",
            "HONO<PERSON><PERSON><PERSON>",
            "SIR",
            "LADY",
            "AMBASSADOR",
            "LORD",
            "BRIGADIER",
            "SENATOR",
            "DAME",
            "JUSTICE",
            "UK",
        ]
    ] = None
    first_name: str
    last_name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    dob: Optional[str] = None
    gender: Optional[Literal["MALE", "FEMALE", "UNSPECIFIED", "UNDISCLOSED"]] = None
    mileage_plan_number: Optional[str] = None
    marriott_bonvoy_account_number: Optional[str] = None
    traveler_number: Optional[str] = None
    redress_number: Optional[str] = None
    cardholder_name: Optional[str] = None
    card_number: Optional[str] = None
    card_type: Optional[str] = ""
    exp_date: Optional[str] = None
    card_cvc: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    isCVCEdited: Optional[bool] = None
    isCardNumberEdited: Optional[bool] = None
    frequentFlyerNumbers: Optional[list[FrequentFlyerNumber]] = None
    citizenship: Optional[list[str]] = None


class UpdateFrequentFlyerNumber(BaseModel):
    frequentFlyerNumbers: Optional[list[FrequentFlyerNumber]] = []
