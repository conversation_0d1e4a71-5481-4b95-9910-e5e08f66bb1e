from typing import Literal, Optional

from pydantic import BaseModel


class PersonalInformationBaseRequest(BaseModel):
    title: Literal[
        "TITLE_UNKNOWN",
        "MR",
        "MS",
        "MRS",
        "MX",
        "MASTER",
        "MISS",
        "DR",
        "PROFESSOR",
        "<PERSON>P<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "SIR",
        "LADY",
        "AMBA<PERSON>AD<PERSON>",
        "<PERSON>ORD",
        "BR<PERSON><PERSON><PERSON><PERSON>",
        "SENATO<PERSON>",
        "DAM<PERSON>",
        "JUSTICE",
        "UK",
    ]
    first_name: str
    last_name: str
    phone: Optional[str] = None


class PersonalInformationExtendedRequest(PersonalInformationBaseRequest):
    dob: str
    gender: Literal["MALE", "FEMAL<PERSON>", "UN<PERSON>ECIFIED", "UNDISCLOSED"]
    traveler_number: Optional[str] = None
    redress_number: Optional[str] = None
    citizenship: Optional[list[str]] = None
