from typing import Optional

from pydantic import BaseModel


class GoogleAuthenticateModel(BaseModel):
    code: str | None = None
    token_id: str | None = None
    refresh_token: str | None = None
    redirect_uri: str = "postmessage"


class GoogleAuthenticateMobileModel(BaseModel):
    code: str | None = None
    refreshToken: str | None = None


class CalendarConnectRequest(BaseModel):
    enable: bool


# Response model
class CalendarConnectResponse(BaseModel):
    enabled: bool
    calendarAuthUrl: Optional[str] = None
