from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr


class OrganizationDomainCreate(BaseModel):
    domain: str
    is_validated: bool = True


class OrganizationCreate(BaseModel):
    name: str
    domain: str
    auto_approve_domain_users: bool = True
    require_otp_login: bool = True
    preferred_airlines: Optional[List[str]] = None
    preferred_hotels: Optional[List[str]] = None
    travel_policy: Optional[Dict[str, Any]] = None
    billing_contact_email: Optional[EmailStr] = None
    additional_domains: Optional[List[OrganizationDomainCreate]] = None
    admin_email: str


class OrganizationUpdate(BaseModel):
    name: Optional[str] = None
    status: Optional[str] = None
    image: Optional[str] = None
    auto_approve_domain_users: Optional[bool] = None
    require_otp_login: Optional[bool] = None
    preferred_airlines: Optional[List[str]] = None
    preferred_hotels: Optional[List[str]] = None
    travel_policy: Optional[Dict[str, Any]] = None
    billing_contact_email: Optional[EmailStr] = None


class OrganizationResponse(BaseModel):
    id: int
    name: str
    domain: str
    image: Optional[str] = ""
    auto_approve_domain_users: bool
    require_otp_login: bool
    preferred_airlines: Optional[List[str]]
    preferred_hotels: Optional[List[str]]
    travel_policy: Optional[Dict[str, Any]]
    billing_contact_email: Optional[str]
    created_date: datetime
    updated_date: datetime
    user_count: int
    validated_domains: List[str]

    class Config:
        from_attributes = True


class OrganizationListResponse(BaseModel):
    id: int
    name: str
    domain: str
    user_count: int
    created_date: datetime

    class Config:
        from_attributes = True
