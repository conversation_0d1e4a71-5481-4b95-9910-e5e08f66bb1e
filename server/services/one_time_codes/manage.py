import secrets
import string
from datetime import datetime, timedelta, timezone
from typing import <PERSON><PERSON><PERSON>tring

from fastapi import Response
from sqlalchemy import func

from server.database.models.otc_beta_codes import OTCBetaCode
from server.database.models.otc_group import OTCGroup
from server.utils.logger import logger
from server.utils.mongo_connector import users_whitelist_collection
from server.utils.settings import settings


def generate_code(charset: LiteralString):
    return "".join(secrets.choice(charset) for _ in range(settings.ONE_TIME_CODE_LENGTH))


async def generate_otcs(group_name: str, n_codes: int):
    charset: LiteralString = string.ascii_uppercase + string.digits
    codes: list[str] = [generate_code(charset) for _ in range(n_codes)]

    otc_group: OTCGroup | None = await OTCGroup.from_name(group_name)
    if otc_group is None:
        otc_group = OTCGroup(name=group_name)
        await OTCGroup.new_otc_group(otc_group)

    otc_beta_codes = [OTCBetaCode(otc_group.id, code) for code in codes]
    await OTCBetaCode.new_otc_beta_code_batch(otc_beta_codes)

    return codes


async def redeem_otc(otc_code: str, email: str):
    otc_row = await OTCBetaCode.from_code(otc_code)

    if otc_row is None:
        return False

    is_automation_test_code: bool = (
        settings.OTTO_ENV.upper() == "STG"
        and otc_row.group_id == settings.AUTOMATION_TESTS_OTC_GROUP_ID
        and email in settings.AUTOMATION_TESTS_OTC_EMAILS
    )
    if not is_automation_test_code and otc_row.used_by is not None:
        logger.warning(f"{email} tried to use already assigned OTC {otc_code}")
        return False

    await otc_row.update_fields({"used_by": email, "used_at": func.current_timestamp(), "status": "used"})
    await users_whitelist_collection.update_one({}, {"$addToSet": {"whitelisted_emails": email}}, upsert=True)

    return True


def create_otc_email_cookie(email: str):
    response = Response()
    response.set_cookie(
        key="otc_email",
        value=email,
        samesite="none",
        secure=True,
        httponly=True,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + timedelta(hours=1),
    )

    return response.headers.get("set-cookie", "")
