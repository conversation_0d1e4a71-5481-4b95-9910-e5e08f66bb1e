from typing import Optional, <PERSON><PERSON>

from sqlalchemy import select
from sqlalchemy.orm import selectinload

from server.database.models.user import Organization, OrganizationDomain, User
from server.utils.logger import logger
from server.utils.pg_connector import async_session


class OrganizationService:
    @staticmethod
    def get_domain_from_email(email: str) -> str:
        return email.split("@")[1].lower() if "@" in email else ""

    @staticmethod
    async def find_organization_by_domain(session, domain) -> Optional[Organization]:
        stmt = (
            select(OrganizationDomain)
            .options(selectinload(OrganizationDomain.organization).selectinload(Organization.validated_domains))
            .where(OrganizationDomain.domain == domain, OrganizationDomain.is_validated == True)
        )
        result = await session.execute(stmt)
        org_domain = result.scalar_one_or_none()
        return org_domain.organization if org_domain else None

    @staticmethod
    def should_auto_assign_user(organization: Organization, email: str) -> bool:
        if not organization or not organization.auto_approve_domain_users:
            return False

        domain = OrganizationService.get_domain_from_email(email)
        return any(od.domain == domain and od.is_validated for od in organization.validated_domains)

    @staticmethod
    async def can_bypass_whitelist_restrictions(email: str) -> bool:
        domain = OrganizationService.get_domain_from_email(email)

        async with async_session() as session:
            async with session.begin():
                organization = await OrganizationService.find_organization_by_domain(session, domain)
                return organization is not None

    @staticmethod
    async def auto_assign_user_to_organization(user: User) -> Tuple[bool, Optional[str]]:
        async with async_session() as session:
            async with session.begin():
                user_email = user.email
                try:
                    domain = OrganizationService.get_domain_from_email(user_email)
                    if not domain:
                        return False, None

                    organization = await OrganizationService.find_organization_by_domain(session, domain)
                    if not organization:
                        logger.info(f"No organization found for domain: {domain}")
                        return False, None  # No error, just no organization

                    if not OrganizationService.should_auto_assign_user(organization, user_email):
                        logger.info(f"Organization {organization.name} does not allow auto-assignment for {user_email}")
                        return False, None

                    if user.organization_id == organization.id:
                        logger.info(f"User {user_email} already assigned to organization {organization.name}")
                        return True, None

                    if user.organization_id and user.organization_id != organization.id:
                        logger.warning(
                            f"User {user_email} already assigned to different organization (ID: {user.organization_id})"
                        )
                        return False, "User already belongs to another organization"

                    user.organization_id = organization.id
                    session.add(user)

                    logger.info(f"Successfully assigned user {user_email} to organization {organization.name}")
                    return True, None

                except Exception as e:
                    logger.error(f"Error auto-assigning user {user_email} to organization: {str(e)}")
                    return False, f"Assignment failed: {str(e)}"
