import json
from datetime import <PERSON><PERSON><PERSON>
from typing import Any

import httpx
from fastapi import HTT<PERSON>Exception
from msal import ConfidentialClientApplication

from server.database.models.user_profile import Login<PERSON>ethod
from server.utils.remote_cache import RemoteCache
from server.utils.settings import settings

MICROSOFT_REDIRECT_URI = f"{settings.SERVER_DNS}/api/microsoft/callback"


def get_microsoft_auth_url(state: str | None = None, scopes=["User.Read"]):
    """
    Generate Microsoft OAuth authorization URL
    """
    # Use Confidential Client for web apps
    msal_app = ConfidentialClientApplication(
        settings.OUTLOOK_CLIENT_ID,
        authority="https://login.microsoftonline.com/common",
        client_credential=settings.OUTLOOK_CLIENT_SECRET,
    )

    # Generate authorization URL
    auth_url = msal_app.get_authorization_request_url(
        scopes=scopes,
        redirect_uri=MICROSOFT_REDIRECT_URI,
        state=state,
    )
    return auth_url


async def exchange_code_for_token(authorization_code: str):
    """
    Exchange authorization code for access token
    """
    msal_app = ConfidentialClientApplication(
        settings.OUTLOOK_CLIENT_ID,
        authority="https://login.microsoftonline.com/common/",
        client_credential=settings.OUTLOOK_CLIENT_SECRET,
    )

    # Exchange code for token
    result = msal_app.acquire_token_by_authorization_code(
        authorization_code, scopes=["User.Read"], redirect_uri=MICROSOFT_REDIRECT_URI
    )

    if "access_token" in result:
        # Fetch user info from Microsoft Graph
        async with httpx.AsyncClient() as client:
            user_response = await client.get(
                "https://graph.microsoft.com/v1.0/me", headers={"Authorization": f"Bearer {result['access_token']}"}
            )
            user_data = user_response.json()

        family_name = user_data.get("displayName", "")
        given_name = user_data.get("displayName", "")

        if family_name:
            parts = family_name.split()
            if len(parts) >= 1:
                family_name = parts[-1]
                given_name = parts[0]

        email = user_data.get("mail")
        if not email or "outlook_" in email.lower():
            email = user_data.get("userPrincipalName")

        return {
            "access_token": result["access_token"],
            "refresh_token": result["refresh_token"],
            "user_info": {
                "sub": user_data.get("id"),
                "given_name": given_name,
                "email": email,
                "family_name": family_name,
            },
        }

    raise HTTPException(status_code=401, detail="Could not validate credentials")


async def persist_state(email: str, state: dict[str, Any]):
    cache: RemoteCache = RemoteCache()
    await cache.set(
        key=f"OTTO_AUTH_STATE_{email}",
        value=json.dumps({**state, "auth_provider": LoginMethod.MICROSOFT, "email": email}),
        expire=timedelta(hours=1),
    )
