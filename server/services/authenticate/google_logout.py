import traceback

import requests

from server.services.calendar_api.google_api_base import GoogleApiBase
from server.utils.logger import logger


class GoogleLogout(GoogleApiBase):
    def __init__(self, refresh_token: str) -> None:
        super().__init__(refresh_token=refresh_token)

    def logout(self):
        try:
            self.authorize()
            requests.post(
                "https://oauth2.googleapis.com/revoke",
                params={"token": self.creds.token},
                headers={"content-type": "application/x-www-form-urlencoded"},
            )
        except BaseException:
            logger.error(traceback.format_exc())
