import asyncio
import json
from collections import defaultdict
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, cast
from urllib.parse import unquote

from authlib.integrations.starlette_client import <PERSON>Auth
from authlib.jose import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>oken
from authlib.oidc.core import <PERSON><PERSON><PERSON>oken, Imp<PERSON><PERSON><PERSON><PERSON>, UserInfo
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from pymongo.collation import Collation
from starlette.config import Config

from server.database.models.chat_thread import ChatThread
from server.database.models.user import User
from server.database.models.user_profile import LoginMethod, UserProfile
from server.dependencies import manager
from server.services.authenticate.authenticate import (
    AuthenticationProvider,
    validate_user,
)
from server.services.calendar_api.google_calendar_events import GoogleCalendarEvents
from server.services.one_time_codes.manage import create_otc_email_cookie, redeem_otc
from server.services.organization.organization import OrganizationService
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.google_login import IDEN<PERSON>TY_SCOPES
from server.utils.ip_stack_api import get_client_ip, guess_and_update_home_airport
from server.utils.logger import logger
from server.utils.mongo_connector import users_whitelist_collection
from server.utils.remote_cache import RemoteCache
from server.utils.sentry import beta_login_to_sentry, not_invited_login_to_sentry
from server.utils.settings import settings


class GoogleAuthenticate(AuthenticationProvider):
    def __init__(
        self,
        code: str | None = None,
        redirect_uri: str | None = None,
        id_token: str | None = None,
        refresh_token: str | None = None,
        otc_code: str | None = None,
    ):
        if code or id_token or refresh_token:
            self.code = code
            self.id_token = id_token
            self.redirect_uri = redirect_uri
            self.refresh_token = refresh_token
            self.otc_code = otc_code

            assert settings.GOOGLE_CLIENT_ID is not None, "GOOGLE_CLIENT_ID is not set"
            assert settings.GOOGLE_CLIENT_SECRET is not None, "GOOGLE_CLIENT_SECRET is not set"

            oauth: OAuth = OAuth(
                Config(
                    environ={
                        "GOOGLE_CLIENT_ID": settings.GOOGLE_CLIENT_ID,
                        "GOOGLE_CLIENT_SECRET": settings.GOOGLE_CLIENT_SECRET,
                    }
                )
            )

            self.client = oauth.register(
                name="google",
                server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
                client_kwargs={"scope": " ".join(IDENTITY_SCOPES)},
            )

    async def fetch_token(self) -> dict[str, str | dict | None]:
        """
        Fetch google access token data.
        """

        token: dict[str, str | dict | None] = {}
        if self.code:
            token = await self.client.fetch_access_token(  # type: ignore
                grant_type="authorization_code",
                code=unquote(self.code),
                redirect_uri=self.redirect_uri,
            )

        else:
            token = {"id_token": self.id_token, "refresh_token": self.refresh_token}

        if token.get("id_token", None):
            userinfo = await self.parse_id_token(token, None)
            token["user_info"] = userinfo

        return token

    async def parse_id_token(self, token, nonce, claims_options=None):
        """Return an instance of UserInfo from token's ``id_token``."""
        claims_params = dict(
            nonce=nonce,
            client_id=settings.GOOGLE_CLIENT_ID,
        )
        if "access_token" in token:
            claims_params["access_token"] = token["access_token"]
            claims_cls = CodeIDToken
        else:
            claims_cls = ImplicitIDToken

        metadata = await self.client.load_server_metadata()  # type: ignore

        if claims_options is None and "issuer" in metadata:
            claims_options = {"iss": {"values": [metadata["issuer"]]}}

        alg_values = metadata.get("id_token_signing_alg_values_supported")
        if not alg_values:
            alg_values = ["RS256"]

        jwt = JsonWebToken(alg_values)

        jwk_set = await self.client.fetch_jwk_set()  # type: ignore

        try:
            claims = jwt.decode(
                token["id_token"],
                key=JsonWebKey.import_key_set(jwk_set),
                claims_cls=claims_cls,
                claims_options=claims_options,
                claims_params=claims_params,
            )
        except ValueError:
            jwk_set = await self.client.fetch_jwk_set(force=True)  # type: ignore
            claims = jwt.decode(
                token["id_token"],
                key=JsonWebKey.import_key_set(jwk_set),
                claims_cls=claims_cls,
                claims_options=claims_options,
                claims_params=claims_params,
            )
        return UserInfo(claims)

    async def authorize(
        self, user_id_otp_override: int | None = None, request: Request | None = None
    ) -> tuple[tuple[str, str], int, str]:
        """
        Authenticate a user and return own jwt.
        Ensures only one UserProfile per user by prioritizing user identification over Google sub.
        """

        token = await self.fetch_token()
        google_user_info: dict[str, str] = cast(dict, token.get("user_info")) or defaultdict(str)

        google_email: str = google_user_info.get("email", "")
        refresh_token: str | None = cast(str, token.get("refresh_token"))
        google_sub: str = google_user_info.get("sub", "")

        if not user_id_otp_override:
            await self.validate_user_email(
                email=google_email,
                state={"id_token": token.get("id_token"), "refresh_token": token.get("refresh_token")},
            )

        user_profile: UserProfile | None = None
        user: User | None = None

        if user_id_otp_override:
            # OTP override case - use provided user ID
            user = await validate_user(user_id_otp_override)
            user_profile = await UserProfile.from_user_id(user_id_otp_override)

            if user_profile is None:
                # Create new profile for OTP user
                if not refresh_token:
                    logger.error(
                        f"No refresh_token from google. code={self.code}, id_token={self.id_token}, redirect_uri={self.redirect_uri}, otc_code={self.otc_code}"
                    )
                assert refresh_token is not None, "refresh_token should not be None when creating a new user profile"

                user_profile = UserProfile(
                    sub=google_sub + f"-otp-{user_id_otp_override}",
                    users_id=user_id_otp_override,
                    refresh_token=refresh_token,
                    resource_id=None,
                    last_login_method=LoginMethod.GOOGLE,
                )
                await UserProfile.new_user_profile(user_profile)
            else:
                if refresh_token:
                    await user_profile.update_refresh_token(refresh_token, LoginMethod.GOOGLE)

        else:
            # Normal login flow - prioritize user identification over Google sub

            # 1. First try to find existing user by email
            user = await User.from_email(google_email)

            if user is not None:
                # User exists - get their profile by user_id (not by sub)
                user_profile = await UserProfile.from_user_id(user.id)

                if user_profile is not None:
                    # Update existing profile with new Google sub and refresh token
                    if refresh_token:
                        await user_profile.update_refresh_token(refresh_token, LoginMethod.GOOGLE)
                else:
                    # User exists but no profile - create one
                    assert refresh_token is not None, "refresh_token should not be None when creating profile"
                    user_profile = UserProfile(
                        sub=google_sub,
                        users_id=user.id,
                        refresh_token=refresh_token,
                        resource_id=None,
                        last_login_method=LoginMethod.GOOGLE,
                    )
                    await UserProfile.new_user_profile(user_profile)
            else:
                # 2. If no user found by email, try to find by Google sub (for backward compatibility)
                user_profile = await UserProfile.from_sub(google_sub) if google_sub else None

                if user_profile is not None:
                    # Profile exists with this sub - get the associated user
                    user = await validate_user(user_profile.users_id)
                    if refresh_token:
                        await user_profile.update_refresh_token(refresh_token, LoginMethod.GOOGLE)
                else:
                    # 3. Completely new user - create both user and profile
                    user = User(
                        email=google_email,
                        first_name=google_user_info.get("given_name", "").strip(),
                        last_name=google_user_info.get("family_name", "").strip(),
                        profile_picture=google_user_info.get("picture", ""),
                    )

                    assert refresh_token is not None, "refresh_token should not be None for new user"
                    user_profile = await UserProfile.new_user(
                        google_sub, refresh_token, None, LoginMethod.GOOGLE, user=user
                    )

                    if request:
                        # First time user login, let's guess their home airport
                        client_ip = get_client_ip(request)
                        asyncio.create_task(guess_and_update_home_airport(client_ip, user_profile.users_id))

                    await TrackingManager.log_event_in_background(
                        event_type=TrackingEvent.FIRST_LOGIN,
                        user_id=str(user.id),
                        user_email=user.email,
                        event_properties={"login_method": "Google"},
                    )

                    await ChatThread.create_initial_chat_threads(user.id)

        # Update user fields with fresh Google data if needed
        if user is not None:
            fields_to_update = {}
            if not user.first_name and google_user_info.get("given_name", "").strip():
                fields_to_update["first_name"] = google_user_info.get("given_name", "").strip()
            if not user.last_name and google_user_info.get("family_name", "").strip():
                fields_to_update["last_name"] = google_user_info.get("family_name", "").strip()
            if not user.profile_picture and google_user_info.get("picture", ""):
                fields_to_update["profile_picture"] = google_user_info.get("picture", "")

            if fields_to_update:
                asyncio.create_task(
                    user.refresh_fields(
                        {
                            "first_name": google_user_info.get("given_name", "").strip(),
                            "last_name": google_user_info.get("family_name", "").strip(),
                            "profile_picture": google_user_info.get("picture", ""),
                        }
                    )
                )

        assert user_profile is not None, "user_profile should not be None at this point"
        assert user is not None, "User should not be None at this point"

        is_organisation_assigned, message = await OrganizationService.auto_assign_user_to_organization(user)
        if not is_organisation_assigned:
            # TODO Needs to implement an warnings in case of errors.
            pass

        # Handle Google Calendar integration
        google_calendar = GoogleCalendarEvents(user_profile.refresh_token, user_email=google_email)
        if google_calendar.has_calendar_access():
            resource_id = google_calendar.watch_calendar()
            if resource_id:
                await user_profile.update_resource_id(resource_id)
            await user_profile.enable_calendar(True)

        payload: dict[str, str] = user_profile.get_token_payload(user=user)
        return manager.create_custom_jwt(payload), user.id, user.email

    async def validate_user_email(self, email: str, state: dict[str, Any]):
        whitelisted_email = await users_whitelist_collection.find_one(
            {"whitelisted_emails": email}, collation=Collation(locale="en", strength=2)
        )

        if whitelisted_email is None and self.otc_code is not None:
            is_valid: bool = await redeem_otc(self.otc_code, email)
            if is_valid:
                whitelisted_email = email

        bypass_whitelist_restrictions = OrganizationService.can_bypass_whitelist_restrictions(email)
        if whitelisted_email is None and not bypass_whitelist_restrictions:
            logger.info(f"{email} - Redirect. Email is not whitelisted for this domain and no OTC provided.")
            not_invited_login_to_sentry(email, "Google")

            await GoogleAuthenticate.persist_state(email, state)
            raise HTTPException(
                status_code=403, detail="OTC not provided.", headers={"set-cookie": create_otc_email_cookie(email)}
            )
        else:
            beta_login_to_sentry(email, "Google")

    @staticmethod
    async def persist_state(email: str, state: dict[str, Any]):
        cache: RemoteCache = RemoteCache()
        await cache.set(
            key=f"OTTO_AUTH_STATE_{email}",
            value=json.dumps({**state, "auth_provider": LoginMethod.GOOGLE, "email": email}),
            expire=timedelta(hours=1),
        )
