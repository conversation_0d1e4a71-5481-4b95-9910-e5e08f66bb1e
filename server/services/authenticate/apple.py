import json
import time
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from typing import Any, Optional

import httpx
import jwt
from fastapi import HTTPException

from server.database.models.user_profile import <PERSON><PERSON>Method
from server.utils.logger import logger
from server.utils.remote_cache import RemoteCache
from server.utils.settings import settings

APPLE_REDIRECT_URI = f"{settings.SERVER_DNS}/api/apple/callback"


def read_apple_private_key(p8_file_path):
    """
    Read an Apple .p8 private key file and return it in the correct format for JWT signing

    Args:
        p8_file_path (str): Path to the .p8 file

    Returns:
        str: Private key in the correct format
    """
    try:
        with open(p8_file_path, "r") as key_file:
            private_key = key_file.read()

        # The key is already in the correct format with proper line breaks
        # But we ensure it has the right header and footer
        if not private_key.startswith("-----BEGIN PRIVATE KEY-----"):
            raise ValueError("Invalid private key format: Missing header")

        if not private_key.endswith("-----END PRIVATE KEY-----\n"):
            if not private_key.endswith("-----END PRIVATE KEY-----"):
                raise ValueError("Invalid private key format: Missing footer")
            private_key += "\n"

        return private_key
    except FileNotFoundError:
        raise FileNotFoundError(f"Apple private key file not found at: {p8_file_path}")
    except Exception as e:
        raise Exception(f"Error reading Apple private key: {str(e)}")


def generate_apple_client_secret():
    now = int(time.time())
    expiry = now + 15777000  # 6 months in seconds (maximum allowed by Apple)

    headers = {"kid": settings.APPLE_KEY_ID, "alg": "ES256"}

    payload = {
        "iss": settings.APPLE_TEAM_ID,
        "iat": now,
        "exp": expiry,
        "aud": "https://appleid.apple.com",
        "sub": settings.APPLE_CLIENT_ID,
    }

    project_root = Path(__file__).resolve().parents[3]

    private_key_path = str(project_root.joinpath("apple_auth_key").joinpath("AuthKey_XR272XZP95.p8"))
    apple_private_key = read_apple_private_key(private_key_path)

    client_secret = jwt.encode(payload=payload, key=apple_private_key, algorithm="ES256", headers=headers)
    return client_secret


def get_apple_auth_url(state: Optional[str] = None) -> str:
    params = {
        "client_id": settings.APPLE_CLIENT_ID,
        "redirect_uri": APPLE_REDIRECT_URI,
        "response_type": "code id_token",
        "scope": "name email",
        "response_mode": "form_post",
    }

    if state:
        params["state"] = state

    query_params = "&".join([f"{k}={v}" for k, v in params.items()])
    return f"https://appleid.apple.com/auth/authorize?{query_params}"


async def exchange_code_for_token(code):
    """
    Exchange the authorization code for tokens and user info
    """
    client_secret = generate_apple_client_secret()

    async with httpx.AsyncClient() as client:
        token_response = await client.post(
            "https://appleid.apple.com/auth/token",
            data={
                "client_id": settings.APPLE_CLIENT_ID,
                "client_secret": client_secret,
                "code": code,
                "grant_type": "authorization_code",
                "redirect_uri": APPLE_REDIRECT_URI,
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

    if token_response.status_code != 200:
        logger.error(f"Error exchanging code for token: {token_response.text}")
        raise HTTPException(status_code=400, detail="Failed to retrieve tokens from Apple")

    tokens = token_response.json()

    id_token = tokens.get("id_token")
    if not id_token:
        raise HTTPException(status_code=400, detail="No ID token received from Apple")

    decoded_token = jwt.decode(id_token, options={"verify_signature": False}, audience=settings.APPLE_CLIENT_ID)

    user_info = {
        "sub": decoded_token.get("sub"),  # Apple User ID
        "email": decoded_token.get("email", ""),
        "email_verified": decoded_token.get("email_verified", False),
    }

    if "refresh_token" in tokens:
        return {
            "access_token": tokens.get("access_token"),
            "refresh_token": tokens.get("refresh_token"),
            "user_info": user_info,
        }
    else:
        return {"access_token": tokens.get("access_token"), "user_info": user_info}


async def persist_state(email: str, state: dict[str, Any]):
    cache: RemoteCache = RemoteCache()
    await cache.set(
        key=f"OTTO_AUTH_STATE_{email}",
        value=json.dumps({**state, "auth_provider": LoginMethod.APPLE, "email": email}),
        expire=timedelta(hours=1),
    )
