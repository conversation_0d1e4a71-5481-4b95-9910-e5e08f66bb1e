import asyncio
import json
from datetime import <PERSON><PERSON><PERSON>
from typing import Any

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request
from pymongo.collation import Collation

from server.database.models.chat_thread import ChatThread
from server.database.models.user import User
from server.database.models.user_profile import LoginMethod
from server.dependencies import manager
from server.services.authenticate.authenticate import Authenti<PERSON><PERSON><PERSON>ider
from server.services.one_time_codes.manage import create_otc_email_cookie, redeem_otc
from server.services.organization.organization import OrganizationService
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.ip_stack_api import get_client_ip, guess_and_update_home_airport
from server.utils.logger import logger
from server.utils.mongo_connector import users_whitelist_collection
from server.utils.remote_cache import RemoteCache
from server.utils.sentry import beta_login_to_sentry, not_invited_login_to_sentry


class EmailAuthenticate(AuthenticationProvider):
    def __init__(self, email: str, code: str):
        self.email = email
        self.code = code
        self.cache = RemoteCache()

    async def authorize(self, request: Request | None = None) -> tuple[tuple[str, str], int, str]:
        """
        Authenticate a user and return own jwt.
        """

        await self.validate_user_email(self.email)

        redis_key: str = self.otp_redis_key_format(self.code)
        cache_data = await self.cache.get(redis_key)
        if cache_data is None:
            raise HTTPException(status_code=400, detail="Invalid code!")

        otp: dict[str, str] = json.loads(cache_data)

        user: User | None = await User.from_email(otp.get("email", ""))

        if user is None:
            user = User(
                self.email,
                first_name="",
                last_name="",
                profile_picture="",
            )

            await User.new_user(user)

            if request:
                # First time user login, let's guess their home airport
                client_ip = get_client_ip(request)
                asyncio.create_task(guess_and_update_home_airport(client_ip, user.id))

            await TrackingManager.log_event_in_background(
                event_type=TrackingEvent.FIRST_LOGIN,
                user_id=str(user.id),
                user_email=user.email,
                event_properties={"login_method": "Google"},
            )

            await ChatThread.create_initial_chat_threads(user.id)

        (
            is_organisation_assigned,
            message,
        ) = await OrganizationService.auto_assign_user_to_organization(user)
        if not is_organisation_assigned:
            # TODO Needs to implement an warnings in case of errors.
            pass

        await self.cache.delete(redis_key)

        payload: dict[str, str] = {
            "sub": str(user.id),
            "first_name": user.first_name,
            "last_name": user.last_name,
        }

        return manager.create_custom_jwt(payload), user.id, user.email

    @staticmethod
    async def validate_user_email(email: str, otc_code: str | None = None, otp: str | None = None):
        whitelisted_email = await users_whitelist_collection.find_one(
            {"whitelisted_emails": email}, collation=Collation(locale="en", strength=2)
        )

        if whitelisted_email is None and otc_code is not None:
            is_valid: bool = await redeem_otc(otc_code, email)
            if is_valid:
                whitelisted_email = email

        bypass_whitelist_restrictions = await OrganizationService.can_bypass_whitelist_restrictions(email)
        if whitelisted_email is None and not bypass_whitelist_restrictions:
            logger.info(f"{email} - Redirect. Email is not whitelisted for this domain and no OTC provided.")
            not_invited_login_to_sentry(email, "Email")

            await EmailAuthenticate.persist_state(email, {"otp": otp})
            raise HTTPException(
                status_code=403,
                detail="OTC not provided.",
                headers={"set-cookie": create_otc_email_cookie(email)},
            )
        else:
            beta_login_to_sentry(email, "Email")

    @staticmethod
    def otp_redis_key_format(otp_code: str):
        return f"OTTO_OTP_{otp_code}"

    @staticmethod
    async def persist_state(email: str, state: dict[str, Any]):
        cache: RemoteCache = RemoteCache()
        await cache.set(
            key=f"OTTO_AUTH_STATE_{email}",
            value=json.dumps({**state, "auth_provider": LoginMethod.EMAIL, "email": email}),
            expire=timedelta(hours=1),
        )
