import asyncio
from datetime import datetime
from typing import Any

from server.schemas.spotnana.create_spotnana_profile import (
    CreateSpotnanaProfile,
    UpdateFrequentFlyerNumber,
)
from server.schemas.spotnana.credit_card import CompanyEnum, CreditCard
from server.schemas.spotnana.user import (
    CreateSpotnanaUser,
    MembershipInfo,
    MembershipInfos,
)
from server.utils.settings import settings
from server.utils.spotnana_api import spotnana_api


async def get_spotnana_user_by_email(email: str):
    return (await spotnana_api.get_user_by_email(email)).get("elements", [{}])[0]


async def get_spotnana_user_id(email: str):
    spotnana_user = await get_spotnana_user_by_email(email)

    return spotnana_user.get("id", None)


async def update_spotnana_user_profile(
    user_profile: CreateSpotnanaProfile | dict[str, Any],
    email: str,
    spotnana_user_id: str | None = None,
    original_user_persona: str | None = None,
):
    user_profile_non_dict = user_profile if isinstance(user_profile, CreateSpotnanaProfile) else None
    if isinstance(user_profile, dict):
        user_profile["isCVCEdited"] = False
        user_profile["isCardNumberEdited"] = False
        user_profile_non_dict = CreateSpotnanaProfile(**user_profile)

    assert user_profile_non_dict is not None, "user_profile_non_dict is None at this point"
    user_memberships: MembershipInfos = MembershipInfos(**{"membershipInfos": []})
    if user_profile_non_dict.frequentFlyerNumbers is not None:
        for frequent_flyer in user_profile_non_dict.frequentFlyerNumbers:
            if frequent_flyer.number is not None:
                user_memberships.membershipInfos.append(
                    MembershipInfo(
                        **{
                            "appliedTo": [frequent_flyer.IATACode],
                            "id": frequent_flyer.number,
                            "issuedBy": frequent_flyer.IATACode,
                            "type": "AIR",
                        }
                    )
                )

    phone_number_list = []
    if user_profile_non_dict.phone:
        phone_number_list.append(
            {
                "countryCode": 1,
                "countryCodeSource": "FROM_NUMBER_WITH_PLUS_SIGN",
                "isoCountryCode": "US",
                "rawInput": user_profile_non_dict.phone,
                "type": "MOBILE",
            }
        )
    new_user_profile_dict: dict[str, Any] = {
        "personalInfo": {
            "dob": {"iso8601": datetime.strptime(user_profile_non_dict.dob, "%m/%d/%Y").strftime("%Y-%m-%d")}
            if user_profile_non_dict.dob
            else None,
            "name": {
                "family1": user_profile_non_dict.last_name,
                "given": user_profile_non_dict.first_name,
            },
            "gender": user_profile_non_dict.gender,
            "email": email,
            "title": user_profile_non_dict.title,
            "travelerName": {
                "family1": user_profile_non_dict.last_name,
                "given": user_profile_non_dict.first_name,
            },
            "phoneNumbers": phone_number_list,
            "identityDocs": [],
        },
        "persona": "GUEST"
        if spotnana_user_id is None
        else original_user_persona,  # if update user (spotnana_user_id exists) then don't update persona.
        "businessInfo": {
            "email": email,
            "legalEntityRef": {"id": settings.SPOTNANA_COMPANY_LEGAL_ID},
            "organizationRef": {"id": settings.SPOTNANA_COMPANY_GUID},
        },
        "membershipInfo": user_memberships,
    }

    if user_profile_non_dict.traveler_number is not None:
        new_user_profile_dict["personalInfo"]["identityDocs"].append(
            {"ktn": {"number": user_profile_non_dict.traveler_number, "issueCountry": "US"}}
        )

    if user_profile_non_dict.redress_number is not None:
        new_user_profile_dict["personalInfo"]["identityDocs"].append(
            {"redress": {"number": user_profile_non_dict.redress_number, "issueCountry": "US"}}
        )

    new_user_profile: CreateSpotnanaUser = CreateSpotnanaUser(**new_user_profile_dict)
    new_spotnana_user_profile: CreateSpotnanaUser = CreateSpotnanaUser(**new_user_profile.model_dump())

    if spotnana_user_id:
        await spotnana_api.update_user(spotnana_user_id, new_spotnana_user_profile)
        return spotnana_user_id
    else:
        res = await spotnana_api.create_user(new_spotnana_user_profile)
        return res.get("id")


async def update_spotnana_payment_profile(payment_profile: CreateSpotnanaProfile | dict[str, Any], email: str):
    payment_profile_non_dict: CreateSpotnanaProfile | None = (
        payment_profile if isinstance(payment_profile, CreateSpotnanaProfile) else None
    )
    if isinstance(payment_profile, dict):
        payment_profile["isCVCEdited"] = False
        payment_profile["isCardNumberEdited"] = False
        payment_profile_non_dict = CreateSpotnanaProfile(**payment_profile)

    traveler_info: dict[str, Any] = await spotnana_api.get_traveler_by_email(email)
    user_org_id = traveler_info["results"][0]["userOrgId"]

    traveler_cards = (await spotnana_api.traveler_read(user_org_id)).get("traveler").get("user").get("paymentInfos")  # type: ignore
    if len(traveler_cards) > 0:
        await asyncio.gather(
            *[spotnana_api.delete_credit_card(card.get("card").get("id"), user_org_id) for card in traveler_cards]
        )

    assert payment_profile_non_dict is not None, "payment_profile_non_dict is None at this point"
    spotnana_payment_profile: CreditCard = CreditCard(
        **{
            "card": {
                # [ NONE, VISA, MASTERCARD, AMEX, DISCOVER, AIR_TRAVEL_UATP, CARTE_BLANCHE, DINERS_CLUB, JCB, EURO_CARD, ACCESS_CARD, BREX, UNION_PAY, UNRECOGNIZED ]
                "company": CompanyEnum.get((payment_profile_non_dict.card_type or "").upper()).value,
                "type": 1,  # [ UNKNOWN, CREDIT, DEBIT, UNRECOGNIZED ]
                "address": {
                    "regionCode": "US",
                    "postalCode": payment_profile_non_dict.zip_code,
                    "administrativeArea": payment_profile_non_dict.state,
                    "locality": payment_profile_non_dict.city,
                    "addressLines": [payment_profile_non_dict.address],
                    "description": "",
                },
                "label": "OTTO credit card",
                "name": payment_profile_non_dict.cardholder_name,
                "number": payment_profile_non_dict.card_number,
                "expiryMonth": (payment_profile_non_dict.exp_date or "").split(" / ")[0],
                "expiryYear": (payment_profile_non_dict.exp_date or "").split(" / ")[1],
                "cvv": payment_profile_non_dict.card_cvc,
            },
            "userOrgId": user_org_id,
            "applicableTo": [1, 2],  # [ UNKNOWN_APPLICABLE_TO, AIR, HOTEL, RAIL, CAR, SERVICE_FEE, UNRECOGNIZED ]
        }
    )

    credit_card_result = await spotnana_api.create_credit_card(spotnana_payment_profile)

    stripe_card_tokens = credit_card_result.get("stripeCardTokens")
    spotnana_payload = {
        "stripeCardTokens": stripe_card_tokens,
        "userOrgId": user_org_id,
    }

    await spotnana_api.confirm_credit_card(spotnana_payload)


async def update_spotnana_membership_info(frequent_flyer_numbers: UpdateFrequentFlyerNumber, spotnana_user_id: str):
    user_memberships: MembershipInfos = MembershipInfos(**{"membershipInfos": []})
    for frequent_flyer in frequent_flyer_numbers.frequentFlyerNumbers or []:
        if frequent_flyer.number is not None:
            user_memberships.membershipInfos.append(
                MembershipInfo(
                    **{
                        "appliedTo": [frequent_flyer.IATACode],
                        "id": frequent_flyer.number,
                        "issuedBy": frequent_flyer.IATACode,
                        "type": "AIR",
                    }
                )
            )

    await spotnana_api.update_user_membership_info(spotnana_user_id, user_memberships)
