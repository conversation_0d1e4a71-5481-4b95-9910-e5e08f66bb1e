from typing import Any

from server.utils.mongo_connector import payment_profile_collection


async def get_user_payment_profile(user_id: int) -> dict[str, Any] | None:
    user_payment_profile: dict[str, Any] | None = await payment_profile_collection.find_one(
        {"users_id": user_id}, {"_id": 0}
    )

    return user_payment_profile


async def update_user_payment_profile(user_payment_profile: dict[str, Any], user_id: int):
    query: dict[str, Any] = {"users_id": user_id}
    update: dict[str, Any] = {"$set": {"users_id": user_id, **user_payment_profile}}
    await payment_profile_collection.update_one(query, update, upsert=True)


def is_hotel_payment_profile_complete(user_payment_profile: dict[str, Any] | None):
    if user_payment_profile is None:
        return False

    required_fields = [
        "first_name",
        "last_name",
        "phone",
        "address",
        "city",
        "state",
        "zip_code",
        "card_number",
        "card_cvc",
        "cardholder_name",
        "exp_date",
    ]
    empty_fields = [field for field in required_fields if user_payment_profile.get(field, "") == ""]

    return len(empty_fields) == 0


def is_flight_payment_profile_complete(user_payment_profile: dict[str, Any] | None):
    if user_payment_profile is None:
        return False

    required_fields = [
        "title",
        "first_name",
        "last_name",
        "phone",
        "dob",
        "gender",
        "address",
        "city",
        "state",
        "zip_code",
        "card_number",
        "card_cvc",
        "cardholder_name",
        "exp_date",
    ]
    empty_fields = [field for field in required_fields if user_payment_profile.get(field, "") == ""]

    return len(empty_fields) == 0


def get_missing_flight_payment_profile_fields(
    user_payment_profile: dict[str, Any] | None,
) -> tuple[bool, list[str] | None]:
    if user_payment_profile is None or len(user_payment_profile) == 0:
        return (False, None)

    required_fields = [
        "title",
        "first_name",
        "last_name",
        "phone",
        "dob",
        "gender",
        "address",
        "city",
        "state",
        "zip_code",
        "card_number",
        "card_cvc",
        "cardholder_name",
        "exp_date",
    ]
    empty_fields = [field for field in required_fields if user_payment_profile.get(field, "") == ""]

    return (True, empty_fields)
