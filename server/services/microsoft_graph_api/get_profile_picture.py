import base64

import requests

from server.utils.logger import logger

GRAPH_API_ENDPOINT = "https://graph.microsoft.com/v1.0"


async def get_profile_picture(access_token):
    """
    Get user profile picture from Microsoft Graph API
    """
    image_data_url = None
    try:
        response = requests.get(
            f"{GRAPH_API_ENDPOINT}/me/photo/$value",
            headers={"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"},
        )
        response.raise_for_status()

        # Encode the binary data into a Base64 string
        image_binary = response.content
        image_base64 = base64.b64encode(image_binary).decode("utf-8")

        # Create a data URL for the image
        content_type = response.headers.get("Content-Type", "image/jpeg")
        image_data_url = f"data:{content_type};base64,{image_base64}"
    except Exception as e:
        logger.error(f"Failed to retrieve profile picture from Microsoft: {e}")
    return image_data_url
