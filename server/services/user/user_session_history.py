from typing import <PERSON><PERSON>

from sqlalchemy import Row, func, select, text

from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.utils.pg_connector import async_session
from server.utils.settings import settings


async def get_user_last_session(user_id: int) -> Row[Tuple[ChatThread]] | None:
    async with async_session() as session:
        async with session.begin():
            query = (
                select(ChatThread)
                .join(Checkpoint, ChatThread.id == Checkpoint.thread_id)
                .where(
                    (ChatThread.users_id == user_id)
                    & (
                        ChatThread.title.not_in(
                            [
                                settings.ONBOARDING_THREAD_TITLE,
                                settings.PREFERENCES_THREAD_TITLE,
                                settings.FUTURE_TRIPS_THREAD_TITLE,
                                settings.TRAVEL_POLICY_THREAD_TITLE,
                                settings.ONBOARDING_PAGE_TITLE,
                            ]
                        )
                    )
                )
                .order_by(Checkpoint.created_date.desc())
                .limit(1)
            )

            result = (await session.execute(query)).fetchone()

            return result


async def get_user_first_session(user_id: int) -> Row[Tuple[ChatThread]] | None:
    async with async_session() as session:
        async with session.begin():
            query = (
                select(ChatThread)
                .join(Checkpoint, ChatThread.id == Checkpoint.thread_id)
                .where(
                    (ChatThread.users_id == user_id)
                    & (
                        ChatThread.title.not_in(
                            [
                                settings.ONBOARDING_THREAD_TITLE,
                                settings.PREFERENCES_THREAD_TITLE,
                                settings.FUTURE_TRIPS_THREAD_TITLE,
                                settings.TRAVEL_POLICY_THREAD_TITLE,
                                settings.ONBOARDING_PAGE_TITLE,
                            ]
                        )
                    )
                )
                .order_by(Checkpoint.created_date.asc())
                .limit(1)
            )

            result = (await session.execute(query)).fetchone()
            return result


async def get_user_current_trip(user_id: int) -> Row[Tuple[Booking, ChatThread]] | None:
    async with async_session() as session:
        async with session.begin():
            now = func.now()

            query = (
                select(Booking, ChatThread)
                .join(ChatThread, Booking.thread_id == ChatThread.id)
                .where(
                    (ChatThread.users_id == user_id)
                    & (
                        (
                            (Booking.end_date.is_(None))
                            & (now >= Booking.start_date - text("interval '72 hours'"))
                            & (now <= Booking.start_date)
                        )
                        | (
                            (Booking.end_date.is_not(None))
                            & (now >= Booking.start_date - text("interval '72 hours'"))
                            & (now <= Booking.end_date)
                        )
                    )
                    & ((ChatThread.is_deleted.is_(False)) | (ChatThread.is_deleted.is_(None)))
                    & (Booking.status.in_(["BOOKED", "PENDING"]))
                )
                .order_by(Booking.start_date.asc())
                .limit(1)
            )

            result = (await session.execute(query)).fetchone()
            return result


async def get_thread_current_bookings(thread_id: int) -> list[Booking]:
    async with async_session() as session:
        async with session.begin():
            now = func.now()

            query = select(Booking).where(
                (Booking.thread_id == thread_id)
                & (
                    (
                        (Booking.end_date.is_(None))
                        & (now >= Booking.start_date - text("interval '72 hours'"))
                        & (now <= Booking.start_date)
                    )
                    | (
                        (Booking.end_date.is_not(None))
                        & (now >= Booking.start_date - text("interval '72 hours'"))
                        & (now <= Booking.end_date)
                    )
                )
                & (Booking.status.in_(["BOOKED", "PENDING"]))
            )

            result = await session.execute(query)
            bookings = result.scalars().all()

            return list(bookings)
