import json
import urllib.parse
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from server.utils.mongo_connector import user_activities
from server.utils.settings import settings


async def update_user_activity(user_id: str, activity_type: str, data: Optional[Dict[str, Any]] = None) -> None:
    """
    Update user activity tracking document
    """
    now = datetime.now(timezone.utc)

    data = data or {}

    update_ops = {
        "$set": {
            "last_updated": now,
        }
    }

    if activity_type == "sign_in":
        update_ops["$set"]["last_sign_in"] = now

    elif activity_type == "flight_search":
        update_ops.update(
            {
                "$set": {
                    "last_flight_search": now,
                },
                "$min": {"first_flight_search": now},
            }
        )
        if "has_company_policy" in data:
            update_ops["$set"]["has_company_policy"] = data["has_company_policy"]

    elif activity_type == "hotel_search":
        update_ops.update(
            {
                "$set": {
                    "last_hotel_search": now,
                },
                "$min": {"first_hotel_search": now},
            }
        )
        if "has_company_policy" in data:
            update_ops["$set"]["has_company_policy"] = data["has_company_policy"]

    for key, value in data.items():
        if key not in ["has_company_policy"]:
            update_ops["$set"][f"metadata.{key}"] = value

    await user_activities.update_one({"user_id": user_id}, update_ops, upsert=True)


async def update_otto_ruid(user_id, otto_ruid):
    """
    Update user's otto_ruid if it doesn't exist or has changed

    Args:
        user_id: Unique identifier for the user
        otto_ruid: RUID tracking data containing referral information
    """

    decoded_str = urllib.parse.unquote(otto_ruid)
    otto_ruid = json.loads(decoded_str)

    existing_user = await user_activities.find_one({"user_id": user_id}, {"otto_ruid": 1})

    if not existing_user:
        await user_activities.update_one(
            {"user_id": user_id}, {"$set": {"otto_ruid": otto_ruid, "last_updated": datetime.utcnow()}}, upsert=True
        )
    else:
        existing_ruid = existing_user.get("otto_ruid", {})
        if existing_ruid.get("RUID") != otto_ruid.get("RUID"):
            await user_activities.update_one(
                {"user_id": user_id}, {"$set": {"otto_ruid": otto_ruid, "last_updated": datetime.utcnow()}}
            )


async def get_user_activity(user_id: str) -> Dict[str, Any]:
    doc = await user_activities.find_one({"user_id": user_id})
    if not doc:
        return {}

    if "last_suggested_capabilities" in doc:
        now = datetime.now(timezone.utc)
        expiration_cutoff = now - settings.CAPABILITY_SUGGESTION_EXPIRATION_PERIOD

        filtered_capabilities = []
        for item in doc.get("last_suggested_capabilities", []):
            if isinstance(item, str):
                pass
            elif isinstance(item, dict) and "category" in item and "timestamp" in item:
                suggestion_time = datetime.fromtimestamp(item["timestamp"], timezone.utc)
                if suggestion_time >= expiration_cutoff:
                    filtered_capabilities.append(item["category"])

        doc["last_suggested_capabilities"] = filtered_capabilities

    return doc


async def get_users_activities(user_ids: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Fetch activities for multiple users efficiently.

    Args:
        user_ids: List of user IDs to fetch activities for
    """

    cursor = user_activities.find({"user_id": {"$in": user_ids}})
    activities = await cursor.to_list(length=None)
    return {act["user_id"]: act for act in activities}


async def update_suggested_capability(user_id: str, suggested_capability: str):
    """
    Update user's suggested capability tracking

    Args:
        user_id: Unique identifier for the user
        suggested_capability: The capability that was suggested
    """
    now = datetime.now(timezone.utc)

    update_ops = {
        "$set": {
            "last_timestamp_of_suggested_capability": now.timestamp(),
        },
        "$push": {
            "last_suggested_capabilities": {
                "$each": [{"category": suggested_capability, "timestamp": now.timestamp()}],
                "$slice": -5,  # Keep only the last 5 items
            }
        },
    }

    await user_activities.update_one({"user_id": user_id}, update_ops, upsert=True)


async def get_suggested_capabilities(user_id: str) -> Dict[str, Any]:
    """
    Get user's suggested capability tracking data

    Args:
        user_id: Unique identifier for the user

    Returns:
        Dictionary containing suggested capability tracking data
    """
    doc = await user_activities.find_one({"user_id": user_id})
    return doc.get("last_suggested_capabilities") if doc else {}
