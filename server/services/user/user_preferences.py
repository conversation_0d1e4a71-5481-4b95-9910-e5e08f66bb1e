from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List

from baml_client.types import ResponseAllPreferences
from server.utils.logger import logger
from server.utils.mongo_connector import user_preferences_collection


class SearchType(Enum):
    FLIGHT = "flight"
    HOTEL = "hotel"


class UserPreferences(ResponseAllPreferences):
    hide_sample_trips: bool = False
    triggered_flight_search_amount: int = 0
    triggered_hotel_search_amount: int = 0
    updated_at: datetime | None = None


async def has_user_preferences(user_id: int) -> bool:
    user_preferences: dict[str, str] | None = await user_preferences_collection.find_one(
        {"users_id": user_id}, {"_id": 0, "users_id": 0}
    )

    return user_preferences is not None


async def get_user_preferences(user_id: int) -> ResponseAllPreferences:
    user_preferences: dict[str, str] | None = await user_preferences_collection.find_one(
        {"users_id": user_id}, {"_id": 0, "users_id": 0}
    )

    if user_preferences:
        return ResponseAllPreferences(**user_preferences)
    else:
        return ResponseAllPreferences()


async def get_user_preferences_with_updated_time(user_id: int) -> UserPreferences:
    user_preferences: dict[str, str] | None = await user_preferences_collection.find_one(
        {"users_id": user_id}, {"_id": 0, "users_id": 0}
    )

    if user_preferences:
        return UserPreferences(**user_preferences)
    else:
        return UserPreferences()


async def save_user_preferences(user_id: int, user_event_selections: ResponseAllPreferences):
    query: dict[str, Any] = {"users_id": user_id}

    # Check if preferences have changed by retrieving current preferences
    current_preferences = await get_user_preferences(user_id)
    current_preferences_dict = current_preferences.model_dump()
    new_preferences_dict = user_event_selections.model_dump()

    if current_preferences_dict == new_preferences_dict:
        return

    logger.info(f"Updated user preference to be saved to db: {new_preferences_dict}")

    # Only update timestamp if preferences have changed
    timestamp = datetime.now(timezone.utc)
    update_dict = {"users_id": user_id, **new_preferences_dict}
    update_dict["updated_at"] = timestamp

    update: dict[str, Any] = {"$set": update_dict}
    await user_preferences_collection.update_one(query, update, upsert=True)


async def get_users_preferences(user_ids: List[str]) -> Dict[str, Dict[str, Any]]:
    cursor = user_preferences_collection.find({"users_id": {"$in": [int(uid) for uid in user_ids]}})
    activities = await cursor.to_list(length=None)

    return {act["users_id"]: act for act in activities}


async def set_hide_sample_trips(user_id: int, value: bool = True) -> None:
    """
    Set the hide_sample_trips preference to the specified value (defaults to True).
    """
    query: dict[str, Any] = {"users_id": user_id}
    timestamp = datetime.now(timezone.utc)

    update: dict[str, Any] = {"$set": {"hide_sample_trips": value, "updated_at": timestamp}}

    await user_preferences_collection.update_one(query, update, upsert=True)
    logger.info(f"Updated hide_sample_trips to {value} for user {user_id}")


async def increment_triggered_search_amount(
    user_id: int, search_type: SearchType, increment: int = 1
) -> UserPreferences:
    """
    Increment the triggered_search_amount by the specified amount (defaults to 1).
    Returns the new triggered_search_amount value.
    """
    query: dict[str, Any] = {"users_id": user_id}
    timestamp = datetime.now(timezone.utc)

    update: dict[str, Any] = {}
    if search_type == SearchType.FLIGHT:
        update: dict[str, Any] = {
            "$inc": {"triggered_flight_search_amount": increment},
            "$set": {"updated_at": timestamp},
        }
    elif search_type == SearchType.HOTEL:
        update: dict[str, Any] = {
            "$inc": {"triggered_hotel_search_amount": increment},
            "$set": {"updated_at": timestamp},
        }

    await user_preferences_collection.update_one(query, update, upsert=True)

    # Get and return the updated value
    updated_preferences = await get_user_preferences_with_updated_time(user_id)
    logger.info(
        f"Incremented triggered_search_amount by {increment} for user {user_id}. New value: {updated_preferences.model_dump()}"
    )

    return updated_preferences
