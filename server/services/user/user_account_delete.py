import asyncio

from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.database.models.user import User
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.database.models.user_profile import UserProfile
from server.services.authenticate.google_logout import GoogleLogout
from server.services.calendar_api.microsoft_calendar_events import MicrosoftCalendarEvents
from server.utils.logger import logger
from server.utils.mongo_connector import (
    google_calendar_events_collection,
    trip_travel_context_collection,
    user_preferences_collection,
)
from server.utils.spotnana_api import spotnana_api


async def _clear_user_data(user_id: int, user_email: str, include_profile: bool = False):
    """Base function to clear user data.

    Args:
        user_id: The user's ID
        user_email: The user's email
        include_profile: Whether to clear the Google profile
    """
    # Get all user threads, including deleted ones
    threads = await ChatThread.from_user_id(user_id, False, include_deleted=True)
    threads_ids: list[int] = [thread.ChatThread.id for thread in threads]

    # Clear all user-related data in parallel
    tasks = [
        user_preferences_collection.delete_many({"users_id": user_id}),
        Booking.delete_batch({"thread_id": threads_ids}),
        trip_travel_context_collection.delete_many({"thread_id": {"$in": threads_ids}}),
        google_calendar_events_collection.delete_many({"user_id": user_id}),
    ]

    await asyncio.gather(*tasks)

    # Clear checkpoints for threads
    await Checkpoint.delete_threads(threads_ids)

    # Clear travel policy if exists
    user_company_travel_policy: UserCompanyTravelPolicy | None = await UserCompanyTravelPolicy.from_user_id(user_id)
    if user_company_travel_policy is not None:
        await user_company_travel_policy.delete()

    user_profile: UserProfile | None = await UserProfile.from_user_id(user_id)

    # Optionally clear Google profile
    if include_profile and user_profile is not None:
        await user_profile.delete()

    if user_profile is not None:
        try:
            google_logout = GoogleLogout(user_profile.refresh_token)
            google_logout.logout()
        except Exception:
            pass

        try:
            microsoft_calendar = MicrosoftCalendarEvents(user_profile.refresh_token, user_email)
            await microsoft_calendar.revoke_access_token()
        except Exception:
            pass

    # Clear Spotnana data if exists
    try:
        spotnana_user = await spotnana_api.get_user_by_email(user_email)
        if spotnana_user and spotnana_user.get("data"):
            for user_data in spotnana_user["data"]:
                spotnana_user_id = user_data["id"]
                await spotnana_api.delete_user(spotnana_user_id)
                logger.info(f"Deleted Spotnana user with ID: {spotnana_user_id}")
    except Exception as e:
        logger.warning(f"Failed to delete Spotnana user for email {user_email}: {str(e)}")


async def clear_user_state(user_id: int, user_email: str):
    """Clear a user's state without deleting the user.

    This removes all user data but keeps the user account intact.
    """
    user = await User.from_id(user_id)
    if user is not None:
        await user.reset_tutorial()
        await _clear_user_data(user_id, user_email, include_profile=False)


async def delete_user_account(user_id: int, user_email: str):
    """Delete a user account completely.

    This clears all user data and deletes the user account.
    """
    await _clear_user_data(user_id, user_email, include_profile=True)

    # Delete the user account
    user = await User.from_id(user_id)
    if user:
        await user.delete()
