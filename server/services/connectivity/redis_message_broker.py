import asyncio
import json
import uuid
from typing import Any, As<PERSON><PERSON>enerator, Dict, Optional

from server.services.connectivity.message_broker import Message<PERSON>roker
from server.utils.logger import logger
from server.utils.redis_connector import RedisConnector


class RedisMessageBroker(MessageBroker):
    """
    Redis implementation of the message broker interface.
    Uses Redis pub/sub functionality for messaging.
    """

    def __init__(self, default_channel: str):
        """
        Initialize the Redis message broker.

        Args:
            default_channel: The default channel to use for publish/subscribe operations
        """
        self.default_channel = default_channel
        self.sender_id = str(uuid.uuid4())

    async def _get_connection(self):
        """Get the Redis connection from the connector."""
        conn = await RedisConnector.get_connection(raise_on_error=True)
        if conn is None:
            raise ConnectionError("Redis connection is not available")
        return conn

    async def publish(self, is_user_chat_message: bool, payload: Dict[str, Any], channel: Optional[str] = None) -> bool:
        """
        Publish a message to the specified channel.

        Args:
            payload: The payload to publish as a dictionary
            channel: The channel to publish to. If None, uses the default channel.
        """
        channel_name = channel or self.default_channel
        connection = None
        try:
            connection = await self._get_connection()
            await connection.publish(
                channel_name,
                json.dumps({"sid": self.sender_id, "is_usr_chat": is_user_chat_message, **payload}),
            )
            return True
        except Exception as e:
            logger.error(f"Error publishing message: {e}")
            return False
        finally:
            if connection:
                await connection.aclose()

    async def subscribe(self, channel: Optional[str] = None) -> Optional[AsyncGenerator[Dict[str, Any], None]]:
        """
        Subscribe to messages from the specified channel.

        Args:
            channel: The channel to subscribe to. If None, uses the default channel.

        Returns:
            An async generator that yields messages as they are received
        """
        channel_name = channel or self.default_channel
        try:
            connection = await self._get_connection()
            pubsub = connection.pubsub()

            async def message_generator():
                await pubsub.subscribe(channel_name)

                try:
                    async for message in pubsub.listen():
                        try:
                            if message["type"] == "message":
                                data = message.get("data")
                                if data:
                                    res = json.loads(data)
                                    is_user_chat_message = res.get("is_usr_chat", False)
                                    # Skip all user messages from self
                                    if res.get("sid", None) == self.sender_id and is_user_chat_message:
                                        logger.info(f"Received message from self id:{self.sender_id}, skipping")
                                        continue

                                    # remove sid from the message
                                    res.pop("sid")
                                    res.pop("is_usr_chat")
                                    yield res
                        except Exception:
                            logger.error(f"Error processing message: {message}")
                            pass
                except asyncio.CancelledError:
                    logger.info(f"RedisMessageBroker: subscription task for channel {channel_name} cancelled")
                    pass
                finally:
                    await pubsub.unsubscribe(channel_name)
                    await pubsub.aclose()

            return message_generator()
        except Exception as e:
            logger.error(f"Error subscribing to channel: {e}")
            return None

    async def close(self) -> None:
        """
        Close the message broker connection and release resources.
        """
        pass
