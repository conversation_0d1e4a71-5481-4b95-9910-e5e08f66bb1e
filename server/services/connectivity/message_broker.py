from abc import ABC, abstractmethod
from typing import Any, AsyncGenerator, Dict, Optional


class MessageBroker(ABC):
    """
    Abstract base class for message brokers.
    Defines the interface that all message broker implementations must follow.
    """

    @abstractmethod
    async def publish(self, is_user_chat_message: bool, payload: Dict[str, Any], channel: Optional[str] = None) -> bool:
        """
        Publish a message to the channel.

        Args:
            is_user_chat_message: Whether the message is a user chat message
            payload: The payload to publish as a dictionary
            channel: The channel to publish to. If None, uses the default channel.

        Returns:
            True if the message was published successfully, False otherwise
        """
        pass

    @abstractmethod
    async def subscribe(self, channel: Optional[str] = None) -> Optional[AsyncGenerator[Dict[str, Any], None]]:
        """
        Subscribe to messages from the channel.

        Args:
            channel: The channel to subscribe to. If None, uses the default channel.

        Returns:
            An async generator that yields messages as they are received, or None if the subscription failed
        """
        pass

    @abstractmethod
    async def close(self) -> None:
        """
        Close the message broker connection and release resources.
        """
        pass
