from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import UserProfile
from server.schemas.spotnana.flight_statuses import FlightStatuses
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.utils.logger import logger


async def handle_webhook_ticket_refunded(payload):
    trip_id: str = payload.get("tripId", None)
    updates: dict[str, bool] = {}

    for index, leg in enumerate(payload.get("airPnr", {}).get("legs", [])):
        if leg.get("legStatus") == "CANCELLED_STATUS":
            updates[f"content.legs.{index}.cancelled"] = True

    if len(updates) > 0:
        await Booking.update_fields(
            {"type": "flight", "content.trip_id": trip_id},
            {**updates, "content.status": FlightStatuses.CANCELLED.value, "status": FlightStatuses.CANCELLED.name},
        )

        booking_data: Booking | None = await Booking.from_query({"type": "flight", "content.trip_id": trip_id})
        if booking_data:
            thread_id: int = booking_data.thread_id
            thread_info: ChatThread | None = await ChatThread.from_id(thread_id)
            if thread_info is not None:
                user_profile: UserProfile | None = await UserProfile.from_user_id(thread_info.users_id)
                if user_profile is not None:
                    google_calendar_api = CalendarProviderManager(user_profile=user_profile)

                    has_calendar_access = google_calendar_api.has_calendar_access()

                    # hardcode the index 0 as outbound and 1 as return for now
                    if "legs.0.cancelled" in updates.keys() and has_calendar_access:
                        delete_calendar_outbound_event(booking_data, thread_info, google_calendar_api)

                    if "legs.1.cancelled" in updates.keys() and has_calendar_access:
                        delete_calendar_return_event(booking_data, thread_info, google_calendar_api)


def delete_calendar_outbound_event(
    booking_data: Booking, thread_info: ChatThread, google_calendar_api: CalendarProviderManager
):
    outbound_airline_code: str = (
        booking_data.content.get("outbound", {})
        .get("flight_segments", [{}])[0]
        .get("flight_stops", [{}])[0]
        .get("airline_code")
    )
    outbound_flight_number: str = (
        booking_data.content.get("outbound", {})
        .get("flight_segments", [{}])[0]
        .get("flight_stops", [{}])[0]
        .get("flight_number")
    )
    event_title = f"Flight to {thread_info.title} ({outbound_airline_code} {outbound_flight_number})"
    event_description_partial = (
        f"Airline confirmation number: {booking_data.content.get('airline_confirmation_number', '')}"
    )

    events = google_calendar_api.search_event(f"{event_title} {event_description_partial}")

    if len(events) == 1:
        event_id = events[0].get("id")
        google_calendar_api.delete_event(event_id)
        logger.info(f"Removing google calendar event with id {event_id} and title {event_title}")


def delete_calendar_return_event(
    booking_data: Booking, thread_info: ChatThread, google_calendar_api: CalendarProviderManager
):
    return_airline_code: str = (
        booking_data.content.get("return", {})
        .get("flight_segments", [{}])[0]
        .get("flight_stops", [{}])[0]
        .get("airline_code")
    )
    return_flight_number: str = (
        booking_data.content.get("return", {})
        .get("flight_segments", [{}])[0]
        .get("flight_stops", [{}])[0]
        .get("flight_number")
    )
    event_title = f"Flight to {thread_info.title} ({return_airline_code} {return_flight_number})"
    event_description_partial = (
        f"Airline confirmation number: {booking_data.content.get('airline_confirmation_number', '')}"
    )

    events = google_calendar_api.search_event(f"{event_title} {event_description_partial}")

    if len(events) == 1:
        event_id = events[0].get("id")
        google_calendar_api.delete_event(event_id)
        logger.info(f"Removing google calendar event with id {event_id} and title {event_title}")
