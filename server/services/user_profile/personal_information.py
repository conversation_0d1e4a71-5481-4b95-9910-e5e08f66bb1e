import asyncio
from datetime import datetime
from typing import Any

from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.schemas.authenticate.user import User
from server.schemas.spotnana.user import CreateSpotnanaUser
from server.schemas.user_profile.personal_information import PersonalInformationExtendedRequest
from server.utils.mongo_connector import user_profile_personal_information_collection
from server.utils.settings import settings
from server.utils.spotnana_api import spotnana_api


async def get_user_profile_personal_information(user_id: int):
    user_personal_information = await user_profile_personal_information_collection.find_one(
        {"users_id": user_id}, {"_id": 0}
    )

    return user_personal_information


async def save_personal_information_spotnana(
    user_personal_information: PersonalInformationExtendedRequest,
    user: User,
    spotnana_user_id: str | None = None,
    original_user_persona: str | None = None,
):
    phone_number_list = []
    if user_personal_information.phone:
        phone_number_list.append(
            {
                "countryCode": 1,
                "countryCodeSource": "FROM_NUMBER_WITH_PLUS_SIGN",
                "isoCountryCode": "US",
                "rawInput": user_personal_information.phone,
                "type": "MOBILE",
            }
        )

    new_user_profile_dict: dict[str, Any] = {
        "personalInfo": {
            "dob": {"iso8601": datetime.strptime(user_personal_information.dob, "%m/%d/%Y").strftime("%Y-%m-%d")}
            if user_personal_information.dob
            else None,
            "name": {
                "family1": user_personal_information.last_name,
                "given": user_personal_information.first_name,
            },
            "gender": user_personal_information.gender,
            "email": user.email,
            "title": user_personal_information.title,
            "travelerName": {
                "family1": user_personal_information.last_name,
                "given": user_personal_information.first_name,
            },
            "phoneNumbers": phone_number_list,
            "identityDocs": [],
        },
        "persona": "GUEST"
        if spotnana_user_id is None
        else original_user_persona,  # if update user (spotnana_user_id exists) then don't update persona.
        "businessInfo": {
            "email": user.email,
            "legalEntityRef": {"id": settings.SPOTNANA_COMPANY_LEGAL_ID},
            "organizationRef": {"id": settings.SPOTNANA_COMPANY_GUID},
        },
        "membershipInfo": {"membershipInfos": []},
    }

    if user_personal_information.traveler_number is not None and user_personal_information.traveler_number != "":
        new_user_profile_dict["personalInfo"]["identityDocs"].append(
            {"ktn": {"number": user_personal_information.traveler_number, "issueCountry": "US"}}
        )

    if user_personal_information.redress_number is not None and user_personal_information.redress_number != "":
        new_user_profile_dict["personalInfo"]["identityDocs"].append(
            {"redress": {"number": user_personal_information.redress_number, "issueCountry": "US"}}
        )

    new_user_profile: CreateSpotnanaUser = CreateSpotnanaUser(**new_user_profile_dict)
    new_spotnana_user_profile: CreateSpotnanaUser = CreateSpotnanaUser(**new_user_profile.model_dump())

    if spotnana_user_id:
        await spotnana_api.update_user(spotnana_user_id, new_spotnana_user_profile)
    else:
        res = await spotnana_api.create_user(new_spotnana_user_profile)
        spotnana_user_id = res.get("id")

    # Link new traveler to travel arranger
    if user.organization_id is not None and user.role != UserRole.company_admin:
        assert spotnana_user_id is not None, "Spotnana user id should exisst at this point."
        asyncio.create_task(add_user_to_travel_arranger(user, spotnana_user_id))

    return spotnana_user_id


def is_user_profile_personal_information_complete(personal_information: dict[str, Any] | None):
    if personal_information is None:
        return False

    required_fields = ["title", "first_name", "last_name", "phone", "dob", "gender"]
    empty_fields = [field for field in required_fields if personal_information.get(field, None) is None]

    return len(empty_fields) == 0


def is_hotel_user_profile_personal_information_complete(personal_information: dict[str, Any] | None):
    if personal_information is None:
        return False

    required_fields = ["first_name", "last_name", "phone"]
    empty_fields = [field for field in required_fields if personal_information.get(field, "") == ""]

    return len(empty_fields) == 0


def is_flight_user_profile_personal_information_complete(personal_information: dict[str, Any] | None):
    if personal_information is None:
        return False

    required_fields = ["title", "first_name", "last_name", "phone", "dob", "gender"]
    empty_fields = [field for field in required_fields if personal_information.get(field, "") == ""]

    return len(empty_fields) == 0


def get_missing_user_profile_personal_information_fields(
    personal_information: dict[str, Any] | None,
) -> tuple[bool, list[str] | None]:
    if personal_information is None or len(personal_information) == 0:
        return (False, None)

    required_fields = ["title", "first_name", "last_name", "phone", "dob", "gender"]
    empty_fields = [field for field in required_fields if personal_information.get(field, "") == ""]

    return (True, empty_fields)


async def update_spotnana_travel_arranger_role(spotnana_user_id: str, arranger_for=[]):
    roles_update_request = {
        "roles": [
            {
                "roleType": "TRAVEL_ARRANGER",
                "metadata": {"roleType": "TRAVEL_ARRANGER", "arrangerFor": arranger_for},
                "roleMetadata": {"travelArrangerMetadata": {"arrangerFor": arranger_for}},
            }
        ]
    }

    await spotnana_api.update_user_roles(spotnana_user_id, roles_update_request)


async def add_user_to_travel_arranger(user: User, spotnana_user_id: str):
    # Get organization admin from db
    assert user.organization_id is not None, "The company admin must be in an organization"
    company_admin_user: UserDB | None = await UserDB.from_organization_id_and_role(
        user.organization_id, UserRole.company_admin
    )
    assert company_admin_user is not None, "User assigned to an organization without admin!"

    # Get spotnana user id for admin
    spotnana_admin_user_id: str | None = (
        (await spotnana_api.get_user_by_email(company_admin_user.email)).get("elements", [{}])[0].get("id")
    )
    assert spotnana_admin_user_id is not None, "Organization Admin not found in spotnana!"

    # Get spotnana user roles for admin
    spotnana_user_roles = await spotnana_api.get_user_roles(spotnana_admin_user_id)
    assert spotnana_user_roles.get("roles", [{}])[0].get("roleType") == "TRAVEL_ARRANGER", (
        "Organization Admin role is not TRAVEL_ARRANGER"
    )

    # Update the metadata to include the new user
    metadata_arranger_for: list[dict] = (
        spotnana_user_roles.get("roles", [{}])[0].get("metadata", {}).get("arrangerFor", [])
    )
    assigned_users = [item.get("userId") for item in metadata_arranger_for]

    # Update spotnana user roles for admin
    if spotnana_user_id not in assigned_users:
        metadata_arranger_for.append(
            {"userId": spotnana_user_id, "sendConfirmationEmail": True, "sendFlightStatsNotificationEmail": True}
        )

        await update_spotnana_travel_arranger_role(spotnana_admin_user_id, metadata_arranger_for)
