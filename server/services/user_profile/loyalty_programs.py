from typing import Any

from server.utils.mongo_connector import user_profile_loyalty_programs_collection


async def get_user_profile_flights_loyalty_programs(user_id: int):
    user_flights_loyalty_programs = await user_profile_loyalty_programs_collection.find_one({"users_id": user_id})

    return user_flights_loyalty_programs


async def update_user_profile_flights_loyalty_programs(loyalty_programs: dict[str, Any], user_id: int):
    query: dict[str, Any] = {"users_id": user_id}
    update: dict[str, Any] = {"$set": {"users_id": user_id, **loyalty_programs}}
    await user_profile_loyalty_programs_collection.update_one(query, update, upsert=True)
