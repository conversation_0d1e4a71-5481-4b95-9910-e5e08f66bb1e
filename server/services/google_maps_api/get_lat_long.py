from fastapi.exceptions import HTTPException

from server.utils.async_requests import make_get_request, make_post_request
from server.utils.logger import logger
from server.utils.settings import settings


async def get_lat_long_from_loc_str(location_description: str) -> str | None:
    """
    Asynchronously call Google Maps Places API to get latitude and longitude for a given location name.
    """

    url = "https://places.googleapis.com/v1/places:searchText"

    data = {
        "textQuery": location_description,
    }

    headers = {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": settings.GOOGLE_PLACE_API_KEY,
        "X-Goog-FieldMask": "places.displayName,places.formattedAddress,places.location",
    }
    lat_long_str = None
    try:
        response_data = await make_post_request(url=url, headers=headers, data=data)
        logger.info(f"From google place api response: {response_data}")

        if (
            response_data.get("places")
            and response_data["places"][0].get("location")
            and response_data["places"][0]["location"].get("latitude")
            and response_data["places"][0]["location"].get("longitude")
        ):
            location = response_data["places"][0]["location"]
            lat_long_str = f"{location['latitude']}, {location['longitude']}"

    except HTTPException as e:
        logger.error(f"Error calling Google Place API: {e}")

    if lat_long_str:
        return lat_long_str

    # Fallback to Google Maps Geocoding API to get latitude and longitude for a given location name.
    url = "https://maps.googleapis.com/maps/api/geocode/json"

    params = {
        "address": location_description,
        "key": settings.GOOGLE_MAPS_API_KEY,
    }

    try:
        response_data = await make_get_request(url=url, params=params)
        if response_data["status"] == "OK":
            location = response_data["results"][0]["geometry"]["location"]
            lat_long_str = f"{location['lat']}, {location['lng']}"
            return lat_long_str
        else:
            logger.error(f"Error in Google Geo Code response: {response_data['status']}")
            return None

    except HTTPException as e:
        logger.error(f"Error calling Google Geo Code API: {e}")
        return None
