from datetime import datetime

import aiohttp

from server.utils.logger import logger
from server.utils.mongo_connector import google_maps_cache_collection
from server.utils.settings import settings


async def get_timezone_from_coordinates(lat: float, lng: float) -> str | None:
    """
    Get timezone ID from coordinates using Google Time Zone API

    Args:
        lat: Latitude
        lng: Longitude

    Returns:
        Timezone ID string (e.g., 'America/New_York') or None if error
    """
    try:
        mongo_doc = await google_maps_cache_collection.find_one({"lat": lat, "lng": lng})
        if mongo_doc and mongo_doc.get("timezone"):
            return mongo_doc["timezone"]

        timestamp = int(datetime.now().timestamp())

        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"https://maps.googleapis.com/maps/api/timezone/json?location={lat},{lng}&timestamp={timestamp}&key={settings.GOOGLE_MAPS_API_KEY}"
            ) as res:
                data = await res.json()

                if data.get("status") == "OK":
                    timezone_id = data.get("timeZoneId", None)
                    if timezone_id:
                        # Update the existing cache entry with timezone or create new one
                        await google_maps_cache_collection.update_one(
                            {"lat": lat, "lng": lng},
                            {"$set": {"timezone": timezone_id}},
                            upsert=True,
                        )
                        return timezone_id
                else:
                    logger.error(f"Google Time Zone API error: {data.get('status')}")
                    return None

    except Exception as e:
        logger.error(f"Error getting timezone from coordinates: {e}")
        return None
