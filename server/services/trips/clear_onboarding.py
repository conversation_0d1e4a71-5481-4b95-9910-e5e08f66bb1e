from typing import Callable

from sqlalchemy import delete

from server.database.models.checkpoint import Checkpoint
from server.utils.logger import logger
from server.utils.pg_connector import async_session


async def clear_state_data(onboarding_thread_id: int, post_cleanup: Callable | None = None) -> None:
    try:
        async with async_session() as session:
            async with session.begin():
                query = delete(Checkpoint).where(Checkpoint.thread_id == onboarding_thread_id)
                await session.execute(query)
        if post_cleanup:
            await post_cleanup()

    except Exception as e:
        logger.error(f"Error clearing state data: {e}")
