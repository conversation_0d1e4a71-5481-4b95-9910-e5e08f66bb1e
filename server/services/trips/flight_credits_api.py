from datetime import datetime, timedelta, timezone
from typing import Any

from server.utils.logger import logger
from server.utils.mongo_connector import flight_credits_collection
from server.utils.settings import settings
from server.utils.spotnana_api import spotnana_api


class FlightCreditsApi:
    async def sync_flight_credits(self, email: str):
        unused_credits_raw: dict[str, Any] = await self.fetch_unused_credits_from_spotnana(email)
        unused_credits: list[dict[str, Any]] = self.map_spotnana_results(unused_credits_raw)

        await self.save_to_mongo(email, unused_credits)

    async def fetch_unused_credits_from_spotnana(self, email: str):
        traveler_info = await spotnana_api.get_traveler_by_email(email)
        if results := traveler_info.get("results"):
            if user_id := results[0].get("userOrgId", {}).get("userId", {}).get("id"):
                res: dict[str, Any] = await spotnana_api.get_user_unused_credits(
                    {"travelerInfo": {"userId": {"id": user_id}}}
                )
                return res
        return {}

    def _map_unused_credit_info(self, credit_info):
        return {
            "airline": credit_info.get("airlineInfo", {}).get("airlineName"),
            "ticket_number": credit_info.get("ticketNumber"),
            "credits": {
                "amount": credit_info.get("totalFare", {}).get("amount"),
                "currency_code": credit_info.get("totalFare", {}).get("currencyCode"),
                "expire_date": credit_info.get("expiryDate", {}).get("iso8601"),
                "restrictions": [],
            },
            "extra": credit_info,
        }

    def map_spotnana_results(self, results: dict[str, Any]):
        unused_credits = results.get("unusedCreditInfo", [])
        return [self._map_unused_credit_info(credit_info) for credit_info in unused_credits]

    async def save_to_mongo(self, email: str, unused_credits: list[dict[str, Any]]):
        await flight_credits_collection.update_one(
            {"email": email},
            {"$set": {"unused_credits": unused_credits, "timestamp": datetime.now().isoformat()}},
            upsert=True,
        )

    async def get_user_unused_credits(self, email: str, ignore_cache: bool = False):
        mongo_results = await flight_credits_collection.find_one({"email": email}, {"_id": 0})

        timestamp: datetime | None = None
        if mongo_results is not None:
            timestamp = datetime.fromisoformat(mongo_results.get("timestamp"))

        cache_expired: bool = (
            mongo_results is None
            or timestamp is None
            or datetime.now() - timestamp > settings.SPOTNANA_UNUSED_CREDIT_CACHE_LIFESPAN
        )
        if cache_expired or ignore_cache:
            unused_credits_raw: dict[str, Any] = await self.fetch_unused_credits_from_spotnana(email)
            unused_credits: list[dict[str, Any]] = self.map_spotnana_results(unused_credits_raw)

            await self.save_to_mongo(email, unused_credits)

            return unused_credits

        return mongo_results.get("unused_credits")  # type: ignore

    async def invaliate_mongo_cache(self, email):
        logger.info(f"Invalidating flight credits cache for {email}")
        await flight_credits_collection.update_one(
            {"email": email},
            {"$set": {"timestamp": (datetime.now() - settings.SPOTNANA_UNUSED_CREDIT_CACHE_LIFESPAN).isoformat()}},
            upsert=True,
        )

    def group_credits_by_airline(self, unused_credits: list[dict[str, Any]]):
        grouped_credits_dict: dict[str, Any] = {}
        for credit_info in unused_credits:
            if credit_info.get("airline") not in grouped_credits_dict.keys():
                grouped_credits_dict[credit_info.get("airline", "")] = {
                    "label": credit_info.get("airline"),
                    "credits": [],
                }

            grouped_credits_dict[credit_info.get("airline", "")]["credits"].append(credit_info.get("credits"))

        return list(grouped_credits_dict.values())

    def map_user_profile_airline_credits(self, airline_credits: list[dict[str, Any]]):
        def _map_function(credit_info: dict[str, Any]):
            return {
                "airline_code": credit_info.get("extra", {}).get("airlineCode", ""),
                "airline_name": credit_info.get("airline", ""),
                "credit": {
                    "amount": credit_info.get("credits", {}).get("amount"),
                    "currency_code": credit_info.get("credits", {}).get("currency_code"),
                },
                "expire_date": credit_info.get("credits", {}).get("expire_date"),
                "expires_soon": datetime.fromisoformat(credit_info.get("credits", {}).get("expire_date"))
                - datetime.now(tz=timezone.utc)
                < timedelta(days=30),
            }

        return list(map(_map_function, airline_credits))


flight_credits_api = FlightCreditsApi()
