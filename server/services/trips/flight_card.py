from front_of_house_agent.flight_utils import get_timezone_from_iata_airport_code


def map_flight_stop(flight_stop):
    flight_stops = []
    total_fs_origin = len(
        [
            key
            for key in flight_stop
            if key.startswith("fs") and key.endswith("_origin") and flight_stop[key] is not None
        ]
    )

    for idx in range(total_fs_origin):
        # for each lay over...
        if flight_stop.get(f"fs{idx}_origin"):
            flight_stops.append(
                {
                    "origin_code": flight_stop.get(f"fs{idx}_origin"),
                    "origin_name": flight_stop.get(f"fs{idx}_origin_name"),
                    "destination_code": flight_stop.get(f"fs{idx}_destination"),
                    "destination_name": flight_stop.get(f"fs{idx}_destination_name"),
                    "airline_code": flight_stop.get(f"fs{idx}_airline_code"),
                    "airline_name": flight_stop.get(f"fs{idx}_airline_name"),
                    "operating_airline_code": flight_stop.get(f"fs{idx}_operating_airline_code"),
                    "operating_flight_number": flight_stop.get(f"fs{idx}_operating_flight_number"),
                    "aircraft_iata_code": flight_stop.get(f"fs{idx}_aircraft_iata_code"),
                    "aircraft_name": flight_stop.get(f"fs{idx}_aircraft_name"),
                    "flight_number": flight_stop.get(f"fs{idx}_flight_number"),
                    "duration": flight_stop.get(f"fs{idx}_duration"),
                    "cabin": flight_stop.get(f"fs{idx}_cabin"),
                    "booking_code": flight_stop.get(f"fs{idx}_booking_code"),
                    "departure": flight_stop.get(f"fs{idx}_departure_time"),
                    "departure_timezone": flight_stop.get(f"fs{idx}_depature_timezone")
                    or get_timezone_from_iata_airport_code(flight_stop.get(f"fs{idx}_origin")),
                    "arrival": flight_stop.get(f"fs{idx}_arrival_time"),
                    "arrival_timezone": flight_stop.get(f"fs{idx}_arrival_timezone")
                    or get_timezone_from_iata_airport_code(flight_stop.get(f"fs{idx}_destination")),
                    "seat": flight_stop.get(f"fs{idx}_seat_number"),
                    "confirmation": flight_stop.get(f"fs{idx}_vendor_confirmation_number"),
                }
            )

    if len(flight_stops) == 0:
        # backward compatibility
        flight_stops.append(
            {
                "origin_code": flight_stop.get("origin"),
                "origin_name": flight_stop.get("origin_name"),
                "destination_code": flight_stop.get("destination"),
                "destination_name": flight_stop.get("destination_name"),
                "airline_code": flight_stop.get("airline_code"),
                "airline_name": flight_stop.get("airline_name"),
                "aircraft_iata_code": flight_stop.get("aircraft_iata_code"),
                "aircraft_name": flight_stop.get("aircraft_name"),
                "flight_number": flight_stop.get("flight_number"),
                "duration": flight_stop.get("duration"),
                "cabin": flight_stop.get("cabin"),
                "booking_code": flight_stop.get("booking_code"),
                "departure": flight_stop.get("departure_time"),
                "departure_timezone": get_timezone_from_iata_airport_code(flight_stop.get("origin")),
                "arrival": flight_stop.get("arrival_time"),
                "arrival_timezone": get_timezone_from_iata_airport_code(flight_stop.get("destination")),
            }
        )

    return flight_stops


def map_flight_segment(flight_segment):
    flight_stops = map_flight_stop(flight_segment)
    first_flight_stop = flight_stops[0]
    last_flight_stop = flight_stops[-1]
    return {
        "origin_code": first_flight_stop.get("origin_code"),
        "origin_name": first_flight_stop.get("origin_name"),
        "destination_code": last_flight_stop.get("destination_code"),
        "destination_name": last_flight_stop.get("destination_name"),
        "flight_stops": flight_stops,
    }


def construct_flight_card_dict(
    option,
    is_outbound_flight_choices: bool = False,
    is_change_flight: bool = False,
    flight_search_type: str | None = None,
    current_segment_index: int | None = None,  # start from 0, None if not applicable
):
    change_prefix = "attempt change " if is_change_flight else ""
    action = ""
    if current_segment_index is not None:
        action = (
            f"For my {change_prefix}flight segment {current_segment_index + 1} "
            f"I choose flight id: {option.get('id_token_key')}."
        )
    else:
        action = (
            f"For my "
            f"{f'{change_prefix}outbound' if is_outbound_flight_choices else f'{change_prefix}return'} "
            f"I choose flight id: {option.get('id_token_key')}."
        )
    recommendation_reasons = (option.get("selection_reason", "") or "").splitlines()
    all_fares = [
        construct_flight_card_dict(
            fare, is_outbound_flight_choices, is_change_flight, flight_search_type, current_segment_index
        )
        for fare in option.get("all_fares") or []
    ]
    for fare in all_fares:
        if fare.get("id") == option.get("id_token_key"):
            fare["recommendationReasons"] = recommendation_reasons
    return {
        "img": {
            "alt": option.get("airline_code"),
            "src": None,  # we don't have it yet
        },
        "highlight": None,  # we don't have it yet
        "id": option.get("id_token_key"),
        "action": action,
        "cancelled": option.get("cancelled", None),
        "price": {
            "amount": f"{option.get('price', 0):.2f}",
            "currency": option.get("currency"),
        },
        "credits": {
            "amount": f"{option.get('credits', 0):.2f}",
            "currency": option.get("currency"),
        },
        "net_price": {
            "amount": f"{option.get('net_price', 0):.2f}",
            "currency": option.get("currency"),
        },
        "type": "CHANGE" if is_change_flight else flight_search_type,
        "cabin": option.get("cabin"),
        "seat": option.get("seat"),
        "booking_code": option.get("booking_code"),
        "total_distance_miles": option.get("total_distance_miles"),
        "total_duration": option.get("total_duration"),
        "within_policy": option.get("within_policy"),
        "within_or_out_policy_reason": option.get("within_or_out_policy_reason"),
        "baggage_policy": option.get("baggage_policy"),  # example: 1 free checked bag
        "exchange_policy": option.get("exchange_policy"),  # example: Changes for free with conditions
        "cancellation_policy": option.get("cancellation_policy"),  # example: Refunds for free with conditions
        "fare_option_name": option.get("fare_option_name"),  # example: "Refundable First Class"
        "flight_segments": [map_flight_segment(option)],
        "recommendationReasons": recommendation_reasons,
        "seat_selection_policy": option.get(
            "seat_selection_policy"
        ),  # example: Seat selection for free with conditions
        "boarding_policy": option.get("boarding_policy"),  # example: Priority boarding
        "operating_airline_code": option.get("operating_airline_code"),  # example: "AA"
        "operating_flight_number": option.get("operating_flight_number"),  # example: "1234"
        "keyword": option.get("keyword"),  # example: "cheapest"
        "is_red_eye": option.get("is_red_eye"),  # example: "true"
        "source": option.get("source"),  # example: "SABRE"
        "allFares": all_fares,
    }


def construct_flight_itinerary_dict(flights, confirmation_id):
    return {
        "outbound": construct_flight_card_dict(flights[0]),
        "return": construct_flight_card_dict(flights[1]) if len(flights) > 1 else None,
        "legs": [construct_flight_card_dict(flight) for flight in flights],
        "price_summary": {
            "base": {
                "amount": flights[-1].get("base_price"),
                "currency": flights[-1].get("base_currency"),
            },
            "tax": {
                "amount": flights[-1].get("tax_price"),
                "currency": flights[-1].get("tax_currency"),
            },
            "total_seat_price": {
                "amount": flights[-1].get("total_seat_price"),
                "currency": flights[-1].get("base_currency"),
            },
            "total": {
                "amount": flights[-1].get("price"),
                "currency": flights[-1].get("currency"),
            },
        },
        "confirmation_id": confirmation_id,
    }
