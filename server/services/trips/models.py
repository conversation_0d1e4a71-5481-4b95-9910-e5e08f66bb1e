from typing import List, Optional

from pydantic import BaseModel


class FlightStop(BaseModel):
    origin: str
    origin_name: str
    destination: str
    destination_name: str
    departure_time: str
    arrival_time: str
    airline_code: str
    airline_name: str
    flight_number: str
    duration: str
    cabin: str
    booking_code: Optional[str] = None
    aircraft_iata_code: Optional[str] = None
    aircraft_name: Optional[str] = None
    seat_number: Optional[str] = None
    vendor_confirmation_number: Optional[str] = None
    operating_airline_code: Optional[str] = None
    operating_flight_number: Optional[str] = None


class FlightLeg(BaseModel):
    stops: List[FlightStop]
    origin: str
    origin_name: str
    destination: str
    destination_name: str
    departure_time: str
    arrival_time: str
    airline_code: str
    airline_name: str
    flight_number: str
    duration: str
    cabin: str
    booking_code: Optional[str] = None
    price: float
    currency: str
    base_price: float
    base_currency: str
    tax_price: float
    tax_currency: str
    total_seat_price: Optional[float] = None
    fare_option_name: Optional[str] = None
    cancelled: bool = False


class FlightItinerary(BaseModel):
    legs: List[FlightLeg]

    def to_flight_option_dict_list(self) -> list[dict]:
        """It is mainlly used for backward compatibility"""
        flight_options = []
        for leg in self.legs:
            flight_option = {
                "origin": leg.origin,
                "origin_name": leg.origin_name,
                "destination": leg.destination,
                "destination_name": leg.destination_name,
                "departure_time": leg.departure_time,
                "arrival_time": leg.arrival_time,
                "airline_code": leg.airline_code,
                "airline_name": leg.airline_name,
                "flight_number": leg.flight_number,
                "duration": leg.duration,
                "booking_code": leg.booking_code,
                "cabin": leg.cabin,
                "price": leg.price,
                "currency": leg.currency,
                "base_price": leg.base_price,
                "base_currency": leg.base_currency,
                "tax_price": leg.tax_price,
                "tax_currency": leg.tax_currency,
                "fare_option_name": leg.fare_option_name,
                "total_seat_price": leg.total_seat_price,
                "cancelled": leg.cancelled,
            }

            for i, stop in enumerate(leg.stops):
                flight_option[f"fs{i}_origin"] = stop.origin
                flight_option[f"fs{i}_origin_name"] = stop.origin_name
                flight_option[f"fs{i}_destination"] = stop.destination
                flight_option[f"fs{i}_destination_name"] = stop.destination_name
                flight_option[f"fs{i}_airline_code"] = stop.airline_code
                flight_option[f"fs{i}_airline_name"] = stop.airline_name
                flight_option[f"fs{i}_flight_number"] = stop.flight_number
                flight_option[f"fs{i}_duration"] = stop.duration
                flight_option[f"fs{i}_booking_code"] = stop.booking_code
                flight_option[f"fs{i}_cabin"] = stop.cabin
                flight_option[f"fs{i}_departure_time"] = stop.departure_time
                flight_option[f"fs{i}_arrival_time"] = stop.arrival_time
                flight_option[f"fs{i}_aircraft_iata_code"] = stop.aircraft_iata_code
                flight_option[f"fs{i}_aircraft_name"] = stop.aircraft_name
                flight_option[f"fs{i}_operating_airline_code"] = stop.operating_airline_code
                flight_option[f"fs{i}_operating_flight_number"] = stop.operating_flight_number
                flight_option[f"fs{i}_seat_number"] = stop.seat_number
                flight_option[f"fs{i}_vendor_confirmation_number"] = stop.vendor_confirmation_number

            flight_options.append(flight_option)

        return flight_options
