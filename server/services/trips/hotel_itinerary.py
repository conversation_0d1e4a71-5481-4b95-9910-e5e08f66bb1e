from typing import Any

from server.cron.check_hotel_status import CheckHotelStatus
from server.utils.logger import logger


async def get_hotel_itinerary(hotel_details: dict[str, Any]):
    reservation_number: str | None = None
    try:
        check_hotel_status_api = CheckHotelStatus()

        reservation_number = hotel_details.get("reservation_number")
        if reservation_number is not None:
            trip_details = await check_hotel_status_api.get_hotel_order_details([int(reservation_number)])

            if (
                len(trip_details) == 1
                and trip_details[0].get("status", "") in check_hotel_status_api.CANCELLED_STATUSES
            ):
                hotel_details["status"] = "cancelled"
    except Exception as e:
        logger.warning(
            f"Failed to check hotel status, reservation id {reservation_number or '<no reservation_number>'}, error: {e}"
        )

    return hotel_details
