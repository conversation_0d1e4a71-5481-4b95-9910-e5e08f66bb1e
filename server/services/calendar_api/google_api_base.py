from typing import Any, Dict, Optional

import jwt
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

from server.utils.settings import settings


class GoogleApiBase:
    def __init__(self, refresh_token: str) -> None:
        self.refresh_token = refresh_token
        self.client_id = settings.GOOGLE_CLIENT_ID
        self.mobile_client_id = settings.GOOGLE_MOBILE_CLIENT_ID
        self.client_secret = settings.GOOGLE_CLIENT_SECRET
        self.creds = self.init_credentials()
        self._id_token_claims: Optional[Dict[str, Any]] = None

    def init_credentials(self, client_id: str | None = None) -> Credentials:
        client_secret = ""
        if client_id is None:
            client_id = self.client_id
            client_secret = self.client_secret

        return Credentials(
            token=None,
            refresh_token=self.refresh_token,
            token_uri="https://www.googleapis.com/oauth2/v3/token",
            client_id=client_id,
            client_secret=client_secret,
        )

    def refresh_credentials(self) -> None:
        try:
            self.creds.refresh(Request())
            # Extract ID token claims after successful refresh
            self._extract_id_token_claims()
        except Exception as e:
            # Log the exception if needed
            print(f"Error refreshing credentials with client_id {self.client_id}: {e}")
            # Reinitialize credentials with mobile_client_id if refresh fails
            self.creds = self.init_credentials(client_id=self.mobile_client_id)
            self.creds.refresh(Request())
            # Extract ID token claims after successful refresh
            self._extract_id_token_claims()

    def _extract_id_token_claims(self) -> None:
        """Extract claims from ID token if present"""
        if hasattr(self.creds, "id_token") and self.creds.id_token:
            try:
                # Decode without verification (Google's tokens are already verified)
                self._id_token_claims = jwt.decode(self.creds.id_token, options={"verify_signature": False})
            except Exception as e:
                print(f"Error decoding ID token: {e}")
                self._id_token_claims = None

    def get_sub(self) -> Optional[str]:
        """Get the 'sub' (subject identifier) from the ID token"""
        if self._id_token_claims:
            return self._id_token_claims.get("sub")
        return None

    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get all user info from the ID token"""
        return self._id_token_claims

    def authorize(self) -> None:
        if self.creds and self.creds.refresh_token and (self.creds.expired or self.creds.token is None):
            self.refresh_credentials()
