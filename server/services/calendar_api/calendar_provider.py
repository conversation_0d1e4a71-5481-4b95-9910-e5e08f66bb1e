import traceback
from datetime import datetime
from typing import Any, Dict, List, Optional

from server.database.models.user_profile import UserProfile
from server.services.calendar_api.google_calendar_events import GoogleCalendarEvents
from server.services.calendar_api.microsoft_calendar_events import MicrosoftCalendarEvents
from server.utils.logger import logger
from server.utils.settings import settings


class CalendarProviderManager:
    """
    Manages different calendar providers and provides a unified interface
    for calendar operations across different authentication methods.
    """

    # Supported calendar providers
    PROVIDERS = {"Google": GoogleCalendarEvents, "Microsoft": MicrosoftCalendarEvents}

    def __init__(self, user_profile: UserProfile, user_email=None):
        """
        Initialize the calendar provider manager
        Stores active provider instances
        """

        provider = user_profile.last_login_method
        self.user_profile = user_profile

        if provider in self.PROVIDERS:
            provider_class = self.PROVIDERS[provider]
            self._provider_instances = provider_class(refresh_token=user_profile.refresh_token, user_email=user_email)

    def extract_relevant_fields(self, events) -> List[Dict]:
        if not hasattr(self, "_provider_instances"):
            return []

        if self._provider_instances:
            return self._provider_instances.extract_relevant_fields(events)
        return []

    def has_calendar_access(self) -> bool:
        if not hasattr(self, "_provider_instances"):
            return False

        if not self.user_profile.calendar_enabled:
            return False

        if self._provider_instances:
            return self._provider_instances.has_calendar_access()
        return False

    async def get_events(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        keywords: Optional[str] = settings.GOOGLE_CALENDAR_EVENTS_KEYWORDS,
    ) -> List[Dict]:
        """
        Retrieve events from a specific or all authenticated providers

        :param provider: Specific provider to retrieve events from
        :param start_date: Start of date range
        :param end_date: End of date range
        :param keywords: Keywords to filter events
        :return: List of events
        """

        try:
            # Use default dates if not provided
            if start_date is None:
                start_date = datetime.now().replace(day=1)
            if end_date is None:
                end_date = datetime.now()

            # Retrieve events from the provider
            provider_events = await self._provider_instances.get_events(
                start_date=start_date, end_date=end_date, keywords=keywords
            )

            return provider_events

        except Exception as e:
            logger.error(
                f"Failed to retrieve calendar events for users_id={self.user_profile.users_id} from {self.user_profile.last_login_method}: {e}, stacktrace: {traceback.format_exc()}"
            )

        return []

    def create_event(
        self,
        title: str,
        date_start: datetime,
        date_end: datetime,
        description: str = "",
        timezone_start: Optional[str] = None,
        timezone_end: Optional[str] = None,
        location: Optional[str] = None,
    ) -> Optional[str]:
        """
        Create an event in a specific provider's calendar

        :param provider: Calendar provider
        :param title: Event title
        :param date_start: Start datetime
        :param date_end: End datetime
        :param description: Event description
        :param timezone_start: Start timezone
        :param timezone_end: End timezone
        :param location: Location
        :return: Event ID or None
        """

        try:
            return self._provider_instances.create_event(
                title=title,
                date_start=date_start,
                date_end=date_end,
                description=description,
                timezone_start=timezone_start,
                timezone_end=timezone_end,
                location=location,
            )
        except Exception:
            # logger.error(f"Event creation failed for {provider}: {e}")
            return None

    def update_event(self, event_id: str, updates: Dict[str, Any]):
        """
        Update an existing event in a specific provider's calendar

        :param provider: Calendar provider
        :param event_id: ID of the event to update
        :param updates: Dictionary of updates to apply
        """

        try:
            self._provider_instances.update_event(event_id=event_id, updates=updates)
        except Exception:
            pass
            # logger.error(f"Event update failed for {provider}: {e}")

    def search_event(self, param):
        return self._provider_instances.search_event(param)

    def delete_event(self, event_id):
        self._provider_instances.delete_event(event_id)

    def update_event_overwrite(
        self,
        event_id: str,
        title: str,
        date_start: datetime,
        date_end: datetime,
        description: str = "",
        timezone_start: str | None = None,
        timezone_end: str | None = None,
    ):
        self._provider_instances.update_event_overwrite(
            event_id, title, date_start, date_end, description, timezone_start, timezone_end
        )
