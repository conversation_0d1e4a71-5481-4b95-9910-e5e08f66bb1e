import asyncio
from typing import Set

from server.utils.logger import logger


class GlobalTaskManager:
    _active_tasks: Set[asyncio.Task] = set()
    _is_shutting_down: bool = False

    @classmethod
    def register_task(cls, task: asyncio.Task) -> bool:
        if cls._is_shutting_down:
            task.cancel()
            return False

        cls._active_tasks.add(task)

        def _remove_task(completed_task):
            cls._active_tasks.discard(completed_task)

        task.add_done_callback(_remove_task)
        return True

    @classmethod
    async def shutdown(cls, grace_period: int = 60):
        logger.info("Shutting down ongoing tasks")
        cls._is_shutting_down = True

        if not cls._active_tasks:
            logger.info("No tasks, shutdown immediately")
            return

        active_task_count = len(cls._active_tasks)
        if active_task_count > 0:
            logger.info(f"Waiting for {active_task_count} tasks to complete (grace period: {grace_period}s)...")

            try:
                # wait for all tasks to complete
                _, pending = await asyncio.wait(
                    cls._active_tasks, timeout=grace_period, return_when=asyncio.ALL_COMPLETED
                )

                # if there are still tasks that did not complete
                if pending:
                    remaining_count = len(pending)
                    task_ids = [task.get_name() for task in pending]
                    logger.warning(
                        f"{remaining_count} tasks did not complete within grace period, forcing cancellation, task_ids: {task_ids}"
                    )

                    # cancel all tasks that did not complete
                    for task in pending:
                        if not task.done():
                            task.cancel()

                    # wait for the cancelled tasks to complete for another grace period
                    _, forced_pending = await asyncio.wait(pending, timeout=5, return_when=asyncio.ALL_COMPLETED)
                    if forced_pending:
                        task_ids = [task.get_name() for task in forced_pending]
                        logger.warning(
                            f"{len(forced_pending)} tasks did not cancelled within 5 seconds after forced cancellation, skipping, task_ids: {task_ids}"
                        )
                else:
                    logger.info("All tasks completed gracefully")

            except Exception as e:
                logger.error(f"Error during graceful shutdown: {e}")

        cls._active_tasks.clear()
        logger.info("Task shutdown completed")
