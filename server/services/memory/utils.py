from datetime import datetime
from enum import Enum
from zoneinfo import ZoneInfo

from flight_agent.flight_data import FlightInfo
from server.database.models.bookings import Booking
from server.utils.logger import logger


class DateTimeFormatter:
    @staticmethod
    def format_datetime(datetime_str: str | None = None, timezone: str | None = None) -> str:
        """Convert ISO datetime string to formatted date and time.

        Args:
            datetime_str (str): ISO format datetime string (e.g. "2024-12-28T15:30:00Z")
            timezone (str | None): IANA timezone string (e.g. "America/New_York") or None

        Returns:
            str: Formatted date string (e.g. "Dec 28, 2024")
        """
        if not datetime_str:
            return ""
        dt = datetime.fromisoformat(datetime_str.replace("Z", "+00:00"))
        if timezone:
            dt = dt.astimezone(ZoneInfo(timezone))
        return dt.strftime("%b %d, %Y")


class BookingOperation(Enum):
    EXCHANGE = "Updated"
    CANCELLED = "Cancelled"
    BOOKED = "Booked"


class FlightDirection(Enum):
    OUTBOUND = "Outbound"
    RETURN = "Return"


class FlightSearchMemoryFormatter:
    @staticmethod
    def format(chosen_flight: dict, direction: FlightDirection) -> str:
        message = f"The {direction} flight from {chosen_flight.get('origin_name')}({chosen_flight.get('origin')}) to {chosen_flight.get('destination_name')}({chosen_flight.get('destination')})\n"
        message += f"Departure: {DateTimeFormatter.format_datetime(chosen_flight.get('departure_time', None), chosen_flight.get('departure_timezone', None))}\n"
        message += f"Arrival: {DateTimeFormatter.format_datetime(chosen_flight.get('arrival_time', None), chosen_flight.get('arrival_timezone', None))} with price {chosen_flight.get('price')}\n"
        message += (
            f"Airline: {chosen_flight.get('airline_name')} with flight number {chosen_flight.get('flight_number')}\n"
        )
        return message


class FlightBookingMemoryFormatter:
    @staticmethod
    def format(booking: dict, operation: BookingOperation | None = None) -> str:
        result_messages = ""
        outbound_flight_info = None
        return_flight_info = None

        if booking.get("outbound"):
            outbound_flight_info = FlightInfo.from_flight_data(booking.get("outbound") or {})
        if booking.get("return"):
            return_flight_info = FlightInfo.from_flight_data(booking.get("return") or {})

        # Common booking information
        booking_info = f"Your flight with confirmation id: {booking.get('confirmation_id')} and airline confirmation number: {booking.get('airline_confirmation_number')}"
        # Outbound flight details
        if outbound_flight_info:
            outbound_message = f"{booking_info}\n\n"
            outbound_message += (
                f"The Outbound Flight From Origin Airport {outbound_flight_info.origin_name}({outbound_flight_info.origin_code}) to Destination Airport {outbound_flight_info.destination_name}({outbound_flight_info.destination_code})\n"
                f"Flight Numbers: {', '.join(outbound_flight_info.flight_numbers)}\n"
                f"Departure: {DateTimeFormatter.format_datetime(outbound_flight_info.departure_time, outbound_flight_info.departure_timezone)}\n"
                f"Arrival: {DateTimeFormatter.format_datetime(outbound_flight_info.arrival_time, outbound_flight_info.arrival_timezone)} with price {outbound_flight_info.price_string}"
            )
            if operation:
                outbound_message += f" has been {operation.value}"
            result_messages += outbound_message

        # Return flight details
        if return_flight_info:
            return_message = f"{booking_info}\n\n"
            return_message += (
                f"The Return Flight From Origin Airport {return_flight_info.origin_name}({return_flight_info.origin_code}) to Destination Airport {return_flight_info.destination_name}({return_flight_info.destination_code})\n"
                f"Flight Numbers: {', '.join(return_flight_info.flight_numbers)}\n"
                f"Departure: {DateTimeFormatter.format_datetime(return_flight_info.departure_time, return_flight_info.departure_timezone)}\n"
                f"Arrival: {DateTimeFormatter.format_datetime(return_flight_info.arrival_time, return_flight_info.arrival_timezone)} with price {return_flight_info.price_string}"
            )
            if operation:
                return_message += f" has been {operation.value}"
            result_messages += return_message

        logger.info(f"FlightBookingMemoryFormatter.format_flight_booking: {result_messages}")
        return result_messages


class AccommodationBookingMemoryFormatter:
    @staticmethod
    def format(
        booking_raw: Booking,
        operation: BookingOperation | None = None,
    ) -> str:
        booking = booking_raw.content
        if not booking:
            return ""

        message = f"Accommodation reservation number: {booking.get('reservation_number')} and order number: {booking.get('order_number')}\n\n"
        hotel_name = booking.get("hotel")

        address = booking.get("mapMarker", {}).get("address")
        room_info = booking.get("room", {})
        room_info_str = f"Number of Nights: {room_info['no_nights']}, Price: {room_info['price']}"

        # Handle check-in/out datetimes
        check_in = f"{booking.get('check_in_date', '')} {booking.get('check_in_time', '')}"
        check_out = f"{booking.get('check_out_date', '')} {booking.get('check_out_time', '')}"

        message += f"The hotel {hotel_name} has been booked\n\n"
        message += f"Address: {address}\n\n"
        message += f"Room: {room_info_str}\n\n"

        if check_in:
            message += f"Check in: {check_in}\n\n"
        if check_out:
            message += f"Check out: {check_out}\n\n"

        if operation:
            message += f" has been {operation.value}"

        return message
