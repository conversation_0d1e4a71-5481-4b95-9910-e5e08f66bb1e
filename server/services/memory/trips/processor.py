import asyncio
import threading
import uuid
from dataclasses import dataclass
from typing import ClassV<PERSON>, Dict, Final, List, Optional

from pydantic import BaseModel

from server.services.memory.memory_store import MemoryIsolation<PERSON>eys, MemoryStore, MessageRole, UserMemories
from server.utils.logger import logger


@dataclass
class Task:
    """Represents a task to be processed by the memory processor"""

    task_id: str
    user_id: str
    memory_group: MemoryIsolationKeys
    thread_id: str
    message: str
    role: MessageRole
    memory_isolation_key: str
    metadata: Optional[dict] = None
    custom_fact_extract_prompt: Optional[str] = None


class TripContext(BaseModel):
    memory: str
    metadata: dict


class TripContextMemory(BaseModel):
    trip_context: TripContext
    associated_memories: List[str]


class TripMemoryProcessor:
    _instance: ClassVar[Optional["TripMemoryProcessor"]] = None
    _lock: ClassVar[threading.Lock] = threading.Lock()
    TRIP_GRAPH_ESTABLISHMENT_SCORE_THRESHOLD: Final[float] = (
        0.4  # the maximum relevance score for considering whether a memory is relevant to the current trip, score more than this threshold will not be associated.
    )
    USER_QUERY_SCORE_THRESHOLD: Final[float] = 0.6  # score threshold, for resolving user query
    TRIP_PLAN_MEM_ID_FIELD: Final[str] = "trip_plan_mem_id"

    def __init__(self):
        if TripMemoryProcessor._instance is not None:
            raise RuntimeError("Use TripMemoryProcessor.get_instance() instead")

        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.is_running: bool = False
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._task: Optional[asyncio.Task] = None
        self.memory_stores: Dict[str, MemoryStore] = {}

    @classmethod
    def get_instance(cls) -> "TripMemoryProcessor":
        """Get or create the singleton instance of TripMemoryProcessor"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = cls()
                cls._instance.start()
        return cls._instance

    @classmethod
    async def shutdown(cls) -> None:
        """Shutdown the singleton instance"""
        if cls._instance is not None:
            await cls._instance.stop()
            cls._instance = None

    def get_memory_store(self, custom_prompt: Optional[str] = None) -> MemoryStore:
        """Get or create a memory store for a specific prompt"""
        prompt_key = custom_prompt or "default"
        if prompt_key not in self.memory_stores:
            self.memory_stores[prompt_key] = MemoryStore(fact_retrieval_prompt=custom_prompt)
        return self.memory_stores[prompt_key]

    async def process_task(self, task: Task) -> None:
        """Process memory storage tasks"""
        try:
            memory_store = self.get_memory_store(task.custom_fact_extract_prompt)

            metadata = task.metadata or {}

            if (
                task.memory_group == MemoryIsolationKeys.FLIGHT_SELECTION
                or task.memory_group == MemoryIsolationKeys.FLIGHT_BOOKING
            ):
                memories = self._find_trip_plans(user_id=task.user_id, thread_id=task.thread_id, query=task.message)

                if memories:
                    for memory in memories:
                        logger.info(f"Found trip plan memory: {memory}")
                        metadata[self.TRIP_PLAN_MEM_ID_FIELD] = memory.get("id")

            logger.info(f"Task ID: {task.task_id}, Storing memory for: {task.memory_group} with metadata: {metadata}")
            if task.metadata:
                memory_store.store_memory_with_metadata(
                    user_id=task.user_id,
                    role=task.role,
                    message=task.message,
                    metadata=metadata,
                    memory_isolation_key=task.memory_isolation_key,
                    thread_id=task.thread_id,
                )
            else:
                memory_store.store_memory(
                    user_id=task.user_id,
                    role=task.role,
                    message=task.message,
                    memory_isolation_key=task.memory_isolation_key,
                    thread_id=task.thread_id,
                )
        except Exception as e:
            logger.error(f"Error processing memory task: {e}")

    async def search_trip_memories(
        self,
        user_id: str,
        thread_id: str,
        query: str,
        relevance_score_threshold: float = USER_QUERY_SCORE_THRESHOLD,
        limit: int = 3,
    ) -> List[TripContextMemory]:
        """Search for trip memories based on the query and relevance score threshold"""
        try:
            filtered_results = self._find_trip_plans(
                user_id=user_id,
                thread_id=thread_id,
                limit=limit,
                query=query,
                relevance_score_threshold=relevance_score_threshold,
            )

            trip_plans = []
            for result in filtered_results:
                trip_context_memory = result.get("memory", "")
                trip_plan_mem_id = result.get("id", "")
                trip_plan_memories = await self.get_memory_store().get_memories_by_json_field(
                    field=self.TRIP_PLAN_MEM_ID_FIELD, value=trip_plan_mem_id
                )

                trip_context = TripContext(
                    memory=trip_context_memory,
                    metadata=result.get("metadata", {}),
                )
                trip_context_memory = TripContextMemory(
                    trip_context=trip_context,
                    associated_memories=self._build_trip_associated_memories(trip_plan_memories),
                )
                trip_plans.append(trip_context_memory)

            return trip_plans
        except Exception as e:
            logger.error(f"Error getting relevant trip context: {e}")
            return []

    def _build_trip_associated_memories(self, memories: List[UserMemories]) -> List[str]:
        associated_memories = []
        for memory in memories:
            if memory.payload.get("type") == MemoryIsolationKeys.FLIGHT_SELECTION.name:
                mem_str = f"User was considering: {memory.payload.get('data', {})}"
                associated_memories.append(mem_str)

        return associated_memories

    def _find_trip_plans(
        self,
        user_id: str,
        thread_id: str,
        query: str,
        limit: int = 3,
        relevance_score_threshold: float = TRIP_GRAPH_ESTABLISHMENT_SCORE_THRESHOLD,
    ) -> List:
        """Retrieve relevant previous conversations based on the current query"""
        try:
            results = (
                self.get_memory_store()
                .search_relevant_context(
                    user_id=user_id,
                    thread_id=thread_id,
                    query=query,
                    additional_filters={"type": MemoryIsolationKeys.TRIP_PLAN.name},
                    limit=limit,
                )
                .get("results", [])
            )

            # Filter scores < RELEVANCE_SCORE_THRESHOLD and sort by score in ascending order
            filtered_results = sorted(
                [r for r in results if r.get("score", 1.0) < relevance_score_threshold],
                key=lambda x: x.get("score", 1.0),
            )

            return filtered_results
        except Exception as e:
            logger.error(f"Error retrieving memory: {str(e)}")
            return []

    async def _run_loop(self) -> None:
        """Main processing loop for async tasks"""
        try:
            while self.is_running:
                task = await self.task_queue.get()
                try:
                    await self.process_task(task)
                except Exception as e:
                    logger.error(f"Error processing task: {e}")
                finally:
                    self.task_queue.task_done()
        except Exception as e:
            logger.error(f"Error in memory processor loop: {e}")
            pass

    def start(self) -> None:
        """Start the memory processor"""
        if self.is_running:
            return

        self.is_running = True
        self._loop = asyncio.get_event_loop()
        self._task = self._loop.create_task(self._run_loop())

    async def stop(self) -> None:
        """Stop the memory processor"""
        if not self.is_running:
            return

        self.is_running = False
        if self._task:
            await self.task_queue.join()
            self._task.cancel()
            await asyncio.gather(self._task, return_exceptions=True)
            self._task = None

    async def store_memory(
        self,
        user_id: str,
        memory_group: MemoryIsolationKeys,
        message: str,
        thread_id: str,
        memory_isolation_key: str,
        metadata: Optional[dict] = None,
        custom_fact_extract_prompt: Optional[str] = None,
    ) -> None:
        """Helper method to store memory (async)"""
        task = Task(
            task_id=str(uuid.uuid4()),
            user_id=user_id,
            memory_group=memory_group,
            thread_id=thread_id,
            message=message,
            role=MessageRole.ASSISTANT,
            memory_isolation_key=memory_isolation_key,
            metadata=metadata,
            custom_fact_extract_prompt=custom_fact_extract_prompt,
        )
        await self.task_queue.put(task)
