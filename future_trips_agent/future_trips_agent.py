import itertools
import json
import re
from datetime import datetime, timedelta, timezone
from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
)
from langchain_core.runnables.config import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.graph.graph import CompiledGraph

from baml_client import b
from future_trips_agent.models import AgentState
from future_trips_agent.prompts import (
    future_trips_agent_analysis_prompt,
    future_trips_agent_system_prompt,
    future_trips_exit_string,
    future_trips_traveler_prod_string,
    opening_string,
)
from llm_utils.llm_utils import get_message_buffer_as_strings
from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.calendar_api.google_calendar_events import GoogleCalendarEvents
from server.utils.logger import logger
from server.utils.mongo_connector import google_calendar_events_collection
from server.utils.settings import settings
from server.utils.websocket_no_op import partial_no_op
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import Timings

config: RunnableConfig = {"configurable": {"recursion_limit": 10}}


class TravelAgentFutureTrips:
    def __init__(
        self,
        user: User,
        thread_id: int,
        websocket_send_message: partial[Coroutine[Any, Any, None]] | None,
        is_onboarding: bool = False,
    ):
        self.websocket_send_message = websocket_send_message if websocket_send_message is not None else partial_no_op

        self.user: User = user
        self.thread_id = thread_id
        self.travel_calendar_events = []
        self.is_onboarding = is_onboarding

        # Add memory
        self.history = PostgresChatMessageHistory(thread_id=thread_id)

        self.selected_calendar_events: list[dict] | None = None
        self.messages: list[BaseMessage] = []

        # flag that can be polled by the outside world about whether the
        # conversation is in-process.
        self.running = True
        self.do_calendar_analysis = False

        # Define a new graph
        self.workflow: StateGraph = StateGraph(AgentState)

        # Define the two nodes we will cycle between
        self.workflow.add_node("future_trips_agent", self.future_trips_runnable_function)

        self.workflow.add_node("supervisor", self.supervisor)

        # The supervisor always leads to the future_trips_agent
        self.workflow.add_edge("supervisor", "future_trips_agent")

        # We now add a conditional edge
        self.workflow.add_conditional_edges(
            # First, we define the start node. We use `preferences_agent `.
            # This means these are the edges taken after the `agent` node is
            # called.
            "future_trips_agent",
            # Next, we pass in the function that will determine which node is
            # called next.
            self.handle_next_message,
            {
                "end": END,
            },
        )

        # Set the entrypoint as `supervisor`
        # This means that this node is the first one called
        self.workflow.set_entry_point("supervisor")

        # This compiles it into a LangChain Runnable,
        # meaning you can use it as you would any other runnable
        self.graph: CompiledGraph = self.workflow.compile()

    async def future_trips_runnable_function(self, state):
        message_buffer = state["messages"]
        message_buffer_strs = get_message_buffer_as_strings(message_buffer)

        show_event_cards = False

        if self.do_calendar_analysis:
            calendar_events_str = json.dumps(self.travel_calendar_events)
            for event in self.travel_calendar_events:
                logger.info(
                    f"[FutureTripAgent] calendar analysis: \n{json.dumps(event)}",
                    mask="\033[91m {}\033[00m",
                )
            self.do_calendar_analysis = False
            show_event_cards = True  # We'll have fresh events

            t = Timings("BAML: ConverseFutureTrips")
            response = await b.ConverseFutureTrips(
                prompt=future_trips_agent_analysis_prompt,
                cal_events=calendar_events_str,
                messages=message_buffer_strs,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("purple")
            logger.log_baml()
        else:
            t = Timings("BAML: ConverseFutureTrips")
            response = await b.ConverseFutureTrips(
                prompt=future_trips_agent_system_prompt,
                cal_events=None,
                messages=message_buffer_strs,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("purple")
            logger.log_baml()

        # JTB - The response class isn't JSON, it is a regular class so we need
        # to doctor a bit so the outside world knows what to do with the data
        new_message = AIMessage(content="")
        json_representation = response.model_dump_json()
        new_message.additional_kwargs = {"function_call": {"arguments": json_representation}}

        if show_event_cards:
            new_message.additional_kwargs["show_event_cards"] = True

        return {"messages": [new_message]}

    def handle_next_message(self, state):
        messages = state["messages"]
        # Last response from the model
        last_message = messages[-1]
        logger.info(last_message, mask="\033[93m {}\033[00m")

        arguments_string = last_message.additional_kwargs["function_call"]["arguments"]
        arguments_obj = json.loads(arguments_string)
        # Check to see if the conversation is done
        if arguments_obj["is_conversation_finished"]:  # or len(arguments_obj.get("selected_calendar_events", [])) > 0:
            self.running = False
            # Hijack the exit message
            arguments_obj["agent_response"] = future_trips_exit_string.format(user_name=self.user.name).strip()
            last_message.additional_kwargs["function_call"]["arguments"] = json.dumps(arguments_obj)
            self.selected_calendar_events = arguments_obj.get("selected_calendar_events", [])

        last_message.content = arguments_obj["agent_response"]

        return "end"

    def supervisor(self, state):
        return {"messages": [state["input"]]}

    async def run(self, message=None, message_type="text", extra_payload=None):
        to_send: list[dict[str, str | bool | None]] = []

        # Onboarding init
        if message is None:
            to_send = await self.get_history_messages()

            # The messages list is empty, send the opening message
            if len(self.messages) == 0:
                if self.is_onboarding:
                    await self.load_future_google_calendar_events()
                else:
                    await self.load_events_from_database()

                # The messages list is empty, send the opening messages
                await self.add_timestamp_message(send_ws_message=True)

                agent_response = AIMessage(content=opening_string.format(user_name=self.user.name).strip())
                self.history.add_pending_message(agent_response)
                self.messages.append(agent_response)
                await self.websocket_send_message(
                    message={
                        **self.map_websocket_message(agent_response)[0],
                        "expectResponse": False,
                    }
                )

                # Stimulate the model to do an analysis of the events
                agent_response = HumanMessage(content=future_trips_traveler_prod_string)
                agent_response.additional_kwargs["message_type"] = "silent_prompt"
                self.history.add_pending_message(agent_response)
                self.messages.append(agent_response)  # JTB 2024-08-24 - Questioning whether this is neccesary

            elif isinstance(self.messages[-1], AIMessage):
                # In the previous session we were waiting for user input,
                # return the entire history from previous session
                to_send[-1]["expectResponse"] = True
                return to_send
            else:
                # In the previous session we were waiting for LLM, wait for LLM
                # new message then return it with the entire history from
                # previous session
                agent_response = self.messages.pop()

                history_message = {
                    "type": "history",
                    "messages": [{**message, "is_history": True} for message in to_send],
                }

                await self.websocket_send_message(message=history_message)
                to_send = []
        else:
            if message_type == "update":
                agent_response = HumanMessage(
                    content=", ".join(map(str, message)),
                    additional_kwargs={"is_card_update": True},
                )  # noq
            elif message_type == "silent_prompt":
                agent_response = HumanMessage(content=message)
                agent_response.additional_kwargs["message_type"] = message_type
            else:
                agent_response = HumanMessage(content=message)
            self.history.add_pending_message(agent_response)
            self.messages.append(agent_response)  # JTB 2024-08-24 - Questioning whether this is neccesary

        response = await self.graph.ainvoke({"messages": list(self.messages), "input": agent_response}, config=config)

        lastest_model_message = response["messages"][-1]
        self.history.add_pending_message(lastest_model_message)
        self.messages.append(lastest_model_message)

        to_send += self.map_websocket_message(lastest_model_message)

        if not self.running:
            to_send.append(
                {
                    "type": "future_trips_exit",
                    "expectResponse": True,
                }
            )

            logger.info(f"SELECTED CALENDAR EVENTS: {self.selected_calendar_events}")
            if self.selected_calendar_events:
                await self.create_trips_from_events(self.selected_calendar_events)

        return to_send

    def construct_event_dict(self, event):
        city = event.get("city", "")
        start_date = event.get("start_date", "")
        end_date = event.get("end_date", "")
        event_id = f"{city} - {start_date} to {end_date}"

        return {
            "id": event_id,
            "city": city,
            "startDate": start_date,
            "endDate": end_date,
            "action": f"Create trip from event {event_id}",
        }

    def construct_event_str(self, event):
        city = event.get("city", "")
        start_date = (
            datetime.strptime(event.get("start_date", None), "%Y-%m-%d").strftime("%B %-d")
            if event.get("start_date", None) is not None
            else ""
        )
        end_date = (
            datetime.strptime(event.get("end_date", None), "%Y-%m-%d").strftime("%B %-d")
            if event.get("end_date", None) is not None
            else ""
        )

        return f"""**{city}**&NewLine;{start_date} - {end_date}"""

    def construct_message_dict(self, message: BaseMessage, content: str, event_list: list[dict] = []):
        return {
            "type": message.additional_kwargs.get("message_type", "prompt"),
            "text": content,
            "events": event_list or [],
            "expectResponse": True,
            "isBotMessage": isinstance(message, AIMessage),
            "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                message.additional_kwargs.get("agent_classification", ""), None
            ),
        }

    def map_websocket_message(self, message: BaseMessage):
        messages = []

        # Extract arguments if present
        arguments_string = message.additional_kwargs.get("function_call", {}).get("arguments", "")
        if arguments_string:
            arguments_obj = json.loads(arguments_string)

            if message.additional_kwargs.get("show_event_cards", False) and (
                self.is_onboarding or len(arguments_obj.get("base_calendar_events", [])) >= 3
            ):
                events = [self.construct_event_dict(event) for event in arguments_obj.get("base_calendar_events", [])]
                messages.append(self.construct_message_dict(message, str(message.content).strip(), events))
            elif message.additional_kwargs.get("show_event_cards", False) and self.selected_calendar_events is not None:
                events = [self.construct_event_str(event) for event in self.selected_calendar_events]
                events_str = "&NewLine;&NewLine;".join(events)
                events_message = f"""Selected events:&NewLine;{events_str}"""

                messages.append(self.construct_message_dict(message, content=events_message))
            else:
                agent_response = arguments_obj.get("agent_response", "Response not found.")
                messages.append(self.construct_message_dict(message, agent_response))
        else:
            messages.append(self.construct_message_dict(message, str(message.content)))

        return messages

    async def add_timestamp_message(self, new_timestamp: str | None = None, send_ws_message: bool = False):
        if new_timestamp is None:
            new_timestamp = datetime.now(timezone.utc).isoformat()

        if datetime.fromisoformat(new_timestamp) - self.history.max_created_date > settings.MIN_MESSAGE_TIMESTAMP_DELTA:
            timestamp_message = AIMessage(content="", additional_kwargs={"timestamp": new_timestamp})
            self.history.add_pending_message(timestamp_message)

            if send_ws_message:
                await self.websocket_send_message(
                    message={
                        "type": "prompt",
                        "timestamp": timestamp_message.additional_kwargs.get("timestamp"),
                    }
                )

    async def load_future_google_calendar_events(self):
        today = datetime.now()
        twelve_months_in_future = today + timedelta(days=365)

        user_profile: UserProfile | None = await UserProfile.from_user_id(self.user.id)
        assert user_profile is not None, "User profile should not be none when FutureTripsAgent is initialized"
        google_calendar_api = CalendarProviderManager(user_profile=user_profile)
        events = google_calendar_api.get_events(
            today, twelve_months_in_future, keywords=""
        )  # JTB:  Note the empty string being passed in to NOT filter by any keywords
        self.travel_calendar_events = google_calendar_api.extract_relevant_fields(events)
        self.local_event_filtering(self.travel_calendar_events)
        self.do_calendar_analysis = True

    async def create_trips_from_events(self, events: list[dict]):
        for event in events:
            new_trip: ChatThread = ChatThread(self.user.id, event.get("city", settings.NEW_TRIP_TITLE))
            new_trip.date_start = (
                datetime.strptime(event["start_date"], "%Y-%m-%d") if event.get("start_date") else None
            )
            new_trip.date_end = datetime.strptime(event["end_date"], "%Y-%m-%d") if event.get("end_date") else None
            await ChatThread.new_chat_thread(new_trip)

    def local_event_filtering(self, events):
        # Do some experimental event filtering to reduce payload size and false
        # positive event selection
        for event in events:
            # Get rid of timezones that have city names in them
            # start = event.get("start").pop("timeZone", None)
            # end = event.get("end").pop("timeZone", None)

            # Get rid of html tags to reduce payload size
            description_text = event.get("description")
            clean_text = self.remove_html_tags(description_text)

            # Look for zoom junk and zero it out
            if "zoom" in clean_text.lower():
                clean_text = ""

            event["description"] = clean_text

            logger.info(event, mask="\033[91m {}\033[00m")

    def remove_html_tags(self, text):
        # Define a regular expression pattern for HTML tags
        tag_pattern = re.compile(r"<[^>]+>")

        # Use sub() to replace HTML tags with an empty string
        return tag_pattern.sub("", text)

    def is_final_message(self, message: BaseMessage):
        try:
            arguments_string = message.additional_kwargs["function_call"]["arguments"]
            arguments_obj = json.loads(arguments_string)

            return arguments_obj.get("is_conversation_finished", False)
        except BaseException:
            return False

    async def get_history_messages(self):
        self.messages = await self.history.persisted_messages

        history: list[list[dict[str, str | bool | None]]] = []
        for idx, message in reversed(list(enumerate(self.messages))):
            if message.additional_kwargs.get("is_card_update", False):
                continue
            elif message.additional_kwargs.get("timestamp", False):
                history.append(
                    [
                        {
                            "type": "prompt",
                            "timestamp": message.additional_kwargs.get("timestamp"),
                        }
                    ]
                )
                del self.messages[idx]
            else:
                history.append(self.map_websocket_message(message))

                if self.is_final_message(message):
                    arguments_string = message.additional_kwargs["function_call"]["arguments"]
                    arguments_obj = json.loads(arguments_string)

                    self.selected_calendar_events = arguments_obj.get("selected_calendar_events", [])

        history.reverse()
        to_send = list(itertools.chain.from_iterable(history))

        # This is history, do not wait for user input
        for msg in to_send:
            msg["expectResponse"] = False

        return to_send

    async def load_events_from_database(self):
        user_events_doc = await google_calendar_events_collection.aggregate(
            [
                {"$match": {"user_id": self.user.email}},
                {"$unwind": {"path": "$events"}},
                {"$match": {"events.is_new": True}},
                {"$group": {"_id": "$_id", "events": {"$push": "$events"}}},
            ]
        ).to_list(None)
        events = user_events_doc[0].get("events", []) if len(user_events_doc) > 0 else []

        if len(events) > 0:
            await google_calendar_events_collection.update_one(
                {"user_id": self.user.email}, {"$set": {"events.$[].is_new": False}}
            )

        self.travel_calendar_events = GoogleCalendarEvents.extract_relevant_fields(events)
        self.local_event_filtering(self.travel_calendar_events)
        self.do_calendar_analysis = True
