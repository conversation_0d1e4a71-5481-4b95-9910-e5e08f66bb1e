import json
from datetime import datetime
from typing import List, Optional

from prometheus_client import Counter

from baml_client import b
from front_of_house_agent.common_models import FlightOption, SpotnanaFlightOption
from server.utils.logger import logger
from server.utils.settings import settings

NO_MATCH_COUNT = Counter(
    "flight_no_match_spotnana",
    "Number of no match flights in Spotnana",
    ["environment"],
)

MATCH_COUNT = Counter(
    "flight_match_spotnana",
    "Number of match flights in Spotnana",
    ["environment"],
)


class FlightComparator:
    @staticmethod
    async def get_exact_match_flight(
        flight: FlightOption, candidate_flights: List[SpotnanaFlightOption], need_match_price: bool
    ) -> Optional[SpotnanaFlightOption]:
        semi_final_candidate_flights = [
            candidate_flight
            for candidate_flight in candidate_flights
            if FlightComparator.__is_exact_match(flight, candidate_flight, False)
        ]

        logger.info(f"Found matches: {semi_final_candidate_flights}")
        semi_final_candidate_flights = await FlightComparator.__similar_match_based_on_llm(
            flight, semi_final_candidate_flights
        )
        logger.info(f"Found matches after LLM: {semi_final_candidate_flights}")

        if need_match_price:
            semi_final_candidate_flights = [
                candidate_flight
                for candidate_flight in semi_final_candidate_flights
                if FlightComparator.__price_within_n_percent(
                    flight.total_price,
                    candidate_flight.total_price,
                    settings.SERP_SPOTNANA_FLIGHT_PRICE_DIFF_THRESHOLD,
                )
            ]

        matched = None

        if semi_final_candidate_flights:
            matched = min(semi_final_candidate_flights, key=lambda x: abs(flight.total_price - x.total_price))

        if not matched:
            logger.error(
                f"POTENTIAL ISSUE: No match found for flight: {flight}, candidate_flights: {candidate_flights}"
            )
            NO_MATCH_COUNT.labels(environment=settings.OTTO_ENV.upper()).inc()
        else:
            MATCH_COUNT.labels(environment=settings.OTTO_ENV.upper()).inc()
        return matched

    @staticmethod
    def __is_exact_match(flight: FlightOption, candidate_flight: FlightOption, need_match_price: bool) -> bool:
        overall_match = (
            flight.departure_airport_code == candidate_flight.departure_airport_code
            and flight.arrival_airport_code == candidate_flight.arrival_airport_code
            and FlightComparator.__time_within_n_minutes(flight.departure_time, candidate_flight.departure_time)
            and FlightComparator.__time_within_n_minutes(flight.arrival_time, candidate_flight.arrival_time)
            and (
                (
                    need_match_price
                    and FlightComparator.__price_within_n_percent(
                        flight.total_price,
                        candidate_flight.total_price,
                        settings.SERP_SPOTNANA_FLIGHT_PRICE_DIFF_THRESHOLD,
                    )
                )
                or (not need_match_price)
            )
        )
        if not overall_match:
            return False

        for stop, candidate_stop in zip(flight.stops, candidate_flight.stops):
            if (
                stop.departure_airport_code != candidate_stop.departure_airport_code
                or stop.arrival_airport_code != candidate_stop.arrival_airport_code
                or (
                    stop.airline_code != candidate_stop.airline_code
                    and stop.airline_code != candidate_stop.operating_airline_code
                )
                or (
                    stop.flight_number != candidate_stop.flight_number
                    and stop.flight_number != candidate_stop.operating_flight_number
                )
                or not FlightComparator.__time_within_n_minutes(flight.departure_time, candidate_flight.departure_time)
                or not FlightComparator.__time_within_n_minutes(flight.arrival_time, candidate_flight.arrival_time)
            ):
                return False
        return True

    @staticmethod
    def __price_within_n_percent(target_price: float, candidate_price: float, percentage: float = 25) -> bool:
        return abs(target_price - candidate_price) / target_price <= percentage / 100

    @staticmethod
    def __time_within_n_minutes(time1: str, time2: str, limit_minutes: float = 30) -> bool:
        dt1 = datetime.fromisoformat(time1)
        dt2 = datetime.fromisoformat(time2)

        diff_seconds = abs((dt2 - dt1).total_seconds())

        return diff_seconds <= (limit_minutes * 60)

    @staticmethod
    async def __similar_match_based_on_llm(
        flight: FlightOption, candidate_flights: List[SpotnanaFlightOption]
    ) -> List[SpotnanaFlightOption]:
        candidates = []

        for index, f in enumerate(candidate_flights):
            flight_dict = f.to_flight_cabin_compare_candidate_dict(ignore_none=True)
            flight_dict["index_token"] = str(index)
            candidates.append(json.dumps(flight_dict))

        target_flight = flight.to_flight_cabin_compare_candidate_dict(ignore_none=True)
        if target_flight:
            response = await b.GetSimilarFlight(
                json.dumps(target_flight), candidates, baml_options={"collector": logger.collector}
            )
            logger.log_baml()
        else:
            return candidate_flights

        return [candidate_flights[i] for i in response.index_array if i < len(candidate_flights)]
