from datetime import timedelta

from temporalio import activity, workflow
from temporalio.common import RetryPolicy


@activity.defn
async def run_eval_conversation_activity():
    """Activity that runs the eval conversation main function."""
    from eval.eval_conversation import main

    await main()


@workflow.defn
class EvalConversationWorkflow:
    """Workflow that runs conversation evaluation daily."""

    @workflow.run
    async def run(self) -> str:
        """Run the eval conversation workflow."""
        # Run the eval conversation activity
        await workflow.execute_activity(
            run_eval_conversation_activity,
            start_to_close_timeout=timedelta(minutes=30),
            retry_policy=RetryPolicy(
                initial_interval=timedelta(seconds=30),
                maximum_interval=timedelta(minutes=10),
                maximum_attempts=3,
            ),
        )

        return "Eval conversation completed successfully"
