from temporalio import activity, workflow

with workflow.unsafe.imports_passed_through():
    import asyncio
    from datetime import timedelta

    from jinja2 import Environment, FileSystemLoader

    from server.cron.check_hotel_status import CheckHotelStatus
    from server.cron.hotel_data_cache import HotelDataCache
    from server.utils.logger import logger
    from server.utils.settings import settings

hotels_data_cache_api = HotelDataCache()
env = Environment(loader=FileSystemLoader("email_templates"))

TASK_BATCH_SIZE = 20


@activity.defn
async def get_all_supported_countries() -> list[str]:
    return await hotels_data_cache_api.get_all_supported_countries()


@activity.defn
async def sync_hotel_cache(country_code: str) -> dict[str, int]:
    return await hotels_data_cache_api.sync_all(country_code.lower())


@workflow.defn
class SyncHotelCacheWorkflow:
    @workflow.run
    async def run(self) -> dict[str, int]:
        inserted_count = 0
        updated_count = 0
        if settings.OTTO_ENV.upper() == "LIVE":
            logger.info("Syncing hotel cache in live environment")
            countries = await workflow.execute_activity(
                get_all_supported_countries,
                schedule_to_close_timeout=timedelta(hours=1),
            )

            tasks = []
            for country in countries:
                logger.info(f"Syncing hotels for country: {country}")
                task = workflow.execute_activity(
                    sync_hotel_cache,
                    country,
                    schedule_to_close_timeout=timedelta(hours=1),
                )
                tasks.append(task)
                if len(tasks) >= TASK_BATCH_SIZE:
                    results = await asyncio.gather(*tasks)
                    for result in results:
                        logger.info(f"Sync result: {result}")
                        inserted_count += result.get("inserted_count", 0)
                        updated_count += result.get("updated_count", 0)
                    tasks.clear()

            if tasks:
                results = await asyncio.gather(*tasks)
                for result in results:
                    logger.info(f"Sync result: {result}")
                    inserted_count += result.get("inserted_count", 0)
                    updated_count += result.get("updated_count", 0)
        else:
            logger.info("Syncing hotel cache in non-live environment")

        return {
            "inserted_count": inserted_count,
            "updated_count": updated_count,
        }


@workflow.defn
class SyncHotelStatusWorkflow:
    @workflow.run
    async def run(self) -> None:
        return await workflow.execute_activity(
            sync_hotel_status,
            schedule_to_close_timeout=timedelta(minutes=30),
        )


@workflow.defn
class NotifyHotelChangesWorkflow:
    @workflow.run
    async def run(self) -> None:
        return await workflow.execute_activity(
            notifiy_hotel_changes,
            schedule_to_close_timeout=timedelta(minutes=30),
        )


@activity.defn
async def notifiy_hotel_changes() -> None:
    cron_check_hotel_status = CheckHotelStatus()
    return await cron_check_hotel_status.notify_in_trip_hotel_changes()


@activity.defn
async def sync_hotel_status() -> None:
    cron_check_hotel_status = CheckHotelStatus()
    await cron_check_hotel_status.sync_hotels_status()
