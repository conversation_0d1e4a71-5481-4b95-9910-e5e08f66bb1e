from temporalio import workflow
from temporalio.client import Client, Schedule, ScheduleActionStartWorkflow, ScheduleSpec
from temporalio.worker import Worker

from workflows.email_proces_workflow import MailgunEmailWorkflow, execute_foh_agent, send_email_response
from workflows.eval_conversation_workflow import EvalConversationWorkflow, run_eval_conversation_activity
from workflows.hotel_sync_workflows import (
    NotifyHotelChangesWorkflow,
    SyncHotelCacheWorkflow,
    SyncHotelStatusWorkflow,
    get_all_supported_countries,
    notifiy_hotel_changes,
    sync_hotel_cache,
    sync_hotel_status,
)

with workflow.unsafe.imports_passed_through():
    import asyncio

    from jinja2 import Environment, FileSystemLoader

    from server.cron.hotel_data_cache import HotelDataCache
    from server.utils.logger import logger
    from server.utils.settings import settings

hotels_data_cache_api = HotelDataCache()
env = Environment(loader=FileSystemLoader("email_templates"))

TASK_BATCH_SIZE = 20


async def get_temporal_client() -> Client:
    return await Client.connect(
        settings.TEMPORAL_HOST or "",
        namespace=settings.TEMPORAL_NS or "",
        tls=settings.RUN_CONTEXT == "server",
        api_key=settings.TEMPORAL_API_KEY or "",
    )


async def create_temporal_worker():
    def __get_cron():
        if settings.OTTO_ENV.upper() == "LIVE":
            return "0 8 * * *"
        else:
            return "0 11 * * *"

    logger.info("Temporal worker starting")
    try:
        client = await get_temporal_client()

        async with Worker(
            client,
            task_queue="otto-worker-queue",
            workflows=[
                SyncHotelCacheWorkflow,
                SyncHotelStatusWorkflow,
                NotifyHotelChangesWorkflow,
                MailgunEmailWorkflow,
                EvalConversationWorkflow,
            ],
            activities=[
                sync_hotel_cache,
                sync_hotel_status,
                notifiy_hotel_changes,
                get_all_supported_countries,
                execute_foh_agent,
                send_email_response,
                run_eval_conversation_activity,
            ],
        ):
            if settings.OTTO_ENV.upper() == "LIVE":
                schedule_sync_hotel_cache_id = f"schedule-sync-hotel-cache-{settings.OTTO_ENV.lower()}"

                if settings.HOTEL_FAKE_BOOKING:
                    cron_spec = None
                else:
                    cron_spec = ScheduleSpec(
                        cron_expressions=[__get_cron()],
                    )
                try:
                    await client.get_schedule_handle(schedule_sync_hotel_cache_id).delete()
                except Exception as e:
                    logger.warn(f"Error deleting schedule: {e}")
                if cron_spec:
                    await client.create_schedule(
                        schedule_sync_hotel_cache_id,
                        Schedule(
                            action=ScheduleActionStartWorkflow(
                                SyncHotelCacheWorkflow.run,
                                id=schedule_sync_hotel_cache_id,
                                task_queue="otto-worker-queue",
                            ),
                            spec=cron_spec,
                        ),
                    )
                logger.info("Temporal hotel cache worker started")
            if settings.HOTEL_FAKE_BOOKING == False:
                schedule_notify_hotel_changes_id = f"schedule-notify-hotel-changes-{settings.OTTO_ENV.lower()}"
                try:
                    await client.get_schedule_handle(schedule_notify_hotel_changes_id).delete()
                except Exception as e:
                    logger.warn(f"Error deleting schedule: {e}")
                logger.info("Starting workflow for NotifyHotelChangesWorkflow")
                await client.create_schedule(
                    schedule_notify_hotel_changes_id,
                    Schedule(
                        action=ScheduleActionStartWorkflow(
                            NotifyHotelChangesWorkflow.run,
                            id=schedule_notify_hotel_changes_id,
                            task_queue="otto-worker-queue",
                        ),
                        spec=ScheduleSpec(
                            cron_expressions=[settings.CRON_EXPR_CHECK_HOTEL_STATUS or "*/2 * * * *"],
                        ),
                    ),
                )
                logger.info("Temporal hotel changes notification worker started")

                schedule_sync_hotel_status_id = f"schedule-sync-hotel-status-{settings.OTTO_ENV.lower()}"
                try:
                    await client.get_schedule_handle(schedule_sync_hotel_status_id).delete()
                except Exception as e:
                    logger.warn(f"Error deleting schedule: {e}")
                await client.create_schedule(
                    schedule_sync_hotel_status_id,
                    Schedule(
                        action=ScheduleActionStartWorkflow(
                            SyncHotelStatusWorkflow.run,
                            id=schedule_sync_hotel_status_id,
                            task_queue="otto-worker-queue",
                        ),
                        spec=ScheduleSpec(
                            cron_expressions=[settings.CRON_EXPR_CHECK_HOTEL_STATUS or "*/2 * * * *"],
                        ),
                    ),
                )
                logger.info("Temporal hotel status worker started")

            # Create schedule for EvalConversationWorkflow to run daily at 3 AM (LIVE environment only)
            if settings.OTTO_ENV.upper() == "LIVE":
                schedule_eval_conversation_id = f"schedule-eval-conversation-{settings.OTTO_ENV.lower()}"
                try:
                    await client.get_schedule_handle(schedule_eval_conversation_id).delete()
                except Exception as e:
                    logger.warn(f"Error deleting eval conversation schedule: {e}")

                await client.create_schedule(
                    schedule_eval_conversation_id,
                    Schedule(
                        action=ScheduleActionStartWorkflow(
                            EvalConversationWorkflow.run,
                            id=schedule_eval_conversation_id,
                            task_queue="otto-worker-queue",
                        ),
                        spec=ScheduleSpec(
                            cron_expressions=["0 3 * * *"],  # Run daily at 3 AM
                        ),
                    ),
                )
                logger.info("Temporal eval conversation worker started")

            await asyncio.Future()
    except Exception as e:
        logger.error(f"Error starting Temporal worker: {e}")
        raise
