## Creating EKS cluster & node group

### Step-01: Create EKS Cluster using `eksctl`
Creating the cluster:
```
eksctl create cluster \
    --name=circleci-runners \
    --region=us-east-2 \
    --zones=us-east-2a,us-east-2b,us-east-2c \
    --without-nodegroup
```

Then verify it using `eksctl get cluster`

### Step-02: Create & Associate IAM OIDC Provider for our EKS Cluster
```
eksctl utils associate-iam-oidc-provider \
    --region us-east-2 \
    --cluster circleci-runners \
    --approve
```

### Step-03: Create EC2 Keypair
Create a new EC2 Keypair with name as `otto-agent-testing`
This keypair we will use it when creating the EKS NodeGroup.
This will help us to login to the EKS Worker Nodes using Terminal.

![Creating EC2 Keypair](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*MHYFf7uZT-FZfcb4rtiIzw.png)

### Step-04: Create Node Group with additional Add-Ons in Public Subnets

Dry-run first:
```
eksctl create nodegroup --cluster=circleci-runners \
    --region=us-east-2 \
    --name=eks-circleci-highmem \
    --node-type=r6i.xlarge \
    --nodes=1 \
    --nodes-min=1 \
    --nodes-max=5 \
    --node-volume-size=20 \
    --ssh-access \
    --ssh-public-key=otto-agent-testing \
    --managed \
    --asg-access \
    --external-dns-access \
    --full-ecr-access \
    --appmesh-access \
    --alb-ingress-access \
    --dry-run
```

Then run the actual command:
```
eksctl create nodegroup \
    --cluster=circleci-runners \
    --region=us-east-2 \
    --name=eks-circleci-highmem \
    --node-type=r6i.xlarge \
    --nodes=1 \
    --nodes-min=1 \
    --nodes-max=5 \
    --node-volume-size=20 \
    --ssh-access \
    --ssh-public-key=otto-agent-testing \
    --managed \
    --asg-access \
    --external-dns-access \
    --full-ecr-access \
    --appmesh-access \
    --alb-ingress-access
```

(failed here)

### Step-05: Verify Cluster & Nodes

Add the new cluster to kubectl:
```
aws eks update-kubeconfig --region us-east-2 --name circleci-runners
```

Then veirfy cluster and nodes
```
# List EKS clusters
eksctl get cluster

# List NodeGroups in a cluster
eksctl get nodegroup --cluster=circleci-runners

# List Nodes in current kubernetes cluster
kubectl get nodes -o wide

# Our kubectl context should be automatically changed to new cluster
kubectl config view --minify
```

