version: 2.1
orbs:
  python: circleci/python@2

commands:
  setup-env:
    steps:
      - run:
          name: Generate .env file
          command: |
            echo "OTTO_ENV=${OTTO_ENV}" >> .env
            echo "AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}" >> .env
            echo "AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}" >> .env
            echo "AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}" >> .env
            echo "REDIS_URL=redis://redis-master.circleci.svc.cluster.local:6379" >> .env
            cat .env
      - python/install-packages
  generate-baml-client:
    steps:
      - run:
          name: Generate baml_client
          command: |
            baml-cli generate
  dry-run-tests:
    parameters:
      test_file:
        type: string
      test_scenario:
        type: string
    steps:
      - run:
          name: Dry-run tests
          command: |
            echo "Running dry-run for << parameters.test_file >> with scenario << parameters.test_scenario >>"
            collected_tests=$(python -m pytest "<< parameters.test_file >>" -k "<< parameters.test_scenario >>" --collect-only | grep -c '<Coroutine')
            if [ "$collected_tests" -eq 0 ]; then
              echo "No test cases were collected for scenario: << parameters.test_scenario >>."
              exit 1
            fi
            echo "$collected_tests test cases were collected."

jobs:
  test-oneway-flight:
    docker:
      - image: cimg/python:3.11.10
    resource_class: gke-otto/aws-eks-highmem # defined on CircleCI
    parameters:
      test_scenario:
        type: string
    steps:
      - checkout
      - setup-env
      - generate-baml-client
      - dry-run-tests:
          test_file: "virtual_travel_agent_tests/test_end_to_end_oneway_flight.py"
          test_scenario: "<< parameters.test_scenario >>"
      - run:
          name: Run tests
          command: python -m pytest "virtual_travel_agent_tests/test_end_to_end_oneway_flight.py" -k "<< parameters.test_scenario >>" --junitxml=test-results/junit.xml
      - store_test_results:
          path: test-results

  test-roundtrip-flight:
    docker:
      - image: cimg/python:3.11.10
    resource_class: gke-otto/aws-eks-highmem # defined on CircleCI
    parameters:
      test_scenario:
        type: string
    steps:
      - checkout
      - setup-env
      - generate-baml-client
      - dry-run-tests:
          test_file: "virtual_travel_agent_tests/test_end_to_end_roundtrip_flight.py"
          test_scenario: "<< parameters.test_scenario >>"
      - run:
          name: Run tests
          command: python -m pytest "virtual_travel_agent_tests/test_end_to_end_roundtrip_flight.py" -k "<< parameters.test_scenario >>" --junitxml=test-results/junit.xml
      - store_test_results:
          path: test-results

workflows:
  agent-tests:
    jobs:
      - test-oneway-flight:
          context:
            - aws-secrets
          matrix:
            parameters:
              test_scenario:
                - FLIGHT_SEARCH
                - FLIGHT_VALIDATION and cheapest_flight_n_button
                - FLIGHT_VALIDATION and earliest_flight_n_fuzzy
                - FLIGHT_VALIDATION and earliest_flight_n_vague
                - FLIGHT_VALIDATION and latest_flight_n_vague
                - FLIGHT_BOOKING
      - test-roundtrip-flight:
          context:
            - aws-secrets
          matrix:
            parameters:
              test_scenario:
                - FLIGHT_SEARCH_1
                - FLIGHT_SEARCH_2 and cheapest_flight_n_button
                - FLIGHT_SEARCH_2 and earliest_flight_n_fuzzy
                - FLIGHT_SEARCH_2 and earliest_flight_n_vague
                - FLIGHT_SEARCH_2 and latest_flight_n_vague
                - FLIGHT_VALIDATION and cheapest_flight_n_button and not earliest_flight_n_fuzzy and not earliest_flight_n_vague and not latest_flight_n_vague
                - FLIGHT_VALIDATION and cheapest_flight_n_button and earliest_flight_n_fuzzy
                - FLIGHT_VALIDATION and cheapest_flight_n_button and earliest_flight_n_vague
                - FLIGHT_VALIDATION and cheapest_flight_n_button and latest_flight_n_vague
                - FLIGHT_VALIDATION and earliest_flight_n_fuzzy
                - FLIGHT_VALIDATION and earliest_flight_n_vague
                - FLIGHT_VALIDATION and latest_flight_n_vague
                - FLIGHT_BOOKING
