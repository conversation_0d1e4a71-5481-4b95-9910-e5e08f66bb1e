import json
import re

import requests
from langchain.tools import tool

from server.utils.logger import logger

serp_api_hotels_ids_file_name = "virtual_travel_agent/Hotel_Brands_Chain_IDs.csv"
serp_api_hotels_ids = None


def load_serp_api_hotels_ids(file_name) -> None:
    """
    Load the SERPAPI hotel IDs so they can be passed to the hotel search function

    Args:
    None

    Returns:
    dict: A dictionary of key/value pairs:  brand: ID
    """
    global serp_api_hotels_ids

    result_dict = {}
    with open(file_name, "r") as file:
        for line in file:
            if "," in line:
                hotel_name, hotel_id = line.strip().split(",", 1)
                result_dict[hotel_name] = hotel_id

    serp_api_hotels_ids = result_dict
    logger.info("Loaded SERP API Hotel IDs")


def find_partial_matches(my_dict, search_string):
    return {key: value for key, value in my_dict.items() if search_string in key}


def get_brand_string(brand_prefs) -> str:
    hotel_ids = []
    if serp_api_hotels_ids is not None:
        for brand in brand_prefs:
            match_dict = find_partial_matches(serp_api_hotels_ids, brand)
            hotel_ids.extend(list(match_dict.values()))

    comma_separated_hotel_id_string = ",".join(hotel_ids)
    logger.info(f"hotel ID string {comma_separated_hotel_id_string}")
    return comma_separated_hotel_id_string


class HotelSearchTools:
    import re

    @staticmethod
    def safe_file_name(filename: str, replace="-") -> str:
        """
        Sanitize a string to create a safe file name.

        Args:
        filename (str): The original file name.
        replace (str): The character to replace unsafe characters with, default is space.

        Returns:
        str: A sanitized version of the file name.
        """
        # Remove leading and trailing whitespace
        filename = filename.strip()
        # Replace file system unsafe characters including comma with `replace`
        filename = re.sub(r'[\\/*?:"<>|,]', replace, filename)
        # Replace or condense multiple spaces or underscores with a single
        # replace character
        filename = re.sub(r"[\s" + re.escape(replace) + "]+", replace, filename).strip()
        # Remove trailing dots or replace characters that might cause issues in
        # Windows
        filename = re.sub(r"[\.*" + re.escape(replace) + "]*$", "", filename)
        return filename

    @tool
    @staticmethod
    def search_hotels_function(hotel_params: str) -> str:
        """
        Search for hotels using an API

        Parameters
        ----------
        hotel_params : str
            A JSON string containing data needed for the hotel search, including:
            city,
            check_in_date,
            check_out_date,
            preferred_hotel_brands

        Returns
        -------
        str
            A list of hotel option objects in JSON form

        """

        should_filter = True

        # Load the Serp hotel id table if it isn't already loaded
        if serp_api_hotels_ids is None:
            load_serp_api_hotels_ids(serp_api_hotels_ids_file_name)

        # Convert our json string into a python object
        hotel_params_dict = json.loads(hotel_params)

        # https://serpapi.com/google-hotels-api
        url = "https://serpapi.com/search"
        api_key = "ae57a699ad7a38b601680a5614fb880c461143a269a5e0a64b98cb082a17a709"
        engine = "google_hotels"
        hl = "en"
        currency = "USD"

        # clean up the string which may have json characters in it:
        chars_to_remove = r"['\(\)\[\]]"
        pattern = re.compile(chars_to_remove)
        query_string = f"{hotel_params_dict['city']} {hotel_params_dict['general_hotel_preferences']} {hotel_params_dict['trip_specific_hotel_preferences']}"
        query_string = pattern.sub("", query_string)

        # https://serpapi.com/google-hotels-api
        params = {
            "api_key": api_key,
            "engine": engine,
            "hl": hl,
            # "travel_class": type,
            "adults": "1",
            "currency": currency,
            "q": query_string,
            "check_in_date": hotel_params_dict["check_in_date"],
            "check_out_date": hotel_params_dict["check_out_date"],
            "hotel_class": "3,4,5",
            "brands": get_brand_string(hotel_params_dict["brand_hotel_preferences"]),
            "no_cache": "true",
        }

        headers = {"Accept": "application/json"}

        logger.info(f"q={params['q']}")

        response = requests.get(url, params=params, headers=headers)

        if response.status_code == 200:
            data = response.json()
            best_hotels = data.get("properties", [])

            length = len(best_hotels)

            # Choose first 20 hotels
            # all_hotels = best_hotels[:20]
            all_hotels = best_hotels
            if length > 50:
                all_hotels = best_hotels[:50]

            no_value_found_str = "No value found."

            # JTB - Disabled - only for use when debugging
            # city = hotel_params_dict['city']
            # check_in = hotel_params_dict['check_in_date']
            # check_out = hotel_params_dict['check_out_date']
            # random_int = random.randint(1, 1000000)
            # file_name = f'serpapi_hotels_{city}_{check_in}_{check_out}_{random_int}.json'
            # file_name = HotelSearchTools.safe_file_name(file_name)
            # with open(file_name, 'w') as file:
            #     file.write(json.dumps(all_hotels, indent=4))

            if should_filter:
                new_list = []

                for hotel in all_hotels:
                    new_hotel = {}
                    new_hotel["type"] = hotel.get("type", no_value_found_str)
                    new_hotel["name"] = hotel.get("name", no_value_found_str)
                    new_hotel["description"] = hotel.get("description", no_value_found_str)
                    new_hotel["link"] = hotel.get("link", no_value_found_str)
                    new_hotel["gps_coordinates"] = hotel.get("gps_coordinates", no_value_found_str)

                    new_hotel["rate_per_night_lowest"] = hotel.get("rate_per_night", {}).get(
                        "lowest", no_value_found_str
                    )
                    new_hotel["total_rate_lowest"] = hotel.get("total_rate", {}).get("lowest", no_value_found_str)
                    new_hotel["total_rate_lowest"] = hotel.get("total_rate", {}).get("lowest", no_value_found_str)

                    # This is an array
                    nearby_list = []

                    new_hotel["nearby_places"] = hotel.get("nearby_places", no_value_found_str)

                    if new_hotel["nearby_places"] != no_value_found_str:
                        for place in hotel["nearby_places"]:
                            nearby_list.append(place["name"])
                        new_hotel["nearby_places"] = nearby_list

                    new_hotel["hotel_class"] = hotel.get("hotel_class", no_value_found_str)
                    new_hotel["extracted_hotel_class"] = hotel.get("extracted_hotel_class", no_value_found_str)
                    new_hotel["overall_rating"] = hotel.get("overall_rating", no_value_found_str)
                    new_hotel["amenities"] = hotel.get("amenities", no_value_found_str)

                    new_hotel["images"] = hotel.get("images", no_value_found_str)

                    if new_hotel["images"] != no_value_found_str:
                        new_hotel["images"] = hotel["images"][0]["original_image"]

                    new_list.append(new_hotel)

                all_hotels = new_list

            all_hotels_str = json.dumps(all_hotels)
            logger.info(
                f"\nsearch_hotels_function ••••••• Success ••••••• Found {length} hotel properties. Message payload Size: {len(all_hotels_str)} characters\n"
            )
            return f'{{"hotel_options": {all_hotels_str}}}'
        else:
            logger.error(f"search_hotels_function - Error: Failed to retrieve hotel options. {response.status_code}")
            return "hotel_options: {{}}"
