from enum import Enum
from typing import List, Optional

from pydantic import BaseModel

from server.utils.logger import logger
from virtual_travel_agent.helpers import console_masks

booking_log_mask = console_masks["booking.com"]


class BookingStatus(Enum):
    BOOKED = "booked"
    CANCELLED = "cancelled"
    CANCELLED_BY_ACCOMMODATION = "cancelled_by_accommodation"
    CANCELLED_BY_GUEST = "cancelled_by_guest"
    NO_SHOW = "no_show"
    STAYED = "stayed"
    UNKNOWN = "unknown"


class PriceObject(BaseModel):
    accommodation_currency: Optional[float] = None
    booker_currency: Optional[float] = None


class CancellationPolicy(BaseModel):
    from_date: Optional[str] = None
    price: Optional[PriceObject] = None


# Models for products field structure
class ProductAllocation(BaseModel):
    adults: int
    children: List[int]
    guests: int


class ProductPolicies(BaseModel):
    cancellation: List[CancellationPolicy]


class ProductExtraCharge(BaseModel):
    charge: int
    mode: str
    percentage: Optional[float] = None
    total_amount: Optional[PriceObject] = None
    unit_amount: Optional[PriceObject] = None


class ProductExtraCharges(BaseModel):
    conditional: List[ProductExtraCharge]
    non_conditional: List[ProductExtraCharge]


class ProductPrice(BaseModel):
    base: Optional[PriceObject] = None
    extra_charges: Optional[ProductExtraCharges] = None
    total: Optional[PriceObject] = None


class Product(BaseModel):
    allocation: ProductAllocation
    policies: ProductPolicies
    price: ProductPrice
    room: int
    room_reservation: int
    status: str


class BookingDetails(BaseModel):
    order_id: str
    status: BookingStatus
    check_in: str
    check_out: str
    property_id: int
    property_name: str
    cancellation_policies: List[CancellationPolicy]
    error_message: Optional[str] = None
    pin_code: Optional[str] = None
    product: Optional[Product] = None

    @staticmethod
    def _create_price_object(price_data: dict) -> Optional[PriceObject]:
        """
        Helper method to create a PriceObject from a dictionary.

        Args:
            price_data (dict): Dictionary containing price information

        Returns:
            PriceObject: Created price object with accommodation and booker currency
        """
        if not price_data:
            return None

        return PriceObject(
            accommodation_currency=price_data.get("accommodation_currency"),
            booker_currency=price_data.get("booker_currency"),
        )

    @classmethod
    def from_api_response(cls, response_data: dict) -> "BookingDetails":
        """
        Create an BookingDetails instance from the API response.

        Args:
            response_data (dict): Raw API response data

        Returns:
            BookingDetails: Structured booking details
        """
        try:
            data_slice = response_data.get("data", [])
            data = {}
            if len(data_slice) > 0:
                data = data_slice[0]

            # Convert string status to enum
            status = BookingStatus(data.get("status", "unknown"))
            if status == BookingStatus.UNKNOWN:
                logger.error(f"Unknown order status: {data}", mask=booking_log_mask)
                return cls(
                    order_id="",
                    status=BookingStatus.UNKNOWN,
                    check_in="",
                    check_out="",
                    property_id=0,
                    property_name="",
                    cancellation_policies=[],
                    error_message=data.get("error_message"),
                )

            # Parse dates
            check_in = data.get("checkin", "")
            check_out = data.get("checkout", "")

            # Get accommodation details
            accommodation = data.get("accommodation_details", {})

            # Parse products
            products = []
            raw_products = data.get("products", [])

            for raw_product in raw_products:
                try:
                    # Parse allocation
                    allocation_data = raw_product.get("allocation") or {}
                    allocation = ProductAllocation(
                        adults=allocation_data.get("adults", 0),
                        children=allocation_data.get("children", []),
                        guests=allocation_data.get("guests", 0),
                    )

                    # Parse policies
                    policies_data = raw_product.get("policies") or {}

                    # Parse cancellation policies
                    cancellation_policies_data = policies_data.get("cancellation", [])
                    product_cancellation_policies = []
                    for cancel_policy in cancellation_policies_data:
                        product_cancellation_policies.append(
                            CancellationPolicy(
                                from_date=cancel_policy.get("from"),
                                price=cls._create_price_object(cancel_policy.get("price", {})),
                            )
                        )

                    policies = ProductPolicies(
                        cancellation=product_cancellation_policies,
                    )

                    # Parse price
                    price_data = raw_product.get("price") or {}

                    # Parse extra charges
                    extra_charges_data = price_data.get("extra_charges") or {}
                    conditional_charges = []
                    non_conditional_charges = []

                    for charge_data in extra_charges_data.get("conditional") or []:
                        conditional_charges.append(
                            ProductExtraCharge(
                                charge=charge_data.get("charge", 0),
                                mode=charge_data.get("mode", ""),
                                percentage=charge_data.get("percentage", 0.0),
                                total_amount=cls._create_price_object(charge_data.get("total_amount", {})),
                                unit_amount=cls._create_price_object(charge_data.get("unit_amount", {})),
                            )
                        )

                    for charge_data in extra_charges_data.get("non_conditional") or []:
                        non_conditional_charges.append(
                            ProductExtraCharge(
                                charge=charge_data.get("charge", 0),
                                mode=charge_data.get("mode", ""),
                                percentage=charge_data.get("percentage", 0.0),
                                total_amount=cls._create_price_object(charge_data.get("total_amount", {})),
                                unit_amount=cls._create_price_object(charge_data.get("unit_amount", {})),
                            )
                        )

                    extra_charges = ProductExtraCharges(
                        conditional=conditional_charges, non_conditional=non_conditional_charges
                    )

                    product_price = ProductPrice(
                        base=cls._create_price_object(price_data.get("base", {})),
                        extra_charges=extra_charges,
                        total=cls._create_price_object(price_data.get("total", {})),
                    )

                    # Create product
                    product = Product(
                        allocation=allocation,
                        policies=policies,
                        price=product_price,
                        room=raw_product.get("room", 0),
                        room_reservation=raw_product.get("room_reservation", 0),
                        status=raw_product.get("status", ""),
                    )
                    products.append(product)
                except Exception as e:
                    logger.error(f"Error parsing product: {e}, product data: {raw_product}", mask=booking_log_mask)
                    continue

            # Get first product (room) details
            first_product = products[0] if products else None

            # Parse cancellation policies from products
            cancellation_policies = []
            if first_product:
                policies = first_product.policies
                for policy in policies.cancellation:
                    cancellation_policies.append(policy)

            return cls(
                order_id=str(data.get("id", "")),
                status=status,
                check_in=check_in,
                check_out=check_out,
                property_id=data.get("accommodation", 0),
                property_name=accommodation.get("name", ""),
                cancellation_policies=cancellation_policies,
                pin_code=data.get("pin_code"),
                error_message=data.get("error_message"),
                product=first_product,
            )

        except Exception as e:
            logger.error(f"Error parsing order details: {e}, data: {response_data}", mask=booking_log_mask)
            return cls(
                order_id="",
                status=BookingStatus.UNKNOWN,
                check_in="",
                check_out="",
                property_id=0,
                property_name="",
                cancellation_policies=[],
                error_message=str(e),
            )


class ApiResponseStatus(Enum):
    SUCCESS = "success"
    ERROR = "error"


class CancellationResponse(BaseModel):
    status: ApiResponseStatus
    message: str
    error_code: Optional[int] = None
    details: Optional[dict] = None
