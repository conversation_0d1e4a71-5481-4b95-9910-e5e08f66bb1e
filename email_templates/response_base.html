
<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <style>
    td,th,div,p,a,h1,h2,h3,h4,h5,h6 {font-family: "Segoe UI", sans-serif; mso-line-height-rule: exactly;}
    .mso-break-all {word-break: break-all;}
  </style>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" media="screen">
<style type="text/css">
@media only screen and (max-width: 600px) {
  .flight-card-table, 
  .flight-card-table tbody, 
  .flight-card-table tr, 
  .flight-card-table td {
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
  .flight-card-td {
    display: block !important;
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .flight-card-label {
    font-weight: bold !important;
    margin-bottom: 4px !important;
    display: block !important;
  }
  .hotel-card-table,
  .hotel-card-table tbody,
  .hotel-card-table tr,
  .hotel-card-table td {
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
  .hotel-card-td {
    display: block !important;
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .flight-roundtrip-card-table,
  .flight-roundtrip-card-table tbody,
  .flight-roundtrip-card-table tr,
  .flight-roundtrip-card-table td {
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
  .flight-roundtrip-card-td {
    display: block !important;
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
@media only screen and (max-width: 600px) {
  .flight-roundtrip-price-desktop { display: none !important; }
  .flight-mobile-price-row { display: table-row !important; }
}
@media only screen and (min-width: 601px) {
  .flight-roundtrip-price-desktop { display: table-cell !important; }
  .flight-mobile-price-row { display: none !important; }
}
</style>
  <style>.absolute {
    position: absolute !important
}
.relative {
    position: relative !important
}
.-top-px {
    top: -1px !important
}
.left-0 {
    left: 0 !important
}
.right-0 {
    right: 0 !important
}
.z-0 {
    z-index: 0 !important
}
.z-10 {
    z-index: 10 !important
}
.m-0 {
    margin: 0 !important
}
.mx-auto {
    margin-left: auto !important;
    margin-right: auto !important
}
.mb-0 {
    margin-bottom: 0 !important
}
.mb-1 {
    margin-bottom: 4px !important
}
.mb-2 {
    margin-bottom: 8px !important
}
.mb-3 {
    margin-bottom: 12px !important
}
.mb-4 {
    margin-bottom: 16px !important
}
.mb-6 {
    margin-bottom: 24px !important
}
.ml-2 {
    margin-left: 8px !important
}
.ml-auto {
    margin-left: auto !important
}
.mt-0 {
    margin-top: 0 !important
}
.mt-1 {
    margin-top: 4px !important
}
.mt-3 {
    margin-top: 12px !important
}
.mt-4 {
    margin-top: 16px !important
}
.block {
    display: block !important
}
.inline-block {
    display: inline-block !important
}
.flex {
    display: flex !important
}
.table {
    display: table !important
}
.hidden {
    display: none !important
}
.size-0_259rem {
    width: 0.259rem !important;
    height: 0.259rem !important
}
.h-32 {
    height: 128px !important
}
.h-48 {
    height: 192px !important
}
.h-5_5 {
    height: 1.375rem !important
}
.h-8 {
    height: 32px !important
}
.h-full {
    height: 100% !important
}
.h-px {
    height: 1px !important
}
.w-1-2 {
    width: 50% !important
}
.w-4 {
    width: 16px !important
}
.w-8 {
    width: 32px !important
}
.w-702px {
    width: 702px !important
}
.w-758px {
    width: 758px !important
}
.w-fit {
    width: fit-content !important
}
.w-full {
    width: 100% !important
}
.min-w-60 {
    min-width: 240px !important
}
.min-w-60px {
    min-width: 60px !important
}
.max-w-full {
    max-width: 100% !important
}
.flex-1 {
    flex: 1 1 0% !important
}
.flex-auto {
    flex: 1 1 auto !important
}
.shrink-0 {
    flex-shrink: 0 !important
}
.cursor-pointer {
    cursor: pointer !important
}
.flex-col {
    flex-direction: column !important
}
.items-start {
    align-items: flex-start !important
}
.items-end {
    align-items: flex-end !important
}
.items-center {
    align-items: center !important
}
.justify-end {
    justify-content: flex-end !important
}
.justify-center {
    justify-content: center !important
}
.justify-between {
    justify-content: space-between !important
}
.justify-around {
    justify-content: space-around !important
}
.gap-4 {
    gap: 16px !important
}
.gap-x-1 {
    column-gap: 4px !important
}
.gap-x-1_5 {
    column-gap: 6px !important
}
.gap-x-2 {
    column-gap: 8px !important
}
.gap-x-3 {
    column-gap: 12px !important
}
.gap-x-4 {
    column-gap: 16px !important
}
.gap-y-1 {
    row-gap: 4px !important
}
.gap-y-2 {
    row-gap: 8px !important
}
.gap-y-4 {
    row-gap: 16px !important
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
    margin-top: 8px !important;
    margin-bottom: 0px !important
}
.self-start {
    align-self: flex-start !important
}
.overflow-hidden {
    overflow: hidden !important
}
.whitespace-nowrap {
    white-space: nowrap !important
}
.rounded {
    border-radius: 4px !important
}
.rounded-full {
    border-radius: 9999px !important
}
.rounded-lg {
    border-radius: 8px !important
}
.border {
    border-width: 1px !important
}
.border-b {
    border-bottom-width: 1px !important
}
.border-t {
    border-top-width: 1px !important
}
.border-solid {
    border-style: solid !important
}
.border-gray-200 {
    border-color: #e5e7eb !important
}
.border-gray-300 {
    border-color: #D7D7CF !important
}
.border-neutral-250 {
    border-color: #E1E1E1 !important
}
.border-slate-200 {
    border-color: #e2e8f0 !important
}
.bg-blue-600 {
    background-color: #013B61 !important
}
.bg-blue-900 {
    background-color: #1e3a8a !important
}
.bg-neutral-250 {
    background-color: #E1E1E1 !important
}
.bg-red-600 {
    background-color: #EC221F !important
}
.bg-slate-50 {
    background-color: #f8fafc !important
}
.bg-teal-100 {
    background-color: #C5FEF1 !important
}
.bg-white {
    background-color: #fffffe !important
}
.object-cover {
    object-fit: cover !important
}
.p-0 {
    padding: 0 !important
}
.p-4 {
    padding: 16px !important
}
.py-4 {
    padding-top: 16px !important;
    padding-bottom: 16px !important
}
.pl-4 {
    padding-left: 16px !important
}
.pr-0 {
    padding-right: 0 !important
}
.pr-1 {
    padding-right: 4px !important
}
.px-2 {
    padding-left: 8px !important;
    padding-right: 8px !important
}
.px-9 {
    padding-left: 36px !important;
    padding-right: 36px !important
}
.py-0_5 {
    padding-top: 2px !important;
    padding-bottom: 2px !important
}
.py-6 {
    padding-top: 24px !important;
    padding-bottom: 24px !important
}
.pl-3 {
    padding-left: 12px !important
}
.pr-3 {
    padding-right: 12px !important
}
.pr-4 {
    padding-right: 16px !important
}
.pt-1 {
    padding-top: 4px !important
}
.pt-4 {
    padding-top: 16px !important
}
.text-left {
    text-align: left !important
}
.text-center {
    text-align: center !important
}
.text-right {
    text-align: right !important
}
.align-top {
    vertical-align: top !important
}
.align-middle {
    vertical-align: middle !important
}
.font-inter {
    font-family: Inter, ui-sans-serif, system-ui, -apple-system, "Segoe UI", sans-serif !important
}
.text-2xl-8 {
    font-size: 24px !important;
    line-height: 32px !important
}
.text-2xs {
    font-size: 0.625rem !important
}
.text-0_688rem {
    font-size: 0.688rem !important
}
.text-base {
    font-size: 16px !important
}
.text-base-6 {
    font-size: 16px !important;
    line-height: 24px !important
}
.text-lg {
    font-size: 18px !important
}
.text-sm {
    font-size: 14px !important
}
.text-xs {
    font-size: 12px !important
}
.text-xs-5 {
    font-size: 12px !important;
    line-height: 20px !important
}
.font-bold {
    font-weight: 700 !important
}
.font-medium {
    font-weight: 500 !important
}
.font-normal {
    font-weight: 400 !important
}
.font-semibold {
    font-weight: 600 !important
}
.uppercase {
    text-transform: uppercase !important
}
.leading-4_5 {
    line-height: 1.125rem !important
}
.leading-5 {
    line-height: 20px !important
}
.tracking-wide {
    letter-spacing: 0.025em !important
}
.text-blue-500 {
    color: #007FDB !important
}
.text-blue-600 {
    color: #013B61 !important
}
.text-gray-900 {
    color: #1D1C1D !important
}
.text-green-300 {
    color: #86efac !important
}
.text-green-500 {
    color: #26BE31 !important
}
.text-green-550 {
    color: #2AA526 !important
}
.text-green-600 {
    color: #16a34a !important
}
.text-orange-500 {
    color: #FC9B34 !important
}
.text-primary-300 {
    color: #D0F2F4 !important
}
.text-primary-400 {
    color: #1384BA !important
}
.text-primary-500 {
    color: #08C0CC !important
}
.text-primary-600 {
    color: #99F9E7 !important
}
.text-primary-900 {
    color: #025279 !important
}
.text-purple-300 {
    color: #d8b4fe !important
}
.text-purple-500 {
    color: #a855f7 !important
}
.text-purple-700 {
    color: #7e22ce !important
}
.text-purple-800 {
    color: #6b21a8 !important
}
.text-red-500 {
    color: #D85040 !important
}
.text-red-650 {
    color: #DB2A1D !important
}
.text-slate-500 {
    color: #64748b !important
}
.text-slate-600 {
    color: #475569 !important
}
.text-slate-800 {
    color: #1e293b !important
}
.text-slate-900 {
    color: #0f172a !important
}
.text-white {
    color: #fffffe !important
}
.shadow-md {
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, .08) !important
}
.underline {
    text-decoration: underline !important
}
.mso-font-width-150pc {
    mso-font-width: 150%
}
.-webkit-font-smoothing-antialiased {
    -webkit-font-smoothing: antialiased !important
}
.border-1px_solid_themecolors_slate_200 {
    border: 1px solid #e2e8f0 !important
}
.word-break-break-word {
    word-break: break-word !important
}
    img {
    max-width: 100%;
    vertical-align: middle
}
    @media (max-width: 600px) {
    .sm-p-6 {
        padding: 24px !important
    }
    .sm-px-4 {
        padding-left: 16px !important;
        padding-right: 16px !important
    }
    .sm-px-6 {
        padding-left: 24px !important;
        padding-right: 24px !important
    }
}
    @media (min-width: 992px) {
    .lg-flex {
        display: flex !important
    }
}
    .and-_button-leading-5 button {
    line-height: 20px !important
}
  </style>
  
</head>
<body class="m-0 p-0 w-full word-break-break-word -webkit-font-smoothing-antialiased bg-white">
  <div role="article" aria-roledescription="email" aria-label="" lang="en">
    
  <div class="bg-slate-50 sm-px-4 font-inter">
    <table align="center" class="m-0 mx-auto" cellpadding="0" cellspacing="0" role="none">
      <tr>
        <td class="w-702px max-w-full bg-slate-50">

        <div role="separator" style="line-height: 24px">&zwj;</div>

          <table class="w-full" cellpadding="0" cellspacing="0" role="none">
            <tr>
              <td class="py-6 px-9 sm-p-6 bg-white border-1px_solid_themecolors_slate_200 rounded-lg">
                <a href="{{ base_url }}">
                  <img src="https://otto-web.s3.us-east-2.amazonaws.com/otto-logo/otto-logo-banner.png" width="70" alt="ottotheagent">
                </a>

                <div role="separator" style="line-height: 24px">&zwj;</div>

                <!-- Agent Response Summary -->
                <div class="m-0 mb-6 text-sm text-slate-600">
                <p>{{ summary_html | safe }}</p>
                </div>
                
                <!-- Flight and Hotel cards -->
                {% if cards_html %}
                {{ cards_html }}
                {% endif %}

                <!-- End of Flight and Hotel cards -->
                {% if eding_html %}
                <div class="m-0 mb-6 text-sm text-slate-600">
                    <p>{{ eding_html }}</p>
                </div>
                {% endif %}
                
                <div role="separator" style="line-height: 24px">&zwj;</div>

                <p class="m-0 text-base-6 text-slate-600">
                  Thanks,
                  <br>
                  <span class="font-semibold">Otto</span>
                </p>

                <div role="separator" style="height: 1px; line-height: 1px; background-color: #cbd5e1; margin-top: 24px; margin-bottom: 24px">&zwj;</div>

                <p class="m-0 text-xs-5 text-slate-600 mso-break-all">
                  If you're having trouble viewing the content, copy and paste the following URL into your web browser and chat with me there:
                  <a href="{{ base_url }}trips/{{ trip_id }}" class="text-slate-800 underline">{{ base_url }}trips/{{ trip_id }}</a>
                </p>
              </td>
            </tr>
          </table>

          <table class="w-full" cellpadding="0" cellspacing="0" role="none">
            <tr>
              <td class="py-6 px-9 sm-px-6">
                <p class="m-0 text-xs text-slate-500">
                  &copy; 2025 Otto. All rights reserved.
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>

  </div>
</body>
</html>
