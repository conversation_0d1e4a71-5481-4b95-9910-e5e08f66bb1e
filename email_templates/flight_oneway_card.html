<!-- Flights Section -->
<div class="mb-6 border border-solid border-gray-300 rounded-lg p-4">
    <h2 class="text-lg font-semibold text-slate-900 mb-4 mt-0">Flights <span class="text-sm font-normal text-slate-500">{{
            flight_date }}</span></h2>

    <div class="border border-slate-200 rounded-lg">
        <table class="w-full flight-card-table" cellpadding="0" cellspacing="0" border="0">
            {% for flight in flights %}
            <!-- Flight Option {{ loop.index }} -->
            <tr>
                <td colspan="5" class="border-b border-slate-20 flight-card-td">
                    <div class="flex justify-between items-start mb-3">
                        <span class="text-xs font-medium text-slate-600 uppercase tracking-wide">OPTION {{ loop.index }}</span>
                    </div>
                </td>
            </tr>
            <!-- Flight Details -->
            <tr>
                <td class="w-8 align-top pr-3 flight-card-td">
                    <img src="https://app.ottotheagent.com/images/airlines/{{ flight.img.alt }}.png" alt="{{ flight.img.alt }}"
                        class="w-8 h-8 rounded-full object-contain">
                </td>
                <td class="align-top pr-4 flight-card-td">
                    <div>
                        {% set first_segment = flight.flight_segments[0] %}
                        {% set first_stop = first_segment.flight_stops[0] %}
                        {% set last_stop = first_segment.flight_stops[-1] %}
                        <span class="font-semibold">
                            {{ first_stop.departure[11:16] }} - {{ last_stop.arrival[11:16] }}
                        </span>
                        <div class="text-sm text-slate-600">{{ first_stop.airline_name }}</div>
                    </div>
                </td>
                <td class="align-top pr-4 flight-card-td">
                    <div class="text-sm font-semibold">{{ first_segment.origin_code }} - {{ first_segment.destination_code }}</div>
                    <div class="text-xs text-slate-500">
                        {% for stop in first_segment.flight_stops %}
                        {{ stop.airline_code }} {{ stop.flight_number }}{% if not loop.last %}, {% endif %}
                        {% endfor %}
                    </div>
                </td>
                <td class="align-top pr-4 flight-card-td">
                    <div class="text-sm font-semibold">
                        {% if first_segment.flight_stops|length == 1 %}
                        {{ first_stop.duration|replace('PT', '')|replace('H', 'h ')|replace('M', 'm')|lower }}
                        {% else %}
                        Multi-stop
                        {% endif %}
                    </div>
                    <div class="text-xs text-slate-500">
                        {% if first_segment.flight_stops|length == 1 %}
                        nonstop
                        {% else %}
                        {{ first_segment.flight_stops|length - 1 }} stop{% if first_segment.flight_stops|length > 2 %}s{% endif %}
                        {% if first_segment.flight_stops|length == 2 %}
                        in {{ first_segment.flight_stops[0].destination_code }}
                        {% endif %}
                        {% endif %}
                    </div>
                </td>
                <td class="align-middle flight-card-td">
                    <div class="text-lg font-bold">${{ flight.price.amount }}</div>
                    <div class="text-xs text-slate-500 mt-1">{{ flight.fare_option_name }}</div>
                    {% if flight.credits and flight.credits.amount != '0.00' %}
                    <div class="text-xs text-green-600 mt-1">Credits: ${{ flight.credits.amount }}</div>
                    {% endif %}
                </td>
            </tr>
            
            <!-- Policy Information -->
            {% if flight.within_policy == false %}
            <tr>
                <td colspan="5" class="text-sm text-slate-600">
                    <div class="mt-3 text-sm text-slate-600">
                        <strong class="text-red-600">Out-of-Policy:</strong>
                        {% if flight.within_or_out_policy_reason %}{{ flight.within_or_out_policy_reason }}{% endif %}
                    </div>
                </td>
            </tr>
            {% endif %}
            
            <!-- Recommendation Reasons -->
            {% if flight.recommendationReasons %}
            <tr>
                <td colspan="5" class="text-sm text-slate-600">
                    <div class="mt-3 {% if not loop.last %}mb-4{% endif %} text-sm text-slate-600">
                        <strong>Why I recommend it:</strong>
                        {{ flight.recommendationReasons[0] }}
                    </div>
                </td>
            </tr>
            {% endif %}
            
            {% if not loop.last %}
            <!-- Separator between options -->
            <tr>
                <td colspan="5" class="py-4"></td>
            </tr>
            {% endif %}
            
            {% endfor %}
        </table>
    </div>
</div>
