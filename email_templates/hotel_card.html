<!-- Hotels Section -->
<div class="mb-6 border border-solid border-gray-300 rounded-lg py-4 pl-4 pr-0">
    <h2 class="text-lg font-semibold text-slate-900 mb-0 mt-0">Hotels <span
            class="text-sm font-normal text-slate-500">{{
            hotel_date }}</span></h2>
    <table class="w-full mb-4 hotel-card-table" style="table-layout: fixed;">
        <tr>
            {% for accommodation in accommodations %}
            <!-- Hotel Option {{ loop.index }} -->
            <td class="w-1/2 pr-4 align-top hotel-card-td" style="width: 50%;">
                <div class="border border-slate-200 rounded-lg overflow-hidden" style="height: 100%;">
                    <h3 class="text-lg font-semibold text-blue-500 mb-1"
                        style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                        <span class="text-xs font-medium text-slate-600 uppercase tracking-wide">OPTION {{ loop.index
                            }}</span><br>
                        {% if accommodation.mapMarker and accommodation.mapMarker.address %}
                        <a href="https://www.google.com/maps/search/?api=1&query={{ accommodation.mapMarker.address|urlencode }}"
                            target="_blank" style="color: inherit; text-decoration: none;">{{ accommodation.hotel }}</a>
                        {% else %}
                        {{ accommodation.hotel }}
                        {% endif %}
                    </h3>
                    <div class="mb-4">
                        <span class="text-sm">{{ accommodation.rating }}/10</span>
                        <span class="text-sm text-slate-600 ml-2">{{ accommodation.rating_description|title }}</span>
                    </div>
                    {% if accommodation.rooms and accommodation.rooms|length > 0 %}
                    {% set room = accommodation.rooms[0] %}
                    <div class="mb-4">
                        <div class="text-base font-bold">${{ "%.2f"|format(room.price) }} total ({{ room.no_nights }}
                            nights)</div>
                        <div class="text-sm text-slate-500">${{ "%.2f"|format(room.pricePerNight) }} per night</div>
                        {% if room.taxAndFees %}
                        <div class="text-xs text-slate-500">Includes taxes and fees</div>
                        {% endif %}
                    </div>
                    <div class="mb-4">
                        <div class="font-bold mb-2"
                            style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ room.option_title
                            }}</div>
                        <img src="{{ room.room_photo or accommodation.img.src }}" alt="{{ room.option_title }}"
                            style="width: 100%; height: 143px;" class="object-cover rounded">
                    </div>
                    <div class="text-sm mb-0">
                        {% if room.cancellation_policy %}
                        <div class="mb-2"><strong>Cancellation:</strong> {{ room.cancellation_policy.display_policy }}
                        </div>
                        {% endif %}
                        {% if room.payment_policy %}
                        <div class="mb-2"><strong>Payment:</strong> {{ room.payment_policy.display_label or 'Multiple
                            options available' }}
                        </div>
                        {% endif %}
                        {% if room.within_policy == false %}
                        <div class="mb-2"><strong>Out-of-policy:</strong> {{ room.within_or_out_policy_reason or 'This
                            option does not comply with company travel policy' }}
                        </div>
                        {% endif %}
                        {% if room.recommendation_reason or
                        accommodation.recommendationReasons.preference_alignment_reason %}
                        <div class="mb-2"><strong>Why I recommend it:</strong>
                            {% if accommodation.recommendationReasons.preference_alignment_reason %}
                            {{ accommodation.recommendationReasons.preference_alignment_reason }}
                            {% if room.recommendation_reason %} {{ room.recommendation_reason }}{% endif %}
                            {% else %}
                            {{ room.recommendation_reason }}
                            {% endif %}
                        </div>
                        {% endif %}
                        {% if accommodation.mapMarker and accommodation.mapMarker.address %}
                        <div class="mb-0"><strong>Location:</strong> {{ accommodation.mapMarker.address }}</div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </td>
            {% if loop.index == 2 or loop.last %}
        </tr>
        {% if not loop.last %}
        <tr>
            {% endif %}
            {% endif %}
            {% endfor %}
    </table>

    <!-- Map Section -->
    {% if accommodations %}
    <div class="mb-0 pr-4">
        {% set map_center = accommodations[0].mapMarker.coordinates.lat + "," +
        accommodations[0].mapMarker.coordinates.lng %}
        {% set markers = [] %}
        {% for accommodation in accommodations %}
        {% if accommodation.mapMarker and accommodation.mapMarker.coordinates %}
        {% set marker = "color:blue|label:" + loop.index|string + "|" + accommodation.mapMarker.coordinates.lat + "," +
        accommodation.mapMarker.coordinates.lng %}
        {% set _ = markers.append(marker) %}
        {% endif %}
        {% endfor %}
        <img src="https://maps.googleapis.com/maps/api/staticmap?center={{ map_center }}&zoom={{ gmap_zoom_level }}&size=654x197&maptype=roadmap&format=png&{% for marker in markers %}markers={{ marker }}{% if not loop.last %}&{% endif %}{% endfor %}&key=AIzaSyDduEC-Vtt86dcZoY5RBKwtmXcCjhOVwkc"
            alt="Hotels Map" class="w-full h-48 object-cover rounded-lg">
    </div>
</div>
{% endif %}