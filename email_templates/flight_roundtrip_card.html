<!-- Flights Section -->
<div class="mb-6 border border-solid border-gray-300 rounded-lg p-4">
    <h2 class="text-lg font-semibold text-slate-900 mb-4 mt-0">Flights <span class="text-sm font-normal text-slate-500">{{
            flight_date }}</span></h2>
    <div class="border border-slate-200 rounded-lg">
        <table class="w-full flight-roundtrip-card-table">
            {% for combo_key, combo in flight_combos.items() %}
            <!-- Flight Option {{ loop.index }} -->
            <tr>
                <td colspan="5" class="border-b border-slate-200 flight-roundtrip-card-td">
                    <div class="flex justify-between items-start mb-3">
                        <span class="text-xs font-medium text-slate-600 uppercase tracking-wide">{{ combo.alias[0]|upper
                            }}</span>
                    </div>
                </td>
            </tr>

            <!-- Outbound Flight -->
            <tr>
                <td class="w-8 align-top pr-3 flight-roundtrip-card-td">
                    <img src="https://app.ottotheagent.com/images/airlines/{{ combo.outbound_flight.img.alt }}.png"
                        alt="{{ combo.outbound_flight.img.alt }}" class="w-8 h-8 rounded-full object-contain">
                </td>
                <td class="align-top pr-4 flight-roundtrip-card-td">
                    <div>
                        {% set first_segment = combo.outbound_flight.flight_segments[0] %}
                        {% set first_stop = first_segment.flight_stops[0] %}
                        {% set last_stop = first_segment.flight_stops[-1] %}
                        <span class="font-semibold">
                            {{ first_stop.departure[11:16] }} - {{ last_stop.arrival[11:16] }}
                        </span>
                        <div class="text-sm text-slate-600">{{ first_stop.airline_name }}</div>
                    </div>
                </td>
                <td class="align-top pr-3 flight-roundtrip-card-td">
                    <div class="text-sm font-semibold">{{ first_segment.origin_code }} - {{
                        first_segment.destination_code }}</div>
                    <div class="text-xs text-slate-500">
                        {% for stop in first_segment.flight_stops %}
                        {{ stop.airline_code }} {{ stop.flight_number }}{% if not loop.last %}, {% endif %}
                        {% endfor %}
                    </div>
                </td>
                <td class="align-top pr-3 flight-roundtrip-card-td">
                    <div class="text-sm font-semibold">
                        {% if first_segment.flight_stops|length == 1 %}
                        {{ first_stop.duration|replace('PT', '')|replace('H', 'h ')|replace('M', 'm')|lower }}
                        {% else %}
                        <!-- Calculate total duration for multi-stop flights -->
                        Multi-stop
                        {% endif %}
                    </div>
                    <div class="text-xs text-slate-500">
                        {% if first_segment.flight_stops|length == 1 %}
                        nonstop
                        {% else %}
                        {{ first_segment.flight_stops|length - 1 }} stop{% if first_segment.flight_stops|length > 2
                        %}s{% endif %}
                        {% endif %}
                    </div>
                </td>
                <td class="align-top flight-roundtrip-price-desktop" rowspan="2">
                    <div class="text-lg font-bold">from ${{ "%.0f"|format(combo.return_flight.price.amount|float) }}</div>
                    <div class="text-xs text-slate-500">{{ combo.outbound_flight.fare_option_name }}</div>
                    <div class="text-xs text-slate-500">{{ combo.return_flight.fare_option_name }}</div>
                </td>
            </tr>

            <!-- Return Flight -->
            <tr>
                <td class="w-8 align-top pr-3 pt-4 flight-roundtrip-card-td">
                    <img src="https://app.ottotheagent.com/images/airlines/{{ combo.return_flight.img.alt }}.png"
                        alt="{{ combo.return_flight.img.alt }}" class="w-8 h-8 rounded-full object-contain">
                </td>
                <td class="align-top pr-4 pt-4 flight-roundtrip-card-td">
                    <div>
                        {% set return_first_segment = combo.return_flight.flight_segments[0] %}
                        {% set return_first_stop = return_first_segment.flight_stops[0] %}
                        {% set return_last_stop = return_first_segment.flight_stops[-1] %}
                        <span class="font-semibold">
                            {{ return_first_stop.departure[11:16] }} - {{ return_last_stop.arrival[11:16] }}
                        </span>
                        <div class="text-sm text-slate-600">{{ return_first_stop.airline_name }}</div>
                    </div>
                </td>
                <td class="align-top pr-3 pt-4 flight-roundtrip-card-td">
                    <div class="text-sm font-semibold">{{ return_first_segment.origin_code }} - {{
                        return_first_segment.destination_code }}</div>
                    <div class="text-xs text-slate-500">
                        {% for stop in return_first_segment.flight_stops %}
                        {{ stop.airline_code }} {{ stop.flight_number }}{% if not loop.last %}, {% endif %}
                        {% endfor %}
                    </div>
                </td>
                <td class="align-top pr-3 pt-4 flight-roundtrip-card-td">
                    <div class="text-sm font-semibold">
                        {% if return_first_segment.flight_stops|length == 1 %}
                        {{ return_first_stop.duration|replace('PT', '')|replace('H', 'h ')|replace('M', 'm')|lower }}
                        {% else %}
                        Multi-stop
                        {% endif %}
                    </div>
                    <div class="text-xs text-slate-500">
                        {% if return_first_segment.flight_stops|length == 1 %}
                        nonstop
                        {% else %}
                        {{ return_first_segment.flight_stops|length - 1 }} stop{% if
                        return_first_segment.flight_stops|length > 2 %}s{% endif %}
                        {% if return_first_segment.flight_stops|length == 2 %}
                        in {{ return_first_segment.flight_stops[0].destination_code }}
                        {% endif %}
                        {% endif %}
                    </div>
                </td>
            </tr>
            <!-- Mobile Price Row (hidden on desktop) -->
            <tr class="flight-mobile-price-row">
                <td colspan="4">
                    <div class="text-lg font-bold">from ${{ "%.0f"|format(combo.return_flight.price.amount|float) }}</div>
                    <div class="text-xs text-slate-500">{{ combo.outbound_flight.fare_option_name }}</div>
                    <div class="text-xs text-slate-500">{{ combo.return_flight.fare_option_name }}</div>
                </td>
            </tr>

            <!-- Policy Information -->
            {% if combo.outbound_flight.within_policy == false or combo.return_flight.within_policy == false %}
            <tr>
                <td colspan="5" class="text-sm text-slate-600">
                    <div class="mt-3 text-sm text-slate-600">
                        <strong class="text-red-600">Out-of-Policy:</strong>
                        {% if combo.outbound_flight.within_policy == false and combo.outbound_flight.within_or_out_policy_reason
                        %}{{ combo.outbound_flight.within_or_out_policy_reason }}{% endif %}{% if
                        combo.outbound_flight.within_policy == false and combo.outbound_flight.within_or_out_policy_reason and
                        combo.return_flight.within_policy == false and combo.return_flight.within_or_out_policy_reason %} {% endif
                        %}{% if combo.return_flight.within_policy == false and combo.return_flight.within_or_out_policy_reason %}{{
                        combo.return_flight.within_or_out_policy_reason }}{% endif %}
                    </div>
                </td>
            </tr>
            {% endif %}

            <!-- Recommendation Reasons -->
            <tr>
                <td colspan="5" class="text-sm text-slate-600">
                    <div class="mt-3 {% if not loop.last %}mb-4{% endif %} text-sm text-slate-600">
                        <strong>Why I recommend it:</strong>
                        {% if combo.outbound_flight.recommendationReasons %}
                        {{ combo.outbound_flight.recommendationReasons[0] }}
                        {% endif %}
                        {% if combo.return_flight.recommendationReasons %}
                        {{ combo.return_flight.recommendationReasons[0] }}
                        {% endif %}
                    </div>

                </td>
            </tr>

            {% if not loop.last %}
            <!-- Separator between options -->
            <tr>
                <td colspan="5" class="py-4"></td>
            </tr>
            {% endif %}

            {% endfor %}
        </table>
    </div>
</div>
