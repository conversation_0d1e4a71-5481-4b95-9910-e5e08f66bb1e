import logging
import time
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

logger = logging.getLogger(__name__)


def cheapest_flight_n_button(last_message, _):
    """
    Default implementation for selecting a flight.
    This selects the flight with the lowest price.
    """
    logger.info("[Callback] Selecting the cheapest flight via clicking the button.")
    flights = last_message["flights"]
    selected_flight = min(flights, key=lambda flight: float(flight["price"]["amount"]))
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": selected_flight["action"],
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


default_start_date = datetime(2025, 5, 12)
default_flight_prompt = f"one-way to SFO UA on {default_start_date.strftime('%b %d')}"


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="November 13th 2024")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "start_trip_prompt, flight_selection_callback, flight_selection_callback_param, confirm_to_book_prompt, start_change_prompt",
    [(default_flight_prompt, cheapest_flight_n_button, "", "confirm", "I want to change the flight to be one day later")],
)
# fmt: on
@pytest.mark.asyncio
async def test_change_flight(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    _,
    start_trip_prompt,
    flight_selection_callback,
    flight_selection_callback_param,
    confirm_to_book_prompt,
    start_change_prompt,
    mock_user,
    mock_user_profile,
    mock_thread,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("server.utils.smtp.send_booking_email", new_callable=AsyncMock), \
            patch("server.services.calendar_api.calendar_provider.CalendarProviderManager.has_calendar_access", return_value=False), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        try:
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "trip_init", "tripId": mock_thread.id},
            )

            # Step 1. Send flight prompt
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_trip_prompt,
                },
            )

            # at this point flight search for the outbound is done
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info(
                "[FlightSearch] Search result: %d flights found",
                len(last_message["flights"]),
            )

            # Step 2. pick the outbound flight with a prompt (click or text)
            flight_selection = flight_selection_callback(last_message, flight_selection_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            # confirm the seat selection.
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "Sure",
                },
            )            

            # at this point flight validation is done
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info("[FlightValidation] Validation result: %s", last_message["text"])
            # Step 3. confirm the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            # at this point flight should have been booked
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            logger.info("[FlightBooking] Booking result: %s", messages[-1]["text"])

            time.sleep(60)

            # Step 4. start to change the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_change_prompt,
                },
            )
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            logger.info("[FlightChange] Confirm Change: %s", messages[-1]["text"])

            # Step 5. Confirm to change the flight.
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "yes",
                },
            )

            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info(
                "[FlightChange] Search result: %d flights found",
                len(last_message["flights"]),
            )

            # Step 6. Select the change flight.
            flight_selection = flight_selection_callback(last_message, flight_selection_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            # at this point change flight validation is done
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info("[FlightChange] Validation result: %s", last_message["text"])
            # flight details like flight_number, airline_code should be listed in the response.
            assert flight_selection["flight_number"] in last_message["text"]
            assert (
                flight_selection["airline_code"] in last_message["text"]
                or flight_selection["airline_name"] in last_message["text"]
            )

            # Step 7. confirm the change
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            # at this point flight should have been booked
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            assert "confirmation ID" in messages[-1]["text"]
            logger.info("[FlightChange] Booking result: %s", messages[-1]["text"])

            time.sleep(60)

            # Step 8. change to add return flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "I want to add return flight one day later",
                },
            )

            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info(
                "[FlightChange] Add return flight search result: %d flights found",
                len(last_message["flights"]),
            )

            # Step 9. Select the changed return flight.
            flight_selection = flight_selection_callback(last_message, flight_selection_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            # at this point change flight validation is done
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info("[FlightChange] Add return flight validation result: %s", last_message["text"])
            # flight details like flight_number, airline_code should be listed in the response.
            assert flight_selection["flight_number"] in last_message["text"]
            assert (
                flight_selection["airline_code"] in last_message["text"]
                or flight_selection["airline_name"] in last_message["text"]
            )

            # Step 10. confirm the change return flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            # at this point flight should have been booked
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            assert "confirmation ID" in messages[-1]["text"]
            logger.info("[FlightChange] Add return flight booking result: %s", messages[-1]["text"])

        except Exception as e:
            import traceback
            logger.error(traceback.format_exc())
            logger.error(e)
            raise


round_trip_start_date = datetime(2025, 4, 3)
round_trip_end_date = datetime(2025, 4, 12)
round_trip_flight_prompt = f"Flight to LAX DL {round_trip_start_date.strftime('%b %d')}-{round_trip_end_date.strftime('%d')}"


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="November 13th 2024")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "start_trip_prompt, leg1_pick_callback, leg1_pick_callback_param, leg2_pick_callback, leg2_pick_callback_param, confirm_to_book_prompt",
    [
        # fuzzy confirm terms
        (round_trip_flight_prompt, cheapest_flight_n_button, "", cheapest_flight_n_button, "", "confirm"),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_change_roundtrip_to_lax_with_delta(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    _,
    start_trip_prompt,
    leg1_pick_callback,
    leg1_pick_callback_param,
    leg2_pick_callback,
    leg2_pick_callback_param,
    confirm_to_book_prompt,
    mock_user,
    mock_user_profile,
    mock_thread,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.google_profile.GoogleProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        try:
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "trip_init", "tripId": mock_thread.id},
            )

            # Step 1. Send flight prompt
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_trip_prompt,
                },
            )

            # Step 2. pick the outbound flight with a prompt (click or text)
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            flight_selection = leg1_pick_callback(last_message, leg1_pick_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            # Step 3. pick the return flight with a prompt (click or text)
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            flight_selection = leg2_pick_callback(last_message, leg2_pick_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            # confirm the seat selection.
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "Sure",
                },
            )             

            # Step 4. confirm the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            # at this point flight should have been booked
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            logger.info("[FlightBooking] Booking result: %s", messages[-3]["text"])
            assert not messages[-3]["expectResponse"]
            assert "confirmation ID" in messages[-3]["text"]
            assert "airline" in messages[-3]["text"]
            last_message = messages[-1]
            logger.info("[RoundTripFlightChange] exchange detail message: %s", last_message["text"])

            # Step 4. confirm the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "want to change the flight.",
                },
            )
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            logger.info("[RoundTripFlightChange] Confirm Change: %s", messages[-1]["text"])

            # Step 5. Confirm to change the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "want to change the outbound flight to be one day later and return flight to be one day earlier",
                },
            )

            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info(
                "[RoundTripFlightChange] Search result: %d flights found",
                len(last_message["flights"]),
            )

            # Step 6. Select the outbound change flight.
            flight_selection = leg1_pick_callback(last_message, leg1_pick_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            # Step 7. pick the return change flight with a prompt (click or text)
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            flight_selection = leg2_pick_callback(last_message, leg2_pick_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            # at this point flight validation is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            # flight details like flight_number, airline_code should be listed in the response.
            assert flight_selection["flight_number"] in last_message["text"]
            assert (
                flight_selection["airline_code"] in last_message["text"]
                or flight_selection["airline_name"] in last_message["text"]
            )
            logger.info("[RoundTripFlightChange] Validation result: %s", last_message["text"])

            # Step 4. confirm the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            # at this point flight should have been booked
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info("[RoundTripFlightChange][FlightBooking] last message: %s", last_message["text"])
            assert "confirmation ID" in last_message["text"]

        except Exception as e:
            import traceback
            logger.error(traceback.format_exc())
            logger.error(e)
            raise
