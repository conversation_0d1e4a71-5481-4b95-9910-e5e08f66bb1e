"""All the fixtures to be shared, like travel context."""

import asyncio
import json
import logging
from typing import Any, Generator
from unittest.mock import Magic<PERSON>ock

import pytest
import pytest_asyncio

import server.utils.settings  # noqa
from baml_client.types import ResponseAllPreferences
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User


@pytest_asyncio.fixture(scope="function", autouse=True)
async def remove_convese_state_fixture():
    from server.utils.mongo_connector import converse_flight_state_collection, trip_context_v2_collection

    await converse_flight_state_collection.delete_one({"thread_id": 42})
    await trip_context_v2_collection.delete_one({"thread_id": 42})


@pytest.fixture
def mock_user() -> User:
    user = User(
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="Doe",
        profile_picture="",
    )
    user.id = 1
    return user


@pytest.fixture
def mock_user_id_with_memory() -> int:
    return 1736893472


@pytest.fixture
def mock_user_with_memory(mock_user_id_with_memory) -> User:
    user = User(
        email="<EMAIL>",
        first_name="Integration",
        last_name="Test",
        profile_picture="",
    )
    user.id = mock_user_id_with_memory
    return user


@pytest.fixture
def mock_thread_with_memory(mock_user_with_memory) -> ChatThread:
    thread = ChatThread(users_id=mock_user_with_memory.id, title="mock thread")
    thread.id = 43
    return thread


@pytest.fixture
def mock_user_profile() -> Any:
    mock_google_profile = MagicMock()
    mock_google_profile.last_login_method = "Google"
    return mock_google_profile


@pytest.fixture
def mock_user_profile_microsoft() -> Any:
    mock_microsoft_profile = MagicMock()
    mock_microsoft_profile.last_login_method = "Microsoft"
    return mock_microsoft_profile


@pytest.fixture
def mock_thread(mock_user) -> ChatThread:
    thread = ChatThread(users_id=mock_user.id, title="mock thread")
    thread.id = 42
    return thread


class FakeRow:
    def __init__(self, chat_thread: ChatThread):
        self.ChatThread = chat_thread

    def __getitem__(self, key):
        # Delegate attribute lookup to the underlying ChatThread instance
        return getattr(self.ChatThread, key)

    def __getattr__(self, name):
        # Delegate attribute lookup to the underlying ChatThread instance
        return getattr(self.ChatThread, name)


@pytest.fixture
def mock_onboarding_threads(mock_user) -> list[FakeRow]:
    chat_thread = ChatThread(users_id=mock_user.id, title="Onboarding")
    chat_thread.id = 1
    return [FakeRow(chat_thread)]


@pytest.fixture
def mock_payment_profile():
    return {
        "users_id": 104,
        "address": "12345 Elm Street",
        "card_cvc": "tok_sandbox_Egsn7pmLLABsqAPUobHhT",
        "card_number": "***************",
        "card_type": "",
        "cardholder_name": "Chundong Wang",
        "city": "Seattle",
        "dob": "01/10/1991",
        "email": None,
        "exp_date": "12 / 2034",
        "first_name": "Chundong",
        "frequentFlyerNumbers": [
            {"IATACode": "DL", "number": "*********"},
            {"IATACode": "AA", "number": "*********"},
            {"IATACode": "UA", "number": "*********"},
        ],
        "gender": "MALE",
        "last_name": "Wang",
        "phone": "*********0",
        "redress_number": "",
        "state": "WA",
        "title": "MR",
        "traveler_number": "",
        "zip_code": "12345",
    }


@pytest.fixture
def mock_user_preference_json():
    """Mock travel context based on CD's initial preference"""
    return ResponseAllPreferences(
        **json.loads(
            """
        {
            "preferred_home_airport": "SEA",
            "preferred_airline_brands": [
                "American Airlines",
                "Delta Air Lines"
            ],
            "preferred_cabin": [
                "Economy"
            ],
            "preferred_seats": ["Window"],
            "preferred_hotel_brands": [
                "Marriott",
                "Hilton"
            ],
            "preferred_travel_misc": [
                "Non-stop flights",
                "Central hotel locations"
            ]
        }
    """
        )
    )


@pytest.fixture
def mock_travel_context_json():
    """Mock travel context based on CD's initial travel context"""
    return json.loads(
        """
        {
            "trip_destination": null,
            "trip_purpose": null,
            "trip_start_date": null,
            "trip_end_date": null,
            "hotel_location_preference": null,
            "trip_origin_airport": null,
            "trip_destination_airport": null,
            "trip_airline_brands": [],
            "trip_cabin": [],
            "trip_seats": [],
            "trip_hotel_brands": [],
            "trip_travel_misc": [],
            "trip_outbound_departure_time": null,
            "trip_return_departure_time": null
        }
    """
    )


@pytest.fixture(autouse=True)
def cleanup_logging():
    """Cleanup logging handlers after each test"""
    yield
    # Remove all handlers after test
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        handler.close()


@pytest.fixture(scope="session")
def event_loop(request) -> Generator:
    """Create an instance of the default event loop for each test case."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
