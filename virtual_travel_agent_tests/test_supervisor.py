"""Supervisor test cases"""

import logging
from typing import Any
from unittest.mock import patch

import pytest
from langchain_core.messages import (
    AIMessage,
    HumanMessage,
)

import server.utils.settings  # noqa
from baml_client.types import TravelContext
from virtual_travel_agent.common_models import AgentState
from virtual_travel_agent.supervisor import SupervisorHelper

logger = logging.getLogger(__name__)


@patch("virtual_travel_agent.helpers.get_current_date_string", "November 13th 2024")
@pytest.mark.asyncio
async def test_start_of_hotel_supervisor_classification(mock_travel_context_json: Any):
    # load travel context
    travel_context = TravelContext.model_validate(mock_travel_context_json)
    assert travel_context.base_travel_preferences.preferred_home_airport is not None

    supervisor_helper = SupervisorHelper()
    state: AgentState = {
        "messages": [
            AIMessage(
                content="I'll start planning a new trip for you. Please tell me where you are going and your travel dates. "
            ),
            HumanMessage(content="I need a hotel room, 2BR, in NYC within 2 miles of central park for november 27-30"),
        ],
        "travel_context": travel_context,
    }
    res = await supervisor_helper.supervisor_runnable_function(state=state)
    assert "supervisor_message_classification" in res
    assert "travel_context" in res
    res_travel_context: TravelContext = res["travel_context"]
    logger.debug("Travel context output: %s", res_travel_context.model_dump_json())
    assert res["supervisor_message_classification"] == "Hotels"
    assert res_travel_context.trip_start_date == "2024-11-27"
    assert res_travel_context.trip_end_date == "2024-11-30"
    assert res_travel_context.trip_destination == "New York City"


@patch("virtual_travel_agent.helpers.get_current_date_string", "November 13th 2024")
@pytest.mark.asyncio
async def test_start_of_flight_supervisor_classification(mock_travel_context_json: Any):
    # load travel context
    travel_context = TravelContext.model_validate(mock_travel_context_json)
    assert travel_context.base_travel_preferences.preferred_home_airport is not None

    supervisor_helper = SupervisorHelper()
    state: AgentState = {
        "messages": [
            AIMessage(
                content="I'll start planning a new trip for you. Please tell me where you are going and your travel dates. "
            ),
            HumanMessage(content="flight to chicago on 27th, back on 30th"),
        ],
        "travel_context": travel_context,
    }
    res = await supervisor_helper.supervisor_runnable_function(state=state)
    res_travel_context: TravelContext = res["travel_context"]
    logger.debug("Travel context output: %s", res_travel_context.model_dump_json())
    assert res["supervisor_message_classification"] == "Flights"
    assert res_travel_context.trip_start_date == "2024-11-27"
    assert res_travel_context.trip_end_date == "2024-11-30"
    assert res_travel_context.trip_destination == "Chicago"
