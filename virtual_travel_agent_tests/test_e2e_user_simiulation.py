import copy
import logging
from datetime import datetime
from unittest.mock import As<PERSON><PERSON>ock, MagicMock, patch

import pytest

from baml_client import b, types
from virtual_travel_agent_tests.utils import (
    append_message_to_file,
    get_message_buffer_as_strings,
)

logger = logging.getLogger(__name__)

default_hotel_requirement = """
User background: You are a business traveler known for your highly articulate and courteous nature. You enjoy lively discussions and ask detailed about every aspect of the travel arrangements.

Scenario: Do hotel booking.

Constraints:
    - All travel location should be in United States.
    - Focus on Business travel
    - Book hotel only.

Ending condition:
    - The conversation should stop after you clearly received the hotel booking success result from the agent, for example you should see order id and reservation number
"""

default_hotel_cancel_requirement = """
User background: You have limited budget, and you are a business traveler, and you are picky and always asking for questions about hotel.

Scenario: Do hotel booking and cancel it.

Constraints:
    - All travel location should be in United States.
    - Focus on Business travel
    - Book hotel only.

Ending condition:
    - The conversation should stop after you clearly have the confirmation that the hotel booking is cancelled.
"""

default_oneway_flight_requirement = """
User background: You are a frustrated business traveler who prefers minimal conversation.
You don't like to talk much and expect quick, efficient communication.

Scenario: Book a one-way flight.

Constraints:
    - All travel location should be in United States.
    - Focus on Business travel
    - Book flight only.
    - Avoid Dallas to Chicago line for now.

Ending condition:
    - The conversation should stop after you clearly received the flight booking success result from the agent after you confirmed the booking, 
    for example you should see flight ticket number or order number after you confirmed the booking.
"""

default_roundtrip_flight_requirement = """
User background: You are a frustrated business traveler who prefers minimal conversation.
You don't like to talk much and expect quick, efficient communication.

Scenario: Book a roundtrip flight, short trip (2-4 days) in near future (2-4 weeks).

Constraints:
    - All travel location should be in United States.
    - Focus on Business travel
    - Book flight only.
    - Avoid Dallas to Chicago line for now.

Ending condition:
    - The conversation should stop after you clearly received the flight booking success result from the agent after you confirmed the booking, 
    for example you should see flight ticket number or order number after you confirmed the booking.
"""


# fmt: off
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "test_scenario, test_requirement, convo_reply_jsonl_filepath",
    [
        ("Hotel", default_hotel_requirement, ""),
        ("HotelCancel", default_hotel_cancel_requirement, ""),
        ("OnewayFlight", default_oneway_flight_requirement, ""),
        ("RoundtripFlight", default_roundtrip_flight_requirement, ""),

        # To run a Replay: 
        # 1. Add your .jsonl conversation file to virtual_travel_agent_tests/
        # 2. For debugging specific issues, you can truncate the .jsonl file to stop at the problematic point
        # ("Reply", "", "./virtual_travel_agent_tests/e2e_conversation_test.jsonl"),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_e2e_user_simiulation(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    test_scenario,
    test_requirement,
    convo_reply_jsonl_filepath,
    mock_user,
    mock_user_profile,
    mock_thread,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        import json

        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        test_plan: types.TestPlan | None = None
        history_user_replies = []

        processed_message_count = 0
        try:
            current_date = datetime.now()
            full_conversion_messages = []

            if convo_reply_jsonl_filepath:
                with open(convo_reply_jsonl_filepath, "r") as f:
                    for line in f:
                        data = json.loads(line)
                        if "test_plan" in data:
                            test_plan_dict = data["test_plan"]
                            test_plan = types.TestPlan(**test_plan_dict)
                        elif "test_user_reply" in data:
                            history_user_replies.append(data)

            else:
                test_plan = await b.GenerateTestPlan(test_requirement)

            append_message_to_file({
                "test_plan": test_plan.model_dump(),
            })
            
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "trip_init", "tripId": mock_thread.id},
            )

            # just expect the first assistant message
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            for m in messages:
                processed_message_count += 1
                logger.debug("[TripInit] message: %s", m)
                full_conversion_messages.append(m)
                append_message_to_file(m)
            assert all(m["status"] == "success" for m in messages)

            should_conversation_stop = False
            max_turns = 30
            turn_count = 0
            while not should_conversation_stop:
                turn_count += 1

                user_reply = ""
                # Generate user message
                if len(history_user_replies) > 0:
                    reply = history_user_replies.pop(0)
                    user_reply = reply["test_user_reply"]
                    explanation = reply.get("explanation", "")
                    append_message_to_file({
                        "test_user_reply": user_reply,
                        "explanation": explanation,
                        "is_reply_from_history": True,
                    })
                else:
                    input_messages = get_message_buffer_as_strings(full_conversion_messages)
                    result = await b.GenerateTestUserResponse(test_plan, input_messages, current_date.strftime("%B %dth %Y"))
                    if result.stop_with_incapable:
                        should_conversation_stop = True
                        error_message = "Conversation stopped because the assistant is incapable of serving the request"
                        logger.error(error_message)
                        append_message_to_file({
                            "status": "error",
                            "message": error_message,
                        })
                        raise Exception(error_message)

                    should_conversation_stop = result.completion_criteria_met
                    user_reply = result.test_user_reply
                    # Record the user message
                    append_message_to_file(result.model_dump())

                full_conversion_messages.append({
                    "content": user_reply,
                })

                # Send user message
                await ws_manager.on_receive(
                    websocket=mock_websocket,
                    message={
                        "type": "prompt",
                        "tripId": mock_thread.id,
                        "text": user_reply,
                    },
                )

                # Evaluation otto response
                assert mock_websocket.send_json.call_count > 0
                messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
                assert all(m["status"] == "success" for m in messages)

                # Post-process messages
                for m in messages[processed_message_count:]:
                    assistant_message = m
                    full_conversion_messages.append(copy.deepcopy(assistant_message))
                    append_message_to_file(assistant_message)
                    if assistant_message["type"] == "prompt":
                        assert assistant_message["text"] != ""

                processed_message_count = mock_websocket.send_json.call_count
                
                if turn_count >= max_turns:
                    logger.error("Reached max turns")
                    error_message = {"status": "error", "message": "Reached max turns"}
                    append_message_to_file(error_message)
                    raise Exception("Reached max turns")
            

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            logger.error(e)
            raise
