import logging
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

logger = logging.getLogger(__name__)


def cheapest_flight_n_button(last_message, _):
    """
    Default implementation for selecting a flight.
    This selects the flight with the lowest price.
    """
    logger.info("[Callback] Selecting the cheapest flight via clicking the button.")
    flights = last_message["flights"]
    selected_flight = min(flights, key=lambda flight: float(flight["price"]["amount"]))
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": selected_flight["action"],
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


default_start_date = datetime(2025, 5, 12)
default_flight_prompt = f"one-way to SFO UA on {default_start_date.strftime('%b %d')}"


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="November 13th 2024")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "start_trip_prompt, flight_selection_callback, flight_selection_callback_param, confirm_to_book_prompt, start_cancel_prompt, go_ahead_cancel",
    [(default_flight_prompt, cheapest_flight_n_button, "", "confirm", "I want to cancel flight", "go ahead")],
)
# fmt: on
@pytest.mark.asyncio
async def test_cancel_flight(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    _,
    start_trip_prompt,
    flight_selection_callback,
    flight_selection_callback_param,
    confirm_to_book_prompt,
    start_cancel_prompt,
    go_ahead_cancel,
    mock_user,
    mock_user_profile,
    mock_thread,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("server.utils.smtp.send_booking_email", new_callable=AsyncMock), \
            patch("server.services.calendar_api.calendar_provider.CalendarProviderManager.has_calendar_access", return_value=False), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        try:
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "trip_init", "tripId": mock_thread.id},
            )

            # Step 1. Send flight prompt
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_trip_prompt,
                },
            )

            # at this point flight search for the outbound is done
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info(
                "[FlightSearch] Search result: %d flights found",
                len(last_message["flights"]),
            )

            # Step 2. pick the outbound flight with a prompt (click or text)
            flight_selection = flight_selection_callback(last_message, flight_selection_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            # confirm seat selection.
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "Sure",
                },
            )

            # at this point flight validation is done
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info("[FlightValidation] Validation result: %s", last_message["text"])
            # Step 3. confirm the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            # at this point flight should have been booked
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            logger.info("[FlightBooking] Booking result: %s", messages[-3]["text"])

            # Step 4. confirm cancel the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_cancel_prompt,
                },
            )
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            logger.info("[FlightCancel] Confirm Cancel result: %s", messages[-1]["text"])

            # Step 5. go ahead cancel the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": go_ahead_cancel,
                },
            )

            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            logger.info("[FlightCancel] Go ahead Cancel result: %s", messages[-1]['text'])
            assert "cancelled" in messages[-1]['text']

            # Step 6. aks cancel again would said it is already cancelled.
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_cancel_prompt,
                },
            )

            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            logger.info("[FlightCancel] Ask cancel again result: %s", messages[-1]['text'])
            assert "already" in messages[-1]['text']

        except Exception as e:
            logger.error(e)
            raise
