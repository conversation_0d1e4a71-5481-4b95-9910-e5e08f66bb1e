import json
import logging
from math import atan2, cos, radians, sin, sqrt
from typing import List

from server.utils.settings import settings

logger = logging.getLogger(__name__)


def is_within_distance(lat1, lon1, lat2, lon2, max_distance_miles):
    """
    Check if two geographical points are within a specified distance.
    """
    # Earth radius in miles
    EARTH_RADIUS = 3958.8

    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    # Distance in miles
    distance = EARTH_RADIUS * c
    return distance <= max_distance_miles


def get_ordinal_suffix(day):
    if 11 <= day <= 13:  # Special case for 11th, 12th, 13th
        return "th"
    last_digit = day % 10
    if last_digit == 1:
        return "st"
    elif last_digit == 2:
        return "nd"
    elif last_digit == 3:
        return "rd"
    else:
        return "th"


def format_date_with_ordinal(date):
    """Format a date as 'DDth' (e.g., '21st')"""
    return f"{date.day}{get_ordinal_suffix(date.day)}"


def cheapest_room_n_button(last_message, _):
    """
    Default implementation for selecting a room.
    This selects the room with the lowest price.
    """
    logger.info("[Callback] Selecting the cheapest room via clicking the button.")
    accommodations = last_message["accommodations"]
    all_rooms = [
        {"room": room, "accommodation": accommodation}
        for accommodation in accommodations
        for room in accommodation["rooms"]
    ]

    selected_room = min(all_rooms, key=lambda room: float(room["room"]["price"]))
    logger.info("[Callback] Selected room: %s", selected_room)
    return {
        "prompt": selected_room["room"]["action"],
        "room_name": selected_room["room"]["option_title"],
        "room_id": selected_room["room"]["id"],
        "accommodation_name": selected_room["accommodation"]["hotel"],
        "accommodation_id": selected_room["accommodation"]["id"],
        "selected_room": selected_room,
    }


def cheapest_room_n_fuzzy(last_message, room_hotel_template: str):
    """
    Pick the cheapest room and plug into the room_hotel_template.
    """
    accommodations = last_message["accommodations"]
    all_rooms = [
        {"room": room, "accommodation": accommodation}
        for accommodation in accommodations
        for room in accommodation["rooms"]
    ]
    selected_room = min(all_rooms, key=lambda room: float(room["room"]["price"]))

    return {
        "prompt": room_hotel_template.format(
            room_name=selected_room["room"]["option_title"],
            property_name=selected_room["accommodation"]["hotel"],
        ),
        "room_name": selected_room["room"]["option_title"],
        "room_id": selected_room["room"]["id"],
        "accommodation_name": selected_room["accommodation"]["hotel"],
        "accommodation_id": selected_room["accommodation"]["id"],
        "selected_room": selected_room,
    }


def pricest_room_n_fuzzy(last_message, room_hotel_template: str):
    """
    Pick the pricest room and plug into the room_hotel_template.
    """
    accommodations = last_message["accommodations"]
    all_rooms = [
        {"room": room, "accommodation": accommodation}
        for accommodation in accommodations
        for room in accommodation["rooms"]
    ]
    selected_room = max(all_rooms, key=lambda room: float(room["room"]["price"]))

    return {
        "prompt": room_hotel_template.format(
            room_name=selected_room["room"]["option_title"],
            property_name=selected_room["accommodation"]["hotel"],
        ),
        "room_name": selected_room["room"]["option_title"],
        "room_id": selected_room["room"]["id"],
        "accommodation_name": selected_room["accommodation"]["hotel"],
        "accommodation_id": selected_room["accommodation"]["id"],
        "selected_room": selected_room,
    }


def memory_clean_up(mock_user_with_memory):
    from server.services.memory.memory_manager import MemoryStore

    memory_manager = MemoryStore()
    memory_manager.reset_user_memory(mock_user_with_memory.id)

    memories = memory_manager.get_user_history(mock_user_with_memory.id)
    if memories and "results" in memories:
        assert len(memories["results"]) == 0


def get_message_buffer_as_strings(messages: List[dict]) -> List[str]:
    buffer_strings = []
    for message in messages:
        # bot messages are either AIMessage or FunctionMessage
        is_bot_message = message.get("isBotMessage", False)
        if is_bot_message:
            arguments_string = ""

            # Pick one of these below as the arguments_string
            # accomodations
            if message.get("accommodations"):
                accommodations = message.get("accommodations")
                arguments_string = json.dumps(simplify_accommodations_for_user_input(accommodations))
            elif message.get("flights"):
                flights = message.get("flights")
                arguments_string = json.dumps(simplify_flights_for_user_input(flights))

            # other functions calls
            elif message.get("additional_kwargs", {}).get("function_call"):
                arguments_string = json.dumps(
                    message.get("additional_kwargs", {}).get("function_call").get("arguments", "")
                )

            assistant_message = ""
            if message.get("content"):
                assistant_message = message["content"]
            else:
                assistant_message = message.get("text", "")

            buffer_strings.append(f"assistant {assistant_message} {arguments_string}")
        else:
            content = message.get("content")
            if content:
                buffer_strings.append(f"user {content}")
            else:
                continue

    buffer_strings = remove_sequential_duplicates(buffer_strings)

    return buffer_strings


def remove_sequential_duplicates(strings):
    if not strings:
        return []

    # Initialize the result list with the first element
    result = [strings[0]]

    # Iterate through the list starting from the second element
    for current in strings[1:]:
        if current != result[-1]:
            result.append(current)

    return result


def simplify_accommodations_for_user_input(accommodations):
    simplified = []

    for acc in accommodations:
        simplified_acc = {
            "hotel": acc["hotel"],
            "highlight": acc.get("highlight"),
            "rating": acc.get("rating"),
            "hotel_class": acc.get("hotel_class"),
            "price": acc.get("price"),
            "check_in_time": acc["check_in_time"],
            "check_out_time": acc["check_out_time"],
            "within_policy": acc.get("within_policy"),
            "amenities": acc.get("amenities", []),
            "mapMarker": {"address": acc["mapMarker"]["address"], "text": acc["mapMarker"]["text"]},
            "recommendationReasons": acc.get("recommendationReasons", {}),
            "rooms": [],
        }

        # Simplify each room
        for room in acc.get("rooms", []):
            simplified_room = {
                "price": room["price"],
                "pricePerNight": room["pricePerNight"],
                "taxAndFees": room["taxAndFees"],
                "priceExcludingFees": room["priceExcludingFees"],
                "number_of_nights": room["no_nights"],
                "title": room["option_title"],
                "options": room["options"],
                "within_policy": room.get("within_policy"),
                "within_or_out_policy_reason": room.get("within_or_out_policy_reason"),
            }
            simplified_acc["rooms"].append(simplified_room)

        simplified.append(simplified_acc)

    return simplified


def simplify_message_for_recording(data):
    result = None
    # Case 1: Extract test_user_reply and explanation
    if "test_user_reply" in data:
        result = {"test_user_reply": data["test_user_reply"], "explanation": data["explanation"]}

    # Case 2: Extract bot messages
    elif data.get("isBotMessage") is True and "text" in data:
        result = {"otto_message": data["text"]}

    # Case 3: Extract error messages
    elif data.get("status") == "error" and "message" in data:
        result = {"error": data["message"]}

    # Case 4: Extract test_plan
    elif "test_plan" in data:
        result = {"test_plan": data["test_plan"]}

    return result


def append_message_to_file(
    message, filename="./e2e_conversation_test.jsonl", simplified_filename="./e2e_conversation_test_simplified.jsonl"
):
    """
    Append a single message to the specified JSON Lines file.

    Args:
        message: The message object to append
        filename: The target file path (default: ./e2e_conversation_test.jsonl)
    """
    try:
        if settings.is_dev_env:
            with open(filename, "a") as f:
                f.write(json.dumps(message) + "\n")

            with open(simplified_filename, "a") as f:
                simplified_message = simplify_message_for_recording(message)
                if simplified_message:
                    f.write(json.dumps(simplified_message, ensure_ascii=False, indent=2) + "\n")

    except Exception as e:
        logger.error(f"Failed to append message to {filename}: {e}")


def simplify_flights_for_user_input(flights):
    """
    Simplify flight data by removing unnecessary fields and maintaining essential information.

    Args:
        flights: List of flight dictionaries

    Returns:
        List of simplified flight dictionaries
    """
    simplified = []

    for flight in flights:
        simplified_flight = {
            "price": flight["price"],
            "type": flight["type"],
            "cabin": flight["cabin"],
            "exchange_policy": flight["exchange_policy"],
            "cancellation_policy": flight["cancellation_policy"],
            "fare_option_name": flight["fare_option_name"],
            "flight_segments": flight["flight_segments"],
            "recommendationReasons": flight["recommendationReasons"],
        }
        simplified.append(simplified_flight)

    return simplified
