import asyncio
import locale
import logging
from datetime import datetime
from enum import Enum
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from dateutil.relativedelta import relativedelta

from baml_client import b
from virtual_travel_agent_tests.utils import (
    cheapest_room_n_button,
    cheapest_room_n_fuzzy,
    format_date_with_ordinal,
    is_within_distance,
    pricest_room_n_fuzzy,
)

logger = logging.getLogger(__name__)


class HotelSteps(Enum):
    INIT_TRIP = "INIT_TRIP"
    SEARCH = "HOTEL_SEARCH"
    VALIDATION = "HOTEL_VALIDATION"
    BOOKING = "HOTEL_BOOKING"

    def __str__(self):
        return self.value


# Set in the year 2384, Altered Carbon follows <PERSON><PERSON>, a former elite soldier turned private investigator,
# who awakens in a new body at the Raven Hotel in Bay City (a futuristic San Francisco).
# <PERSON><PERSON><PERSON> begins investigating this “murder” shortly after being resleeved into a new body on October 21.
# <PERSON><PERSON>’s death, or rather the destruction of his physical body, occurs on October 14, a week prior.
now = datetime.now()
next_next_month_start = (now + relativedelta(months=2)).replace(day=1)
next_next_month_last_day = next_next_month_start + relativedelta(months=1, days=-1)
default_start_date = next_next_month_last_day - relativedelta(days=6)
default_end_date = next_next_month_last_day

default_start_date = now + relativedelta(months=2)
default_end_date = now + relativedelta(months=2, weeks=1)
default_hotel_prompt = f"Book me a room with Hyatt in San Francisco close to Mission Bay, {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}"
target_location = {"latitude": 37.770104, "longitude": -122.389304}  # Mission Bay


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="November 13th 2024")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock)
@pytest.mark.parametrize(
    "step_to_stop_test, start_trip_prompt, hotel_selection_callback, hotel_selection_callback_param, confirm_to_book_prompt",
    [
        # search for hotel
        (HotelSteps.SEARCH, default_hotel_prompt, "", "", ""),
        (HotelSteps.SEARCH, f"Find me a Hyatt near Mission Bay, SF, for {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Can you secure a room at Hyatt close to Mission Bay, San Francisco, from the {format_date_with_ordinal(default_start_date)} to the {format_date_with_ordinal(default_end_date)} of {default_end_date.strftime('%B')}?", "", "", ""),
        (HotelSteps.SEARCH, f"Hyatt, Mission Bay, SF, {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}. Book it.", "", "", ""),
        (HotelSteps.SEARCH, f"Reserve a room at the Hyatt by Mission Bay, SF, {default_end_date.strftime('%B')} {format_date_with_ordinal(default_start_date)} through {format_date_with_ordinal(default_end_date)}.", "", "", ""),
        (HotelSteps.SEARCH, f"I need a Hyatt room in SF, Mission Bay vicinity, for {default_start_date.strftime('%b %d')} to {default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Book a stay for me at a Hyatt in Mission Bay, San Francisco, from {default_start_date.strftime('%B %d')} to {default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Please arrange accommodation at Hyatt, Mission Bay area, for {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')} at SF", "", "", ""),
        (HotelSteps.SEARCH, f"Hyatt, San Francisco, near Mission Bay, {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')} – make it happen.", "", "", ""),
        (HotelSteps.SEARCH, f"Find a Hyatt near Mission Bay, SF, for my {default_start_date.strftime('%b %d')} to {default_end_date.strftime('%b %d')} trip.", "", "", ""),
        (HotelSteps.SEARCH, f"Need a room at Hyatt, close to Mission Bay, SF, {default_start_date.strftime('%B %d')}-{default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Hyatt, Mission Bay at San Francisco, {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}. Room for me.", "", "", ""),
        (HotelSteps.SEARCH, f"Book me a place at Hyatt near Mission Bay in SF, {default_start_date.strftime('%B %d')} to {default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Secure me a room at a Hyatt near Mission Bay, SF, {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Sort out a Hyatt booking near Mission Bay, SF, for {default_start_date.strftime('%B %d')} to {default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"I’d like a room at the Hyatt near Mission Bay, San Francisco, for {default_start_date.strftime('%b %d')} through {default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Set me up at Hyatt near Mission Bay, SF, for {default_start_date.strftime('%B %d')} to {default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Need a Hyatt room in Mission Bay area, San Francisco, for {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}.", "", "", ""),
        (HotelSteps.SEARCH, f"Please book a Hyatt hotel room near Mission Bay, San Francisco, from the {format_date_with_ordinal(default_start_date)} to the {format_date_with_ordinal(default_end_date)} of {default_start_date.strftime('%B')}.", "", "", ""),
        # validate room of hotel
        (HotelSteps.VALIDATION, default_hotel_prompt, cheapest_room_n_button, "", ""),
        (HotelSteps.VALIDATION, default_hotel_prompt, cheapest_room_n_fuzzy, "I'll pick {room_name} of {property_name}", ""),
        (HotelSteps.VALIDATION, default_hotel_prompt, pricest_room_n_fuzzy, "I'll pick {room_name} of {property_name}", ""),
        # confirm and book a room
        (HotelSteps.BOOKING, default_hotel_prompt, cheapest_room_n_button, "", "confirm"),
        (HotelSteps.BOOKING, default_hotel_prompt, cheapest_room_n_button, "", "Go ahead and book it."),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_hotel_book_w_hyatt_in_sf(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile, mock_payment_profile_get,
    _,
    step_to_stop_test,
    start_trip_prompt,
    hotel_selection_callback,
    hotel_selection_callback_param,
    confirm_to_book_prompt,
    mock_user,
    mock_user_profile,
    mock_thread,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_payment_profile_get.return_value = mock_payment_profile
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("virtual_travel_agent.langchain_chat_persistence.PostgresChatMessageHistory.apersist", new_callable=AsyncMock), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        try:
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "trip_init", "tripId": mock_thread.id},
            )

            # just expect the first assistant message
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            for m in messages:
                logger.debug("[TripInit] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)

            # Step 1. Send user initial prompt
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_trip_prompt,
                },
            )

            await asyncio.sleep(30)

            # at this point hotel search is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            hotel_search_length = len(messages)
            for m in messages[processed_message_count:]:
                logger.debug("[HotelSearch] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            assert any(m["type"] == "hotels_skeleton_async" for m in messages[-5:])
            with_travel_context = [m for m in messages[-5:] if m.get("travel_context")]
            last_message = with_travel_context[-1]
            assert last_message["type"] == "prompt"
            assert last_message["expectResponse"]
            assert last_message["isBotMessage"]
            with_accommodations = [m for m in messages[-5:] if m.get("accommodations")]
            assert (
                "San Francisco" in last_message["travel_context"]["hotel_search_core_criteria"]["city"]
                or "SFO" in last_message["travel_context"]["hotel_search_core_criteria"]["city"]
                or "SF" in last_message["travel_context"]["hotel_search_core_criteria"]["city"]
            )
            assert last_message["travel_context"]["hotel_search_core_criteria"]["check_in_date"] == default_start_date.strftime("%Y-%m-%d")
            assert last_message["travel_context"]["hotel_search_core_criteria"]["check_out_date"] == default_end_date.strftime("%Y-%m-%d")
            assert is_within_distance(
                float(last_message["travel_context"]["hotel_search_core_criteria"]['location_latitude_longitude'].split(",")[0]),
                float(last_message["travel_context"]["hotel_search_core_criteria"]['location_latitude_longitude'].split(",")[1]),
                target_location['latitude'],
                target_location['longitude'],
                max_distance_miles=1
            ), "Location is not within 1 mile of the target location."
            # All accomodations are hyatt as requested (?)
            last_message = with_accommodations[-1]
            assert all("HYATT" in accomodation['hotel'].upper() for accomodation in last_message["accommodations"])
            # All accomodations have a recommendation reason
            assert all(len(accomodation["recommendationReasons"]["choice_characteristic"]) > 0 for accomodation in last_message["accommodations"])
            assert all(len(accomodation["recommendationReasons"]["preference_alignment_reason"]) > 0 for accomodation in last_message["accommodations"])
            assert all(len(accomodation["recommendationReasons"]["distinction_reason_one"]) > 0 for accomodation in last_message["accommodations"])
            assert all(len(accomodation["recommendationReasons"]["distinction_reason_two"]) > 0 for accomodation in last_message["accommodations"])
            # All rooms have a price
            assert all(room['price'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(room['pricePerNight'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(room['taxAndFees'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(room['priceExcludingFees'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(room['no_nights'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            # All rooms have a photo
            assert all(len(room['room_photo']) > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(len(room['image']["src"]) > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            logger.info(
                "[HotelSearch] Search result: %d rooms of %d accommodations found",
                sum(len(accommodation['rooms']) for accommodation in last_message["accommodations"]),
                len(last_message["accommodations"]),
            )
            if step_to_stop_test == HotelSteps.SEARCH:
                logger.info("[HotelSearch] Stopping test at step: %s", step_to_stop_test)
                return

            # Step 2. pick the room with a prompt (click or text)
            room_selection = hotel_selection_callback(last_message, hotel_selection_callback_param)
            pick_room_prompt = room_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_room_prompt,
                },
            )
            
            # wait because of post-processing
            await asyncio.sleep(15)

            # at this point hotel validation is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages[processed_message_count:]:
                logger.debug("[HotelValidation] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)

            # since asyncio.create_task is used, the order of messages is not guaranteed
            # so messages from index: search_message_length should be checked for trip_update, any one of them can be the trip_update.
            assert any(m["type"] == "trip_update" for m in messages[hotel_search_length:])

            # first message that is not trip_update from the end
            last_message = next(filter(lambda m: m["type"] != "trip_update", messages[::-1]))

            # hotel details like room/hotel name and price should be listed in the response.
            assertion_result = await b.AssertDynamicly(f"The room name: {room_selection['room_name'] } can be found with similar name inside {last_message['text']}. similar is good enough, no need to be exact same.")
            assert assertion_result.is_expected, assertion_result.reason
            assertion_result = await b.AssertDynamicly(f"The hotel name: {room_selection['accommodation_name'] } can be found with similar name inside {last_message['text']}. similar is good enough, no need to be exact same.")
            assert assertion_result.is_expected, assertion_result.reason
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
            formatted_price = locale.currency(room_selection['selected_room']['room']['price'], grouping=True)[1:]  # Removes the dollar sign
            assert formatted_price in last_message["text"] or str(room_selection['selected_room']['room']['price']) in last_message["text"]
            logger.info("[HotelValidation] Validation result: %s", last_message["text"])
            if step_to_stop_test == HotelSteps.VALIDATION:
                logger.info("[HotelValidation] Stopping test at step: %s", step_to_stop_test)
                return

            # Step 3. confirm the hotel
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            await asyncio.sleep(20)

            # at this point flight should have been booked
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages[processed_message_count:]:
                logger.debug("[HotelBooking] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)

            # since asyncio.create_task is used, the order of messages is not guaranteed
            # so messages from index: search_message_length should be checked for trip_update, any one of them can be the trip_update
            assert any(m["type"] == "trip_update" for m in messages[hotel_search_length:])

            # first message that is not trip_update from the end
            last_message = list(filter(lambda m: m["type"] != "trip_update", messages[::-1]))

            logger.info("[HotelBooking] Booking result: %s", last_message)
            # assert not last_message["expectResponse"]
            assert any("Order Number" in m["text"] for m in last_message)
            assert any("Reservation Number" in m["text"] for m in last_message)
            if step_to_stop_test == HotelSteps.BOOKING:
                logger.info("[HotelBooking] Stopping test at step: %s", step_to_stop_test)
                return

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            logger.error(e)
            raise
