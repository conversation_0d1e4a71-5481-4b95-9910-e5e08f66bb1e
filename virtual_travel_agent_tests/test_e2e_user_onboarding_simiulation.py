import copy
import logging
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from baml_client import b, types
from virtual_travel_agent_tests.utils import (
    append_message_to_file,
    get_message_buffer_as_strings,
)

logger = logging.getLogger(__name__)

default_onboarding_requirement = """
User background: You are a business traveler with good manner but prefers minimal conversation.
You are expecting quick, efficient communication.

Scenario: 
    - Onboarding conversation.
    - You are willing to share your calendar access
    - You are a new user of the app.

Ending condition:
    - The conversation should stop after you receive the onboarding success message from the agent.
"""


# fmt: off
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "test_scenario, login_method, test_requirement, convo_reply_jsonl_filepath",
    [
        ("Onboarding", "Google", default_onboarding_requirement, ""),
        ("Onboarding", "Microsoft", default_onboarding_requirement, ""),

        # To run a Replay: 
        # 1. Add your .jsonl conversation file to virtual_travel_agent_tests/
        # 2. For debugging specific issues, you can truncate the .jsonl file to stop at the problematic point
        # ("Reply", "", "./virtual_travel_agent_tests/e2e_convo_test_reply.jsonl"),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_e2e_user_onboarding_simiulation(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    test_scenario,
    login_method,
    test_requirement,
    convo_reply_jsonl_filepath,
    mock_user,
    mock_user_profile,
    mock_user_profile_microsoft,
    mock_onboarding_threads,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.services.calendar_api.google_calendar_events.GoogleCalendarEvents.has_calendar_access", return_value=False), \
            patch("llm_utils.llm_utils.load_past_calendar_events", return_value=[]), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile if login_method == "Google" else mock_user_profile_microsoft), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_onboarding_threads[0]), \
            patch("server.database.models.chat_thread.ChatThread.new_chat_thread", new_callable=AsyncMock, return_value=None), \
            patch("server.database.models.chat_thread.ChatThread.from_user_id_and_title", new_callable=AsyncMock, return_value=mock_onboarding_threads), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        import json

        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        test_plan: types.OnboardingTestPlan | None = None
        history_user_replies = []

        processed_message_count = 0
        has_otto_sent_login_prompt = False
        try:
            current_date = datetime.now()
            full_conversion_messages = []

            if convo_reply_jsonl_filepath:
                with open(convo_reply_jsonl_filepath, "r") as f:
                    for line in f:
                        data = json.loads(line)
                        if "test_plan" in data:
                            test_plan_dict = data["test_plan"]
                            test_plan = types.OnboardingTestPlan(**test_plan_dict)
                        elif "test_user_reply" in data:
                            history_user_replies.append(data)

            else:
                test_plan = await b.GenerateOnboardingTestPlan(test_requirement)

            append_message_to_file({
                "test_plan": test_plan.model_dump(),
            })
            
            # Step 0. Initiate the onboarding conversation
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "onboarding_init"},
            )

            # just expect the first assistant message
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            for m in messages:
                assert m["status"] == "success"
                processed_message_count += 1
                logger.debug("[OnboardingInit] message: %s", m)
                full_conversion_messages.append(m)
                append_message_to_file(m)

            should_conversation_stop = False
            max_turns = 30
            turn_count = 0
            while not should_conversation_stop:
                turn_count += 1

                user_reply = ""
                # Generate user message
                if len(history_user_replies) > 0:
                    reply = history_user_replies.pop(0)
                    user_reply = reply["test_user_reply"]
                    explanation = reply.get("explanation", "")
                    append_message_to_file({
                        "test_user_reply": user_reply,
                        "explanation": explanation,
                        "is_reply_from_history": True,
                    })
                else:
                    input_messages = get_message_buffer_as_strings(full_conversion_messages)
                    result = await b.GenerateOnboardingTestUserResponse(test_plan, input_messages, current_date.strftime("%B %dth %Y"))
                    if result.stop_with_incapable:
                        should_conversation_stop = True
                        error_message = "Conversation stopped because the assistant is incapable of serving the request"
                        logger.error(error_message)
                        append_message_to_file({
                            "status": "error",
                            "message": error_message,
                        })
                        raise Exception(error_message)

                    should_conversation_stop = result.should_conversation_stop
                    user_reply = result.test_user_reply
                    # Record the user message
                    append_message_to_file(result.model_dump())

                full_conversion_messages.append({
                    "content": user_reply,
                })

                # Send user message
                await ws_manager.on_receive(
                    websocket=mock_websocket,
                    message={
                        "type": "prompt",
                        "tripId": mock_onboarding_threads[0].id,
                        "text": user_reply,
                    },
                )

                # Evaluation otto response
                assert mock_websocket.send_json.call_count > 0
                messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
                assert all(m["status"] == "success" for m in messages)

                # Post-process messages
                for m in messages[processed_message_count:]:
                    assert m["status"] == "success"
                    assistant_message = m
                    full_conversion_messages.append(copy.deepcopy(assistant_message))
                    append_message_to_file(assistant_message)
                    
                    # normal prompt shouldn't be empty
                    if assistant_message["type"] == "prompt":
                        assert assistant_message["text"] != ""

                    # handle the onboarding success signal
                    if assistant_message["type"] == "onboarding_chat_complete":
                        should_conversation_stop = True
                        # assert if it's meet the test plan requirements
                        assert test_plan.should_grant_calendar_access == has_otto_sent_login_prompt, "The test plan requirement is not met for calendar access"
                        break

                    # handle the login redirect prompt
                    if assistant_message["type"] == "google_login" or assistant_message["type"] == "microsoft_login":
                        login_method = assistant_message["type"]
                        # throw if the login init url is not present for microsoft login
                        if assistant_message["type"] == "microsoft_login":
                            assert (assistant_message["loginInitUrl"] is not None and assistant_message["loginInitUrl"] != "")

                        # throw if it's the test plan is not expecting calendar access granting
                        assert test_plan.should_grant_calendar_access, "Assistant started proceed with calendar access but the test requirement is not to grant calendar access"

                        # assert if the lastest conversation history is actually asking for calendar access
                        assertion_result = await b.Assert(f"The conversation history is: \n\n {', '.join(get_message_buffer_as_strings(full_conversion_messages[-10:]))}", 
                                                          "User has agreed to grant the calendar access based on the conversation history")
                        assert assertion_result.is_expected, assertion_result.reason

                        has_otto_sent_login_prompt = True
                        # send the silent prompt to simulate accept granting calendar access
                        silent_prompt_message = {
                            "type": "silent_prompt",
                            "tripId": mock_onboarding_threads[0].id,
                            "text": "Calendar permission has been granted",
                        }
                        await ws_manager.on_receive(
                            websocket=mock_websocket,
                            message=silent_prompt_message,
                        )
                        append_message_to_file({
                            "test_user_reply": silent_prompt_message["text"],
                            "explanation": "Silent prompt for granting calendar access",
                            "is_silent_prompt": True,
                        })
                        full_conversion_messages.append(silent_prompt_message)
                        
                        otto_replies = []
                        messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
                        for m in messages[processed_message_count:]:
                            assert m["status"] == "success"
                            otto_replies.append(m)
                            full_conversion_messages.append(copy.deepcopy(m))
                            append_message_to_file(m)
                        
                        assert_result = await b.Assert(f"The conversation history is: \n\n {', '.join(get_message_buffer_as_strings(otto_replies))}", "The conversation history should contains thankfulness message about calendar access and calendar analysis result")
                        assert assert_result.is_expected, assert_result.reason
                        break

                processed_message_count = mock_websocket.send_json.call_count
                
                if turn_count >= max_turns:
                    logger.error("Reached max turns")
                    error_message = {"status": "error", "message": "Reached max turns"}
                    append_message_to_file(error_message)
                    raise Exception("Reached max turns")
            

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            logger.error(e)
            error_message = {"status": "error", "message": str(e)}
            append_message_to_file(error_message)
            raise
