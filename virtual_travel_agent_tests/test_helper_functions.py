import json

import pytest

from virtual_travel_agent.helpers import convert_flat_json_like


@pytest.mark.parametrize(
    "test_name, input_string",
    [
        (
            "no quotes and no array values",
            "{trip_destination: Los Angeles, trip_start_date: 2025-04-03, trip_end_date: 2025-04-12, trip_origin_airport: SEA, trip_destination_airport: LAX, trip_airline_brands: [], trip_cabin: [], trip_seats: []}",
        ),
        (
            "no quotes and arrays with values or not",
            "{trip_destination: Los Angeles, trip_start_date: 2025-04-03, trip_end_date: 2025-04-12, trip_origin_airport: SEA, trip_destination_airport: LAX, trip_airline_brands: [Delta Air Lines], trip_cabin: [Economy], trip_seats: []}",
        ),
        (
            "array with more than one values",
            "{trip_destination: Los Angeles, trip_start_date: 2025-04-03, trip_end_date: 2025-04-12, trip_origin_airport: SEA, trip_destination_airport: LAX, trip_airline_brands: [Delta Air Lines, Alaska], trip_cabin: [Economy], trip_seats: []}",
        ),
    ],
)
def test_convert_flat_json_like(test_name: str, input_string: str):
    formatted_json = convert_flat_json_like(input_string)
    json_dict = json.loads(formatted_json)
    assert f"{test_name} json loaded", json_dict is not None
    assert json_dict["trip_destination"] == "Los Angeles"
    assert json_dict["trip_start_date"] == "2025-04-03"
    assert json_dict["trip_airline_brands"] is None or isinstance(json_dict["trip_airline_brands"], list)


def test_empty_object():
    formatted_json = convert_flat_json_like("{}")
    json_dict = json.loads(formatted_json)
    assert json_dict is not None
    assert json_dict == {}


@pytest.mark.parametrize(
    "test_name, input_string",
    [
        (
            "nested structure",
            "{travel_context:{trip_destination: Los Angeles, trip_start_date: 2025-04-03, trip_end_date: 2025-04-12, trip_origin_airport: SEA, trip_destination_airport: LAX, trip_airline_brands: [Delta Air Lines, Alaska], trip_cabin: [Economy], trip_seats: []}}",
        ),
        (
            "literal array",
            "[]",
        ),
        (
            "literal string",
            '"any value"',
        ),
    ],
)
def test_non_flat_raise_error(test_name: str, input_string: str):
    with pytest.raises(ValueError):
        convert_flat_json_like(input_string)
