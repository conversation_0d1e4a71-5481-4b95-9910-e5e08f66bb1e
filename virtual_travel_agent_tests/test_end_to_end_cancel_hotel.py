import locale
import logging
from datetime import datetime
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from dateutil.relativedelta import relativedelta

from baml_client import b
from virtual_travel_agent_tests.utils import (
    cheapest_room_n_button,
    is_within_distance,
)

logger = logging.getLogger(__name__)


# Set in the year 2384, Altered Carbon follows <PERSON><PERSON>, a former elite soldier turned private investigator,
# who awakens in a new body at the Raven Hotel in Bay City (a futuristic San Francisco).
# <PERSON><PERSON><PERSON> begins investigating this “murder” shortly after being resleeved into a new body on October 21.
# <PERSON><PERSON>’s death, or rather the destruction of his physical body, occurs on October 14, a week prior.
now = datetime.now()
next_next_month_start = (now + relativedelta(months=2)).replace(day=1)
next_next_month_last_day = next_next_month_start + relativedelta(months=1, days=-1)
default_start_date = next_next_month_last_day - relativedelta(days=6)
default_end_date = next_next_month_last_day

default_start_date = now + relativedelta(months=2)
default_end_date = now + relativedelta(months=2, weeks=1)
default_hotel_prompt = f"Book me a room with Hyatt in San Francisco close to Mission Bay, {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}"
target_location = {"latitude": 37.770104, "longitude": -122.389304}  # Mission Bay

expected_cancel_hotel_response_criteria = "mention the order id, ask for confirmation for cancellation"
expected_hotel_cancelled_response_criteria = "state that the hotel booking has been cancelled"


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="2024-11-22")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "start_trip_prompt, hotel_selection_callback, hotel_selection_callback_param, confirm_to_book_prompt, cancel_hotel_prompt, assistant_cancel_hotel_response_criteria, cancel_hotel_confirmation_prompt, assistant_hotel_cancelled_response_criteria",
    [
        # search for hotel
        (default_hotel_prompt, cheapest_room_n_button, "", "confirm", "I want to cancel the hotel", expected_cancel_hotel_response_criteria, "cancel confirmed", expected_hotel_cancelled_response_criteria),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_hotel_book_w_hyatt_in_sf(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    _,
    start_trip_prompt,
    hotel_selection_callback,
    hotel_selection_callback_param,
    confirm_to_book_prompt,
    cancel_hotel_prompt,
    assistant_cancel_hotel_response_criteria,
    cancel_hotel_confirmation_prompt,
    assistant_hotel_cancelled_response_criteria,
    mock_user,
    mock_user_profile,
    mock_thread,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        try:
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "trip_init", "tripId": mock_thread.id},
            )

            # just expect the first assistant message
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            for m in messages:
                logger.debug("[TripInit] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)

            # Step 1. Send user initial prompt
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_trip_prompt,
                },
            )

            # at this point hotel search is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            hotel_search_length = len(messages)
            for m in messages[processed_message_count:]:
                logger.debug("[HotelSearch] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            assert any(m["type"] == "hotels_skeleton" for m in messages[-3:])
            last_message = messages[-1]
            assert last_message["type"] == "prompt"
            assert last_message["expectResponse"]
            assert last_message["isBotMessage"]
            assert (
                "San Francisco" in last_message["travel_context"]["trip_destination"]
                or "SFO" in last_message["travel_context"]["trip_destination"]
            )
            assert last_message["travel_context"]["trip_start_date"] == default_start_date.strftime("%Y-%m-%d")
            assert last_message["travel_context"]["trip_end_date"] == default_end_date.strftime("%Y-%m-%d")
            assert 'location_lat_long' in last_message
            assert is_within_distance(
                last_message['location_lat_long']['latitude'],
                last_message['location_lat_long']['longitude'],
                target_location['latitude'],
                target_location['longitude'],
                max_distance_miles=1
            ), "Location is not within 1 mile of the target location."
            # All accomodations are hyatt as requested (?)
            assert all("HYATT" in accomodation['hotel'].upper() for accomodation in last_message["accommodations"])
            # All accomodations have a recommendation reason
            assert all(len(accomodation["recommendationReasons"]["choice_characteristic"]) > 0 for accomodation in last_message["accommodations"])
            assert all(len(accomodation["recommendationReasons"]["preference_alignment_reason"]) > 0 for accomodation in last_message["accommodations"])
            assert all(len(accomodation["recommendationReasons"]["distinction_reason_one"]) > 0 for accomodation in last_message["accommodations"])
            assert all(len(accomodation["recommendationReasons"]["distinction_reason_two"]) > 0 for accomodation in last_message["accommodations"])
            # All rooms have a price
            assert all(room['price'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(room['pricePerNight'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(room['taxAndFees'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(room['priceExcludingFees'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(room['no_nights'] > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            # All rooms have a photo
            assert all(len(room['room_photo']) > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            assert all(len(room['image']["src"]) > 0 for accomodation in last_message["accommodations"] for room in accomodation['rooms'])
            logger.info(
                "[HotelSearch] Search result: %d rooms of %d accommodations found",
                sum(len(accommodation['rooms']) for accommodation in last_message["accommodations"]),
                len(last_message["accommodations"]),
            )

            # Step 2. pick the room with a prompt (click or text)
            room_selection = hotel_selection_callback(last_message, hotel_selection_callback_param)
            pick_room_prompt = room_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_room_prompt,
                },
            )

            # at this point hotel validation is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            for m in messages[processed_message_count:]:
                logger.debug("[HotelValidation] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)

            # since asyncio.create_task is used, the order of messages is not guaranteed
            # so messages from index: search_message_length should be checked for trip_update, any one of them can be the trip_update
            assert any(m["type"] == "trip_update" for m in messages[hotel_search_length:])

            # find proper validatin message. here just in case last one is trip_update, we need use second last.
            last_message = messages[-2] if messages[-1]["type"] == "trip_update" else messages[-1]
            # hotel details like room/hotel name and price should be listed in the response.
            assert room_selection["room_name"] in last_message["text"]
            assert room_selection["accommodation_name"] in last_message["text"]
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
            formatted_price = locale.currency(room_selection['selected_room']['room']['price'], grouping=True)[1:]  # Removes the dollar sign
            assert formatted_price in last_message["text"] or str(room_selection['selected_room']['room']['price']) in last_message["text"]
            logger.info("[HotelValidation] Validation result: %s", last_message["text"])

            # Step 3. confirm the hotel
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            # at this point hotel should have been booked
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            for m in messages[processed_message_count:]:
                logger.debug("[HotelBooking] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            last_message = messages[-1]
            assert last_message["isBotMessage"]
            logger.info("[HotelBooking] Booking result: %s", last_message["text"])
            # assert not last_message["expectResponse"]
            assert "Order Number" in last_message["text"]
            assert "Reservation Number" in last_message["text"]

            # Step 4. cancel the hotel
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": cancel_hotel_prompt,
                },
            )

            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info("[CancelHotelBooking] Booking message: %s", last_message)

            assertion_result = await b.Assert(last_message["text"], assistant_cancel_hotel_response_criteria)
            assert f"Reason: {assertion_result.reason}", assertion_result.is_expected
            
            # Step 5. confirm the cancellation
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": cancel_hotel_confirmation_prompt,
                },
            )

            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            last_message = messages[-1]
            logger.info("[CancelHotelBooking] Booking cancelled message: %s", last_message)

            assertion_result = await b.Assert(last_message["text"], assistant_hotel_cancelled_response_criteria)
            assert f"Reason: {assertion_result.reason}", assertion_result.is_expected


        except Exception as e:
            import traceback
            print(traceback.format_exc())
            logger.error(e)
            raise
