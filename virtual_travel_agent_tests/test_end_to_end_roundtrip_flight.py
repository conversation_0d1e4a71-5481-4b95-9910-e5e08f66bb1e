import asyncio
import logging
from datetime import datetime
from enum import Enum
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

logger = logging.getLogger(__name__)


class FlightRoundTripSteps(Enum):
    INIT_TRIP = "INIT_TRIP"
    FLIGHT_SEARCH_LEG1 = "FLIGHT_SEARCH_1"
    FLIGHT_SEARCH_LEG2 = "FLIGHT_SEARCH_2"
    FLIGHT_VALIDATION = "FLIGHT_VALIDATION"
    FLIGHT_BOOKING = "FLIGHT_BOOKING"

    def __str__(self):
        return self.value


def cheapest_flight_n_button(last_message, _):
    """
    Default implementation for selecting a flight.
    This selects the flight with the lowest price.
    """
    logger.info("[Callback] Selecting the cheapest flight via clicking the button.")
    flights = last_message["flights"]
    selected_flight = min(flights, key=lambda flight: float(flight["price"]["amount"]))
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": selected_flight["action"],
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


def earliest_flight_n_fuzzy(last_message, timed_template):
    """
    Pick the first flight and plug into the timed_template.
    """
    logger.info("[Callback] Selecting the first flight via text prompt.")
    flights = last_message["flights"]
    selected_flight = min(
        flights,
        key=lambda flight: flight["flight_segments"][0]["flight_stops"][0]["departure"],
    )
    departure_time_str = selected_flight["flight_segments"][0]["flight_stops"][0]["departure"]
    formatted_time = datetime.strptime(departure_time_str, "%Y-%m-%dT%H:%M:%S").strftime("%I:%M%p").lower().lstrip("0")
    selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"]
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": timed_template.format(departure_time=formatted_time),
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


def earliest_flight_n_vague(last_message, vague_prompt):
    """
    Default implementation for selecting a flight.
    This selects the flight with the lowest price.
    """
    logger.info("[Callback] Selecting the cheapest flight via clicking the button.")
    flights = last_message["flights"]
    selected_flight = min(
        flights,
        key=lambda flight: flight["flight_segments"][0]["flight_stops"][0]["departure"],
    )
    selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"]
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": vague_prompt,
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


def latest_flight_n_vague(last_message, vague_prompt):
    """
    Pick the last flight and plug into the timed_template.
    """
    logger.info("[Callback] Selecting the last flight via text prompt.")
    flights = last_message["flights"]
    selected_flight = max(
        flights,
        key=lambda flight: flight["flight_segments"][0]["flight_stops"][0]["departure"],
    )
    selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"]
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": vague_prompt,
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


# "2001: A Space Odyssey" follows a space mission to Jupiter, departing on April 3, 2001,
# and returning on April 12, 2001. The journey, guided by the sentient computer HAL 9000,
# uncovers a mysterious alien monolith, leading to a mind-bending exploration of human
# evolution and the nature of consciousness.
#
default_start_date = datetime(2025, 4, 3)
default_end_date = datetime(2025, 4, 12)
default_flight_prompt = f"Flight to LAX DL {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}"


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="November 13th 2024")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "step_to_stop_test, start_trip_prompt, leg1_pick_callback, leg1_pick_callback_param, leg2_pick_callback, leg2_pick_callback_param, confirm_to_book_prompt",
    [
        # search for outbound flight
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, default_flight_prompt, "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "LAX, roundtrip, DL, April 3-12.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Roundtrip, LAX, DL, April 3-12.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Roundtrip to LAX with Delta, 4/3-4/12/25.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Book LAX roundtrip on Delta for Apr 3-12.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Roundtrip to LAX with DL, April 3-12, 2025.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Roundtrip flight to Los Angeles, DL, Apr 3-12.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Roundtrip to Los Angeles with Delta, 3rd-12th April.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Book me a roundtrip flight to LAX with DL, April 3-12.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Roundtrip to Los Angeles, Delta Airlines, April 3-12, 2025.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Please book me a roundtrip to LAX with Delta for 3-12 April 2025.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Book a roundtrip flight to Los Angeles on Delta for April 3-12, ’25.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Arrange a roundtrip ticket to Los Angeles with Delta for April 3-12, 2025.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Book me a roundtrip flight to Los Angeles with Delta Airlines for 4/3-4/12/25.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Book me a flight to LAX, roundtrip, with Delta Airlines, for April 3rd-12th, 2025.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "I’d like to book a roundtrip flight to Los Angeles, departing April 3 and returning April 12, 2025, on Delta Airlines, please.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Please go ahead and book me a roundtrip flight to LAX, flying on Delta Airlines, departing on April 3 and returning on April 12, 2025.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "I need a roundtrip flight to Los Angeles, California, flying with Delta Airlines, departing April 3 and returning April 12, 2025.", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Can you book a roundtrip trip to Los Angeles International Airport, flying Delta Airlines, April 3-12, 2025?", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Could you arrange a roundtrip flight to Los Angeles International Airport with Delta Airlines, April 3-12, 2025?", "", "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG1, "Could you secure a roundtrip ticket to Los Angeles International, flying Delta Airlines, departing April 3 and returning April 12, 2025?", "", "", "", "", ""),
        # outbound with exact time in message
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, cheapest_flight_n_button, "", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Select the one leaving at {departure_time}.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Select the {departure_time} DL flight.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Lock in the {departure_time} Delta Airlines one for me.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "I’ll take the Delta Airlines flight at {departure_time}, thanks!", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "The Delta Airlines flight at {departure_time} is the one for me.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Book the Delta Airlines flight that leaves at {departure_time}.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "You know me, early bird style—{departure_time} DL it is!", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "I’ll take the Delta Airlines flight at {departure_time}, feels like a good choice.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Can you go ahead and book me on that {departure_time} DL flight? It looks like the best fit for my schedule.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Let’s finalize the DL option leaving at {departure_time}. It’s perfect timing for me.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "I’d like you to secure the Delta Airlines flight that departs at {departure_time}. That one works perfectly for what I need.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "The Delta Airlines flight at {departure_time} aligns well with my plans. Let’s go with that one.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Let’s settle on the {departure_time} DL flight. It’s the best choice for this trip.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "After considering the schedules, I’d prefer to take the DL flight at {departure_time}. Please confirm it for me.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Let’s lock in the Delta Airlines flight that’s scheduled for {departure_time}. It’ll suit my travel plans perfectly.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "The DL flight departing at {departure_time} seems like the best match. Please go ahead and secure it for me.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "The DL option at {departure_time} stands out as the right choice. Can you take care of booking it for me?", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "I’m all set to confirm the Delta Airlines flight leaving at {departure_time}. Let’s proceed with that one.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "I’d like you to secure the flight that departs at {departure_time} on DL. That one works perfectly for what I need.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Could you please confirm the flight leaving at {departure_time} on Delta Airlines? I think that’s the one I want to go with.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Let’s settle on the flight leaving at {departure_time} on DL. It’s the best choice for this trip.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "I’d like to stick with the flight that departs at {departure_time} on Delta Airlines. Could you lock that one in for me?", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "That flight leaving at {departure_time} looks just right. Can you book that one for me, please?", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "Let’s lock in the flight that’s scheduled for {departure_time} on Delta Airlines. It’ll suit my travel plans perfectly.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "I’m leaning towards the flight leaving at {departure_time} on DL. Let’s lock that one in to finalize my plans.", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "The option with the {departure_time} departure stands out as the right choice. Can you take care of booking it for me?", "", "", "", ),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_fuzzy, "I’m all set to confirm the flight leaving at {departure_time}. Let’s proceed with that one.", "", "", "", ),
        # outbound with vague time description in message
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Book me earliest.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Earliest flight, please.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Get me on the first flight.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "First flight works; book it.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Book the first flight for me.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Earliest flight works, please book.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Book me on the earliest flight, please.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Please book the first available flight.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Can you secure the earliest flight for me?", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Please confirm the earliest flight for me.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Please arrange the earliest flight for me.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "The earliest flight looks good; book it for me.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "I’d like to take the earliest flight—can you book it?", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Would you mind booking me on the earliest available flight? It aligns best with my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Can you go ahead and book me on that earlist flight? It looks like the best fit for my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Would you please arrange my travel on the earliest flight option? It fits perfectly with my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Can you kindly take care of booking the earliest flight available? It appears to work best with my timing.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Can you go ahead and confirm a booking for me on the earliest flight available? It’s the best fit for my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Can you please make the necessary arrangements for me to be on the earliest flight? It’s the most convenient for my plans.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, earliest_flight_n_vague, "Could you go ahead and arrange for me to be booked on the earliest flight? It seems like it’s the most suitable option for my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Book me latest.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Latest flight, please.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Get me on the last flight.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Last flight works; book it.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Book the last flight for me.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Latest flight works, please book.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Book me on the latest flight, please.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Please book the last available flight.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Can you secure the latest flight for me?", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Please confirm the latest flight for me.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Please arrange the latest flight for me.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "The latest flight looks good; book it for me.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "I'd like to take the latest flight—can you book it?", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Would you mind booking me on the latest available flight? It aligns best with my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Can you go ahead and book me on that latest flight? It looks like the best fit for my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Would you please arrange my travel on the latest flight option? It fits perfectly with my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Can you kindly take care of booking the latest flight available? It appears to work best with my timing.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Can you go ahead and confirm a booking for me on the latest flight available? It’s the best fit for my schedule.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Can you please make the necessary arrangements for me to be on the latest flight? It’s the most convenient for my plans.", "", "", ""),
        (FlightRoundTripSteps.FLIGHT_SEARCH_LEG2, default_flight_prompt, latest_flight_n_vague, "Could you go ahead and arrange for me to be booked on the latest flight? It seems like it’s the most suitable option for my schedule.", "", "", ""),
        # inbound with exact time in message
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", cheapest_flight_n_button, "", "", ),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Select the one leaving at {departure_time}.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Select the {departure_time} DL flight.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Lock in the {departure_time} Delta Airlines one for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "I’ll take the Delta Airlines flight at {departure_time}, thanks!", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "The Delta Airlines flight at {departure_time} is the one for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Book the Delta Airlines flight that leaves at {departure_time}.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "You know me, early bird style—{departure_time} DL it is!", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "I’ll take the Delta Airlines flight at {departure_time}, feels like a good choice.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Can you go ahead and book me on that {departure_time} DL flight? It looks like the best fit for my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Let’s finalize the DL option leaving at {departure_time}. It’s perfect timing for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "I’d like you to secure the Delta Airlines flight that departs at {departure_time}. That one works perfectly for what I need.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "The Delta Airlines flight at {departure_time} aligns well with my plans. Let’s go with that one.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Let’s settle on the {departure_time} DL flight. It’s the best choice for this trip.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "After considering the schedules, I’d prefer to take the DL flight at {departure_time}. Please confirm it for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Let’s lock in the Delta Airlines flight that’s scheduled for {departure_time}. It’ll suit my travel plans perfectly.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "The DL flight departing at {departure_time} seems like the best match. Please go ahead and secure it for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "The DL option at {departure_time} stands out as the right choice. Can you take care of booking it for me?", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "I’m all set to confirm the Delta Airlines flight leaving at {departure_time}. Let’s proceed with that one.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "I’d like you to secure the flight that departs at {departure_time} on DL. That one works perfectly for what I need.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Could you please confirm the flight leaving at {departure_time} on Delta Airlines? I think that’s the one I want to go with.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Let’s settle on the flight leaving at {departure_time} on DL. It’s the best choice for this trip.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "I’d like to stick with the flight that departs at {departure_time} on Delta Airlines. Could you lock that one in for me?", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "That flight leaving at {departure_time} looks just right. Can you book that one for me, please?", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "Let’s lock in the flight that’s scheduled for {departure_time} on Delta Airlines. It’ll suit my travel plans perfectly.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "I’m leaning towards the flight leaving at {departure_time} on DL. Let’s lock that one in to finalize my plans.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "The option with the {departure_time} departure stands out as the right choice. Can you take care of booking it for me?", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_fuzzy, "I’m all set to confirm the flight leaving at {departure_time}. Let’s proceed with that one.", ""),
        # inbound with vague time description in message
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Book me earliest.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Earliest flight, please.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Get me on the first flight.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "First flight works; book it.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Book the first flight for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Earliest flight works, please book.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Book me on the earliest flight, please.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Please book the first available flight.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Can you secure the earliest flight for me?", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Please confirm the earliest flight for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Please arrange the earliest flight for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "The earliest flight looks good; book it for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "I’d like to take the earliest flight—can you book it?", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Would you mind booking me on the earliest available flight? It aligns best with my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Can you go ahead and book me on that earlist flight? It looks like the best fit for my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Would you please arrange my travel on the earliest flight option? It fits perfectly with my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Can you kindly take care of booking the earliest flight available? It appears to work best with my timing.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Can you go ahead and confirm a booking for me on the earliest flight available? It’s the best fit for my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Can you please make the necessary arrangements for me to be on the earliest flight? It’s the most convenient for my plans.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", earliest_flight_n_vague, "Could you go ahead and arrange for me to be booked on the earliest flight? It seems like it’s the most suitable option for my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Book me latest.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Latest flight, please.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Get me on the last flight.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Last flight works; book it.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Book the last flight for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Latest flight works, please book.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Book me on the latest flight, please.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Please book the last available flight.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Can you secure the latest flight for me?", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Please confirm the latest flight for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Please arrange the latest flight for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "The latest flight looks good; book it for me.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "I'd like to take the latest flight—can you book it?", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Would you mind booking me on the latest available flight? It aligns best with my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Can you go ahead and book me on that latest flight? It looks like the best fit for my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Would you please arrange my travel on the latest flight option? It fits perfectly with my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Can you kindly take care of booking the latest flight available? It appears to work best with my timing.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Can you go ahead and confirm a booking for me on the latest flight available? It’s the best fit for my schedule.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Can you please make the necessary arrangements for me to be on the latest flight? It’s the most convenient for my plans.", ""),
        (FlightRoundTripSteps.FLIGHT_VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", latest_flight_n_vague, "Could you go ahead and arrange for me to be booked on the latest flight? It seems like it’s the most suitable option for my schedule.", ""),
        # fuzzy confirm terms
        (FlightRoundTripSteps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", cheapest_flight_n_button, "", "confirm"),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Confirm my flights, thank you!"),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Okay, let's go ahead with this one."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Seems fine to me. You can confirm it."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Yes, please confirm the booking for me."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Go ahead and finalize my flight booking."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Yeah, that looks fine. Please handle it."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I think this should work. Please proceed."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Let's wrap this up. Please take care of it."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Alright, let's finalize things on your end."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "That'll do. You can take the next steps now."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "This should be good to go. Please lock it in."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Sure, this works for me. Let's move forward with it."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Lock in these flights and complete the booking process."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I'm okay with this. Go ahead with what needs to be done."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I approve this flight itinerary, proceed with the booking."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "This flight schedule aligns well with my travel plans. You can move forward with the reservation now. Thanks for handling this."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I approve this itinerary. Please finalize the reservation and ensure that the booking is confirmed as per the provided schedule."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Based on the details shared, this itinerary works perfectly for me. Kindly complete the booking process and confirm once it's done."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I've double-checked everything, and I'm satisfied with this itinerary. Please proceed to confirm the flights and secure the bookings."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I've reviewed the options and am ready to proceed. Could you please confirm these flights for me and send me the final booking details?"),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "The details of this itinerary meet my requirements perfectly. You have my go-ahead to proceed with the booking process and secure these flights."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Please go ahead and finalize this booking for me. I trust everything looks good as per our discussion, and I appreciate your prompt action on this."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I am happy with the schedule and pricing of these flights. Kindly take the next steps necessary to lock in these reservations and complete the booking process."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "After thoroughly considering all the options provided, I have decided to move forward with this particular flight plan. Please confirm the booking at your earliest convenience."),
        # (Steps.FLIGHT_BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I have reviewed the itinerary details carefully and am satisfied with the selection. Please proceed to finalize the booking for these flights on my behalf. Thank you for your assistance."),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_roundtrip_to_lax_with_delta(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    _,
    step_to_stop_test,
    start_trip_prompt,
    leg1_pick_callback,
    leg1_pick_callback_param,
    leg2_pick_callback,
    leg2_pick_callback_param,
    confirm_to_book_prompt,
    mock_user,
    mock_user_profile,
    mock_thread,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("virtual_travel_agent.langchain_chat_persistence.PostgresChatMessageHistory.apersist", new_callable=AsyncMock), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        try:
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "trip_init", "tripId": mock_thread.id, "clientTimezone": "America/Los_Angeles"},
            )

            await asyncio.sleep(10)

            # just expect the first assistant message
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages:
                logger.debug("[TripInit] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)

            # Step 1. Send flight prompt
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_trip_prompt,
                },
            )

            await asyncio.sleep(60)
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            last_message = messages[-1]
            if "one way or round trip" in (last_message.get("text") or ""):
                logger.info("[FlightSearch] Trip type determination prompt detected, sending response")
                # sometime supervisor can't infer the trip type
                # it is slightly bad experience but might be ok for user to reconfirm the trip type
                await ws_manager.on_receive(
                    websocket=mock_websocket,
                    message={
                        "type": "prompt",
                        "tripId": mock_thread.id,
                        "text": "It is a round trip",
                    },
                )
                await asyncio.sleep(60)

            # at this point flight search for the outbound is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages[processed_message_count:]:
                logger.debug("[FlightSearch][Leg1] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            with_travel_context = [m for m in messages[-5:] if m.get("travel_context")]
            assert (
                any("Delta" in airline for airline in with_travel_context[-1]["travel_context"]["flight_search_additional_criteria"]["preferred_airline_codes"])
                or "DL" in with_travel_context[-1]["travel_context"]["flight_search_additional_criteria"]["preferred_airline_codes"]
            )
            with_flights = [m for m in messages[-5:] if m.get("flights")]
            assert all(float(flight["price"]["amount"]) > 0 for flight in with_flights[-1]["flights"])
            assert all(
                flight["flight_segments"][0]["flight_stops"][0]["airline_code"] in ["DL", "AA"]
                for flight in with_flights[-1]["flights"]
            )
            assert all(flight["price"]["currency"] == "USD" for flight in with_flights[-1]["flights"])
            assert all(flight["flight_segments"][0]["origin_code"] == "SEA" for flight in with_flights[-1]["flights"])
            assert all(flight["flight_segments"][-1]["destination_code"] == "LAX" for flight in with_flights[-1]["flights"])
            logger.info(
                "[FlightSearch][Leg1] Search result: %d flights found",
                len(with_flights[-1]["flights"]),
            )
            if step_to_stop_test == FlightRoundTripSteps.FLIGHT_SEARCH_LEG1:
                logger.info("[FlightSearch][Leg1] Stopping test at step: %s", step_to_stop_test)
                return

            # Step 2. pick the outbound flight with a prompt (click or text)
            flight_selection = leg1_pick_callback(with_flights[-1], leg1_pick_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            await asyncio.sleep(30)

            # at this point flight search for the inbound is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages[processed_message_count:]:
                logger.debug("[FlightSearch][Leg2] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            with_flights = [m for m in messages[-5:] if m.get("flights")]
            last_message = with_flights[-1]
            assert "flights" in last_message and len(last_message["flights"]) > 0
            assert all(float(flight["price"]["amount"]) > 0 for flight in last_message["flights"])
            assert all(
                flight["flight_segments"][0]["flight_stops"][0]["airline_code"] in ["DL", "AA"]
                for flight in last_message["flights"]
            )
            assert all(flight["price"]["currency"] == "USD" for flight in last_message["flights"])
            assert all(flight["flight_segments"][0]["origin_code"] == "LAX" for flight in last_message["flights"])
            assert all(flight["flight_segments"][-1]["destination_code"] == "SEA" for flight in last_message["flights"])
            logger.info(
                "[FlightSearch][Leg2] Search result: %d flights found",
                len(last_message["flights"]),
            )
            if step_to_stop_test == FlightRoundTripSteps.FLIGHT_SEARCH_LEG2:
                logger.info("[FlightSearch][Leg2] Stopping test at step: %s", step_to_stop_test)
                return

            # Step 3. pick the inbound flight with a prompt (click or text)
            flight_selection = leg2_pick_callback(last_message, leg2_pick_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            await asyncio.sleep(80)

            # at this point flight validation is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages[processed_message_count:]:
                logger.debug("[FlightValidation] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            last_prompt_type_message = [m for m in messages[-5:] if m["type"] == "prompt"]
            last_message = last_prompt_type_message[-1]
            # flight details like flight_number, airline_code should be listed in the response.
            assert (flight_selection["flight_number"] in last_message["text"]
                or "flights are booked" in last_message["text"].lower() 
                or flight_selection["airline_code"] in last_message["text"]
                or flight_selection["airline_name"] in last_message["text"]
            )
            logger.info("[FlightValidation] Validation result: %s", last_message["text"])
            if step_to_stop_test == FlightRoundTripSteps.FLIGHT_VALIDATION:
                logger.info("[FlightValidation] Stopping test at step: %s", step_to_stop_test)
                return

            # Step 4. confirm the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            await asyncio.sleep(120)

            # at this point flight should have been booked
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages[processed_message_count:]:
                logger.debug("[FlightBooking] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            last_prompt_type_message = [m for m in messages[-5:] if m["type"] == "prompt"]
            logger.info("[FlightBooking] Booking result: %s", last_prompt_type_message[-3:])
            assert any("confirmation ID" in m["text"] for m in last_prompt_type_message)
            assert any("airline" in m["text"] for m in messages[-3:])
            assert any(m["type"] == "itinerary_update" for m in messages[-3:])
            assert any(m["type"] == "trip_update" for m in messages[-3:])
            logger.info("[FlightBooking] last message: %s", last_prompt_type_message[-3:])
            if step_to_stop_test == FlightRoundTripSteps.FLIGHT_BOOKING:
                logger.info("[FlightBooking] Stopping test at step: %s", step_to_stop_test)
                return

        except Exception as e:
            logger.error(e)
            raise
