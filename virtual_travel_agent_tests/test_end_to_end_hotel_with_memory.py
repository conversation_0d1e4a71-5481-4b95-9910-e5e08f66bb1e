import logging
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from dateutil.relativedelta import relativedelta

from baml_client import b
from virtual_travel_agent_tests.utils import (
    cheapest_room_n_button,
    memory_clean_up,
)

logger = logging.getLogger(__name__)


# Set in the year 2384, Altered Carbon follows <PERSON><PERSON>, a former elite soldier turned private investigator,
# who awakens in a new body at the Raven Hotel in Bay City (a futuristic San Francisco).
# <PERSON><PERSON><PERSON> begins investigating this “murder” shortly after being resleeved into a new body on October 21.
# <PERSON><PERSON>’s death, or rather the destruction of his physical body, occurs on October 14, a week prior.
now = datetime.now()
next_next_month_start = (now + relativedelta(months=2)).replace(day=1)
next_next_month_last_day = next_next_month_start + relativedelta(months=1, days=-1)
default_start_date = next_next_month_last_day - relativedelta(days=6)
default_end_date = next_next_month_last_day

default_start_date = now + relativedelta(months=2)
default_end_date = now + relativedelta(months=2, weeks=1)
default_hotel_prompt = f"Book me a room with Hyatt in San Francisco close to Mission Bay, {default_start_date.strftime('%b %d')}-{default_end_date.strftime('%d')}"
target_location = {"latitude": 37.770104, "longitude": -122.389304}  # Mission Bay
default_memory_prompt = "Tell me about my Hotel at SF?"


async def do_hotel_booking(
    ws_manager,
    mock_websocket,
    mock_thread_with_memory,
    start_trip_prompt,
    hotel_selection_callback,
    hotel_selection_callback_param,
    confirm_to_book_prompt,
):
    # Step 0. Initiate the trip
    await ws_manager.on_receive(
        websocket=mock_websocket,
        message={"type": "trip_init", "tripId": mock_thread_with_memory.id},
    )

    # Step 1. Send user initial prompt
    await ws_manager.on_receive(
        websocket=mock_websocket,
        message={
            "type": "prompt",
            "tripId": mock_thread_with_memory.id,
            "text": start_trip_prompt,
        },
    )

    # at this point hotel search is done
    messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
    last_message = messages[-1]
    # Step 2. pick the room with a prompt (click or text)
    room_selection = hotel_selection_callback(last_message, hotel_selection_callback_param)
    pick_room_prompt = room_selection["prompt"]
    await ws_manager.on_receive(
        websocket=mock_websocket,
        message={
            "type": "prompt",
            "tripId": mock_thread_with_memory.id,
            "text": pick_room_prompt,
        },
    )

    # Step 3. confirm the hotel
    await ws_manager.on_receive(
        websocket=mock_websocket,
        message={
            "type": "prompt",
            "tripId": mock_thread_with_memory.id,
            "text": confirm_to_book_prompt,
        },
    )

    messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
    last_message = messages[-1]


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="November 13th 2024")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "start_trip_prompt, hotel_selection_callback, hotel_selection_callback_param, confirm_to_book_prompt, memory_checking_prompt, assistant_retrived_response_criteria",
    [
        (default_hotel_prompt, cheapest_room_n_button, "", "confirm", default_memory_prompt, "Mention Hyatt hotel in San Francisco Mission Bay"),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_hotel_book_w_hyatt_in_sf_with_memory(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    _,
    start_trip_prompt,
    hotel_selection_callback,
    hotel_selection_callback_param,
    confirm_to_book_prompt,
    assistant_retrived_response_criteria,
    memory_checking_prompt,
    mock_user_with_memory,
    mock_user_profile,
    mock_thread_with_memory,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()

    mock_mem_verify_websocket = MagicMock()
    mock_mem_verify_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread_with_memory), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]), \
            patch("virtual_travel_agent.virtual_travel_agent.is_feature_flag_enabled", new_callable=AsyncMock, return_value=True):
        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user_with_memory)

        try:
            memory_clean_up(mock_user_with_memory)

            # Do the hotel booking
            await do_hotel_booking(ws_manager, mock_websocket, mock_thread_with_memory, start_trip_prompt, hotel_selection_callback, hotel_selection_callback_param, confirm_to_book_prompt)

            # Verify memory
            # Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_mem_verify_websocket,
                message={"type": "trip_init", "tripId": mock_thread_with_memory.id},
            )

            # just expect the first assistant message
            assert mock_mem_verify_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_mem_verify_websocket.send_json.call_args_list]
            for m in messages:
                logger.debug("[TripInit] message: %s", m)
            processed_message_count = mock_mem_verify_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)

            # Step 0. Say hello to give enough time for the memory to be persisted from the previous step
            await ws_manager.on_receive(
                websocket=mock_mem_verify_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread_with_memory.id,
                    "text": "Hey, how are you?",
                },
            )

            # Step 1. Send ask memory prompt
            await ws_manager.on_receive(
                websocket=mock_mem_verify_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread_with_memory.id,
                    "text": memory_checking_prompt,
                },
            )

            # at this point hotel search for the outbound is done
            assert mock_mem_verify_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_mem_verify_websocket.send_json.call_args_list]
            for m in messages[processed_message_count:]:
                logger.debug("[HotelMemorySearch][Leg1] message: %s", m)
            processed_message_count = mock_mem_verify_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            last_message = messages[-1]
            assert last_message["type"] == "prompt"
            assert last_message["expectResponse"]
            assert last_message["isBotMessage"]
            assertion_result = await b.Assert(last_message["text"], assistant_retrived_response_criteria)
            assert f"Reason: {assertion_result.reason}", assertion_result.is_expected

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            logger.error(e)
            raise
