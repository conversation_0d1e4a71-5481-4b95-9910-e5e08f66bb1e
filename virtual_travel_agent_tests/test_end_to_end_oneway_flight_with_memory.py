import logging
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from virtual_travel_agent_tests.utils import (
    memory_clean_up,
)

logger = logging.getLogger(__name__)


def cheapest_flight_n_button(last_message, _):
    """
    Default implementation for selecting a flight.
    This selects the flight with the lowest price.
    """
    logger.info("[Callback] Selecting the cheapest flight via clicking the button.")
    flights = last_message["flights"]
    selected_flight = min(flights, key=lambda flight: float(flight["price"]["amount"]))
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": selected_flight["action"],
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


async def do_flight_booking(
    ws_manager,
    mock_websocket,
    mock_thread_with_memory,
    start_trip_prompt,
    leg1_pick_callback,
    leg1_pick_callback_param,
    confirm_to_book_prompt,
):
    # Step 0. Initiate the trip
    await ws_manager.on_receive(
        websocket=mock_websocket,
        message={"type": "trip_init", "tripId": mock_thread_with_memory.id},
    )

    # Step 1. Send flight prompt
    await ws_manager.on_receive(
        websocket=mock_websocket,
        message={
            "type": "prompt",
            "tripId": mock_thread_with_memory.id,
            "text": start_trip_prompt,
        },
    )

    # at this point flight search for the outbound is done
    messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
    last_message = messages[-1]

    # Step 2. pick the outbound flight with a prompt (click or text)
    flight_selection = leg1_pick_callback(last_message, leg1_pick_callback_param)
    pick_flight_prompt = flight_selection["prompt"]
    await ws_manager.on_receive(
        websocket=mock_websocket,
        message={
            "type": "prompt",
            "tripId": mock_thread_with_memory.id,
            "text": pick_flight_prompt,
        },
    )

    # at this point flight search for the inbound is done
    messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
    last_message = messages[-1]

    # Step 3. confirm the flight
    await ws_manager.on_receive(
        websocket=mock_websocket,
        message={
            "type": "prompt",
            "tripId": mock_thread_with_memory.id,
            "text": confirm_to_book_prompt,
        },
    )


# "2001: A Space Odyssey" follows a space mission to Jupiter, departing on April 3, 2001,
# and returning on April 12, 2001. The journey, guided by the sentient computer HAL 9000,
# uncovers a mysterious alien monolith, leading to a mind-bending exploration of human
# evolution and the nature of consciousness.
#
default_start_date = datetime(2025, 4, 3)
default_flight_prompt = f"one-way to LAX DL {default_start_date.strftime('%b %d')}, book without seat selection"
default_memory_prompt = "Tell me about my flight?"


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="November 13th 2024")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "start_trip_prompt, leg1_pick_callback, leg1_pick_callback_param, confirm_to_book_prompt, memory_checking_prompt",
    [
        (default_flight_prompt, cheapest_flight_n_button, "", "confirm", default_memory_prompt),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_roundtrip_to_lax_with_delta_with_memory(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    _,
    start_trip_prompt,
    leg1_pick_callback,
    leg1_pick_callback_param,
    confirm_to_book_prompt,
    mock_user_with_memory,
    mock_user_profile,
    mock_thread_with_memory,
    mock_payment_profile,
    mock_user_preference_json,
    mock_travel_context_json,
    memory_checking_prompt,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()

    mock_mem_verify_websocket = MagicMock()
    mock_mem_verify_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread_with_memory), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("server.utils.smtp.send_booking_email", new_callable=AsyncMock), \
            patch("server.services.calendar_api.calendar_provider.CalendarProviderManager.has_calendar_access", new_callable=AsyncMock, return_value=False), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]), \
            patch("virtual_travel_agent.virtual_travel_agent.is_feature_flag_enabled", new_callable=AsyncMock, return_value=True):

        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user_with_memory)


        try:
            # Clean up memory before the test
            memory_clean_up(mock_user_with_memory)

            # Book the flight
            await do_flight_booking(
                ws_manager, mock_websocket, mock_thread_with_memory, start_trip_prompt, leg1_pick_callback, leg1_pick_callback_param, confirm_to_book_prompt
            )

            # Verify memory
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_mem_verify_websocket,
                message={"type": "trip_init", "tripId": mock_thread_with_memory.id},
            )

            # just expect the first assistant message
            assert mock_mem_verify_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_mem_verify_websocket.send_json.call_args_list]
            for m in messages:
                logger.debug("[TripInit] message: %s", m)
            processed_message_count = mock_mem_verify_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)

            # Step 1. Send ask memory prompt
            await ws_manager.on_receive(
                websocket=mock_mem_verify_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread_with_memory.id,
                    "text": memory_checking_prompt,
                },
            )

            # at this point flight search for the outbound is done
            assert mock_mem_verify_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_mem_verify_websocket.send_json.call_args_list]
            for m in messages[processed_message_count:]:
                logger.debug("[FlightSearch][Leg1] message: %s", m)
            processed_message_count = mock_mem_verify_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            last_message = messages[-1]
            assert last_message["type"] == "prompt"
            assert last_message["expectResponse"]
            assert last_message["isBotMessage"]
            assert (
                "Los Angeles" in last_message["text"]
                or "LAX" in last_message["text"]
            )

        except Exception as e:
            logger.error(e)
            raise
