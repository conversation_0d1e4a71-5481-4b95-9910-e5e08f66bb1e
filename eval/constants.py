from enum import Enum

"""Langfuse Constants"""


class LF:
    class DataSet(Enum):
        FLIGHT_RANKING_BENCHMARK = "Flight Ranking Benchmark Dataset"
        OTTO_CONVERSATION = "Otto Response Dataset"

    class Experiment(Enum):
        FLIGHT_PICKING_EXPERIMENT = "Flight Picking Experiment"
        CONVERSATION_QUALITY_EXPERIMENT = "Conversation Quality Experiment"

    class Score(Enum):
        FLIGHT_PICKING_SCORE = "Flight Picking Score"
        GRAMMAR_SCORE = "Grammar Score"
        CONSISTENCY_SCORE = "Consistency Score"
        CONCISENESS_SCORE = "Conciseness Score"
        USE_OF_TONE_SCORE = "Use of Tone Score"

    class Prompt(Enum):
        FLIGHT_PICKING_PROMPT = "Flight Picking Prompt"
