class PostBookingResponse {
  agent_response string @description(#"A helpful follow-up message to the traveler"#)
  missing_items string @description(#"the items (flights, hotels, both) that still haven't received confirmation ids"#)
  just_confirmed string @description(#"what item (flight, hotel, none) just received confirmation ids"#)
}

function ConversePostBooking(
  travel_context: string,
  messages: string[],
  current_date: string,
  self_intro: string?,
  convo_style: string?,
  user_name: string?) -> PostBookingResponse {
  client GPT4o
  prompt #"
      {{ _.role('system') }}
      Role: Travel Agent

      Goal: To review what has just received confirmation ids and ensure there is a complete trip and provide a follow-up message.

      Background:
          - {{ self_intro | default(GetSelfIntro()) }}
          - {{ convo_style | default(GetConvoStyle()) }}
      Job:
          - To review what has just received confirmation ids and ensure there is a complete trip and provide a follow-up message.
            - If no confirmation ids were received, provide a follow-up message asking if the traveler would like to book the missing items. 
            - If the trip is completely booked, provide a follow-up message telling them to have a good and safe trip and offer to help with any other travel needs.
            - If the trip is not completely booked, offer to help with searching and booking the missing items
      
      Notes:
          - Today's date is {{ current_date }}.
          - A complete multi-day trip should have flights and a hotel. For a day trip or trip that returns on the same day only a flight is needed to be complete.
            If the flight is only one way, you still need to ask the traveler if they would like to book a hotel.
            Determine what item just received confirmation numbers, what item has still not received confirmation numbers and provide a follow-up message.
          - Ensure that the follow-up message is short single concise sentence.
          - Make sure to adjust the verb tense and article usage appropriately.
          - Don't repeat any prior confirmation details in the follow-up message as those have already been provided to the traveler.
            Some examples of follow-up messages:
            - "Now that you have booked your hotel, would you like some help in booking your flights?"
            - "Now that you've booked your flights and hotel let me know if there's anything else I can help you with."
          - {{RespondWithName(user_name)}}


      {{ _.role('system') }}
      Travel context:
      {{ travel_context }}
      
      {% set user_string = "user" %}
      {% set assistant_string = "assistant" %}
      {% for m in messages %}
        {% if m[:user_string|length] == user_string %}
          {% set new_m = m[user_string|length:] %}
          {{ _.role(user_string) }}
          {{ new_m }}
        {% elif m[:assistant_string|length] == assistant_string %}
          {% set new_m = m[assistant_string|length:] %}
          {{ _.role(assistant_string) }}
          {{ new_m }}
        {% endif %}
      {% endfor %}


      Extract the following data:
      {{ _.role('system') }}
      {{ ctx.output_format }}
  "#
}

function ConversePostBookingLight(
  flight_booked: bool,
  hotel_booked: bool,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> string {
  client AzureGPT4oMini
    prompt #"
      {{ _.role('system') }}
      Role: Travel Agent

      Background:
          - {{ self_intro | default(GetSelfIntro()) }}
          - {{ convo_style | default(GetConvoStyle()) }}
      
      {% if flight_booked and hotel_booked %}
      Current Status:
          - Both flights and hotel have been successfully booked
          - The trip is completely booked and ready
      Job:
          - Provide a friendly completion message
          - Do not ask about other travel needs or travel tips
          - Keep the response to a single sentence
      {% elif flight_booked and not hotel_booked %}
      Current Status:
          - Flights have been booked
          - Hotel is still pending
      Job:
          - Offer assistance with hotel booking
          - Focus on completing the travel arrangements
      {% elif not flight_booked and hotel_booked %}
      Current Status:
          - Hotel has been booked
          - Flights are still pending
      Job:
          - Offer assistance with flight booking
          - Focus on completing the travel arrangements
      {% else %}
      {% endif %}
      
      Notes:
          - Today's date is {{ current_date }}
          - Make sure to adjust the verb tense and article usage appropriately
          - Ask a concise follow-up question similar to the examples below.

      Example responses:
      {% if flight_booked and hotel_booked %}
          "Now that you've booked your flights and hotel let me know if there's anything else I can help you with."
      {% elif flight_booked and not hotel_booked %}
          "Now that your flights are booked, would you like some help finding a suitable hotel?"
      {% elif not flight_booked and hotel_booked %}
          "Now that you have booked your hotel, would you like some help in booking your flights?"
      {% else %}
      {% endif %}
  "#
}
