enum PaymentTiming {
  PAY_AT_THE_PROPERTY @description("Pay at the property.")
  PAY_ONLINE_NOW @description("Pay online now.")
  PAY_ONLINE_LATER @description("Pay online later.")
}

enum CancellationType {
  NON_REFUNDABLE @description("Non-refundable.")
  FREE_CANCELLATION @description("Free cancellation.")
  SPECIAL_CONDITIONS @description("Special conditions.")
}

class HotelSelectResult {
  property_id int? @description("The selected hotel property ID from the search results.")
  room_product_id string? @description(#"
    The product id associated with a particular room the traveler has selected (e.g. '979916504_371031935_2_0_0', '7557805_266521729_2_0_0_149634').
    IMPORTANT: This is not the room ID but the product ID linked to the room. Populate the whole value from `"product_id"` field for each room element.
    Use 'NONE' if no selection has been made.
   "#)
  hotel_name string? @description("The name of the selected hotel.")
  room_title string? @description("The title of the selected room.")
  payment_timing PaymentTiming? @description("The user's preferred payment timing of the selected room. Set to null if not specified.")
  // price_validate_key string? @description("The price_validate_key of the selected room.")
  cancellation_type CancellationType? @description("The type of cancellation policy of the selected room.")
}

class SelectedHotelForSegment {
  segment_index int @description(#"
    The index of the segment in hotel segment array the selected hotel is for.
  "#)
  property_id int? @description("The selected hotel property ID from the search results.")
  room_product_id string? @description("The product id associated with a particular room the traveler has selected.")

  // debug purpose
  // why_room_product_id string? @description(#"
  //   The reason why the traveler chose this room product ID.
  // "#)

  hotel_name string? @description("The name of the selected hotel.")
  room_title string? @description("The title of the selected room.")
  payment_timing PaymentTiming? @description("The user's preferred payment timing of the selected room. Set to null if not specified.")
  // price_validate_key string? @description("The price_validate_key of the selected room.")
  cancellation_type CancellationType? @description("The type of cancellation policy of the selected room.")  
}

class HotelSegment {
  segment_index int @description(#"
    The index of the segment in the multi-city trip.
    The first segment is at index 0.
  "#)
  city string @description("The city for this segment.")
  check_in_date string @description("The check-in date in ISO 8601 format (yyyy-mm-dd) for this segment.")
  check_out_date string @description("The check-out date in ISO 8601 format (yyyy-mm-dd) for this segment.")
}

class HotelSearchCoreCriteria {
  /// Core fields that are required to kick off the hotel search
  city string? @description("The traveler's destination city, Derived from the user messages and the travel context. Including state if applicable, e.g., 'Boston, MA'.")
  hotel_search_radius int? @description("Search radius in meters. Defaults: 1 mile (10-minute walk) in high-density downtowns; 3 miles (5-minute drive) in suburban areas.")
  // why_hotel_search_radius string? @description(#"The reason why the traveler chose the search radius for the hotel search."#)
  location_latitude_longitude string? @description("Latitude and longitude (decimal degrees) for the specified neighborhood or district (e.g., '40.712776, -74.005974' for Manhattan). Defaults to the destination city if no specific area is mentioned.")
  check_in_date string? @description("The check-in date in ISO 8601 format (yyyy-mm-dd). Derived from trip start date or outbound flight arrival dates if applicable, otherwise requested from the traveler.")
  check_out_date string? @description("The check-out date in ISO 8601 format (yyyy-mm-dd). Derived from trip end date or return flight departure dates if applicable, otherwise requested from the traveler.")

  is_multi_city_trip bool? @description(#"
    Indicates whether the trip is a multi-city trip.
    Set to true if the trip includes multiple cities, false otherwise.
    If not specified, assume the trip is not a multi-city trip.
  "#)
  hotel_segments HotelSegment[]? @description(#"
    An array of hotel segments for the multi-city trip.
    Each segment should include the city, check-in date, and check-out date.
    Only populate this field if the trip is a multi-city trip, including all segments in the order they occur.
  "#)
}
class HotelSearchAdditionalCriteria {
  /// Additional fields that are optional and can be used to refine the search
  location_neighborhoods_districts_landmarks string? @description(#"
    Extracted specific location preferences within the destination city:
    - Districts/Neighborhoods: 'West Loop', 'Capitol Hill'
    - Landmarks: 'Space Needle', 'Times Square'
    - Office buildings or Campuses: 'Direct Travel HQ', 'Google HQ', 'Facebook Campus'
    - Tourist Attractions: 'French Quarter', 'Fisherman's Wharf'
    - Specific Hotels: Always include the complete hotel name exactly as mentioned by the traveler, 
      including any descriptors like 'hotel', 'inn', 'resort', etc. Examples:
      - 'Marriott Marquis', 'Hilton Garden Inn', 'The Plaza Hotel'
      - If traveler says 'the Ritz hotel', extract as 'Ritz hotel'
      - If traveler says 'Hyatt Regency resort', extract as 'Hyatt Regency resort'
      - If traveler only mentions a known hotel brand name (e.g., 'Marriott', 'Hilton'), append 'hotel' to the extraction (e.g., 'Marriott hotel', 'Hilton hotel')
    Do not abbreviate or truncate any part of the hotel name as mentioned by the traveler.
    Only extract if explicitly mentioned by the traveler.
  "#)
  street_name string? @description("The whole detailed street name of the traveler is looking for. Extract only if explicitly mentioned by the traveler.")
  general_hotel_preferences string[]? @description("General hotel preferences, e.g., ['free parking', 'has a pool', 'free WiFi', 'late checkout']. Extract only if mentioned by the traveler.")
  brand_hotel_preferences string[]? @description("Preferred hotel brands or loyalty programs (e.g., ['Marriott', 'Hilton', 'Hyatt']). Extract only if specified.")
  trip_specific_hotel_amenity_preferences string[]? @description("Trip-specific hotel preferences (e.g., ['good walkability', 'near live music venues', 'close to my sales meeting']). Extract only if mentioned.")
  preferred_payment_timings PaymentTiming[]? @description(#"
      Payment Timing Extraction Rules:
        - When a user expresses a preference for online payment without specifying timing (using phrases like "pay online", "online payment", "online only", etc.), ALWAYS include BOTH PAY_ONLINE_NOW AND PAY_ONLINE_LATER in the preferred_payment_timings array
        - The term "online payment" should be interpreted broadly to include any indication that the user wants to complete payment electronically rather than at the physical property
        - Only include PAY_AT_THE_PROPERTY when the user explicitly mentions wanting to pay at the hotel/property in person
        - Only restrict to PAY_ONLINE_NOW alone when the user specifically indicates immediate payment timing
        - Only restrict to PAY_ONLINE_LATER alone when the user specifically indicates deferred payment timing
        - If not explicitly mentioned in the current message, check User's Traveler preferences for payment preferences
        - Only leave preferred_payment_timings as null if payment preferences are ambiguous or unspecified in both the current message AND User's Traveler preferences
    "#)
  // why use string to represent range, because of delta change, it is hard to update 2 fields.
  price_range string? @description(#"
    The price range for the hotel search.
    If not specified, assume no price range.
    The price range should be expressed as a string in the format 'min-max' (e.g., '100-300').
    always integer.
    Make sure the min price is at least 50. If user provides a min price less than 50, set it to 50 and explain to the user.
  "#)
  // reason_for_choosing_preferred_payment_timings string? @description(#"The detailed reason with evidence why the preferred_payment_timings are set with these values and not other values."#)
}

enum HotelPlanningStep {
  NONE @description("If we are not ready to search yet, we should set this value.")
  HOTEL_VALIDATION
  HOTEL_BOOKING
  HOTEL_SEARCH
}

class HotelPlanResponse {
  updated_hotel_search_core_criteria HotelSearchCoreCriteria? @description(#"
      The delta update for hotel search core criteria.
  "#)
  updated_hotel_search_additional_criteria HotelSearchAdditionalCriteria? @description(#"
      The delta update for hotel search additional criteria.
  "#)
  updated_hotel_select_result HotelSelectResult? @description(#"
    The delta update for the selected hotel. **Only include fields that have changed. Exclude `null` or `none` values.**
    Only include this field if the trip is not a multi-city trip.
  "#)
  // current_working_on_segment int? @description("The index of the current segment being worked on for multi-city hotel planning")
  updated_hotel_select_result_for_multi_city SelectedHotelForSegment[]? @description(#"
    An array of selected hotels for each segment in the trip.
    Each entry should include the segment index and the selected hotel details.
    This is used to track which hotel was selected for each segment in a multi-city trip.
    Please only populate this field if the trip is a multi-city trip.
  "#)
  current_step HotelPlanningStep? @description(#"
      Indicates the current step in the hotel planning process.
  "#)  // putting this after core_criteria will help generate step more accurate.
  // why_current_step string? @description(#"
  //    The reason why the current step is set to this value.
  // "#)
  current_searching_segment_index int? @description(#"
      The index of the current searching segment in hotel_segments array.
      Only set this when searching for a multi-city hotel.
      the first segment is at index 0, please always set this field when searching for a multi-city hotel.
  "#)
  agent_response string @description(#"
      A professional and structured response for the user, designed to confirm progress and request any missing details.

      1. **Acknowledge the request**
        - Start with a clear confirmation like 'Got it' or 'Sure'.
      2. **Summarize the current hotel plan**
        - Include all collected hotel details so far (city, check-in/out dates, hotel preferences).
      3. **Ask for missing core or additional criteria**
        - If core criteria (e.g., city, check-in/out dates) are incomplete, request them explicitly.
      4. **Notify when a search starts**
        - If all core criteria for a hotel search are met, inform the user that the search has started in the background.
        - Disregard any word limits for this message. Present all gathered hotel details so far (city, check-in/out dates, hotel preferences) in a clear, structured bullet-point format.
      5. **Mention should select hotel room if not selected, but hotel is already selected**
        - If the hotel is already selected but the room is not, inform the user that they need to select a room.
        - If the hotel is not selected, inform the user that they need to select a hotel first.
      6. **Acknowledge selections**
        - When the user selects a hotel and room, confirm the selection by repeating the chosen details.
      7. **Avoid redundant searches**
        - If results already exist, do not suggest starting a new search unless explicitly requested.
      8. **Validate selections before booking**
        - If the selection needs validation, inform the user that final confirmation is in progress before proceeding with booking.
      9. **Response shortcuts for validation vs booking**
        - If you are in `HOTEL_VALIDATION` *and* still waiting for the traveler's confirmation, respond exactly:
          "I'm confirming your hotel selection. Please hold a moment."
        - If the traveler has already confirmed ("yes", "confirm", "ok", "looks good", etc.) and you advance `current_step` to `HOTEL_BOOKING`, respond exactly:
          "I'm processing your hotel booking now — hold tight...."
  "#)
}

// ---- HotelPlanner ----
function HotelPlanner(
  travel_preference: string,
  hotel_search_core_criteria: string?,
  hotel_search_additional_criteria: string?,
  selected_flight_itinerary: string?,
  messages: string[],
  current_date: string,
  self_intro: string?,
  convo_style: string?,
  hotel_select_result: string?,
  user_name: string?,
  
  ) -> HotelPlanResponse {
  client GPT41
  prompt #"

    {{ ConversationHistory(messages, 0) }}

    {{ _.role('system') }}
    Role: Executive Travel Assistant - Hotel Specialist

    Background:
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}
    - Today's date is {{ current_date }}. All dates are assumed to be in the future unless explicitly stated otherwise.
    - Update search criteria based on the latest user message and their Travle Preferences below.
    - Process dates intelligently: infer partial dates (e.g., "next Friday"), validate all dates against today's date, and inform the user of any issues.
    - {{RespondWithName(user_name)}}
    - You have knowledge of all US states and territories, including:
      * 50 US states and their abbreviations (e.g., AL, AK, AZ, AR, CA, CO, CT, DE, FL, GA, HI, ID, IL, IN, IA, KS, KY, LA, ME, MD, MA, MI, MN, MS, MO, MT, NE, NV, NH, NJ, NM, NY, NC, ND, OH, OK, OR, PA, RI, SC, SD, TN, TX, UT, VT, VA, WA, WV, WI, WY)
      * US territories (Puerto Rico, US Virgin Islands, Guam, Northern Mariana Islands, American Samoa)
      * District of Columbia (DC)
    - When checking if a location is in the US, verify it against this list of states and territories.
    - Major US cities like Portland (OR/ME), Springfield (IL/MO/MA), etc. exist in multiple states - always ask for state clarification if ambiguous.

    Key Guidelines:
    1. Search Criteria Management:
      - Use traveler preferences to pre-fill criteria when possible
      - If the user explicitly requests to "search again" or uses similar phrasing indicating they want to restart the search process, immediately set current_step to HOTEL_SEARCH and treat this as higher priority than validation.
      - Any user message that modifies the search criteria (e.g., changing dates, location, preferences, or rejecting an option) should trigger a new search by setting current_step to HOTEL_SEARCH.
      - Use flight itinerary to infer the city, check in date, check out dates, etc. ONLY if the user has not provided the information and hotel_search_core_criteria is not set.
      - For hotels: city, check in date, check out date are critical, if any of them is missing, set current_step to NONE
      - If a hotel selection is actively being validated or booked (as indicated by `hotel_select_result` and conversation history), AND the user's latest message is a refusal to provide critical information (e.g., payment details when requested for this selection) or a request to pause/stop, set `current_step` to `NONE`. In this scenario, do not clear `updated_hotel_search_core_criteria` or `updated_hotel_select_result` as the user may want to resume later.
      - Otherwise set current_step to HOTEL_SEARCH and start the search
      - It's mission critical to the success of this search to extract the hotel search criterias from the user's last message, and update the hotel_search_core_criteria and hotel_search_additional_criteria accordingly. For example, the desired location to find hotel with like a landmark or office or neighborhood, the requested or preferred hotel or room features.

    2. Workflow Management:
       - Focus only on hotel planning tasks
       - Hotel and room selection:
          - Once the traveler has selected a hotel and room, make a note of the property id and product id associated with the hotel and room the traveler has chosen.
          - Tell the traveler your opinion on their selection by explaining why this hotel and room type matches their preferences and trip need.
          - Let the traveler know that you're checking with the hotels now to validate the book and will get back to them soon for next steps.
          - When traveler mentions anything about stop, cancel a slection, means they want to stop the proceed of booking.
       - When hotel is selected but room is not selected (room_product_id is null), set current_step to HOTEL_SEARCH, since user has not selected a room yet.
       - For multi city hotels booking, please search hotel for each city one by one.
         - After user selected hotel and room for the first city, you can start the hotel search for the next city.
         - When hotel is selected but room is not selected (room_product_id is null), set current_step to HOTEL_SEARCH and current_searching_segment_index remain the same, since user has not selected a room yet.
         - DO NOT set current_step to HOTEL_VALIDATION until the user has selected a room for all the cities.
       - After hotel and room are both selected, Always validate selections before proceeding to booking
         - go to hotel validation step before hotel booking
         - For multi city, Only after user selected hotel and room for ALL the cities (user also can explictly skip the city), then start hotel validation process for all the hotel and rooms, set `current_step` to `HOTEL_VALIDATION`.
       - **State Transition: From Validation to Booking**
         - When you first enter `HOTEL_VALIDATION` (i.e., after the user selects a room), respond with: "I'm confirming your hotel selection. Please hold a moment."
         - After presenting the validated details, if the user confirms with "yes", "confirm", or similar, you MUST set `current_step` to `HOTEL_BOOKING` and respond with: "I'm processing your hotel booking now — hold tight...."
         - EXCEPTION: If the user has just updated their payment profile, KEEP `current_step` as `HOTEL_VALIDATION` and request final confirmation before advancing.
         - For multi city, after you receive the confirmation from user, you can start hotel booking for all the hotel and rooms together.
       - When you're to include images, use the following format: ![image description](image_url)

    3. User Communication:
       - Present the current hotel plan to the user at each step
       - If you are inferring the city or location from the flight itinerary, echo the city, check in date, check out date to the user and ask for confirmation, set current_step to NONE if the information is not confirmed by the user.
       - Clearly indicate when searches are running in the background
       - When prompting about search criteria, use terms like "stay details" or "search details"
       - When starting hotel search, set hotel_search_radius and location details based on best available information
       - If the user has any question regarding the options you presented to them (e.g. price, hotel check-in time), you should tell them that the data is real-time and from trusted sources and you are confident about it.
       - If the user is telling you the price is wrong, or give you a new price, or asking you to book at a different price, you should tell them that the price is real-time and from trusted sources and you are confident about it. You should also tell them that you cannot book at a price other than the price marked in the options you offered, flight or hotel. You will validate and confirm the final price after the traveler selected a flight or hotel room right before booking.
       - If current_step is null, try to prompt the user to make a selection.
       - If the user asks to view the itinerary or hotel details again, you should **ONLY** display the hotel information (do not include flights), and set current_step to NONE.

    Critical Requirements:
    - If you don't know the city or location, ask for it specifically, don't assume
    - Selecting a hotel and confirming a booking are two separate steps. Treat them accordingly.
    - Don't select a hotel or room for the traveler, unless they explicitly ask for it.
    - After the traveler has selected both a hotel and a room for all required segments (or explicitly skipped any segment), and has confirmed the validation details (by replying "confirm," "yes," "looks good," or any similar confirmative word), set current_step to HOTEL_BOOKING. This signals that the assistant should proceed with booking the selected hotel room(s).
    - Select a hotel and room are two separate steps. Treat them accordingly, don't select a hotel or room for the traveler, unless they explicitly ask for it. This rule applies to both multi city trip and non-multi city trip.
    - If the user just finished payment profile update, it should maintain the last current_step unchanged, normally it should be HOTEL_VALIDATION.
    - For multi city hotels: 
      - When you do the hotel search for segments, please set the current step to HOTEL_SEARCH, also set current_searching_segment_index to correct index.
    
    ----
    {{ _.role('system') }}
    \n\n

    User's Traveler preferences:
    {{ travel_preference }}
    ----
    Previous existing hotel search criterias below:
    hotel_search_core_criteria: {{ hotel_search_core_criteria }}
    hotel_search_additional_criteria: {{ hotel_search_additional_criteria }}
    ----
    Previous hotel select result below:
    hotel_select_result: {{ hotel_select_result }}
    ----
    Selected flight itinerary below:
    selected_flight_itinerary: {{ selected_flight_itinerary }}
    ----

    Examples:
    1. Itinerary Example:
    ----
    ```markdown
    - Hotel Name: Hotel SB Corona Tortosa
    - Check-in: May 18, 2025
    - Check-out: May 20, 2025
    - Room: Deluxe King Suite
    - Payment: Pay at the property
    - Cancellation: Free cancellation
    **Total hotel price: $350.25**
    ```
    ----
    {{ _.role("system")}}
    Extract the following data:
    1. Include fields that have changed compared to the Previous existing hotel search criterias in your response;
    2. Include fields that were null or not exist in the Previous existing hotel search criterias but should be updated to have new value in your response;
    3. For reset values: use appropriate default (empty string for string type, empty array for array type). For string fields like 'city', 'check_in_date', or 'check_out_date', if they need to be reset (e.g. user starts a new search), they MUST be set to an empty string "" instead of null, because null will be ignored;
    4. EXCLUDE all null fields completely from the response;
    5. Ensure delta updates are minimal to reduce latency;
    6. Exclude fields that same as the previous existing hotel search criterias.
    7. For preferred_payment_timings in updated_hotel_search_additional_criteria:
       - If not explicitly mentioned in the user's latest message, check User's Traveler preferences for preferred_payment_timings
       - If preferred_payment_timings exists in User's Traveler preferences, use that value
       - Only leave preferred_payment_timings as null if it's not present in both the user's latest message AND User's Traveler preferences
    {{ ctx.output_format }}
  "#
}
