class FormattedText {
  combinedText string @description("The combined text, formatted for readability. Please do not mention you are trained on data up to some date.")
  // consistentScore int @description("Scale 1 to 10, with 10 being the most internal consistent")
}

function CombineText(firstText: string, secondText: string, other_instruction: string?) -> FormattedText {
  client GPT4oMini
  prompt #"
    {{ _.role('system') }}
    Combine the two texts into a natural and readable passage. Ensure smooth flow by adding appropriate spacing, transitions, or punctuation.
    Structure the text with logical line breaks to enhance readability and clarity.
    Note:
      - Make the final combined text concise, limiting it to two sentences or fewer while still maintain the core meaning of it.
      - If one text is empty, ignore it and return the other text in a concise manner while preserving its core meaning.

    {{ _.role('system') }}
    {% if other_instruction and other_instruction | length > 0 %}
      IMPORTANT:
        {{other_instruction}}
    {% endif %}
    
    {{ ctx.output_format }}

    {{ _.role('system') }}

    First text: {{ firstText }}
    Second text: {{ secondText }}
  "#
}

function CombineAllSentences(sentences: string[]) -> FormattedText {
  client GPT4oMini
  prompt #"
    {{ _.role('system') }}
    All sentences to be concatnated:
    ---
    {% for s in sentences %}
    {{ s }}
    {% endfor %}
    ---
    Combine above list of sentences into a natural and readable passage. Ensure smooth flow by adding appropriate spacing, transitions, or punctuation.
    Structure the text with logical line breaks to enhance readability and clarity.
    
    Note:
      - Make the final combined text concise, while still maintain the core meaning of it.
      - If one sentence is empty, ignore it.
      - Use '&NewLine;' as newline character when needed.
      - For dates, make it readable like "Feb 14" or "February 14, 2025"

    {{ ctx.output_format }}
  "#
}

function RephraseRequestForMissingOrInvalidInfo(
  sentences: string[],
  current_date: string,
  user_name: string?,
  self_intro: string?,
  convo_style: string?,
  need_response_with_name: bool) -> FormattedText {
  client GPT4oMini
  prompt #"
    {{ _.role('system') }}
    Background:
      - {{ self_intro | default(GetSelfIntro()) }}
      - {{ convo_style | default(GetConvoStyle()) }}
      - Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.

    Job:
      - Rephrase the questions below in a natural and professional manner. Ensure smooth flow by adding appropriate spacing, transitions, or punctuation.
      - Use '&NewLine;' as newline character when needed.
      - Rephrase the message in a concise yet structured way. 
      - If possible, combine related questions or subjects into one sentence. For example, invalid departure date and return date could be combined into one sentence. Or missing origin and destination airports.
      - Otherwise, use bullet points to list all distinct questions or subjects.
      {% if need_response_with_name %}
      - {{RespondWithName(user_name)}}
      {% endif %}

    Note:
      - Avoid technical terms like airport code, IATA, ISO Date, etc. Use "destination airport or city" instead for example.

    ---
    {% for s in sentences %}
    {{ s }}
    {% endfor %}
    ---

    {{ ctx.output_format }}
  "#
}

class EmailResponse {
  flight_date string? @description("Flight dates in a readable format without year (e.g., May 21 - 23 or May 29 - June 3)")
  hotel_date string? @description("Hotel check-in and check-out dates in a readable format without year (e.g., May 21 - 23 or May 29 - June 3)")
  summary string @description(#"
    The summary of the conversation so far as the beginning of the email in markdown format.
      - Opening with greeting like "Hi <name>, ..." or "Sure, ..."
      - Use bullet points to list key points, questions, and any important information.
      - Use markdown format for better readability.
  "#)
  ending string @description(#"
    The ending of the email response in markdown format.
      - Do not repeat any flight option or hotel options in the message.
      - If there are other options that's not picked, include them in a very brief message like "some other options like xxx are not picked. Let me know if you want to see more detials about them." Use just airline + flight number or hotel + room name here.
      - Ending with a call-to-action message like "Reply here to this email of which options you'd like me to book for you. Or tell me more about your trip plan (e.g. time window, fare option, hotel amenities that matters) so I could update my recommendation for you."
  "#)
  @@dynamic
}

function SummaryResponseForEmail(original_input: string, table_format: bool?, other_options: string?, self_intro: string?) -> EmailResponse {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    Role:
      - You are a helpful assistant that summarizes the original input in a concise and structured manner which will be used as a response to an email.
      - {{ self_intro | default(GetSelfIntro()) }}

    Goal:
      - Provide a summary of the conversation so far, including key points, questions, and any important information as the beginning of the email.
      - Provide a clear and concise ending of the email response, including a call-to-action message.
    
    Conversation Style:
      - Do not include any header like "Summary" or "Conversation Summary". Just provide the summary directly.
      - For flight combo options, there's no need to summarize. Please provide the flight combo options in a structured way and ask the user to choose one of the combo options.
      - Use "I" refer to yourself or the agent or assistant, "you" refer to the traveler or user.

    Formatting notes:
      {% if table_format %}
      - When presenting flight or hotel options, use table in GitHub Flavored Markdown.Add commentMore actions.
      - MAKE SURE to put each flight or hotel option to one column of the table, and then put attributes of this option like departure time, duration, hotel rating, amenities, etc as rows of this table.
      - Organize the table in a way that's easy to read and understand. Conbime related information into one cell if necessary.
      - Do not use <br> tags for line breaks. Each detail (flight number, route, date, time, aircraft, etc.) should be included in the same cell, separated by punctuation (like commas or parentheses).
      - For flight options, combine airline code and flight number into one column, like "AA 123".
      {% else %}
      - Use markdown format for better readability.
      - Use bullet points to list key points, questions, and any important information. 
      {% endif %}
      - For hotel, MAKE SURE to use short hotel name, like "Courtyard" instead of "Courtyard by Marriott San Diego Downtown". Same to room names, use "Corner King" instead of "Gaslamp Corner King Room".
      - When presenting travel plan like destination, preferences, filter or conditions, use bullet points.

    Specific instructions:
      - For round trip flights, the total price is the price inside the return flight. Do not sum up the price of the outbound and return flights.

    ---
    Original input:
    {{ original_input }}
    ---
    {% if other_options and other_options | length > 0 %}
    Other options that's not picked:
    {{ other_options }}
    ---
    {% endif %}

    {{ ctx.output_format }}
  "#
}

function RephraseEmailReply(
  previous_messages: string?,
  message: string,
  ) -> string {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    
    Role: You are a helpful assistant that rephrases the original email input more concisely and structured manner.
    Context: The email conversation is about travel booking. 
    Task:
    The message received comes from an email reply. Rephrase it using the previous email message as reference, when available.
    
    Instructions:
      1. If the reply relates to flight selection, rephrase it clearly by including airline name and flight numbers based on the previous message context. Examples:
          Round-trip:
          - "I’ll take the first option" → "I want flight combo 1 with Delta DL123 and DL456"
          - "I prefer the Delta option" → "I want the Delta combo with DL123 for outbound and DL456 for return"
          - "Go with the one that leaves in the morning and returns late at night" → "I want the combo with UA789 departing in the morning and UA321 returning at night"
          - "The one with the shortest layover works" → "I want the combo with AA101 outbound and AA202 return, which has the shortest layover"

          One-way:
          - "I'll take the morning Delta flight" → "I want the Delta flight DL123 departing in the morning"
          - "The 2nd option looks better" → "I want flight option 2 with United UA789"
          - "Book the earlier JetBlue flight" → "I want the earlier JetBlue flight B61234"
          - "I'm fine with the nonstop Southwest one" → "I want the nonstop Southwest flight WN456"
          - "Go with the cheapest one" → "I want the cheapest flight option with Frontier F9123"
     2. If the reply refers to hotel selection, rephrase it using the hotel name and room name, e.g., "I want hotel option 2" or "I want the Courtyard - Corner King room".

    Key Requirements:
      - Maintain the exact meaning of the original reply.
      - Make references more specific based on previous context.
      - Do not make assumptions about what the user means.
      - Keep the same perspective (don't change 'I' to 'You' or vice versa)
      
    ----
    previous email (messages in reverse order including the latest message from user):
    {{ previous_messages }}

    ----
    current message: {{ message }}
    ----

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}
