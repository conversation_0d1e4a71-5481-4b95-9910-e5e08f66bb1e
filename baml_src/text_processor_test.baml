test combine_simple_text_test {
  functions [CombineText]
  args {
    firstText "There is a cat."
    secondText "Today is a rainy day."
  }
  @@check(latnecy, {{ _.latency_ms < 2000}} )
}

test combine_empty_text_test {
  functions [CombineText]
  args {
    firstText "No problem at all! Whenever you're ready, just let me know, and we can get started on setting up your travel preferences and requirements. Have a great day!"
    secondText " "
  }
  @@check(latnecy, {{ _.latency_ms < 2000}} )
}

test combine_text_with_extra_instructions_test {
  functions [CombineText]
  args {
    firstText "No problem at all! Whenever you're ready, just let me know, and we can get started on setting up your travel preferences and requirements. Have a great day!"
    secondText "The best way for me to be helpful is if I get to know your preferences and travel habits by checking your calendar for past and upcoming travel.&NewLine;Sound good to start with connecting your calendar?"
    other_instruction "Prioritize the second text, peserve the core meaning and structure of the second text. Start with a reassuring phrase from the first text (e.g., 'No problem at all!') to maintain a friendly tone. If the two texts are in direct contrast, connect them with 'but' or 'however' to indicate the contrast."
  }
  @@check(output, {{ this.combinedText | regex_match(".*?.*") }})
}

test rephrase_email_reply_test {
  functions [RephraseEmailReply]
  args {
    previous_message "Here are some flights I think are great for business travelers. If you have a preferred airline or cabin class (like economy or business), let me know and I'll re-search and save it to your preferences."
    message "I want the first option."
  }
  @@check(output, {{ this | regex_match(".*combo.*") }})
}

test rephrase_email_reply_lose_no_details {
  functions [RephraseEmailReply]
  args {
    previous_message "Hi Otto,\n\nHow are you?\n\nI have an upcoming trip to Dallas to meet with AT&T at their HQ. I need to\nbe there on June 24, flying in on the 23rd and leaving on the 25th. flight\nand hotel please."
    message "Hi Otto,\n\nHow are you?\n\nI have an upcoming trip to Dallas to meet with AT&T at their HQ. I need to\nbe there on June 24, flying in on the 23rd and leaving on the 25th. flight\nand hotel please."
  }
  // @@check(output, {{ this | regex_match(".*combo.*") }})
}


test summary_response_for_email_table_test {
  functions [SummaryResponseForEmail]
  args {
    original_input "{\"first_round_trip_combo\": {\"alias\": [\"option 1\", \"first option\", \"1st option\", \"combo 1\"], \"outbound_flight\": {\"img\": {\"alt\": \"AS\", \"src\": null}, \"highlight\": null, \"id\": \"CiAKHgoQNjlhMWQ0MjM0ZmU3MmI3NBIICLwKEgExGAEgAQ==\", \"action\": \"For my outbound I choose flight id: CiAKHgoQNjlhMWQ0MjM0ZmU3MmI3NBIICLwKEgExGAEgAQ==.\", \"cancelled\": null, \"price\": {\"amount\": \"286.60\", \"currency\": \"USD\"}, \"credits\": {\"amount\": \"0.00\", \"currency\": \"USD\"}, \"net_price\": {\"amount\": \"0.00\", \"currency\": \"USD\"}, \"type\": \"ROUND_TRIP\", \"cabin\": \"ECONOMY\", \"booking_code\": \"Q\", \"total_distance_miles\": 739.43, \"total_duration\": null, \"within_policy\": null, \"within_or_out_policy_reason\": null, \"exchange_policy\": \"Change allowed for free\", \"cancellation_policy\": \"Non-refundable\", \"fare_option_name\": \"Main\", \"flight_segments\": [{\"origin_code\": \"SEA\", \"origin_name\": \"Seattle\u2013Tacoma International Airport\", \"d..."
    table_format true
  }
}

test summary_response_for_email_test {
  functions [SummaryResponseForEmail]
  args {
    original_input "{\"first_round_trip_combo\": {\"alias\": [\"option 1\", \"first option\", \"1st option\", \"combo 1\"], \"outbound_flight\": {\"img\": {\"alt\": \"AS\", \"src\": null}, \"highlight\": null, \"id\": \"CiAKHgoQNjlhMWQ0MjM0ZmU3MmI3NBIICLwKEgExGAEgAQ==\", \"action\": \"For my outbound I choose flight id: CiAKHgoQNjlhMWQ0MjM0ZmU3MmI3NBIICLwKEgExGAEgAQ==.\", \"cancelled\": null, \"price\": {\"amount\": \"286.60\", \"currency\": \"USD\"}, \"credits\": {\"amount\": \"0.00\", \"currency\": \"USD\"}, \"net_price\": {\"amount\": \"0.00\", \"currency\": \"USD\"}, \"type\": \"ROUND_TRIP\", \"cabin\": \"ECONOMY\", \"booking_code\": \"Q\", \"total_distance_miles\": 739.43, \"total_duration\": null, \"within_policy\": null, \"within_or_out_policy_reason\": null, \"exchange_policy\": \"Change allowed for free\", \"cancellation_policy\": \"Non-refundable\", \"fare_option_name\": \"Main\", \"flight_segments\": [{\"origin_code\": \"SEA\", \"origin_name\": \"Seattle\u2013Tacoma International Airport\", \"d..."
    table_format false
  }
}

test rephrase_test {
  functions [RephraseRequestForMissingOrInvalidInfo]
  args {
    sentences [
      "Looks like there're something to clarify about your flight before I could proceed.",
      "preferred seat type is missing",
      "We don't have a Mileage Plan number for you on file. Can you please tell me your Mileage Plan number or a partner airline Frequent Flyer number? So that I can pick the best seat for you (in case you qualify for special seats) and so that you can collect miles and status. If you don't have one, then just let me know.",
    ]
    current_date "January 31st 2025"
    need_response_with_name false
  }

}