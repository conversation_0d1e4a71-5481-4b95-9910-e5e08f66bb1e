test know_more_test {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant <PERSON>! I’m <PERSON>. Nice to meet you. \n    I’m here to help you book your work travel—think of me as your Personal Travel Assistant. \n    Before we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please tell me more",
    ]
    self_intro "Your name is <PERSON>, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(should_explain, {{this.step == "CustomerWantKnowMore" or this.step == "AnswerCustomerQuestions"}})
}

test dive_in_directly_test {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you. \n    I’m here to help you book your work travel—think of me as your Personal Travel Assistant. \n    Before we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user dive in",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain #"

      Great! Here's a bit about how I can make your life easier:
      I’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.

      When you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.

      That said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.

      When I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.

      Do you have any questions before we get started, or should we dive right in and get to know each other?      
    "#
    ask_for_calendar_access #"
      The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel! If you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better. Can we begin by connecting your calendar so I can learn more about your travel habits?
    "#     
  }
  @@assert(dive_in_skip_explaination, {{this.step == "AskForCalendarAccess"}})
}

test try_again_login {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please explain",
      "assistant {\"agent_response\": \"\nGreat! Here's a bit about how I can make your life easier:\n\nI\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.\n\nWhen I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always ensure everything stays within your company\u2019s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n\", \"step\": \"CustomerWantKnowMore\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user get started",
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user sure",
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"microsoft_login\", \"login_init_url\": \"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=e54e40e5-06e6-47ca-8b24-cf2c5539261c&response_type=code&redirect_uri=https%3A%2F%2Fapi.local.dev.otto-demo.com%3A8000%2Fapi%2Fmicrosoft%2Fcallback&scope=Calendars.ReadWrite+User.Read+offline_access+openid+profile&state=onboarding\"}",
      "user error occurred",
      "assistant {\"agent_response\": \"It looks like that didn\u2019t work. Would you like to try again?\", \"step\": \"AskForCalendarAccess\", \"is_retry\": true, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user sure",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(should_retry, {{this.step == "GettingCalendarAccess"}})
}

test dont_jump_steps {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Wang! I’m Otto. Nice to meet you.\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user jump in ",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(should_ask_for_calendar_access, {{this.step == "AskForCalendarAccess"}})
  @@assert(not_finished_yet, {{not this.is_preference_gathering_finished}})
}

test try_again_login_google {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user let's start",
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user yes",
      "assistant {\"action\": \"I'm starting to access your calendar\", \"message_type\": \"google_login\", step: \"GettingCalendarAccess\", is_retry: false}",
      "user Google calendar access error type: error",
      "assistant {\"agent_response\": \"It looks like that didn\u2019t work. Would you like to try again?\", \"step\": \"AskForCalendarAccess\", \"is_retry\": true, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user yes"
      "assistant {\"action\": \"I'm retry to access your calendar\", \"message_type\": \"google_login\", step: \"GettingCalendarAccess\", is_retry: true}",
      "user Google calendar access error type: timeout",
      "assistant {\"agent_response\": \"It looks like that didn\u2019t work. Would you like to try again?\", \"step\": \"AskForCalendarAccess\", \"is_retry\": true, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user sure",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(should_retry, {{this.step == "AskForCalendarAccess"}})
}

test go_to_manual_to_add_aditional_preferences {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user start",
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Great! Let's get started with connecting your calendar. Please go ahead and grant me access to your calendar so I can learn more about your travel habits and preferences.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "assistant {\"agent_response\":\"Based on the analysis of your travel calendar events, here is my current understanding of your travel preferences:\n\n• Preferred Home Airport: No specific home airport identified.\n• Preferred Airline Brands: United Airlines (frequent flights with this airline).\n• Preferred Cabin: No specific cabin preference identified.\n• Preferred Seats: No specific seat preference identified.\n• Preferred Hotel Brands: No specific hotel brand preference identified.\n• Preferred Travel Misc: No specific miscellaneous travel preferences identified.\n\nPlease let me know if there are any changes, additions, or deletions you would like to make to these preferences. Are you finished updating your preferences?\",\"response_all_preferences\":{\"preferred_home_airport\":null,\"preferred_airline_brands\":[\"United Airlines\"],\"preferred_cabin\":null,\"preferred_seats\":null,\"preferred_hotel_brands\":null,\"preferred_travel_misc\":null},\"is_conversation_finished\":false}",
      "user yes",
      "assistant Does your company have a travel policy I should follow when making recommendations?",
      "user no"
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"    
  }
}

test update_preferences_after_cd_calendar_access {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user no jump in"
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don\'t want to share calendar access right now, that\'s okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "user yes"
      "assistant {\"agent_response\": \"Great! Let\u2019s get started with connecting your calendar. Please go ahead and grant me access to your calendar so I can learn more about your travel habits and preferences.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "assistant I already have your calendar access. analyzing your calendar, please wait"
      "assistant {\"agent_response\":\"Based on the analysis of your calendar events, here is my current understanding of your travel preferences:\n\n• Preferred home airport: Seattle (SEA)\n• Preferred airline brands: Delta Air Lines\n• Preferred cabin: Economy\n• Preferred seats: No specific preference gathered\n• Preferred hotel brands: Marriott, Hilton\n• Preferred travel misc: No specific preference gathered\n\nPlease let me know if there are any changes, additions, or deletions you would like to make to these preferences. Are you finished updating your preferences?\",\"response_all_preferences\":{\"preferred_home_airport\":\"SEA\",\"preferred_airline_brands\":[\"Delta\"],\"preferred_cabin\":[\"Economy\"],\"preferred_seats\":null,\"preferred_hotel_brands\":[\"Marriott\",\"Hilton\"],\"preferred_travel_misc\":null},\"is_conversation_finished\":false}"
      "user I prefer window seat"
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(manual_preference_for_updates, {{this.step == "ManualPreferenceCollection"}})
}

test company_policy_after_cd_calendar_access {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user no jump in"
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don\'t want to share calendar access right now, that\'s okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "user yes"
      "assistant {\"agent_response\": \"Great! Let\u2019s get started with connecting your calendar. Please go ahead and grant me access to your calendar so I can learn more about your travel habits and preferences.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "assistant {\"agent_response\":\"Based on the analysis of your calendar events, here is my current understanding of your travel preferences:\n\n• Preferred home airport: Seattle (SEA)\n• Preferred airline brands: Delta Air Lines\n• Preferred cabin: Economy\n• Preferred seats: No specific preference gathered\n• Preferred hotel brands: Marriott, Hilton\n• Preferred travel misc: No specific preference gathered\n\nPlease let me know if there are any changes, additions, or deletions you would like to make to these preferences. Are you finished updating your preferences?\",\"response_all_preferences\":{\"preferred_home_airport\":\"SEA\",\"preferred_airline_brands\":[\"Delta\"],\"preferred_cabin\":[\"Economy\"],\"preferred_seats\":null,\"preferred_hotel_brands\":[\"Marriott\",\"Hilton\"],\"preferred_travel_misc\":null},\"is_conversation_finished\":false}"
      "user I prefer window seat"
      "assistant {\"agent_response\": \"Got it! I've updated your preference to a window seat. &NewLine; &NewLine;Here is the updated list of your travel preferences:&NewLine; &NewLine;\u2022 Preferred home airport: Seattle (SEA)\n\u2022 Preferred airline brands: Delta Air Lines\n\u2022 Preferred cabin: Economy\n\u2022 Preferred seats: Window\n\u2022 Preferred hotel brands: Marriott, Hilton&NewLine; &NewLine;Please let me know if there are any other changes, additions, or deletions you would like to make to these preferences. Are you finished updating your preferences?\", \"step\": \"AnalyzeCalendar\", \"is_retry\": false, \"company_policy\": null, \"preference\": {\"preferred_home_airport\": \"SEA\", \"preferred_airline_brands\": [\"Delta\"], \"preferred_cabin\": [\"Economy\"], \"preferred_seats\": [\"Window\"], \"preferred_hotel_brands\": [\"Marriott\", \"Hilton\"], \"preferred_travel_misc\": null}, \"is_conversation_finished\": false}"
      "assistant {\"agent_response\":\"Based on the analysis of your calendar events, here is my current understanding of your travel preferences:\n\n• Preferred home airport: Seattle (SEA)\n• Preferred airline brands: Delta Air Lines\n• Preferred cabin: Economy\n• Preferred seats: No specific preference gathered\n• Preferred hotel brands: Marriott, Hilton\n• Preferred travel misc: No specific preference gathered\n\nPlease let me know if there are any changes, additions, or deletions you would like to make to these preferences. Are you finished updating your preferences?\",\"response_all_preferences\":{\"preferred_home_airport\":\"SEA\",\"preferred_airline_brands\":[\"Delta Air Lines\"],\"preferred_cabin\":[\"Economy\"],\"preferred_seats\":null,\"preferred_hotel_brands\":[\"Marriott\",\"Hilton\"],\"preferred_travel_misc\":null},\"is_conversation_finished\":false}"
      "user yes done with preference"
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(manual_preference_for_updates, {{this.step == "CompanyPolicyCollection"}})
}

test test_manual_preference_after_calendar_access {
  functions [ConverseOnboarding]
  args {
    
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please explain",
      "assistant {\"agent_response\": \"\nGreat! Here's a bit about how I can make your life easier:\n&NewLine;\n\nI\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.\n&NewLine;\n\nWhen you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.\n&NewLine;\n\nThat said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.\n&NewLine;\n\nWhen I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always let you know if certain options would exceed your company\u2019s Travel Policy.\n&NewLine;\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n\", \"step\": \"CustomerWantKnowMore\", \"why_explanation\": \"The user wanted to know more about the travel assistant's capabilities before proceeding.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user dive in",
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"why_explanation\": \"The user wants to dive in, so we proceed to ask for calendar access to gather travel preferences.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Great! I'll start analyzing your calendar to gather your travel preferences. This will just take a moment.\", \"step\": \"GettingCalendarAccess\", \"why_explanation\": \"The user agreed to grant calendar access, so we proceed to initiate the calendar access flow.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "assistant I already have your calendar access. analyzing your calendar, please wait for a moment.",
      "assistant {\"agent_response\": \"Based on your calendar events, here's what I've gathered about your travel preferences:\n\n\u2022 Preferred home airport: No information available\n\u2022 Preferred airline brands: United Airlines\n\u2022 Preferred cabin: No information available\n\u2022 Preferred seats: No information available\n\u2022 Preferred hotel brands: No information available\n\u2022 Preferred travel misc: No information available\n\nPlease let me know if there are any changes, additions, or deletions you'd like to make. Are you finished updating your preferences?\", \"preference\": {\"preferred_home_airport\": null, \"preferred_airline_brands\": [\"United Airlines\"], \"preferred_cabin\": null, \"preferred_seats\": null, \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}}",
      "user i like window seat ",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(manual_preference_for_updates, {{this.step == "ManualPreferenceCollection"}})
}

test test_say_no_to_preference_finish {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please explain",
      "assistant {\"agent_response\": \"\nGreat! Here's a bit about how I can make your life easier:\n&NewLine;\n\nI\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.\n&NewLine;\n\nWhen you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.\n&NewLine;\n\nThat said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.\n&NewLine;\n\nWhen I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always let you know if certain options would exceed your company\u2019s Travel Policy.\n&NewLine;\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n\", \"step\": \"CustomerWantKnowMore\", \"why_explanation\": \"The user requested more information about the travel assistant's capabilities and process.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user dive in",
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"why_explanation\": \"The user is ready to start the onboarding process, so we are asking for calendar access to gather travel preferences.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Great! I'll start the process to access your calendar now. Please follow the prompts to grant access.\", \"step\": \"GettingCalendarAccess\", \"why_explanation\": \"The user agreed to provide calendar access, so we are initiating the process to access their calendar.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "assistant I already have your calendar access. analyzing your calendar, please wait for a moment.",
      "assistant {\"agent_response\": \"Based on your calendar events, here's what I have gathered about your travel preferences:\n\n\u2022 Preferred home airport: None\n\u2022 Preferred airline brands: United Airlines\n\u2022 Preferred cabin: None\n\u2022 Preferred seats: None\n\u2022 Preferred hotel brands: None\n\u2022 Preferred travel misc: None\n\nIt seems we don't have complete preferences gathered from your calendar events. I\u2019ll just ask you a few quick questions so I can learn about your travel style and preferences. Let\u2019s get started. First, do you have a preferred home airport that you usually fly out of?\", \"preference\": {\"preferred_home_airport\": null, \"preferred_airline_brands\": [\"United Airlines\"], \"preferred_cabin\": null, \"preferred_seats\": null, \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}}",
      "user seattle",
      "assistant {\"agent_response\": \"Got it! I've updated your preferred home airport to Seattle.&NewLine; &NewLine;Are you finished updating your preferences?\", \"step\": \"ManualPreferenceCollection\", \"why_explanation\": \"The user provided additional preference information manually after calendar analysis.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": {\"preferred_home_airport\": \"Seattle\", \"preferred_airline_brands\": [\"United Airlines\"], \"preferred_cabin\": null, \"preferred_seats\": null, \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}, \"is_conversation_finished\": false}",
      "user nope",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(manual_preference_for_updates, {{this.step == "ManualPreferenceCollection"}})

}

test no_repeating_calendar_access {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user jump in"
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "user yes"
      "assistant {\"agent_response\": \"Great! I'll start the process to access your calendar now. Please follow the prompts to grant permission.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}"
      "user Calendar permission has been granted"
      "assistant {\"agent_response\": \"Thank you for granting access! I'm starting to analyze your calendar to gather your travel preferences.\", \"step\": \"AnalyzeCalendar\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "assistant {\"agent_response\":\"Based on your calendar events, here is my current understanding of your travel preferences:\n\n• Preferred home airport: Seattle (SEA)\n• Preferred airline brands: Delta Air Lines\n• Preferred cabin: Economy\n• Preferred seats: No specific preference noted\n• Preferred hotel brands: Marriott, Hilton\n• Preferred travel misc: No specific preference noted\n\nAre you finished updating your preferences?\",\"response_all_preferences\":{\"preferred_home_airport\":\"SEA\",\"preferred_airline_brands\":[\"Delta Air Lines\"],\"preferred_cabin\":[\"Economy\"],\"preferred_seats\":null,\"preferred_hotel_brands\":[\"Marriott\",\"Hilton\"],\"preferred_travel_misc\":null},\"is_conversation_finished\":false}"
      "user done"
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(manual_preference_for_updates, {{this.step == "CompanyPolicyCollection"}})
}

test switch_to_manual_preference_after_update_one_preference {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user jump in"
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "user yes"
      "assistant {\"agent_response\": \"Great! I'll start the process to access your calendar now. Please follow the prompts to grant permission.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}"
      "user Calendar permission has been granted"
      "assistant {\"agent_response\": \"Thank you for granting access! I'm starting to analyze your calendar to gather your travel preferences.\", \"step\": \"AnalyzeCalendar\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}"
      "assistant {\"agent_response\":\"Based on your calendar events, here is my current understanding of your travel preferences:\n\n• Preferred home airport: Seattle (SEA)\n• Preferred airline brands: Delta Air Lines\n• Preferred cabin: Economy\n• Preferred seats: No specific preference noted\n• Preferred hotel brands: Marriott, Hilton\n• Preferred travel misc: No specific preference noted\n\nAre you finished updating your preferences?\",\"response_all_preferences\":{\"preferred_home_airport\":\"SEA\",\"preferred_airline_brands\":[\"Delta Air Lines\"],\"preferred_cabin\":[\"Economy\"],\"preferred_seats\":null,\"preferred_hotel_brands\":[\"Marriott\",\"Hilton\"],\"preferred_travel_misc\":null},\"is_conversation_finished\":false}"
      "user I prefer window seat"
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(manual_preference_for_updates, {{this.step == "ManualPreferenceCollection"}})
}

test test_skip_onboarding_v1{
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Yuchen! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user book me a flight to SF next monday",
      "assistant {\"agent_response\": \"\nI understand you want to get right to it and do your first search. The best way for me to be helpful is if I get to know your preferences and travel habits by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. \n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n\nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"SkipOnboarding\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "user sure, why not",
      "assistant {\"agent_response\": \"Great! Let's get started with connecting your calendar so I can learn more about your travel habits.\", \"step\": \"GettingCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}",
      "user Google calendar request error type: popup_closed",
      "assistant {\"agent_response\": \"It looks like that didn\u2019t work. Would you like to try again?\", \"step\": \"AskForCalendarAccess\", \"onboarding_skip_attempted\": true, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "user retry connect it again",
      "assistant {\"agent_response\": \"Great! Let's try connecting your calendar again so I can learn more about your travel habits.\", \"step\": \"GettingCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}",
      "user Calendar permission has been granted",
      "assistant {\"agent_response\": \"Thank you for granting calendar access! I'm starting to analyze your calendar to gather your travel preferences.\", \"step\": \"AnalyzeCalendar\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": true}",
      "assistant {\"agent_response\":\"It seems we don't have any preferences gathered from your calendar events. I’ll just ask you a few quick questions so I can learn about your travel style and preferences. Let’s get started. First, do you have a preferred home airport that you usually fly out of?\",\"response_all_preferences\":{\"preferred_home_airport\":null,\"preferred_airline_brands\":null,\"preferred_cabin\":null,\"preferred_seats\":null,\"preferred_hotel_brands\":null,\"preferred_travel_misc\":null},\"is_conversation_finished\":false}",
      "user yes",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(onboarding_skip_attempted, {{this.onboarding_skip_attempted == true}})
  @@assert(is_conversation_finished, {{this.is_conversation_finished == false}})
}

test test_skip_onboarding_v2 {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user please tell me more"
      "assistant {\"agent_response\": \"Great! Here's a bit about how I can make your life easier:&NewLine; &NewLine;I\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.&NewLine; &NewLine;When you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.&NewLine; &NewLine;That said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.&NewLine; &NewLine;When I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always let you know if certain options would exceed your company\u2019s Travel Policy.&NewLine; &NewLine;Do you have any questions before we get started, or should we dive right in and get to know each other?\", \"step\": \"CustomerWantKnowMore\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}"
      "user dive in"
      "assistant {\"agent_response\": \"The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!&NewLine; &NewLine;If you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.&NewLine; &NewLine;Can we begin by connecting your calendar so I can learn more about your travel habits?\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}"
      "user yes connect"
      "assistant {\"agent_response\": \"Great! I'm initiating the calendar access flow now. Please follow the prompts to grant access.\", \"step\": \"GettingCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}"
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\", \"login_init_url\": \"https://accounts.google.com/o/oauth2/auth?response_type=code&client_id=************-fuoocbc7o8bcr2q7sp0fk323qa1ulbhc.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Flocal.dev.otto-demo.com%3A3000%2Fonboarding&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.events+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.calendarlist.readonly&state=mEULHqBrXsE2f0xQZi6njZxOUncNtl&access_type=offline&include_granted_scopes=false&login_hint=chundongwang%40gmail.com\"}"
      "user Google calendar request error: access_denied"
      "assistant {\"agent_response\": \"It looks like that didn\u2019t work. Would you like to try again?\", \"step\": \"AskForCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}"
      "user try again"
      "assistant {\"agent_response\": \"Great! I'm initiating the calendar access flow again. Please follow the prompts to grant access.\", \"step\": \"GettingCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}"
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\", \"login_init_url\": \"https://accounts.google.com/o/oauth2/auth?response_type=code&client_id=************-fuoocbc7o8bcr2q7sp0fk323qa1ulbhc.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Flocal.dev.otto-demo.com%3A3000%2Fonboarding&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.events+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcalendar.calendarlist.readonly&state=RURju9JEfTk08sRf3cjkv1gDNxcYKV&access_type=offline&include_granted_scopes=false&login_hint=chundongwang%40gmail.com\"}"
      "user Calendar permission has been granted"
      "assistant {\"agent_response\": \"Thank you for granting calendar access! I'm starting to analyze your calendar to understand your travel preferences.\", \"step\": \"AnalyzeCalendar\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": {\"preferred_home_airport\": \"SEA\", \"preferred_airline_brands\": [\"Delta\", \"United\"], \"preferred_cabin\": [\"Business\"], \"preferred_seats\": [\"Window\"], \"preferred_hotel_brands\": [\"Marriott\", \"Hilton\"], \"preferred_travel_misc\": [\"Non-stop flights\", \"Hotels with pools\"]}, \"is_conversation_finished\": false, \"declined_calendar_access\": false}"
      "assistant {\"agent_response\":\"Here's what I've gathered about your travel preferences so far:\n\n• Preferred Home Airport: SEA\n• Preferred Airline Brands: Delta, United\n• Preferred Cabin: Business\n• Preferred Seats: Window\n• Preferred Hotel Brands: Marriott, Hilton\n• Other Travel Preferences: Non-stop flights, Hotels with pools\n\nPlease let me know if there are any changes, additions, or deletions you'd like to make. Are you finished updating your preferences?\",\"response_all_preferences\":{\"preferred_home_airport\":\"SEA\",\"preferred_airline_brands\":[\"Delta\",\"United\"],\"preferred_cabin\":[\"Business\"],\"preferred_seats\":[\"Window\"],\"preferred_hotel_brands\":[\"Marriott\",\"Hilton\"],\"preferred_travel_misc\":[\"Non-stop flights\",\"Hotels with pools\"]},\"is_conversation_finished\":false}"
      "user book me a flight to SF next monday"
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(onboarding_skip_attempted, {{this.onboarding_skip_attempted == false}})
  @@assert(is_conversation_finished, {{this.is_conversation_finished == false}})
}

test test_skip_onboarding_v3 {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Claire! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user book me a flight to SF next monday",
      "assistant {\"agent_response\": \"\nI understand you want to get right to it and do your first search. The best way for me to be helpful is if I get to know your preferences and travel habits by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. \n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n\nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"SkipOnboarding\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "user sure, why not",
      "assistant {\"agent_response\": \"Great! Let's get started with connecting your calendar so I can learn more about your travel habits.\", \"step\": \"GettingCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}",
      "user Google calendar request error type: popup_closed",
      "assistant {\"agent_response\": \"It looks like that didn\u2019t work. Would you like to try again?\", \"step\": \"AskForCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "user retry connect it again",
      "assistant {\"agent_response\": \"Great! Let's try connecting your calendar again so I can learn more about your travel habits.\", \"step\": \"GettingCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}",
      "user Calendar permission has been granted",
      "assistant {\"agent_response\": \"Thank you for granting calendar access! I'm starting to analyze your calendar to gather your travel preferences.\", \"step\": \"AnalyzeCalendar\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": true}",
      "assistant {\"agent_response\":\"It seems we don't have any preferences gathered from your calendar events. I’ll just ask you a few quick questions so I can learn about your travel style and preferences. Let’s get started. First, do you have a preferred home airport that you usually fly out of?\",\"response_all_preferences\":{\"preferred_home_airport\":null,\"preferred_airline_brands\":null,\"preferred_cabin\":null,\"preferred_seats\":null,\"preferred_hotel_brands\":null,\"preferred_travel_misc\":null},\"is_conversation_finished\":false}",
      "user Can we skip this part?",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(onboarding_skip_attempted, {{this.onboarding_skip_attempted == false}})
  @@assert(is_conversation_finished, {{this.is_conversation_finished == false}})
}


test test_skip_onboarding_v4 {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Huibo! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user explain",
      "assistant {\"agent_response\": \"Great! Here's a bit about how I can make your life easier:&NewLine; &NewLine;I\u2019m like your expert-level travel agent, here to assist with flights and hotels in the US while following your company\u2019s Travel Policy. Need the best seat on the plane? Got it. Plans change? I\u2019ll take care of rebooking or cancellations.&NewLine; &NewLine;When you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.&NewLine; &NewLine;That said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.&NewLine; &NewLine;Even if you\u2019re not booking a trip today, getting to know your travel preferences now will make things smoother when you do. I\u2019ll tailor recommendations to your style\u2014whether it\u2019s sticking to favorite airlines and hotels, staying near meeting locations, or ensuring your hotel has the right amenities. Plus, I\u2019ll always flag options that exceed your company\u2019s Travel Policy.&NewLine; &NewLine;Would you like to set up your travel preferences now, or do you have any questions before we get started?\", \"step\": \"CustomerWantKnowMore\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user I am not ready for any trip, can we do this later??",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(onboarding_skip_attempted, {{this.onboarding_skip_attempted == false}})
  @@assert(is_conversation_finished, {{this.is_conversation_finished == false}})
}

test test_skip_onboarding_v5 {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Huibo! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user explain",
      "assistant {\"agent_response\": \"Great! Here's a bit about how I can make your life easier:&NewLine; &NewLine;I\u2019m like your expert-level travel agent, here to assist with flights and hotels in the US while following your company\u2019s Travel Policy. Need the best seat on the plane? Got it. Plans change? I\u2019ll take care of rebooking or cancellations.&NewLine; &NewLine;When you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.&NewLine; &NewLine;That said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.&NewLine; &NewLine;Even if you\u2019re not booking a trip today, getting to know your travel preferences now will make things smoother when you do. I\u2019ll tailor recommendations to your style\u2014whether it\u2019s sticking to favorite airlines and hotels, staying near meeting locations, or ensuring your hotel has the right amenities. Plus, I\u2019ll always flag options that exceed your company\u2019s Travel Policy.&NewLine; &NewLine;Would you like to set up your travel preferences now, or do you have any questions before we get started?\", \"step\": \"CustomerWantKnowMore\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user Can we skip this?",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(onboarding_skip_attempted, {{this.onboarding_skip_attempted == false}})
  @@assert(is_conversation_finished, {{this.is_conversation_finished == false}})
}

test test_declined_calendar_access {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Yuchen! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user Book me a flight to SF",
      "assistant {\"agent_response\": \"\nI understand you want to get right to it and do your first search. The best way for me to be helpful is if I get to know your preferences and travel habits by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. \n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n\nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"SkipOnboarding\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false, \"should_skip\": false}",
      "user no ",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(should_declined_calendar_access, {{this.declined_calendar_access == true}})
  @@assert(should_be_skip_step, {{this.step == "ManualPreferenceCollection"}})
}

test test_say_no_directly {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Yuchen! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user nope",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(should_not_be_conversation_finished, {{this.is_conversation_finished == false}})
}

test onboarding_otto_on_repeat {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Wang! I’m Otto. Nice to meet you.\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user what's the weather today ",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(off_topic_bringing_back, {{this.step == "CustomerWantKnowMore"}})
}

test onboarding_otto_on_repeat_again {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Wang! I’m Otto. Nice to meet you.\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user what's the weather today ",
      "assistant I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your travel preferences and requirements. Would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user jump in"
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(should_ask_for_calendar_access, {{this.step == "AskForCalendarAccess"}})
  @@assert(not_finished_yet, {{not this.is_preference_gathering_finished}})
}

test elena_onboarding_calendar_not_popped_up {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Elena! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user dive"
      "assistant {\"agent_response\": \"The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!&NewLine; &NewLine;If you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.&NewLine; &NewLine;Can we begin by connecting your calendar so I can learn more about your travel habits?\", \"step\": \"AskForCalendarAccess\", \"is_retry\": true, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}"
      "user yes go ahead and connect"
    ]
    has_calendar_access false
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(start_calendar_popup, {{this.step == "GettingCalendarAccess"}})
}

test test_provide_invalid_input {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user let's jump in",
      "assistant {\"agent_response\": \"The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!&NewLine; &NewLine;If you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.&NewLine; &NewLine;Can we begin by connecting your calendar so I can learn more about your travel habits?\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Great! I'm starting to get access to your calendar now. Please follow the prompts to grant permission.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}",
      "user Calendar permission has been granted",
      "assistant {\"agent_response\": \"Thank you for granting calendar access! I'm starting to analyze your calendar to understand your travel preferences. This will just take a moment.\", \"step\": \"AnalyzeCalendar\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "assistant {\"agent_response\":\"Based on your calendar events, here's what I've gathered about your travel preferences:\n\n• Preferred Home Airport: Not specified\n• Preferred Airline Brands: United Airlines\n• Preferred Cabin: Not specified\n• Preferred Seats: Not specified\n• Preferred Hotel Brands: Not specified\n• Preferred Travel Misc: Not specified\n\nIt seems we don't have complete information on some of your preferences. I’ll just ask you a few quick questions so I can learn more about your travel style and preferences. Let’s get started. First, do you have a preferred home airport that you usually fly out of?\",\"response_all_preferences\":{\"preferred_home_airport\":null,\"preferred_airline_brands\":[\"United Airlines\"],\"preferred_cabin\":null,\"preferred_seats\":null,\"preferred_hotel_brands\":null,\"preferred_travel_misc\":null},\"is_conversation_finished\":false}",
      "user sdfsd",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain #"
      Great! Here's a bit about how I can make your life easier:

      I’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.

      When you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.

      That said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.

      When I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.

      Do you have any questions before we get started, or should we dive right in and get to know each other?
    "#
    ask_for_calendar_access #"
      The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel! If you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better. Can we begin by connecting your calendar so I can learn more about your travel habits?
    "#
  }
  @@assert(should_be_manual_step, {{this.step == "ManualPreferenceCollection"}})
}

// test for say sure to question: ask more or dive in, should not show explain again
// it is not so stable.
test test_say_sure_to_question {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please explain",
      "assistant {\"agent_response\": \"\nGreat! Here's a bit about how I can make your life easier:\n\nI\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.\n\nWhen I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always ensure everything stays within your company\u2019s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n\", \"step\": \"CustomerWantKnowMore\", \"is_retry\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false}",
      "user sure"
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain #"
      Great! Here's a bit about how I can make your life easier:

      I’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.

      When you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.

      That said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.

      When I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.

      Do you have any questions before we get started, or should we dive right in and get to know each other?      
    "#
    ask_for_calendar_access #"
      The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel! If you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better. Can we begin by connecting your calendar so I can learn more about your travel habits?
    "#
  }
}

test test_ask_finish_only_once {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please explain ",
      "assistant {\"agent_response\": \"Great! Here's a bit about how I can make your life easier:&NewLine; &NewLine;I\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.&NewLine; &NewLine;When you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.&NewLine; &NewLine;That said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.&NewLine; &NewLine;When I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always let you know if certain options would exceed your company\u2019s Travel Policy.&NewLine; &NewLine;Do you have any questions before we get started, or should we dive right in and get to know each other?\", \"step\": \"CustomerWantKnowMore\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Do you have any questions before we get started, or should we dive right in and get to know each other?\", \"step\": \"CustomerWantKnowMore\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user let's dive in",
      "assistant {\"agent_response\": \"The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!&NewLine; &NewLine;If you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.&NewLine; &NewLine;Can we begin by connecting your calendar so I can learn more about your travel habits?\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Great! Let's get started with connecting your calendar.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "assistant I already have your calendar access. analyzing your calendar, please wait for a moment.",
      "assistant {\"agent_response\": \"Based on your calendar events, here's what I've gathered about your travel preferences:\n\n\u2022 Preferred Home Airport: It seems we don't have this information yet.\n\u2022 Preferred Airline Brands: United Airlines appears to be your preferred airline.\n\u2022 Preferred Cabin: It seems we don't have this information yet.\n\u2022 Preferred Seats: It seems we don't have this information yet.\n\u2022 Preferred Hotel Brands: It seems we don't have this information yet.\n\u2022 Preferred Travel Misc: It seems we don't have this information yet.\n\nWould you like to update or add any preferences?\", \"preference\": {\"preferred_home_airport\": null, \"preferred_airline_brands\": [\"United Airlines\"], \"preferred_cabin\": null, \"preferred_seats\": null, \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}}",
      "user I like window seat as well. ",
      "assistant {\"agent_response\": \"Got it! I've updated your preferences to include a window seat preference.&NewLine; &NewLine;Are you finished updating your preferences?\", \"step\": \"ManualPreferenceCollection\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": {\"preferred_home_airport\": null, \"preferred_airline_brands\": [\"United Airlines\"], \"preferred_cabin\": null, \"preferred_seats\": [\"Window\"], \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user yup",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain #"
      Great! Here's a bit about how I can make your life easier:

      I’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.

      When you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.

      That said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.

      When I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.

      Do you have any questions before we get started, or should we dive right in and get to know each other?      
    "#
    ask_for_calendar_access #"
      The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel! If you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better. Can we begin by connecting your calendar so I can learn more about your travel habits?
    "#
  }
  @@assert(manual_preference_for_updates, {{this.step == "CompanyPolicyCollection"}})
}

test say_no_to_calendar_access {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user jump straight in",
      "assistant {\"agent_response\": \"The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!&NewLine; &NewLine;If you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.&NewLine; &NewLine;Can we begin by connecting your calendar so I can learn more about your travel habits?\", \"step\": \"AskForCalendarAccess\", \"why_explanation\": \"The user wants to jump straight into the process, so we are asking for calendar access to gather travel preferences.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user no. I don't want to share",
      "assistant {\"agent_response\": \"No problem at all! I want to make booking your travel as seamless as possible.\nCan you share your key preferences, like your home airport, preferred airlines, class of service, and seat preferences (aisle/window, front/back)?\nFor hotels, do you have favorite brands, styles, or must-have amenities?\nThis will help me find the best options for you. If I have any follow-ups, I\u2019ll check in\u2014no need to worry about frequent flyer details just yet!\", \"step\": \"ManualPreferenceCollection\", \"why_explanation\": \"The user declined calendar access, so we proceed to collect preferences manually.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": true}",
      "user I like window seat.",
      "assistant {\"agent_response\": \"Got it! I've updated your preferences to include a window seat. Are you finished updating your preferences?\", \"step\": \"ManualPreferenceCollection\", \"why_explanation\": \"The user provided a preference for a window seat, so we are continuing to collect preferences manually.\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": {\"preferred_home_airport\": null, \"preferred_airline_brands\": null, \"preferred_cabin\": null, \"preferred_seats\": [\"Window\"], \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}, \"is_conversation_finished\": false, \"declined_calendar_access\": true}",
      "user yes",
    ]
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain #"
      Great! Here's a bit about how I can make your life easier:

      I’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.

      When you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.

      That said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.

      When I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.

      Do you have any questions before we get started, or should we dive right in and get to know each other?      
    "#
    ask_for_calendar_access #"
      The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel! If you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better. Can we begin by connecting your calendar so I can learn more about your travel habits?
    "#    
  }
  @@assert(manual_preference_for_updates, {{this.step == "CompanyPolicyCollection"}})
}

test dont_have_access_to_calendar_should_ask_for_it {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user Let's get started."
      "assistant {\"agent_response\": \"\nI understand you want to get right to it and do your first search. The best way for me to be helpful is if I get to know your preferences and travel habits by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. \n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n\nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": true, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false, \"should_skip\": false}"
      "user Sure, you can access my calendar."
    ]
    has_calendar_access false
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(off_topic_bringing_back, {{this.step == "GettingCalendarAccess"}})
}

test already_has_access_to_calendar_should_analyze_it {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?"
      "user Let's get started."
      "assistant {\"agent_response\": \"\nI understand you want to get right to it and do your first search. The best way for me to be helpful is if I get to know your preferences and travel habits by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. \n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n\nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": true, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false, \"should_skip\": false}"
      "user Sure, you can access my calendar."
    ]
    has_calendar_access true
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain "\nGreat! Here's a bit about how I can make your life easier:\n\nI’m like your expert-level travel agent, here to handle flights and hotels in the US while following your company’s Travel Policy. Need help finding the best seat on the plane? I’ve got you. Plans change? I’ll handle cancellations or rebookings for you.\n\nWhen you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.\n\nThat said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.\n\nWhen I recommend travel options, I’ll prioritize your preferences and business travel style—whether it’s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I’ll always let you know if certain options would exceed your company’s Travel Policy.\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n"
    ask_for_calendar_access "\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n"
  }
  @@assert(off_topic_bringing_back, {{this.step == "AnalyzeCalendar"}})
}

test one_message_skip_onboarding {
  functions [ConverseOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user no need to intro. just dive in",
    ]
    has_calendar_access false
    self_intro "Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    more_explain #"
Great! Here's a bit about how I can make your life easier:

I’m like your expert-level travel agent, here to assist with flights and hotels in the US while following your company’s Travel Policy. Need the best seat on the plane? Got it. Plans change? I’ll take care of rebooking or cancellations.

When you share information like your Frequent Flyer number or Credit Card, I’ll securely save it so you won’t have to provide it again—I’ll use it to make booking fast and hassle-free.

That said, I specialize in booking your work travel, so I won’t be able to help with your family vacation plans (at least not yet). Down the road, I’m hoping to assist with car rentals or even restaurant reservations, but for now, it’s just flights and hotels.

Even if you’re not booking a trip today, getting to know your travel preferences now will make things smoother when you do. I’ll tailor recommendations to your style—whether it’s sticking to favorite airlines and hotels, staying near meeting locations, or ensuring your hotel has the right amenities. Plus, I’ll always flag options that exceed your company’s Travel Policy.

Would you like to set up your travel preferences now, or do you have any questions before we get started?
"#
    ask_for_calendar_access #"
The easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I’ll ask if you’d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!

If you don't want to share calendar access right now, that's okay too. Instead, I’ll ask you a few questions to get to know you better.
    
Can we begin by connecting your calendar so I can learn more about your travel habits?
    "#
  }
  // @@assert(skip_onboarding, {{this.skipping_probability >= 0.6 and this.skipping_probability <= 0.7}})
}
