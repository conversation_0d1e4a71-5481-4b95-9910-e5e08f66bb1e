test summarize_flight_info_includes_leg_status {
  functions [SummarizeFlightInfo]
  args {
    flight_json #"
{
    "legs": [
        {
            "flights": [
                {
                    "departureDateTime": {
                        "iso8601": "2025-05-22T11:30:00"
                    },
                    "arrivalDateTime": {
                        "iso8601": "2025-05-22T14:22:00"
                    },
                    "duration": {
                        "iso8601": "PT2H53M"
                    },
                    "flightId": "CgNTRUESA1NBThoVChMyMDI1LTA1LTIyVDExOjMwOjAwIhUKEzIwMjUtMDUtMjJUMTQ6MjI6MDA=",
                    "origin": "SEA",
                    "destination": "SAN",
                    "departureGate": {
                        "gate": "",
                        "terminal": ""
                    },
                    "arrivalGate": {
                        "gate": "2",
                        "terminal": "TERMINAL 2"
                    },
                    "marketing": {
                        "num": "2572",
                        "airlineCode": "DL"
                    },
                    "operating": {
                        "num": "2572",
                        "airlineCode": "DL"
                    },
                    "operatingAirlineName": "",
                    "hiddenStops": [],
                    "vendorConfirmationNumber": "JGS8UU",
                    "cabin": "ECONOMY",
                    "bookingCode": "K",
                    "flightStatus": "CONFIRMED",
                    "otherStatuses": [],
                    "co2EmissionDetail": {
                        "emissionValue": 0.266024,
                        "averageEmissionValue": 0.3023,
                        "flightDistanceKm": 1788.0,
                        "isApproximate": false
                    },
                    "restrictions": [],
                    "sourceStatus": "HK",
                    "equipment": {
                        "code": "738",
                        "type": "",
                        "name": "Boeing 737-800"
                    },
                    "distance": {
                        "length": 1050.0,
                        "unit": "MILE"
                    },
                    "flightWaiverCodes": [],
                    "amenities": [
                        {},
                        {},
                        {},
                        {},
                        {},
                        {},
                        {},
                        {}
                    ],
                    "flightIndex": 0
                }
            ],
            "brandName": "Main Cabin",
            "validatingAirlineCode": "DL",
            "legStatus": "PROCESSING_STATUS",
            "sortingPriority": 0,
            "travelerRestrictions": [
                {
                    "userId": {
                        "id": "515be11e-e5fe-4063-a1c1-e1fa271277e8"
                    },
                    "restrictions": [
                        "SEAT_EDIT_NOT_ALLOWED",
                        "LOYALTY_EDIT_NOT_ALLOWED",
                        "KTN_EDIT_NOT_ALLOWED",
                        "REDRESS_EDIT_NOT_ALLOWED"
                    ]
                }
            ],
            "fareOffers": [
                {
                    "userId": {
                        "id": "515be11e-e5fe-4063-a1c1-e1fa271277e8"
                    },
                    "baggagePolicy": {
                        "checkedIn": [
                            {
                                "description": "1 checked bag, 50 lbs (35 USD)"
                            }
                        ],
                        "carryOn": [
                            {
                                "description": "1 carry-on bag"
                            }
                        ]
                    }
                }
            ],
            "legId": "CgNTRUESA1NBThoKNjcyNjY3NzI1OA==",
            "rateType": "PUBLISHED",
            "preferredTypes": [],
            "preferences": [],
            "legIndex": 0
        }
    ],
    "airPnrRemarks": [
        {
            "remarkString": "OTTO TRIP, INC."
        },
        {
            "remarkString": "S*UD212 OTTO TRIP INC."
        },
        {
            "remarkString": "HTL-RATECODE-THR"
        },
        {
            "remarkString": "NOVATO CA US 94947"
        },
        {
            "remarkString": "S*ICSPOTNANA"
        },
        {
            "remarkString": "TRIPFEE-BC0.00/EXCX0.00"
        },
        {
            "remarkString": "PNRTYPE AIR"
        },
        {
            "remarkString": "ENVIRONMENT SBOXMETA"
        },
        {
            "remarkString": "ISPASSIVEPNR FALSE"
        },
        {
            "remarkString": "AA-TCC859996"
        },
        {
            "remarkString": "HTL-RATECODE-SIG"
        },
        {
            "remarkString": "TRAVELERPID 515BE11E-E5FE-4063-A1C1-E1FA271277E8"
        },
        {
            "remarkString": "S*UD78 IN POLICY"
        },
        {
            "remarkString": "S*SA804"
        },
        {
            "remarkString": "TRACEID 133ECE48EF5D30F0"
        },
        {
            "remarkString": "*CA5XXXXXXXXXXX5154\u00a504/28-XN"
        },
        {
            "remarkString": "BOOKEDBYORGID 4ECABB34-5EB3-4192-A1C8-C634A151DC41"
        },
        {
            "remarkString": "PPT DOB-11/11/1989 WANG/CHUNDONG -M"
        },
        {
            "remarkString": "BA-TCC859996"
        },
        {
            "remarkString": "HTL-RATECODE-ABC"
        },
        {
            "remarkString": "HTL-RATECODE-FHD"
        },
        {
            "remarkString": "WORKFLOWID 422573EEBE1D972E"
        },
        {
            "remarkString": "OBT-COMMISSION-SUCCESS-1747172030"
        },
        {
            "remarkString": "2-SABREPROFILES\u00a5OTTO TRIP INC."
        },
        {
            "remarkString": "S*HU"
        },
        {
            "remarkString": "PHONESPOTNANA"
        },
        {
            "remarkString": "TRIPID 5182963265"
        },
        {
            "remarkString": "NO-COMMISSION-APPLIES-1747172030"
        },
        {
            "remarkString": "OBT-COMMISSION-PROCESSED-1747172030"
        },
        {
            "remarkString": "MAILBOXSPOTNANA"
        },
        {
            "remarkString": "CURR-USD"
        },
        {
            "remarkString": "HTL-RATECODE-PP6"
        },
        {
            "remarkString": "NO EMAIL"
        },
        {
            "remarkString": "S*UD166 SPOTNANA"
        },
        {
            "remarkString": "11 BROOKE DR"
        },
        {
            "remarkString": "HTL-RATECODE-FHP"
        },
        {
            "remarkString": "AIR-SEQ1"
        },
        {
            "remarkString": "PNRID 6726677258"
        },
        {
            "remarkString": "S*UD3 5182963265"
        },
        {
            "remarkString": "HTL-RATECODE-WTH"
        },
        {
            "remarkString": "IB-TCC859996"
        },
        {
            "remarkString": "BOOKEDBYUSERID 2A8658AD-0572-480C-BBC0-B8BD3B3FB78C"
        },
        {
            "remarkString": "TRAVELERORGID 4ECABB34-5EB3-4192-A1C8-C634A151DC41"
        }
    ],
    "travelerInfos": [
        {
            "airVendorCancellationInfo": {
                "airVendorCancellationObjects": []
            },
            "createdMcos": [],
            "travelerIdx": 0,
            "userId": {
                "id": "515be11e-e5fe-4063-a1c1-e1fa271277e8"
            },
            "paxType": "ADULT",
            "tickets": [],
            "boardingPass": [],
            "booking": {
                "seats": [],
                "luggageDetails": [],
                "otherAncillaries": [],
                "itinerary": {
                    "totalFare": {
                        "base": {
                            "amount": 249.47,
                            "currencyCode": "USD",
                            "convertedAmount": 249.47,
                            "convertedCurrency": "USD",
                            "otherCoinage": []
                        },
                        "tax": {
                            "amount": 34.01,
                            "currencyCode": "USD",
                            "convertedAmount": 34.01,
                            "convertedCurrency": "USD",
                            "otherCoinage": []
                        }
                    },
                    "totalFlightsFare": {
                        "base": {
                            "amount": 249.47,
                            "currencyCode": "USD",
                            "convertedAmount": 249.47,
                            "convertedCurrency": "USD",
                            "otherCoinage": []
                        },
                        "tax": {
                            "amount": 34.01,
                            "currencyCode": "USD",
                            "convertedAmount": 34.01,
                            "convertedCurrency": "USD",
                            "otherCoinage": []
                        }
                    },
                    "flightFareBreakup": [
                        {
                            "legIndices": [
                                0
                            ],
                            "flightsFare": {
                                "base": {
                                    "amount": 249.47,
                                    "currencyCode": "USD",
                                    "convertedAmount": 249.47,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": []
                                },
                                "tax": {
                                    "amount": 34.01,
                                    "currencyCode": "USD",
                                    "convertedAmount": 34.01,
                                    "convertedCurrency": "USD",
                                    "otherCoinage": []
                                }
                            }
                        }
                    ],
                    "fareComponents": [
                        {
                            "fareBasisCode": "KA7NA0MC",
                            "tourCode": "",
                            "ticketDesignator": "",
                            "baseFare": {
                                "amount": 249.3,
                                "currencyCode": "USD",
                                "convertedAmount": 249.3,
                                "convertedCurrency": "USD",
                                "otherCoinage": []
                            },
                            "flightIds": [
                                {
                                    "legIdx": 0,
                                    "flightIdx": 0
                                }
                            ]
                        }
                    ],
                    "otherAncillaryFares": []
                },
                "otherCharges": []
            },
            "appliedCredits": [],
            "specialServiceRequestInfos": []
        }
    ],
    "automatedCancellationInfo": {
        "supportedCancellations": [
            {
                "cancelType": "VOID",
                "totalFare": {
                    "base": {
                        "amount": 249.47,
                        "currencyCode": "USD",
                        "convertedAmount": 249.47,
                        "convertedCurrency": "USD",
                        "otherCoinage": []
                    },
                    "tax": {
                        "amount": 34.01,
                        "currencyCode": "USD",
                        "convertedAmount": 34.01,
                        "convertedCurrency": "USD",
                        "otherCoinage": []
                    }
                },
                "refund": {
                    "amount": 283.48,
                    "currencyCode": "USD",
                    "convertedAmount": 283.48,
                    "convertedCurrency": "USD",
                    "otherCoinage": []
                }
            }
        ]
    },
    "bookingMetadata": {
        "fareStatistics": {
            "statisticsItems": [
                {
                    "statisticType": "MINIMUM",
                    "totalFare": {
                        "base": {
                            "amount": 102.68,
                            "currencyCode": "USD",
                            "convertedAmount": 102.68,
                            "convertedCurrency": "USD"
                        },
                        "tax": {
                            "amount": 32.7,
                            "currencyCode": "USD",
                            "convertedAmount": 32.7,
                            "convertedCurrency": "USD"
                        }
                    }
                },
                {
                    "statisticType": "MEDIAN",
                    "totalFare": {
                        "base": {
                            "amount": 457.85,
                            "currencyCode": "USD",
                            "convertedAmount": 457.85,
                            "convertedCurrency": "USD"
                        },
                        "tax": {
                            "amount": 59.34,
                            "currencyCode": "USD",
                            "convertedAmount": 59.34,
                            "convertedCurrency": "USD"
                        }
                    }
                },
                {
                    "statisticType": "MAXIMUM",
                    "totalFare": {
                        "base": {
                            "amount": 6698.6,
                            "currencyCode": "USD",
                            "convertedAmount": 6698.6,
                            "convertedCurrency": "USD"
                        },
                        "tax": {
                            "amount": 532.6,
                            "currencyCode": "USD",
                            "convertedAmount": 532.6,
                            "convertedCurrency": "USD"
                        }
                    }
                }
            ]
        }
    },
    "otherServiceInfos": [],
    "disruptedFlightDetails": [
        {
            "departureDateTime": {
                "iso8601": "2025-05-22T11:30:00"
            },
            "arrivalDateTime": {
                "iso8601": "2025-05-22T14:22:00"
            },
            "cabin": "ECONOMY",
            "originAirportCode": "SEA",
            "destinationAirportCode": "SAN",
            "marketing": {
                "num": "2572"
            },
            "operating": {
                "num": "2572"
            }
        }
    ]
}
    "#
  }
  @@check(processing_status, {{ this | regex_match("*Processing*") }})
}
