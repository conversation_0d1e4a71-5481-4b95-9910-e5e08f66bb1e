enum TaskType {
  FLIGHT @description("Handle flight search and selection workflow")
  HOTEL @description("Handle hotel search and selection workflow")
  NONE @description("No planning task needed, provide response to user")
}

class DateFlexibilityInfo {
  start_date string @description("Start date for flexible date range (YYYY-MM-DD)")
  end_date string @description("End date for flexible date range (YYYY-MM-DD)")
}

class TripPlanTask {
  task_type TaskType @description("Type of planning task to execute")
  outbound_date string? @description("Specific outbound date for this task (YYYY-MM-DD)")
  return_date string? @description("Specific return date for this task (YYYY-MM-DD)")
}

class TripPlanResponse {
  tasks TripPlanTask[] @description("Array of planning tasks to execute for multi-day searches")
  agent_response string @description("Response to the user about the current planning status")
}

class FlightSummaryResponse {
  summary string @description("Summarized flight options from multiple searches")
}

function FlightSummary(
  flight_results: string[],
  search_dates: string[],
  user_name: string
) -> FlightSummaryResponse {
  client GPT4o
  prompt #"
    You are <PERSON>, a helpful travel assistant. You need to summarize flight search results from multiple dates.
    
    Flight Results: {{ flight_results }}
    Search Dates: {{ search_dates }}
    User Name: {{ user_name }}
    
    Please provide a clear, concise summary of the flight options found across all the searched dates. 
    Group the results by date and highlight the best options for each day.
    Be conversational and helpful in your tone.
  "#
}

function TripPlanner(
  travel_preference: string,
  messages: string[],
  current_date: string,
  self_intro: string,
  convo_style: string,
  user_name: string?
) -> TripPlanResponse {
  client GPT41
  prompt #"
    {{ _.role('system') }}
    Role: Executive Travel Assistant - Trip Planning Coordinator

    Background:
    - {{ self_intro }}
    - {{ convo_style }}
    - Today's date is {{ current_date }}
    - {{RespondWithName(user_name)}}

    Your role is to coordinate trip planning by determining whether to handle flight search/selection or hotel search/selection. You focus on the search and selection phases, stopping before booking functionality.

    Planning Workflow Logic:
    - If user needs flight search/selection → create FLIGHT task
    - If flight is selected and user needs hotel search/selection → create HOTEL task
    - If both flight and hotel are selected → set task_type to NONE and inform user planning is complete
    - If user wants to modify selections → determine appropriate task type

    Flight Selection Validation:
    - For round trip flights: Ensure both outbound and return flights are selected before proceeding to hotel planning
    - For multi-leg flights: Ensure all required flight segments are selected
    - Check flight_type in travel context to determine if round trip validation is needed
    - Only create HOTEL task after all required flights are properly selected

    Hotel Task Creation Rules:
    - Validate that check_in_date and check_out_date are both available before hotel search

    Trip Completion Flow:
    - When both flights and hotels are selected (or skipped), provide a comprehensive trip summary.
    - Include selected flight details, hotel details, and total estimated cost
    - Ask user explicitly if they want to proceed with booking or need to adjust any selections.
    - Set task_type to NONE with confirmation request message

    Planning Coordination:
    - Flight planning includes: search, selection (stops before booking)
    - Hotel planning includes: search, selection (stops before booking)
    - Handle flexible date searches by coordinating multiple search calls
    - Provide clear communication about planning progress and next steps
    - When transitioning from flight to hotel (same for hotel to flight), please ask the user if they want to hotel or vice versa. set the task_type to NONE

    Multi-Day Search Rules:
    - For date range searches, create multiple FLIGHT tasks with specific dates
    - Maximum date range is 2 days for both outbound and return dates
    - Only support one-way flights (no multi-leg or round trip)
    - Each task in the array should have a specific outbound_date
    - Generate separate tasks for each date within the specified range
    - If user requests date flexibility, create tasks for each date

    Instructions:
    - Determine what type of planning task needs to be executed: FLIGHT, HOTEL, or NONE
    - For date flexibility requests, create multiple FLIGHT tasks with specific dates within the range
    - Validate that date ranges do not exceed 2 days maximum
    - For single date requests, create a single task in the array
    - Provide clear communication about planning progress and next steps
    - Focus on search and selection, not booking execution

    User's Travel Preferences:
    {{ travel_preference }}

    Recent Messages:
    {{ ConversationHistory(messages, 0) }}

    {{ ctx.output_format }}
  "#
}

enum TripPlanningWorkType {
  TripPlanning @description(#"
    When the user wants to plan a trip including flight search, selection, and hotel search and selection. This handles the coordination between flight and hotel planning phases, stopping before booking.
  "#)
  TripBooking @description(#"
    When the user wants to proceed with booking selected flights and/or hotels. This handles the booking workflow phases including checkout, validation, credit checks, and final booking after selections have been made.
  "#)
}

class NewSupervisorResponse {
  work_types TripPlanningWorkType[] @description(#"The types of work we "#)
  // reason string @description(#"The reason for the work"#)
}

function NewSupervisorDoConverse(
  messages: string[], 
  current_date: string,
  travel_context: string?) -> NewSupervisorResponse {
  client GPT41
  prompt #"

  {{ ConversationHistory(messages, 0) }}

  Role: Front of house supervisor

  Goal: Categorize the recent conversation into TripPlanningWorkType(s).

  Procedure:
    - Review the conversation history and identify the categories relevant to the current context.

  {{ ctx.output_format }}
  "#
}