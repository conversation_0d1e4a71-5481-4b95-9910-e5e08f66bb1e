test test_flight_match {
  functions [GetSimilarFlight]
  args {
    input_flight #"{"exchange_policy": "Free change, possible fare difference", "fare_option_name": "Delta Comfort"}"#
    candidate_flights [
      #"{"cancellation_policy": "Non-refundable", "exchange_policy": "Change allowed for free", "fare_option_name": "Delta Miain", "index_token": "0"}"#,
      #"{"cancellation_policy": "Refundable for free", "exchange_policy": "Change allowed for free", "fare_option_name": "Delta Main Refundable", "index_token": "1"}"#,
      #"{"cancellation_policy": "Non-refundable", "exchange_policy": "Change allowed for free", "fare_option_name": "Delta One", "index_token": "2"}"#,
      #"{"cancellation_policy": "Refundable for free", "exchange_policy": "Change allowed for free", "fare_option_name": "Delta One Refundable", "index_token": "3"}"#
    ]
  }
  @@assert(index_matched, {{ this.index_array == [] }})
}

test get_similar_flight {
  functions [GetSimilarFlight]
  args {
    input_flight #"
      {"cancellation_policy": "No refunds", "exchange_policy": "Free change, possible fare difference", "fare_option_name": "Economy Classic"}
    "#
    candidate_flights ["{\"cancellation_policy\": \"Non-refundable\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Economy Classic\", \"index_token\": \"0\"}", "{\"cancellation_policy\": \"Refundable for free\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Economy Flex\", \"index_token\": \"1\"}", "{\"cancellation_policy\": \"Refundable for 101 USD\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Economy Classic\", \"index_token\": \"2\"}", "{\"cancellation_policy\": \"Non-refundable\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Premium Economy Classic\", \"index_token\": \"3\"}", "{\"cancellation_policy\": \"Refundable for 654 USD\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Premium Economy Classic\", \"index_token\": \"4\"}", "{\"cancellation_policy\": \"Refundable for 654 USD\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Premium Economy Flex\", \"index_token\": \"5\"}", "{\"cancellation_policy\": \"Refundable for 101 USD\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Premium Economy Classic\", \"index_token\": \"6\"}", "{\"cancellation_policy\": \"Refundable for free\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Premium Economy Flex\", \"index_token\": \"7\"}", "{\"cancellation_policy\": \"Non-refundable\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Business Classic\", \"index_token\": \"8\"}", "{\"cancellation_policy\": \"Refundable for 959 USD\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Business Classic\", \"index_token\": \"9\"}", "{\"cancellation_policy\": \"Refundable for free\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Business Flex\", \"index_token\": \"10\"}", "{\"cancellation_policy\": \"Refundable for 134 USD\", \"exchange_policy\": \"Change allowed for free\", \"fare_option_name\": \"Business Classic\", \"index_token\": \"11\"}"]
  }

  @@assert(index_matched, {{ this.index_array == [0] }})
}
