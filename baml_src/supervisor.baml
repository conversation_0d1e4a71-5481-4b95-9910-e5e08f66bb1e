enum FlightType {
    OneWay @description(#"Flight only has trip start date, trip origianl location, trip destination location, but without returning date"#)
    RoundTrip @description(#"Flight has trip start date, trip return date, trip original location, trip destination location"#)
    MultiLegs @description(#"A flight that goes from one airport (or location) to another on one date, then to another on another date, and so on."#)

}
enum SupervisorClassificationLabel {
    Hotels @description(#"The content and intent of the traveler message relates to anything about a hotels conversation thread, 
      including searching, selecting, booking, questions, ackowledgement, confirmation, gratitude, etc."#)

    CancelHotel @description(#"The content and intent of the traveler message relates to cancel ALREADY BOOKED hotel. It is important that customer must provide an already booked hotel."#)

    Flights @description(#"The content and intent of the traveler message relates to anything about flights. 
      - This includes **searching, selecting, booking, pricing, and flight preferences**.
      - Includes **seat selection before booking is finalized** (e.g., "Can I get a window seat?").
      - Includes **modifications to a flight that has NOT been booked yet** (e.g., "Change my seat," "Change the departure time," or "Upgrade to business class before booking").
      - Does **NOT** include modifying or exchanging already booked flights (those belong to `ExchangeFlights`)."#)

    ExchangeFlights @description(#"The content and intent of the traveler message relates to:
      1. **Modifying an ALREADY BOOKED flight** (must have a confirmation number).
      2. **Changing seats for an ALREADY BOOKED flight**.
      - Only applies **after the booking is confirmed with a PNR (Passenger Name Record) or confirmation number**.
      - Examples: "Change my booked flight to an earlier one," "Upgrade my seat after booking."
      - If the user requests seat changes before the flight is booked, classify under `"Flights"`, NOT `"ExchangeFlights"`."#)

    CancelFlights @description(#"The content and intent of the traveler message relates to cancel ALREADY BOOKED flight.
      It is important that the customer must provide an already booked flight with a confirmation number."#)

    ProfileHandler @description(#"The content and the intent of the traveler message relates to checking or changing the traveler's profile information, such as name, email, phone number, payment method, or other personal details."#)

    Inquiry @description(#"
      The content and intent of the traveler message is asking for explanations, clarifications, or reasoning about:
        1. Why certain choices were made
        2. Questions about policies, costs
        3. General comments about travel and destination cities or locations
        4. Messages to specify destination and travel dates
        This classification should be used when the traveler is seeking information rather than taking action.
        Also includes neutral acknowledgments that don't introduce new actions/requests
    "#)

    Other @description(#"A catch-all category for messages or general pleasantries or small talk that don't fit other categories."#)
}

class SupervisorRouting {
  /// Explain and classifies the most recent message from the traveler.
  /// Extract the travel context using the existing travel context, travel preference and the most recent message from the traveler.
  supervisor_message_classification SupervisorClassificationLabel @description(#"The label that best describes the traveler's most recent message"#)
  travel_context_json string? @description(#"
    The updated travel context must follow strict JSON formatting:

    - **Only include fields that have non-null, non-empty string, and non-empty array values.**
    - If a field's value is `null`, an empty string (`""`), or an empty array (`[]`), **completely omit** it from the output.
    - This is a delta update: output only fields that have changed.
    - All dates are future-oriented unless explicitly stated otherwise.
    - When `supervisor_message_classification` is `Inquiry` or `Other`, ensure you include the delta update for `trip_flight_type` if applicable.

    Example schema:
    ```json
    {
      trip_destination: string?, //The overall destination city or location of the trip, the major city where the hotel destination is located in or where the airport is located if no hotel.
      trip_start_date: string?, //The depature date, e.g. "2024-06-24" (ISO 8601 format). Omit if needs more classification.
      trip_end_date: string?, //The return date, e.g. "2024-06-24" (ISO 8601 format). Omit if needs more classification.
      trip_origin_airport: string?, // The origin airport code (IATA, e.g., BOS, SEA, DFW). 
                                    // If a city is mentioned without an airport, use the corresponding MAC (e.g., CHI for Chicago, NYC for New York City). 
                                    // If no MAC exists, default to the city's **primary** airport.
                                    // This field MUST be populated if a flight conversation is detected.

      trip_destination_airport: string?, // The destination airport code (IATA, e.g., BOS, SEA, DFW).
                                        // If a city is mentioned without an airport, use the corresponding MAC (e.g., CHI for Chicago, NYC for New York City). 
                                        // If no MAC exists, default to the city's **primary** airport.
                                        // This field MUST be populated if a flight conversation is detected.      trip_airline_brands: string[]?, //The airlines traveler wants (e.g. Delta, United). If not specified use the preference. 
      trip_cabin: string[]?, //The cabin preference (e.g. First, Business). If not specified use the preference. 
      trip_seats: string[]?, //The seat preference (e.g. aisle/window, front of plane). If not specified use the preference. 
      trip_outbound_departure_time: string?, //The desired time to depart.
      trip_outbound_arrival_time: string?, //The desired time to arrive.
      trip_return_departure_time: string?, //The desired time to depart.
      trip_return_arrival_time: string?, //The desired time to arrive.
      hotel_location_preference: string?, //The preferred location of the hotel (e.g. city center, near airport).
      trip_hotel_brands: string[]?, //The hotel brands preferred (e.g. Marriott, Hilton). If not specified use the preference. 
      hotel_room_preference: string?, //The traveler's preference for the room type (e.g. bed size, view, floor level) or features (e.g. bathroom, wheelchair, workspace).
      trip_travel_misc: string[]?, //Other needs that's related to this specific trip which are not mentioned above (e.g. extra blanket, non-stop flights, hotels with pools, vegeterian).
      trip_flight_type: string? //The type of flight: OneWay, RoundTrip or MultiLegs. OneWay: Flight with start date, origin, and destination; RoundTrip: Flight with start, return date, origin, and destination; MultiLegs: Flight with multiple legs on different dates.
    }
    ```

    **IMPORTANT:** DO NOT include any field that is `null`, an empty string (`""`), or an empty array (`[]`).
    "#)
  should_context_reset bool? @description(#"
    Check the last message from user. If the traveler mentioned a completely different travel destination, you should reset the travel context.
    Note:
    - The return trip of a roundtrip flight is considered the same trip.
    - Switching between flight and hotel bookings is considered the same trip.
    - You should explicitly set fields to their new values if explicitly mentioned by the traveler.
  "#)
  // For debug purpose
  // reason_on_should_context_reset string? @description(#"The reason on why the travel context should be reset in this response"#)
  // reason_on_travel_context_json string? @description(#"The reason on why the travel context is updated in this response"#)
  // message_classification_explanation string @description(#"The explanation of why the label for supervisor_message_classification was chosen for the most recent user message."#)
  // why_the_trip_flight_type string @description(#"The explanation of why the trip_flight_type was chosen for the most recent user message."#)
}

// note that we inherit from ResponseAllPreferences in order to get the user preferences and blend them in with the travel context
class TravelContext {
  /// The state of things for the current trip being planned. The context and preferences are updated with each message from the traveler.
  trip_destination string? @description(#"The overall destination city or location of the trip, the major city where the hotel destination is located in or where the airport is located if no hotel."#)
  trip_start_date string? @description(#"A future depature date, e.g. "2024-06-24" (ISO 8601 format). Omit if needs more classification."#)
  trip_end_date string? @description(#"A future return date, e.g. "2024-06-24" (ISO 8601 format). Omit if needs more classification."#)
  // Mandatory infomration to kick off flight search
  trip_origin_airport string? @description(#"The origin airport. If not specified, assume it's their home city. e.g. BOS, SEA, DFW."#)
  trip_destination_airport string? @description(#"The destination airport (e.g. BOS, SEA, DFW)."#)
  trip_airline_brands string[]? @description(#"The airlines traveler wants (e.g. Delta, United). If not specified use the preference. "#)
  trip_cabin string[]? @description(#"The cabin preference (e.g. First, Business). If not specified use the preference. "#)
  trip_seats string[]? @description(#"The seat preference (e.g. aisle/window, front of plane). If not specified use the preference. "#)
  trip_outbound_departure_time string? @description("TThe desired time to depart.")
  trip_outbound_arrival_time string? @description("TThe desired time to arrive.")
  trip_return_departure_time string? @description("TThe desired time to depart.")
  trip_return_arrival_time string? @description("TThe desired time to arrive.")
  // Mandatory infomration to kick off hotel search
  hotel_location_preference string? @description(#"The preferred location of the hotel (e.g. city center, near airport)."#)
  trip_hotel_brands string[]? @description(#"The hotel brands preferred (e.g. Marriott, Hilton). If not specified use the preference. "#)
  hotel_room_preference string? @description(#"The traveler's preference for the room type (e.g. bed size, view, floor level) or features (e.g. bathroom, wheelchair, workspace)."#)
  trip_travel_misc string[]? @description(#"Other needs that's related to this specific trip which are not mentioned above (e.g. extra blanket, non-stop flights, hotels with pools, vegeterian)."#)
  trip_flight_type FlightType? @description(#"The type of flight: OneWay, RoundTrip or MultiLegs. OneWay: Flight with start date, origin, and destination; RoundTrip: Flight with start, return date, origin, and destination; MultiLegs: Flight with multiple legs on different dates."#)
  // // Alternaniative way to represent the trip with a single string for flight or hotel
  // flight_trip_configuration string? @description(#"
  //   Summarize the details of a trip configuration in one concise sentence, including key information such as origin, destination, airline preference, cabin class, seat preference, and general departure times (e.g., early morning, late night), assuming unspecified values default to user preferences.
  //   Example: A trip from BOS to SEA with Delta in business class, preferring window seats, departing outbound early in the morning and returning late at night.
  // "#)
  // hotel_trip_configuration string? @description(#"
  //   Summarize the details of a hotel booking configuration in one concise sentence, including key information such as location preference, room features, and preferred hotel brands, assuming unspecified values default to user preferences.
  //   Example: A hotel in a city center with a king-sized bed and a workspace, preferring Marriott or Hilton brands.
  // "#)
}


function SupervisorMessageClassification(
  travel_preference: string,
  travel_context: string,
  messages: string[],
  current_date: string,
  self_intro: string?,
  convo_style: string?,
  trip_memories: string[]?) -> SupervisorRouting {
  client GPT4o_AzureFirst
  prompt #"
    {{ _.role('system') }}
    Role: AI Travel Assistant

    Background:
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}
    - Classify the traveler’s latest message into one of: Flights, Hotels, ExchangeFlights, CancelFlights, CancelHotel, Inquiry, or Other.
    - Extract relevant travel details and update the **context** (only include changes).
    - Maintain **clear distinctions** between booking, modifications, and cancellations.
    - Dates must be explicitly stated—avoid assumptions for vague timeframes.
    - Today's date is {{ current_date }}

    **Key Classification Rules:**
    - **Flights**: Any request related to flights (search, selection, pricing, seat preferences, etc.), including follow-ups about an existing flight topic **before the trip is booked**.
    - **ExchangeFlights**: Only applies when:
      1. The trip has **already been booked** (i.e., a confirmation ID exists in the conversation history).
      2. The traveler wants to modify a **finalized** flight (seat changes, date changes, or airline swaps).
    - **CancelFlights**: Cancelling an **already booked** flight (must have a confirmation number).
    - **Inquiry**: Questions about policies, options, or explanations **not directly requesting an action**.
    - **ProfileHandler**: Checking or updating traveler profile information (name, email, phone, payment method).
    - **Other**: Small talk, irrelevant messages, or unsupported trip types (e.g., multi-city flights).
    - **Hotels**: Any hotel search, selection, pricing, or modifications **before booking is confirmed**.
    - **CancelHotel**: Only applies when:
      1. The hotel is **already booked** (i.e., a confirmation ID or order number exists in conversation history).
      2. The user requests cancellation of the confirmed booking.
    - **Hotel Modification Rule (Hotels vs. CancelHotel)**:
      - If the user requests to cancel a hotel **before it's booked**, classify as `"Hotels"`.
      - If the user requests cancellation **after the booking is confirmed** (confirmation ID exists), classify as `"CancelHotel"`.

    **Flight Modification Rule (Flights vs. ExchangeFlights):**
    - If the user requests a flight change **before booking is finalized**, classify as `"Flights"`:
      - Flight selected, final ticket price, seat selection, etc are not considered booked. In these cases, any change including time, fare class or seat are considered as `"Flights"`.
      - Example: "Push flight out by two days", "Change my seat to a window seat".
    - If the user requests a change **after booking is confirmed** (confirmation ID exists), classify as `"ExchangeFlights"`:
      - Flight is considered booked only if PNR (e.g. PFKKLH) is provided
      - Example: "Change my flight to an earlier one", "Upgrade my seat after booking".

    **Multi-Leg / Multi-City Trip Handling (Unsupported):**
    - If the user mentions more than one **distinct destination** in a single trip request, classify as `"Other"`.
    - If different flight legs occur on **different dates** with different destinations, classify as `"Other"`.

    **Date Handling Rules:**
    - If a month/date is mentioned **without a year**, assume the **next occurrence in the future**.
      - Example: If today is **November 13, 2024**, and the user says **"Apr 03-12"**, resolve as **April 3-12, 2025**, not 2024.
    - Never assume a date is in the past unless explicitly stated.

    **Flight Type Rules:**
    - **OneWay**: Has a start date, origin, and destination, but no return date.
    - **RoundTrip**: Has a start and return date, origin, and destination.  
      - If both **outbound and return dates** exist in the user's latest message, classify as `"RoundTrip"`.
    - **MultiLegs (Unsupported)**: Multiple segments on different dates.
      - Classify these under `"Other"` to guide the traveler to a supported format.

    **Context Update Rules (Optimize for Latency):**
    - Output **only changes** (delta update).
    - **Do not** include fields that remain unchanged.
    - **Reset context** if a new destination is mentioned.
    - For destination or origin airports:
      - If the traveler specified an airport, use the IATA code (e.g., SEA, JFK, SFO).
      - If the traveler only mentioned a city, use **Metropolitan City Codes (MACs)** where applicable.
      - **MAC Handling Rules:**
        - **Use CHI for Chicago** instead of ORD or MDW unless the user specifies an airport.
        - **Use NYC for New York City** instead of JFK, LGA, or EWR unless specified.
        - **Use WAS for Washington DC** instead of IAD or DCA unless specified.
      - If no MAC exists for the city, default to the **primary airport** but notify the user.
      - Always extract airports if this is a flight conversation.
    - If the user's latest message provides both **outbound and return dates**, set:
      - `trip_flight_type = "RoundTrip"`
    - If only an **outbound date** exists, set:
      - `trip_flight_type = "OneWay"`

    **Trip Intention Handling:**
    - Extract **trip destination, start date, and return date** only from the latest user message.
    - **Do not assume** destination or dates from history.
    - If a destination switch occurs, **reset previous trip context** and only retain details from the last user message.

    **Capturing Additional Trip Constraints:**
    - If the user mentions trip-related constraints (e.g., **"no overnight layover"**, **"no redeye flights"**, **"direct flight only"**), capture this under `trip_travel_misc`.

    **Existing Travel Context:**
    {{ travel_context }}

    **Traveler Preferences:**
    {{ travel_preference }}

    **Recent Messages:**
    {{ ConversationHistory(messages, 0) }}

    {{ TripMemories(trip_memories) }}

    **Provide Output in JSON Format:**
    - `supervisor_message_classification`: The label for the latest traveler message.
    - `travel_context_json`: **Only changed fields** (omit unchanged/null/empty values).
    - `should_context_reset`: `true` if the traveler switches to a new destination.

    {{ ctx.output_format }}
  "#
}


function SupervisorMessageClassification_Claude(
  travel_preference: string,
  travel_context: string,
  messages: string[],
  current_date: string,
  self_intro: string?,
  convo_style: string?,
  trip_memories: string[]?) -> SupervisorRouting {
  client ClaudeFirst
  prompt #"
    {{ _.role('system') }}
    Role: Travel Agent Supervisor

    Background:
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}
    - Classify the traveler's most recent message into one of these categories: Flights, Hotels, ExchangeFlights, CancelFlights, Inquiry, or Other.
    - Update the travel context based on new information in the most recent message and maintain existing preferences.

    Classification rules (READ CAREFULLY):
    - Flights: 
      * Initial booking requests
      * Any changes to UNBOOKED flights (including date changes, "push flight out by X days")
      * Seat selection for unbooked flights
      * Canceling flight selections (not confirmed bookings)
      * New flight inquiries or requests to discuss a different flight/destination
    - Hotels: All hotel/accommodation related requests
    - ExchangeFlights: 
      * ONLY for changes to ALREADY BOOKED flights (with PNR/confirmation number)
      * Changes to dates, airlines, or seats for confirmed bookings
    - CancelFlights: 
      * ONLY for canceling a FINALIZED booking (with PNR/confirmation number)
    - Inquiry: 
      * General questions about travel policies or options
      * NOT requests to discuss specific flights - those are "Flights"
    - Other: 
      * Multi-city or multi-leg flight bookings (e.g., "I need to be in SF then Denver then back to Seattle")
      * Non-travel topics

    Travel context rules:
    - Today's date is {{ current_date }}
    - If traveler mentions a new destination: RESET all context values
    - Otherwise: Update ONLY the values mentioned in the most recent message
    - ALWAYS incorporate traveler preferences from the preference section into the context
    - Critical details to track: origin/destination airports, airlines, cabin class, seating preferences, hotel brands
    - Flag invalid dates (past dates or impossible formats)
    - Request clarification for vague timeframes ("next month", "in 2 weeks", "during Christmas")

    Airport selection priority:
    - Use Metro Area Codes (MACs) when applicable: NYC for JFK/LGA/EWR, CHI for ORD/MDW, WAS for DCA/IAD/BWI
    - For cities with multiple airports but no MAC, use primary airport code but mention alternatives
    - If no commercial airport exists at specified location, use closest available airport

    Important definitions:
    - FINALIZED/BOOKED trip: Has PNR or confirmation number
    - Flight selection: Chosen but not yet booked
    - Synonyms: flights/airlines/air and hotels/stays/accommodations
    - Multi-city/Multi-leg flights (e.g., "Seattle to SF to Denver to Seattle" or visiting multiple cities) are NOT supported (classify as Other)

    ----
    Existing travel context:
    {{ travel_context }}
    ----
    Traveler preferences:
    {{ travel_preference }}
    ----
    {{ ConversationHistory(messages, 0) }}

    {{ TripMemories(trip_memories) }}

    {{ _.role("system")}}
    Before finalizing your classification, check these common errors:
    1. "Push flight out by X days" for UNBOOKED flights → Flights (not ExchangeFlights)
    2. Requests to discuss a new flight/destination → Flights (not Inquiry)
    3. Any flight with multiple destinations/cities → Other
    4. Have you included all relevant preferences from the preference section?

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}
