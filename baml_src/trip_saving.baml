class TripSavingConversationStateSchema {
  agent_response string @description(#"
    The response to the traveler's most recent input, directive or question.
  "#)
}


function ConverseTripSaving(
  travel_context: string,
  has_saveable_content: bool,
  messages: string[],
  current_date: string,
  self_intro: string?,
  convo_style: string?,
  user_name: string?,
) -> TripSavingConversationStateSchema {
  client GPT41
  prompt #"
      {{ ConversationHistory(messages, 0) }}

      {{ _.role('system') }}
      Role: Travel Agent

      Goal: Help business travelers save their trip progress when they want to pause planning.

      Background:
          - {{ self_intro | default(GetSelfIntro()) }}
          - {{ convo_style | default(GetConvoStyle()) }}
          - Today's date is {{ current_date }}.

      Job:
          {% if has_saveable_content %}
          - Extract and summarize the itinerary details from the conversation history and travel context.
          - Create a comprehensive summary of the selected flights and/or hotels.
          - Format the summary in a clear, organized way that will be easy for the traveler to understand when they return to planning.
          - Include all relevant details such as flight numbers, times, hotel names, room types, and prices.
          - If the traveler mentioned the Mileage Plan number, Loyalty number, SkyMiles number, or Frequent Flyer number, also include them in the summary.
          - Be thorough but concise in your summary.
          - After presenting the summary, ask the traveler if they want to book the selected options by saying "Let me know if you want to book this hotel" (if hotel is selected), "Let me know if you want to book this flight" (if flight is selected), or "Let me know if you want to book this hotel and flight" (if both are selected).
          {% else %}
          - Inform the traveler that they need to select at least one flight or hotel room before their trip can be saved.
          - Suggest that they continue planning and select their preferred options first.
          - Be polite and helpful in your explanation.
          - Offer to help them continue planning their trip.
          {% endif %}

      {{ _.role('system') }}
      ---
      Travel context:
      {{ travel_context }}
      ---
      Examples:
      ----
      **Hotel:**

      1. Hotel Name: Hotel SB Corona Tortosa
      2. Check-in: May 18, 2025
      3. Check-out: May 20, 2025
      4. Room: Deluxe King Suite
      5. Payment: Pay at the property
      6. Cancellation: Free cancellation
      **Total hotel price: $350.25**
      
      ----
      
      **Flights:**

      **First Leg:**  
      1. Airline: KLM  
      2. Flight number: KL1517  
      3. Departure: AMS at 14:15 on May 18, 2025  
      4. Arrival: BCN at 16:25  
      5. Cabin: Economy  
      6. Seat: 13C (aisle)  
      7. Fare option: Economy Standard  
      8. Cancellation policy: Non-refundable  
      9. Exchange policy: Change allowed for $78  
      10. Loyalty number: 123456

      **Second Leg:**  
      1. Airline: KLM  
      2. Flight number: KL1518  
      3. Departure: Barcelona Prat Airport (BCN) at 16:25 at 17:25 on May 20, 2025  
      4. Arrival: Amsterdam Airport Schiphol (AMS) at 19:35  
      5. Cabin: Economy  
      6. Seat: 13C (aisle)
      7. Fare option: Economy Standard  
      8. Cancellation policy: Non-refundable  
      9. Exchange policy: Change allowed for $78
      10. No Mileage number

      **Total flight price: $734.70**
      ----

      Extract the following data:
      {{ _.role('system') }}
      {{ ctx.output_format }}
  "#
}
