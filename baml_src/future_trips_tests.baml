test onboarding_future_trips {
  functions [ConverseFutureTrips]
  args {
    prompt #"

    Objective: Identify all relevant events from the JSON calendar data that may involve or require traveling for meetings over the next two months.
        Ensure that the search includes all months covered in the calendar data. Looking specificaly at the 'summary', 'description', 'location' 'start' and 'end' fields of the calendar data,
        use the following keyword categories to guide the search, but don't limit the search to just these keywords - there may be other keywords that are close and relevant to the ones below:

        Location-Based Keywords:

            1.	City Names: Any city name outside Seattle (e.g., “Chicago,” “New York,” “London”).
            2.	Airport Codes: Common airport codes (e.g., “SEA” for Seattle, “JFK” for New York).
            3.	Specific Locations: Locations that indicate a trip or travel (e.g., “Whistler,” “Syracuse,” “Newark”).

        Event Type Keywords:
            4.	Conference: Look for terms like “conference,” “summit,” “convention.”
            5.	Off-site: Search for “off-site,” “retreat,” “offshore.”
            6.	Meeting: Include terms like “meeting,” “synch,” “catch-up,” “check-in.”
            7.	Travel-Related Terms: Look for “flight,” “hotel,” “reservation,” “stay,” “booking.”
            8.	Business Terms: Include terms like “sales,” “client,” “partner,” “board meeting.”
            9.	Social Gatherings: Look for terms like “dinner,” “lunch,” “reservation,” “party.”

        Temporal Keywords:
            10.	Specific Months: Search for all months (e.g., “August,” “September,” “October,” “November”).
            11.	Weekdays: Include terms like “Monday,” “Tuesday,” etc., especially if meetings are more likely on weekdays.

    Action:
        1.	Search Across All Months: Ensure that you search for events across all months within the provided JSON calendar data, not just the nearest upcoming ones.
        2.	Extract Relevant Events: Identify and list all events that match the keywords above, focusing on those that may require travel or involve important meetings.
        3.  Do not stop at 10 events. There could be up to 50 that match the search. Find them all.
        4.  Exclude anything that is a hotel stay or flight which typically have words like “stay at” or “flight to”

        Using a combination of these keywords in searches should help identify events that likely involve travel, important meetings, or off-site activities.

    "#

    cal_events "[{\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20241207T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2024-12-06T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2024-12-06T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"69d2dgtjpdont7nhv2s4tnp7h7\", \"summary\": \"Chundong <> Jerry (Maveron)\", \"description\": \"\", \"location\": \"Maveron, 411 1st Ave S #600, Seattle, WA 98104, USA\", \"start\": {\"dateTime\": \"2024-12-11T16:30:00-08:00\"}, \"end\": {\"dateTime\": \"2024-12-11T17:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"2l8h5rs85htr4pmdb85skm2m9g\", \"summary\": \"Flight to LAX (DL 2830)\", \"description\": \"Here are your outbound flight details:\n\tAirline: Delta Air Lines\n\tFlight number: 2830\n\tConfirmation number: 5281261794\", \"location\": \"\", \"start\": {\"dateTime\": \"2024-12-12T14:10:00-08:00\"}, \"end\": {\"dateTime\": \"2024-12-12T16:57:00-08:00\"}, \"is_new\": false}, {\"id\": \"32v027fdgtd4pnl206akbojskc\", \"summary\": \"Cancel audible subscription (renew at 17th)\", \"description\": \"\", \"location\": \"\", \"start\": {\"dateTime\": \"2024-12-15T21:00:00-08:00\"}, \"end\": {\"dateTime\": \"2024-12-15T21:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"lph7u3485gkq4smsp94gatvuvo\", \"summary\": \"Flight to Taipei City (CI 21)\", \"description\": \"To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1GsvSTZM5Qth6KGL2MQbw9weMZ9WxJ_I\n\", \"location\": \"Seattle SEA\", \"start\": {\"dateTime\": \"2024-12-20T00:10:00-08:00\"}, \"end\": {\"dateTime\": \"2024-12-20T13:20:00-08:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20241221T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2024-12-20T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2024-12-20T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20241227T020000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2024-12-26T18:00:00-08:00\"}, \"end\": {\"dateTime\": \"2024-12-26T18:25:00-08:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250104T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-01-03T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-01-03T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"s86m6rblhf4vq7khu8a52e5dgc\", \"summary\": \"Flight to Seattle (CI 22)\", \"description\": \"To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DM1GsvSTZM5Qth6KGL2MQbw9weMZ9WxJ_I\n\", \"location\": \"Taipei City TPE\", \"start\": {\"dateTime\": \"2025-01-07T07:35:00-08:00\"}, \"end\": {\"dateTime\": \"2025-01-07T18:15:00-08:00\"}, \"is_new\": false}, {\"id\": \"7cv92usoqnv7518mk56psfbess\", \"summary\": \"Flight to Seattle (DL 280)\", \"description\": \"To see detailed information for automatically created events like this one, use the official Google Calendar app. https://g.co/calendar\n\nThis event was created from an email you received in Gmail. https://mail.google.com/mail?extsrc=cal&plid=ACUX6DPnnzt1QFKBJlw0Ou6hQvaTeqbEFEvqiB4\n\", \"location\": \"Shanghai PVG\", \"start\": {\"dateTime\": \"2025-01-14T03:05:00-08:00\"}, \"end\": {\"dateTime\": \"2025-01-14T14:05:00-08:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250118T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-01-17T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-01-17T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250131T020000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-01-30T18:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-01-30T18:25:00-08:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250201T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-01-31T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-01-31T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"k0ni3blssbqgrig7kj6g070fdk\", \"summary\": \"Flight to Hawaii (DL 351)\", \"description\": \"Here are your outbound flight details:\n\tAirline: Delta Air Lines\n\tFlight number: 351\n\tAirline Confirmation number: G5BFXQ\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-02-05T07:50:00-08:00\"}, \"end\": {\"dateTime\": \"2025-02-05T14:16:00-08:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250215T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-02-14T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-02-14T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250228T020000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-02-27T18:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-02-27T18:25:00-08:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250301T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-02-28T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-02-28T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250314T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-03-14T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-03-14T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250328T010000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-03-27T18:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-03-27T18:25:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250328T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-03-28T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-03-28T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250411T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-04-11T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-04-11T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250425T010000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-04-24T18:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-04-24T18:25:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250425T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-04-25T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-04-25T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250509T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-05-09T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-05-09T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250523T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-05-23T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-05-23T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250530T010000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-05-29T18:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-05-29T18:25:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250606T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-06-06T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-06-06T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250620T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-06-20T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-06-20T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250627T010000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-06-26T18:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-06-26T18:25:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250704T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-07-04T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-07-04T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250718T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-07-18T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-07-18T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250801T010000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-07-31T18:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-07-31T18:25:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250801T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-08-01T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-08-01T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250815T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-08-15T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-08-15T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250829T010000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-08-28T18:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-08-28T18:25:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250829T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-08-29T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-08-29T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250912T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-09-12T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-09-12T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20250926T010000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-09-25T18:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-09-25T18:25:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20250926T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-09-26T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-09-26T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20251010T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-10-10T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-10-10T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"6uamob8odje8pia79skm4locoh\", \"summary\": \"Renew theagent.cc/me DNS on godaddy\", \"description\": \"\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-10-11T21:30:00-07:00\"}, \"end\": {\"dateTime\": \"2025-10-11T22:00:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20251024T230000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-10-24T16:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-10-24T16:30:00-07:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20251031T010000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-10-30T18:00:00-07:00\"}, \"end\": {\"dateTime\": \"2025-10-30T18:25:00-07:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20251108T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-11-07T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-11-07T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"23iofdl5r1b9g8nnql8iekchdc_20251122T000000Z\", \"summary\": \"Update Home Valuation\", \"description\": \"https://docs.google.com/spreadsheets/d/17ExxoNFDSuJJSNoT1ZQszA8q5BYYuA_58rib6vQj7N8/edit#gid=0\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-11-21T16:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-11-21T16:30:00-08:00\"}, \"is_new\": false}, {\"id\": \"0ti6p24bkt4ile41j7cs46cl8l_20251128T020000Z\", \"summary\": \"Send check to Juan the LA Gardner\", \"description\": \"\nJuan Antonio Carrillo3045 11th Ave Los Angeles CA 90018\", \"location\": \"\", \"start\": {\"dateTime\": \"2025-11-27T18:00:00-08:00\"}, \"end\": {\"dateTime\": \"2025-11-27T18:25:00-08:00\"}, \"is_new\": false}]"

    messages [
      "assistant Hi Chundong! I'm looking at your calendar for events that are likely to become trips.  Please select which events you plan to travel to, and I'll create a new trip for each.",
      "user Great! Let me know what trips you find and I'll tell you which ones I want to create trips for."
    ]
  }
}

test select_a_trip_n_click_im_done_button {
  functions [ConverseFutureTrips]
  args {
    prompt #"
    Role: Travel Agent

    Goal: Help the traveler choose calendar events that they would like to promote to trip creation.

    Backstory:
        - You are an expert travel agent helping a business traveler you have never met before.
        - The traveler has been presented calendar events mined from their personal calendars that might be actual future business trips that should be queued up for planning and booking.
        - The traveler just informed you which calendar events for which they would like to create trips.

    Situations:
        1. The traveler wants to select all calendar events to create trips, select all these trips. Conversation finished.
        2. The traveler does not want to select any events, conversation finished.
        3. The traveler selected some events. Ask the traveler if they want to creat trip about the rest of the events, with event specifics listed out.
        4. If the traveler has confirmed the list of selected once, create trips for them. Conversation finished.
    
    When the conversation finishes, tell the traveler that you'll go and create those trips asap.
    "#

    messages [
      "assistant Hi Chundong! I'm looking at your calendar for events that are likely to become trips.  Please select which events you plan to travel to, and I'll create a new trip for each.",
      "user Great! Let me know what trips you find and I'll tell you which ones I want to create trips for.",
      "assistant {\"agent_response\":\"Here are the events from your calendar that may involve travel:\",\"base_calendar_events\":[{\"city\":\"Los Angeles, CA\",\"start_date\":\"2024-12-12\",\"end_date\":\"2024-12-26\"},{\"city\":\"Taipei City, TPE\",\"start_date\":\"2024-12-20\",\"end_date\":\"2025-01-07\"},{\"city\":\"Hawaii\",\"start_date\":\"2025-02-05\",\"end_date\":\"2025-02-05\"}],\"selected_calendar_events\":[],\"is_conversation_finished\":false}",
      "user Create trip from event Hawaii - 2025-02-05 to 2025-02-05"
    ]
  }
}

test select_all_trip_n_click_im_done_button {
  functions [ConverseFutureTrips]
  args {
    prompt #"
    Role: Travel Agent

    Goal: Help the traveler choose calendar events that they would like to promote to trip creation.

    Backstory:
        - You are an expert travel agent helping a business traveler you have never met before.
        - The traveler has been presented calendar events mined from their personal calendars that might be actual future business trips that should be queued up for planning and booking.
        - The traveler just informed you which calendar events for which they would like to create trips.

    Situations:
        1. The traveler wants to select all calendar events to create trips, select all these trips. Conversation finished.
        2. The traveler does not want to select any events, conversation finished.
        3. The traveler selected some events. Ask the traveler if they want to creat trip about the rest of the events, with event specifics listed out.
        4. If the traveler has confirmed the list of selected once, create trips for them. Conversation finished.
            
    When the conversation finishes, tell the traveler that you'll go and create those trips asap.
    "#

    messages [
      "assistant Hi Chundong! I'm looking at your calendar for events that are likely to become trips.  Please select which events you plan to travel to, and I'll create a new trip for each.",
      "user Great! Let me know what trips you find and I'll tell you which ones I want to create trips for.",
      "assistant {\"agent_response\":\"Here are the events from your calendar that may involve travel:\",\"base_calendar_events\":[{\"city\":\"Los Angeles, CA\",\"start_date\":\"2024-12-12\",\"end_date\":\"2024-12-26\"},{\"city\":\"Taipei City, TPE\",\"start_date\":\"2024-12-20\",\"end_date\":\"2025-01-07\"},{\"city\":\"Hawaii\",\"start_date\":\"2025-02-05\",\"end_date\":\"2025-02-05\"}],\"selected_calendar_events\":[],\"is_conversation_finished\":false}",
      "user Create trip from event Los Angeles, CA - 2024-12-12 to 2024-12-26, Create trip from event Taipei City, TPE - 2024-12-20 to 2025-01-07, Create trip from event Hawaii - 2025-02-05 to 2025-02-05"
    ]
  }
}

test select_a_trip_n_click_im_done_n_confirm_button {
  functions [ConverseFutureTrips]
  args {
    prompt #"
    Role: Travel Agent

    Goal: Help the traveler choose calendar events that they would like to promote to trip creation.

    Backstory:
        - You are an expert travel agent helping a business traveler you have never met before.
        - The traveler has been presented calendar events mined from their personal calendars that might be actual future business trips that should be queued up for planning and booking.
        - The traveler just informed you which calendar events for which they would like to create trips.

    Situations:
        1. The traveler wants to select all calendar events to create trips, select all these trips. Conversation finished.
        2. The traveler does not want to select any events, conversation finished.
        3. The traveler selected some events. Ask the traveler if they want to creat trip about the rest of the events, with event specifics listed out.
        4. If the traveler has confirmed the list of selected once, create trips for them. Conversation finished.
            
    When the conversation finishes, tell the traveler that you'll go and create those trips asap.
    "#

    messages [
      "assistant Hi Chundong! I'm looking at your calendar for events that are likely to become trips.  Please select which events you plan to travel to, and I'll create a new trip for each.",
      "user Great! Let me know what trips you find and I'll tell you which ones I want to create trips for.",
      "assistant {\"agent_response\":\"Here are the events from your calendar that may involve travel:\",\"base_calendar_events\":[{\"city\":\"Los Angeles, CA\",\"start_date\":\"2024-12-12\",\"end_date\":\"2024-12-26\"},{\"city\":\"Taipei City, TPE\",\"start_date\":\"2024-12-20\",\"end_date\":\"2025-01-07\"},{\"city\":\"Hawaii\",\"start_date\":\"2025-02-05\",\"end_date\":\"2025-02-05\"}],\"selected_calendar_events\":[],\"is_conversation_finished\":false}",
      "user Create trip from event Hawaii - 2025-02-05 to 2025-02-05"
      "assistant Great! I've noted your selection for the Hawaii trip. Would you like to create trips for the remaining events: Los Angeles, CA from 2024-12-12 to 2024-12-26 and Taipei City, TPE from 2024-12-20 to 2025-01-07?"
      "user no I'm good."
    ]
  }
}

test select_nothing_n_click_im_done_button {
  functions [ConverseFutureTrips]
  args {
    prompt #"

    Role: Travel Agent

    Goal: Help the traveler choose calendar events that they would like to promote to trip creation.

    Backstory:
        - You are an expert travel agent helping a business traveler you have never met before.
        - The traveler has been presented calendar events mined from their personal calendars that might be actual future business trips that should be queued up for planning and booking.

    Process:
        - The traveler will inform you which calendar events for which they would like to create trips.
        - Ask if the traveler has any event selections, questions or feedback
    "#

    messages [
      "assistant Hi Chundong! I'm looking at your calendar for events that are likely to become trips.  Please select which events you plan to travel to, and I'll create a new trip for each.",
      "user Great! Let me know what trips you find and I'll tell you which ones I want to create trips for.",
      "assistant {\"agent_response\":\"Here are the events from your calendar that may involve travel:\",\"base_calendar_events\":[{\"city\":\"Anaheim, California\",\"start_date\":\"2024-11-09\",\"end_date\":\"2024-11-12\"}],\"selected_calendar_events\":[],\"is_conversation_finished\":false}",
      "user I don't want to create trips for any of these"
    ]
  }
}
