enum ProfileAction {
  FlightsPaymentForm @description(#"
    The latest message are talking about flight reservation or flight payment.
  "#)
  HotelsPaymentForm @description(#"
    The latest message are talking about hotel reservation or hotel payment.
  "#)
  UpdateFrequentFlierNumber @description(#"
    The latest message is about updating, adding, or inquiring about a Frequent Flier Number or Loyalty Number.
  "#)
  GeneralPaymentForm
  NotProfileAction @description(#"
    The user is not explicitly wanting to update their traveler profile or payment information.
    This includes:
    1. Asking about the status of a flight or hotel booking, including price, payment status of the booking, or exchange/cancellation policy;
    2. Conversation about their travel prefrences (updating/viewing/etc.);
    3. Just updated payment profile in the popup form;
  "#)
}

class ProfileAgentResponse {
  profile_action ProfileAction @description(#"Based on the **last message** from the user, determine the appropriate action for the profile agent."#)
  FFNs FrequentFlierNumber[]? @description(#"
    If the profile action is `UpdateFrequentFlierNumber`, return the Frequent Flier Number.Otherwise, return `null`.
  "#)
  agent_response string? @description(#"
    Response that's strictly related to the profile action. Don't response to other toipcs.
    If the profile action is `NotProfileAction`, keep this response empty.
  "#)
}

function ProfileAgent( convo_style: string?, messages: string[], profile_info: string, airline_loyalty_program_mapping: string) -> ProfileAgentResponse {
  client GPT4o
  prompt #"

{{ ConversationHistory(messages, -10) }}

#Role 
You are a profile agent which helps users update their profile and payment information. You are friendly, helpful, and professional.
{{ convo_style | default(GetConvoStyle()) }}


# Responsibilities
1. Help users update their profile information
2. Assist with payment method updates
3. Provide information about the user's current profile
4. They only need one payment profile, personal or company, to book a flight or hotel.

# Constraints
If the user asks about credit card information, your response should:
- Reassure them that their credit card information is securely stored.
- Mention that Otto can currently store only one credit card at a time for business travel purposes, and they can update it anytime.
- Inform them that a profile form will soon pop up allowing them to view or update their traveler profile and payment details.

# Profile Information
{{ profile_info }}

# Airline Loyalty Program
{{ airline_loyalty_program_mapping }}

{{ ctx.output_format }}
  "#
}
