test date_fixer_1 {
    functions [CorrectInvalidDate]
    args {
        input_date "2024-12-32"
    }
    @@assert( fix_bad_date, {{ this.updated_date == "2024-12-31" }} )
}

test date_fixer_2 {
    functions [CorrectInvalidDate]
    args {
        input_date "2025-02-29"
    }
    @@assert( fix_bad_date, {{ this.updated_date == "2025-02-28" }} )
}

test date_fixer_3 {
    functions [CorrectInvalidDate]
    args {
        input_date "2024-11-31"
    }
    @@assert( fix_bad_date, {{ this.updated_date == "2024-11-30" }} )
}

test date_fixer_4 {
    functions [CorrectInvalidDate]
    args {
        input_date "2025-14-03"
    }
    @@assert( fix_bad_date, {{ this.updated_date == "2025-03-14" }} )
}

test date_fixer_5 {
    functions [CorrectInvalidDate]
    args {
        input_date "2025-42-03"
        current_date "2025-02-14"
    }
    @@assert( fix_bad_date, {{ this.updated_date | regex_match("^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$")}} )
}

test date_fixer_6 {
    functions [CorrectInvalidDate]
    args {
        input_date "2025-42-03"
        current_date "2025-03-14"
    }
    @@assert( fix_bad_date, {{ this.updated_date | regex_match("^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$")}} )
}

test all_conversation_history {
    functions [ConversationHistoryWrapper]
    args {
        messages [
            "assistant How can I help you?",
            "user alpha is a hippo.",
            "assistant what else?",
            "user beta is a giraff.",
            "assistant what else?",
            "user gamma is a cat.",
            "assistant what else?",
            "user delta is a dog."
            "assistant what else?",
            "user epsilon is a bird."
            "assistant what else?",
            "user based on what I told you before, what is alpha and what is epislon?"
        ]
        start 0
    }
    @@assert(answer_correct, {{ this.answer | regex_match(".*hippo.*") }})
    @@assert(answer_correct, {{ this.answer | regex_match(".*bird.*") }})
}

test only_last_3_conversation_history {
    functions [ConversationHistoryWrapper]
    args {
        messages [
            "assistant How can I help you?",
            "user alpha is a hippo.",
            "assistant what else?",
            "user beta is a giraff.",
            "assistant what else?",
            "user gamma is a cat.",
            "assistant what else?",
            "user delta is a dog."
            "assistant what else?",
            "user epsilon is a bird."
            "assistant what else?",
            "user based on what I told you before, what is delta and what is epislon?"
        ]
        start "-3"
    }
    @@assert(answer_correct, {{ not this.answer | regex_match(".*dog.*") }})
    @@assert(answer_correct, {{ this.answer | regex_match(".*bird.*") }})
}

test only_last_2_conversation_history {
    functions [ConversationHistoryWrapper]
    args {
        messages [
            "assistant How can I help you?",
            "user alpha is a hippo.",
            "assistant what else?",
            "user beta is a giraff.",
            "assistant what else?",
            "user gamma is a cat.",
            "assistant what else?",
            "user delta is a dog."
            "assistant what else?",
            "user epsilon is a bird."
            "assistant what else?",
            "user based on what I told you before, what is delta and what is epislon?"
        ]
        start "-2"
    }
    @@assert(answer_correct, {{ not this.answer | regex_match(".*dog.*") }})
    @@assert(answer_correct, {{ not this.answer | regex_match(".*bird.*") }})
}