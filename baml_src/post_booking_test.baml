test test_converse_post_booking_light_flight_booked {
  functions [ConversePostBookingLight]
  args {
    flight_booked true
    hotel_booked false
    self_intro "Your name is <PERSON>, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    current_date "March 20th 2025"
  }
}

test test_converse_post_booking_light_hotel_booked {
  functions [ConversePostBookingLight]
  args {
    flight_booked false
    hotel_booked true
    self_intro "Your name is <PERSON>, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    current_date "March 20th 2025"
  }
}

test test_converse_post_booking_light_both_booked {
  functions [ConversePostBookingLight]
  args {
    flight_booked true
    hotel_booked true
    self_intro "Your name is <PERSON>, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel, including booking flights and hotels."
    current_date "March 20th 2025"
  }
}