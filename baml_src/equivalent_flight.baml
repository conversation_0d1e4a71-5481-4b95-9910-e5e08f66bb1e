class EquivalentFlightResponse {
    index_array int[] @description("the index_token of the equivalent flight in the candidate_flights array.")
    select_reason string? @description("the reason for selecting the equivalent flight, in short")
}

function GetSimilarFlight(input_flight: string, candidate_flights: string[]) -> EquivalentFlightResponse {
  client GPT41
  prompt #"""
    {{ _.role("system")}}
    Your job is to find the equivalent flights in the candidate_flights array that is most similar to the input flight: {{input_flight}}.
    the candidate_flights array contains the following flights:
    {{ candidate_flights }}

    The rule for determining the similarity is as follows:
    - Compare fare option name, cancellation policy, and excange policy of the input flight with each candidate flight.
    - Match as closely as possible. If the input flight lacks of any above criteria, no need to compare that criteria. 
    - If there is no candidate flight with a matching or highly similar fare option name, return null.


    note that if the cancellation policy is not given, it is usually assumed to be non-refundable.

    Output:
    {{ ctx.output_format }}
  """#
}

function FindSimilarFlightForExchange(
  current_date: string,
  self_intro: string?,
  convo_style: string?,
  input_flight: string, 
  candidate_flights: string, 
  additional_criteria: string?
  ) -> FlightSearchResponse {
  client GPT41
  prompt #"""
    {{ _.role("system")}}
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}    
    Your job is to find the equivalent flights in the candidate_flights array that are most similar to the input flight: {{input_flight}}.
    ----
    The candidate_flights array contains the following flights:
    {{ candidate_flights }}
    ----
    The additional criteria for the search are:
    {{ additional_criteria | default("") }}
    ----

    An equivalent flight is a flight that meets the following criteria:
    1. Prioritize non-stop flights (by checking number_of_stops column), only consider flights with stops if fewer than 6 non-stop options exist or user requests
    2. Same departure and arrival cities
    3. Same airline
    4. Same cabin class
    5. Arrival time within ±1 hour of the original outbound flight
    6. Departure time within ±1 hour of the original return flight
    7. Compare fare option name, cancellation policy, and exchange policy
    
    If there are user preferences in the additional criteria, conside them with higher priority than the similarity criteria.
    If there's no candidate flight that meets the criteria, please return flights which are partially similar to the input flight, and do not return empty flights. Just make sure to mention the differences in the output.

    Output:
    {{ ctx.output_format }}
  """#
}
