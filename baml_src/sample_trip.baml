class SampleTrip {
  title string @description(#"The title of the sample trip."#)
  description string @description(#"The description of the sample trip."#)
}

function GenerateSampleTrip(prompt: string) -> SampleTrip {
  client GPT4oMini_AzureFirst
  prompt #"
    {{ _.role('system') }}
    Goal: Generate a sample trip based on the prompt.

    Prompt:
    {{ prompt }}

    Rules for title:
      - The title should be a single word capture the destination of the trip.
      - If it's a flight with a specific airline, you can use the airport code of the destination together with the airline, for example, "JFK on Delta", "LAX on United", "SFO on Alaska".
      - If it's a hotel, you can use the city name for example, "Hotel in Chicago", "Hotel in London".
      - If it's not clear on flight or hotel, you can use the destination city name directly, for example, "Chicago", "London".

    Rules for description:
      - The description should be concise and only with a few words (less than 6 words) include timing or place to stay, for example, "Tue to Fri next week", "Next Monday", "Wed next month", "Staying near the Unilever HQ".
      - Return either for timing or place to stay, not both.

    {{ ctx.output_format }}
  "#
}