function SummarizeFlightInfo(flight_json: string) -> string {
  client AzureGPT4oMini
  prompt #"

    # Task
    Summarize the flight information:
     - Focus each leg in the flight legs and include the following deta:
       flight number, start airport, end airport, departure time, arrival time and leg status.
       Also include traveler name.
     

    ---
    flight information: 
    {{ flight_json }}
    ---

    # Output
    Please provide the output in user friendly format.
    {{ ctx.output_format }}
  "#
}
