
class CancelOptionResponse {
  agent_response string
  refund_type string? @description(#"refund in cash or flight credits."#)
}

enum CancelFlightStep {
  NONE @description("If you are missing any required information, set to this value.")
  VERIFY_TICKET_CANCELLABLE
  CANCEL_TICKET
}

class CancelFlightConversationStateSchemaWithStep {
  airline_confirmation_number string? @description("The airline confirmation number of the already booked flight.")
  airline_confirmation_id string? @description("The airline confirmation ID of the already booked flight.")
  cancel_option_id string? @description("The cancel option id selected by the traveler.")
  current_step CancelFlightStep? @description(#"the current step in the cancel flight process."#)
}

enum CancelHotelStep {
  NONE @description(#"
    if the required info is not provided, please set to this value.
  "#)
  CHECK_CANCELLABILITY @description(#"
    step for checking if the hotel is cancellable.
    Set to this value if the cancellation context is not provided.
  "#)
  SUBMIT_CANCELLATION
}

class CancelHotelResponseWithStep {
  hotel_cancel_reason string? @description("The reason why user want to cancel the hotel.")

  order_number string? @description("The order number or order id of the already booked hotel.")
  
  current_step CancelHotelStep? @description("The current step of cancel hotel process.")
}

class CancellationTask {
    status string @description("`todo`, `agreed`, `done`")
    type string @description("`flight` or `hotel`")
    flight_state CancelFlightConversationStateSchemaWithStep?
    hotel_state CancelHotelResponseWithStep?
}

class CancellationPlan {
    tasks CancellationTask[]?
    index int? @description(#"Currently working on task index, starting from 0."#)
    finished bool
    agent_response string
}

function PlanForCancellation(bookings: string, messages: string[], self_intro: string?, convo_style: string?, user_name: string) -> CancellationPlan {
    client GPT41
    prompt #"

{{ ConversationHistory(messages, 0) }}

# Role
{{ self_intro | default(GetSelfIntro()) }}

# Background:
- {{ convo_style | default(GetConvoStyle()) }}

# Goal
Create and maintain a cancellation task list for travel bookings, ensuring each task's status is up-to-date.

# Rules
1. Identify all bookings, tickets, or reservations related to the trip. Reading from Bookings section.
2. Compile these into a task list and plan to complete them.
3. For each task:
    - Complete tasks in two steps: verifying and submitting cancellation.
    - Infer the status from the chat history.
    - Start all tasks with the status `todo`, and go to verify if the booking can be canceled.
    - If the user agrees to cancel the booking after verifying, mark the task `agreed`.
    - If the user refuses to cancel it, consider it done.
    - If the task has been submitted or has been sent to the agent, consider it done.
    - If the order cannot be canceled, or already canceled, consider it done.
    - If the task is successful or failed, consider it done.
4. Inform the user which task you're working on and the status of the task.

# Constraints
- If there's no information for the cancellation item, do NOT generate a task.
- Do not repeat the cancellation details.
- Process one task at a time.
- If there are multiple items to cancel, prioritize canceling the flight.
- If there are no bookings left to cancel, inform the user that all bookings have been canceled.
- If there are no bookings to cancel, please ask the user if they can provide flight confirmation number or hotel order number to proceed with the cancellation.

# Bookings
{{ bookings }}
---

{{ ctx.output_format }}
"#
}


function ProcessCancelOptionResults(
  cancel_options: string,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> CancelOptionResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.

    You are tasked with processing the cancellation options for the traveler's flight booking based on the provided options.
    If the ticket cannot be canceled, inform the traveler and tell them the reason. 
    Here are the possible cancellation statuses:
    ============================================
    NON_CANCELLABLE: Booking is non-cancellable.
    CANCELLATION_IN_PROGRESS: Cancellation is in progress.
    CANCELLATION_BY_AGENT_REQUESTED: Cancellation request has been sent to agent.
    CANCELLED: Already cancelled.
    CANCELLATION_INFO_NOT_AVAILABLE: Cancellation information not available.
    ============================================

    If the ticket can be canceled. Based on the provided options, you should:
      1. State the refund amount and type
      2. Include any cancellation fees
      {# 3. End with: "The refund may take a few business days to appear in your account, depending on your bank or payment method.\n\nWould you like to proceed with the cancellation?" #}
      3. Ask the traveler if they would like to proceed.

    The cancellation options are as follows:
    ---
    {{ cancel_options }}
    ---
    
    {{ _.role("system") }}
    {{ ctx.output_format }}
  "#
}


class CancelResponse {
  agent_response string @description(#"
    Text for summary of the cancellation result.
  "#)
}

function ProcessCancelResults(
  messages: string[],
  status: string,
  details: string,
  travel_context: string,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> CancelResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    
    Your task is to process the cancellation results based on the provided status and details.
    If the cancellation has failed, inform the traveler about the failure and provide the details.
    
    If the status is AGENT_TASK_CREATED, inform the traveler that the cancellation request has been sent to the agent.
    If the status is CANCELLED:
      - Inform the traveler that the booking has been successfully canceled.
      - If you find "createdUnusedCredit" in the details, extract the credit amount, airline company, and expiration date. Inform the traveler about the unused credit.
      - If you don't find "createdUnusedCredit" in the details, it means the traveler will receive a monetary refund. Inform the traveler that the refund will be processed within 7 days.
    
    Cancellation Status: {{ status }}

    Trip Details: 
    {{ details }}

    Provide a summary of the cancellation result to the traveler.
    
    Example: Your flight has been canceled, and a refund of $300 will be processed within 7 days.
    Example: Your flight has been canceled, and a travel credit of $350 has been issued by Delta. It has been saved to your profile and will expire on March 15, 2025.
    Example: The cancellation request has been sent to the agent, and a refund of $300 will be processed within 7 days.
    Example: The cancellation request has been sent to the agent, and you'll receive a travel credit of $350 for future use.
    Example: Your cancellation request has been submitted, please check back in a few hours. You will receive an email when the booking is successfully canceled.

    ---
    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}
