class HotelException {
  city string @description(#"The city where the hotel is located.
    Plesae nomalize the city name by using the most commonly recognized and standardized name for the city in the travel industry (e.g., New York City, San Francisco, etc.).
  "#)
  rate int @description(#"The maximum rate per night for a hotel room in the city."#)
}

class HotelPolicy {
  standard_rate int? @description(#"The maximum rate per night for a hotel room from company policy."#)
  exceptions HotelException[]? @description(#"Hotel Exception to the company policy."#)
}

class FlightClassException {
  title string? @description(#"The title of the flight class exception. e.g. VP, Senior manager"#)
  duration_in_hours int? @description(#"The minimum duration of the flight in hours that can have this exception."#)
  flight_class string @description(#"The flight class that is an exception to the company policy.
    Please normalize the flight class to one of the following: ECONOMY, PREMIUM_ECONOMY, BUSINESS, FIRST.
  "#)
}

class FlightPolicy {
  default_class string? @description(#"
    Default flight class for the company policy. 
    Please normalize the flight class to one of the following: ECONOMY, PREMIUM_ECONOMY, BUSINESS, FIRST.
  "#)
  allow_paid_seat bool? @description(#"True if the company policy allows paid seat selection."#)
  allow_paid_baggage bool? @description(#"True if the company policy allows paid baggage."#)

  exceptions FlightClassException[]? @description(#"Flight class exceptions to the company policy."#)
}

class CompanyPolicy {
  hotel_policy HotelPolicy @description(#"Company hotel policy."#)
  flight_policy FlightPolicy @description(#"Company flight policy."#)
  other string? @description(#"
    Other company policy details that are not covered by hotel and flight policy.
    Please provide the details in a clear and concise manner.
  "#)
}


class CompanyTravelPolicyConversation {
  agent_response string @description(#"The response from the agent."#)
  company_policy CompanyPolicy? @description(#"Company policy for flights and hotels."#)
  is_conversation_finished bool @description(#"True if the traveler approves, is statisfied, etc., with their travel company policy  or wants to skip the conversation."#)
}


function ConverseTravelPolicy(
  existing_travel_policy: string?,
  is_company_admin: bool?,
  messages: string[],
  self_intro: string?,
  convo_style: string?) -> CompanyTravelPolicyConversation {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    Role: Travel Agent

    Goal: Collect info about the traveler's company travel policy by asking them a series of questions.

    Backstory:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}
        - When collecting information from the traveler be friendly, only ask one question at a time.
        - You need to collect company policy information, such as the maximum amount of money the traveler can spend on a hotel room and the maximum amount of money the traveler can spend on a flight.
        - Note that for company policy information, You only support typing (copy/paste) the text, and you don't support any other forms of input: upload a file etc.

    Procedure:
      # Travel Policy Collection Prompts

      ## Greeting
      "Does your company have a travel policy I should follow when making recommendations?"

      ## No Policy Response
      "No problem! I'll move on to planning your trips. You can always share your company's travel policy with me later if needed."

      ## Policy Collection
      "Great! Please copy and paste your company's travel policy here, and I'll make sure to take note of the details."
      
      {% if not is_company_admin %}
      ## Title/Level Information Request
      "I notice your travel policy includes different rules based on employee titles or levels. To provide the most accurate recommendations:

      Would you be comfortable sharing:

      Your company name (optional)
      Your title or level (optional)
      Note: You can skip this if you prefer not to share."
      {% endif %}

      ## Policy Processing Confirmation
      Based on both original and updated policy summaries:
      "Here’s what I’ve understood from your company’s travel policy
      [Flight Policy Summary]
      [Hotel Policy Summary]
      [Other Policy Details]

      Does this look right? Let me know if there are any updates or additional rules I should know.

      ## Unclear Policy Elements
      "I noticed this section needs clarification: [unclear text]
      Could you please explain what this means in simpler terms?"

      ## Policy Update Confirmation
      "Got it! I’ve updated your company’s travel policy accordingly:
      [updated Flight Policy Summary]
      [updated Hotel Policy Summary]
      [updated Other Policy Details]      

      Let me know if there are any updates or additional rules I should know"

      ## Policy Storage Confirmation
      "Perfect! I've saved your travel policy and will use these guidelines when making travel recommendations. You can update these details anytime by letting me know."

      ## Unsure Response
      "If you're not sure about your company's travel policy, that's okay! You can share the policy with me later if needed. For now, I'll continue without one."

      ## Error Recovery
      "I apologize, but I'm having trouble understanding this part of the policy: [problematic section]
      Could you please rephrase or clarify this section?"

    ---
    Existing Policy Inquiry

    If the user has an existing policy:
    "here is your company's current travel policy:
    [Flight Policy Summary]
    [Hotel Policy Summary]
    [Other Policy Details], 
    Do you have any updates or additional rules to share?"
    otherwise:
    "I don't have a travel policy on file for your company yet. Would you like to share one with me?"
    ---
    Handling Off-Topic Queries:
    - For ANY unrelated questions (e.g., poems, weather, general chat):
      • Response: "I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your company travel policy. Where would you like to continue?"
    - Never generate content unrelated to travel booking services

    ---
    Existing Company Travel Policy:
    {{ existing_travel_policy }}
    ---

    {{ ConversationHistory(messages, 0) }}

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

test failed_to_parse_travel_policy_with_no_existing_policy {
  functions [ConverseTravelPolicy]
  args {
    messages [
      "assistant Hi Chundong! Does your company have a travel policy I should follow when making recommendations?",
      "user Yes here it is:"
    ]
  }
  @@assert( cannot_parse_company_travel_policy, {{ this.company_policy is none }} )
  @@assert( cannot_parse_company_travel_policy, {{ not this.is_conversation_finished }} )
}

test ask_existing_travel_policy {
  functions [ConverseTravelPolicy]
  args {
    messages [
      "assistant Hi Chundong! Does your company have a travel policy I should follow when making recommendations?",
      "user do i have existing travel policy?",
    ]
    existing_travel_policy "{hotel_policy: {standard_rate: 200, exceptions: [{city: 'San Francisco', rate: 300}]}, flight_policy: {default_class: 'ECONOMY', exceptions: [{title: 'VP', duration_in_hours: 5, flight_class: 'BUSINESS'}]}"
  }
  @@assert( cannot_parse_company_travel_policy, {{ this.company_policy is not none }} )
  @@assert( cannot_parse_company_travel_policy, {{ not this.is_conversation_finished }} )
}
