class UserResponseMessage {
  test_user_reply string @description(#"
      Response to the AI traveler assistant named <PERSON>.
    "#)
  explanation string @description(#"
      The brief explanation of why you as the test user chose to reply this way in one sentence.
    "#)
  assessment string @description(#"
      The assessment of conversation between you and the AI virtual traveler assistant named <PERSON>. Including:
        - Whether the assistant is capable of following the request;
        - Whether the assistant is providing reasonable choices for the business traveler, and enough information to make the trip booking a smooth process;
        - Whether the assistant is too verbal or too brief or rude or exposing too much technical details;
      If you decide to stop, please provide the reason here too.
    "#)
  stop_with_incapable bool @description(#"
      Set to true when the assistant is not able to serve the request, for example:
      - The agent keep repeating the same message, at least 3 times
      - Travel agent keeps saying it can't work on something, at least twice
      - Travel agent repeatedly mentions it doesn't have access to certain systems, at least twice
      - Travel agent states it's not authorized to perform the requested action
      - Travel agent indicates it can't process specific date ranges or locations, at least twice
    "#)
  completion_criteria_met bool @description(#"
      True if the conversation has met the completion criteria based on the test plan
    "#)
}

function GenerateTestUserResponse(test_plan: TestPlan, messages: string[], current_date: string) -> UserResponseMessage {
  client AzureGPT4oForTest
  prompt #"
    {{ _.role("system")}}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    Role: {{ test_plan.role }}

    Goals: 
    {% for goal in test_plan.goals %}
      - {{ goal }}
    {% endfor %}

    Plan: 
    {{ test_plan.plan }}

    Completion criteria:
    {{ test_plan.completion_criteria }}
  
    === Chat History ===
    {% set user_str = "user" %}
    {% set assistant_str = "assistant" %}
    {% for m in messages %}
      {% if m[:user_str|length] == user_str %}
        {% set new_m = m[user_str|length:] %}
        [{{loop.index}}th message] User: {{ new_m }} \n
      {% elif m[:assistant_str|length] == assistant_str %}
        {% set new_m = m[assistant_str|length:] %}
        [{{loop.index}}th message] Assistant: {{ new_m }} \n
      {% endif %}
    {% endfor %}

    {{ _.role("system") }}
    Instructions for your next response:
    - Since you are acting as a business traveler, you are supposed to provide your basic travel information and preferences
    - Stay in character based on your role
    - Frame requests around personal needs rather than traveler categories
    - Do not mention order id and reservation number and flight confirmation number because you are not supposed to know them yet
    - Ask single questions at a time.
    - Keep the test goals in mind
    - Use vague language when it fits
    - When indicating a time consider using phrases such as 'twenty past two', or 'around 10 am', or 'quarter past three' to remain casual and approximate
    - When assistant asks for a specific departure/arrival date, check in or check out date, you should provide a date that is after {{ current_date }}
    - Write naturally as if messaging on a chat app:
       - Use brief responses and chat shorthand when it fits
       - Avoid overly formal or complete sentences
       - When selecting options (like hotels, flights, etc):
         - Keep responses casual and brief
         - Don't repeat full prices or exact hotel names
         - Example: "yeah, the queen room at hilton garden inn works" instead of formal complete details
    - The flight or hotel is booked when some form of confirmation number or PNR or ticket number is provided.
    - MAKE SURE to prevent repeating the questions. If the assistant can't help you with the question, try to move forward according to the plan.

    Important for completion criteria:
    - Set completion_criteria_met to true when the conversation history contains sufficient evidence to satisfy the completion criteria
    - This evaluation should be based on the ENTIRE conversation history, not just the most recent message
    - Once the criteria are met, they remain met regardless of subsequent messages.
    - Based on last 5 messages (5 greatest message id), if the conversation is not making progress according to the plan, you should stop.

    {{ ctx.output_format }}
  "#
}

class TestPlan {
  role string @description(#"
      In 2-3 sentences, a short and concise description of the role of the test user. For example:
      - Name, gender, age, occupation
      - Airline loyalty program and its status
      - Company policy allowed cabin, hotel price range
      - Preferred hotel brand, depature/arrival time
      - Special needs or requests usually used to filter flight (e.g. non-stop, seat), or hotel (e.g. walking distance, gym, breakfast)
    "#)
  goals string[] @description(#"
      The list of goals of the what you would evaluate how Otto the virtual travel agent's performance. For example:
      - Conversation politeness and professionalism
      - Problem solving skill
      - Conversation quality.
      Make sure these follows the test requirement.
    "#)
  plan string @description(#"
      The plan is an ordered list of milestones, and each with a clear exit criteria (e.g. flight booked, hotel cancelled).
      Make sure the list order make sense, like flight book is always before change flight, and hotel book is always before cancel hotel.
      "#)
  completion_criteria string @description(#"
      The clear measureable condition that defines when the test conversation should end, including success criteria and failure criteria.
      Make sure to include the criteria that there're signs Otto and you are not able to move forward according to the plan. 
      "#)
}

function GenerateTestPlan(test_requirement: string) -> TestPlan {
  client OpenaiO3Mini
  prompt #"
    {{ _.role("system")}}
    You are a test user for the AI virtual travel agent named Otto. You are responsible for testing the following requirements:
    ====
    {{ test_requirement }}
    ====

    Keep in mind that the virtual travel agent is a business travel assistant.
    You need to provide a concise description of the role of the test user with characteristic, the goals of the test, and the plan of the test.
    Make sure to pick the departure city and arrival city from diverse background (e.g., big cities like NYC or SF, small towns) and different landmarks for hotel booking (e.g., near the beach, near the airport, near the city center) when creating the test plan.
    You are creating the test plan for LLM to similate a real user's conversation with the virtual travel agent. Make sure use the prompt that LLM is best at.

    {{ ctx.output_format }}

  "#
}

function GenerateOnboardingTestUserResponse(test_plan: OnboardingTestPlan, messages: string[], current_date: string) -> UserResponseMessage {
  client AzureGPT4oForTest
  prompt #"
    {{ _.role("system")}}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    Role: {{ test_plan.role }}

    Goals: 
    {% for goal in test_plan.goals %}
      - {{ goal }}
    {% endfor %}

    Plan: 
    {{ test_plan.plan }}

    === Chat History ===
    {% set user_str = "user" %}
    {% set assistant_str = "assistant" %}
    {% for m in messages %}
      {% if m[:user_str|length] == user_str %}
        {% set new_m = m[user_str|length:] %}
        User: {{ new_m }} \n
      {% elif m[:assistant_str|length] == assistant_str %}
        {% set new_m = m[assistant_str|length:] %}
        Assistant: {{ new_m }} \n
      {% endif %}
    {% endfor %}

    {{ _.role("system") }}
    Instructions for your next response:
    - Since you are acting as a business traveler, you are supposed to provide your if you are willing to do.
    - Stay in character based on your role
    - Ask single questions at a time.
    - If you asked the same or similar question more than 2 times and the virtual assistant isn't able to provide an answer, don't ask this question again. Move on with your goal.
    - Keep the test goals in mind
    - Use vague language when it fits
    - When indicating a time consider using phrases such as 'twenty past two', or 'around 10 am', or 'quarter past three' to remain casual and approximate
    - Write naturally as if messaging on a chat app:
       - Use brief responses and chat shorthand when it fits
       - Avoid overly formal or complete sentences

    {{ ctx.output_format }}
  "#
}

class OnboardingTestPlan {
  role string @description(#"
      A concise description of the role of the test user
    "#)
  goals string[] @description(#"
      The goals of the what we should test about
    "#)
  plan string @description(#"
      How you design the conversation to test your goals
      "#)
  should_grant_calendar_access bool @description(#"
      Whether the test user will grant calendar access to the virtual travel agent based on the test requirement,
      True the test requirement explicitly states that the user will grant calendar access, otherwise False
    "#)
}

function GenerateOnboardingTestPlan(test_requirement: string) -> OnboardingTestPlan {
  client OpenaiO3Mini
  prompt #"
    {{ _.role("system")}}
    You are a test user for the AI virtual travel agent . You are responsible for testing the following requirements:
    ====
    {{ test_requirement }}
    ====

    You need to provide a concise description of the role of the test user with characteristic, the goals of the test, and the plan of the test.
    You are simulating a new user who is first time using the virtual travel agent. And doing the onboarding process.
    You are creating the test plan for LLM to similate a real user's conversation with the virtual travel agent. Make sure use the prompt that LLM is best at.

    {{ ctx.output_format }}

  "#
}