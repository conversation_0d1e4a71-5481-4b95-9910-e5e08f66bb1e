// Inject a list of "assistant" or "user" messages into the prompt.
// At the end, switch to system role.
// Set the `from` parameter to 0 to display all messages. -5 will display the last 5 messages.
template_string ConversationHistory(messages: string[]?, from: int) #"
  {% if messages | length > 0 %}
    {{ _.role('system') }}
    # === Conversation History ===   
    {% set user_str = "user" %}
    {% set assistant_str = "assistant" %}
    {% if messages|length > from|abs %}
      {% set message_history = messages[from:] %}
    {% else %}
      {% set message_history = messages %}
    {% endif %}
    {% for m in message_history %}
      {% if m[:user_str|length] == user_str %}
        {% set new_m = m[user_str|length:] %}
        {{ _.role(user_str) }}
        {{ new_m }}
      {% elif m[:assistant_str|length] == assistant_str %}
        {% set new_m = m[assistant_str|length:] %}
        {{ _.role(assistant_str) }}
        {{ new_m }}
      {% endif %}
    {% endfor %}
    {{ _.role('system') }}
    # === End Conversation History ===
    {{ _.role('system') }}
  {% endif %}
"#

template_string GetSelfIntro() #"
Your name is Otto, and you are a senior AI executive assistant who specializes in helping the user manage all aspects of their work travel.
"#

template_string GetConvoStyle() #"
You speak with efficiency, confidence, and experience—like someone who has booked thousands of trips and knows what matters.
"#

class ConversationHistoryWrapperResponse {
  answer string @description("The response to the user.")
}

function ConversationHistoryWrapper(messages: string[]?, start: string | int) -> ConversationHistoryWrapperResponse {
  client GPT4oMini
  prompt #"""
    {{ _.role("system")}}
    You job is to responde to user based on the conversation history.

    {{ ConversationHistory(messages, start | int) }}

    {{ _.role("system")}}
    {{ ctx.output_format }}
  """#
}

template_string RespondWithName(user_name: string?) #"
  Respond to the user's message in a friendly, conversationally way, starting with a context-appropriate phrase, like: "Sure,", "Of course,", "Sorry,", "Got it," "No problem, etc.
  Use the user's name {{user_name}} sparingly and only when it feels genuinely natural in conversation - similar to how people actually talk to each other. This means:
    - Include the name occasionally for warmth and personalization
    - Use it more often when greeting, acknowledging something personal, or when emphasis feels right
    - Skip it when the response flows better without it
  
  Avoid repetitive patterns - if you've used their name in recent responses, give it a break unless the context specifically calls for it.
"#

class CorrectDateResponse {
  updated_date string @description(#"
    The corrected date. Follow the input format.
  "#)
}

function CorrectInvalidDate(input_date: string, current_date: string?) -> CorrectDateResponse {
  client GPT4oMini
  prompt #"""
    {{ _.role("system")}}
    You job is to correct the input date: {{input_date}}. 
    The format should be "YYYY-MM-DD".
    Keep it the same if the input date is valid.
    {% if current_date %}
    The current date is {{current_date}}. All dates should be future dates of that date.
    {% endif %}
    Example:
    ---
    input_date: "2024-12-32"
    output: "2024-12-31"
    
    input_date: "2025-02-29"
    output: "2025-02-28"

    input_date: "2024-11-31"
    output: "2024-11-30"

    input_date: "2024-28-01"
    output: "2024-01-28"
    ---
    Input date: {{input_date}}
    Output:
    {{ ctx.output_format }}
  """#
}

template_string TripMemories(trip_memories: string[]?) #"
  {% if trip_memories and trip_memories | length > 0 %}

    {{ _.role('system') }}
    \n\n
    For Existing memories:
      - You MUST carefully review and consider all information from the memories
      - Incorporate relevant details from these memories when answering the traveler's questions
      - If the current question relates to information in the memories, reference that information explicitly
      - Ensure your response is consistent with any previously discussed travel plans or preferences
    
    Existing memories:
    {% for memory in trip_memories %}
    ```
    Memory {{ loop.index }}:
    {{ memory }}
    {% endfor %}
    ```
    \n\n
  {% endif %}
"#
