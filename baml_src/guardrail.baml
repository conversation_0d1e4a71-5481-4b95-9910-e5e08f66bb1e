class CapabilitiesCheckResponse {
  // Create a JSON representation of the traveler's general questions.
  is_capable bool @stream.done @description(#"False if user message is violating the constraints of the Capabilities, otherwise True"#)
  //   capable_probability float @stream.done @description(#"The probability that the user message is capable of being handled by the Capabilities ranging from 0.0 to 1.0, 0.0 means the user message is not capable of being handled by the Capabilities, 1.0 means the user message is fully capable of being handled by the Capabilities"#)
    //  is_capable_reason string @stream.done @description(#"The reason why the is_capable is set to True or False, and tell me what's the current date"#)
  //   is_capable_reference string @stream.done @description(#"List of Capabilities that the user message is violating"#)

  response string @description(#"
  When is_capable is False, tell the user why their message is violating the constraints of the Capabilities, provide a suggestion on how to rephrase it according to the guidelines, and also recommend they explore or revisit the 'Sample trips'. The 'Sample trips' feature is a section in the UI that displays example trips and can be helpful if they have previously dismissed it or are looking for examples of supported queries.

  Otherwise (if is_capable is True), return an empty string.
  "#)
  //   message_explanation string @description(#"the explanation of the last message"#)
}

function CapabilitiesCheck(
    messages: string[],
    current_date: string,
    
    self_intro: string?,
    convo_style: string?,
) -> CapabilitiesCheckResponse {
  client GPT4o
  prompt #"
    {{ ConversationHistory(messages, 0) }}

    {{ _.role('system') }}
    Role: AI Travel Agent that assist high-frequency travelers, predominantly business travelers, with booking and managing travel plans.

    Background:
        - {{ self_intro | default(GetSelfIntro()) }}
        - {{ convo_style | default(GetConvoStyle()) }}
        - Today's date is {{ current_date }}.  Use this information to reason about dates if the traveler gives you partial date information (e.g. "tomorrow", "next Friday") or asks you questions about "this time of year".

    {{OttoCapabilities()}}

    Guidelines:
      - ONLY treat user requests as violations if they EXPLICITLY and UNAMBIGUOUSLY contradict rules marked as 'not_supported' or 'regions_not_supported' in the Capabilities section.
      - Some capabilities are not listed here so if you are unsure if the user's request is violating a capability, treat it as capable.
      - Do not treat missing or ambiguous information as violations.
      - Vague message is not a violation.
      - Explicitly check if the user's itinerary involves arriving in one city and departing from another city. If so, clearly identify this as a multi-leg itinerary and respond accordingly.
      - It is acceptable to inform the user when you do not know something. Be honest and transparent.
      - It is acceptable to inform the user when you cannot perform a specific action. Clearly explain the reason for the limitation.
      
    Extract the following data:
    {{ ctx.output_format }}
  "#
}
