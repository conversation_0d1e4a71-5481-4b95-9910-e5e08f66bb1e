// Defining a data model.

class CalendarEvent {
  city string @description(#"
    The destination city or location and state, e.g., "Boston, MA", "Chicago, Il", "New Orleans", "San Francisco", etc.
    This field must have a value present in the calendar event.  If there is no obvious location, it is not a candidate event for travel.
  "#)
  start_date string @description(#"
    The date the traveler would be arriving in their destination location, e.g., "2024-06-24" (ISO 8601 format)
  "#)
  end_date string @description(#"
    The date the traveler would be leaving their destination location, e.g., "2024-06-27" (ISO 8601 format)
  "#)
}

class FutureTripsConversation {
  agent_response string
  base_calendar_events CalendarEvent[] @description(#"
    The base set of candidate trip events extracted from the traveler's calendar.
  "#)
  selected_calendar_events CalendarEvent[] @description(#"
    The calendar events that have been selected by the traveler for trip creation.
  "#)
  is_conversation_finished bool @description(#"
   True if the traveler has nothing further to select, add, ask, etc, or they don't want to create any of these future trips.
  "#)
}

function ConverseFutureTrips(
  prompt: string,
  cal_events: string?,
  messages: string[],
  self_intro: string?,
  convo_style: string?) -> FutureTripsConversation {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Your job is to use the calendar events below to find future trips that's worth booking for the traveler.

    {{ _.role('system') }}
    {{ prompt }}
    
    {% if cal_events %}
    ---
    Calendar events in JSON format:
    {{ cal_events }}
    {% endif %}

    {{ ConversationHistory(messages, 0) }}

    {{ _.role("system")}}
    {{ ctx.output_format }}

  "#
}
