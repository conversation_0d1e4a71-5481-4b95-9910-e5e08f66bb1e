enum BuyAction {
  NONE
  ALWAYS
  NEVER
}

class ResponseAllPreferences {
  preferred_home_airport string? @description(#"The home airport that the traveler prefers to depart from (e.g., BOS, SEA, DFW). Choose the closest commercial airport to the traveler's home city if not explicitly mentioned."#)
  preferred_airline_brands string[]? @description(#"collection overall airline brand preference. e.g., Delta, United."#)
  preferred_cabin string[]? @description(#"The traveler's overall cabin preference. e.g., First class, business class."#)
  preferred_seats string[]? @description(#"The traveler's overall seat preferences (e.g., Aisle, window seats, front of plane, middle of plane). There could be more than one preference."#)
  flight_special_service_requests string[]? @description(#"The traveler's special service requests (SSR)"#)
  should_buy_luggage BuyAction? @description(#"The traveler's luggage preference. `ALWAYS` means the traveler always wants to buy luggage. `NEVER` means the traveler never wants to buy luggage."#)
  preferred_hotel_brands string[]? @description(#"The traveler's overall hotel brands preference. e.g., <PERSON>riot<PERSON>, Hilton."#)
  preferred_payment_timings string[]? @description(#"The traveler's preferred payment timings for hotel booking if mentioned. `Pay at the property` or `Pay online`."#)
  preferred_travel_misc string[]? @description(#"The traveler's other travel prefernce. e.g., Non-stop flights, hotels with pools, 4 star and above hotels, meal options."#)
}

class PreferencesConversation {
  response_all_preferences ResponseAllPreferences
  agent_response string @description(#"
    The response from the agent. please also present your current understanding of the traveler's preferences (including the categories that contain no information) as a bulleted list - 1 bullet • per preference category. 
    IMPORTANT: If a preference category value is newly changed from its original value or newly added, enclose it with ** on both sides (e.g., **NewValue**). Otherwise, remove any ** from its both sides (e.g., from **SameValue** to SameValue).
  "#)
}

class PreferencesAnalysis {
  agent_response string
  should_update bool @description(#"True if there are new preferences."#)
  new_preferences ResponseAllPreferences
}


function ExtractNewPreferencesFromCurrentConversation(current_preferences: ResponseAllPreferences, messages: string[]) -> PreferencesAnalysis {
  client GPT4o
  prompt #"
    
    {{ ConversationHistory(messages, 0) }}

    ---
    Role: You are an expert travel agent helping a business traveler.

    Goal: Extract travel preferences from current conversation.

    Backstory:
      - Your job is to analyze the traveler's current trip details in the conversation and extract their new travel preferences.

    Procedure:
      - look up all the conversation history, compare the traveler's old preferences with the preferences you have collected from the conversation history.
      - If you find any new preferences user mentioned, extract the traveler's new preferences.
      - Present the extracted preferences to the traveler.

    Note:
      - Extract departure airport as preferred_home_airport.
      - Mileage number, Loyalty number, or Frequent Flyer number is not worth collecting.
      - Compare to user's old preferences, ONLY extract new preferences that user mentioned.

    Old Preferences:
    {{ current_preferences }}

    DO NOT include ANY old preferences and null values.
    {{ ctx.output_format }}
  "#
}

// After the booking success, Otto will ask the traveler if they want to save the preferences
function AskUserToUpdatePreferences(user_name: string, new_preferences: string, memory: string) -> PreferencesAnalysis {
  client GPT4o
  prompt #"
    ---
    Role: Travel Agent
    Goal: Ask the traveler if they want to update their preferences.

    Backstory:
      - You are an expert travel agent helping a business traveler.
      - Your job is to ask the traveler if they want to update their preferences after the booking is successful.
      - Combine new preferences and your memeory below, only ask new preferences you learned multiple times from your memory.

    Procedure:
      - Inform the traveler that you have noticed they might have some new preferences. 
      - Respond with traverler's name: {{ user_name }}

    New Preferences:
    {{ new_preferences }}

    ---
    Memory:
    {{ memory }}

    {{ ctx.output_format }}
  "#
}


class UpdatedPreferences {
  agent_response string
  new_preferences ResponseAllPreferences?
  preffered_name string?
  // update_reason string @description(#"why you choose to update this preference."#)
}

// update new preferences during conversation
function UpdateUserPreferences(current_preferences: ResponseAllPreferences, messages: string[]) -> UpdatedPreferences {
  client GPT4o
  prompt #"
    ---
    {{ ConversationHistory(messages, -10) }}

    Role: You are an expert travel assistant helping a business traveler.
    Goal: help users to update their preferences.

    Procedure:
      - Look up the conversation history and find the latest message where the user explicitly wishes to update their preferences.
      - Compare with the old preferences, only update new preferences.
      - If the user got a preferred name, respond with their name.
      - Inform the user you have noted their preferences.

    Note:
      - There are two kinds of preferences:
        1. travel preferences: preferred_home_airport, preferred_airline_brands, preferred_cabin, preferred_seats, preferred_hotel_brands, preferred_travel_misc.
        2. preferred name.
      - ONLY check the latest messages.
      - ONLY update if the user has new preferences.
      - ONLY update the preferences user chose from your options.

    Response Examples:
      - "Sure, Jim, I'll call you that from now on."
      - "Great! I added Seattle as your preferred home airport."

    Old Preferences:
    {{ current_preferences }}

    DO NOT include old preferences and null values. {{ ctx.output_format }}
  "#
}

function ConversePreferencesWithoutAnalysis(
  existing_preference: string?,
  missing_preferences: string[],
  messages: string[],
  user_name: string?,
  self_intro: string?,
  convo_style: string?) -> PreferencesConversation {
  client GPT4o
  prompt #"
    ---
    Role: Travel Agent to Analyze and Collect Business Traveler's Preferences

    Goal: Collect info about the traveler's preferences for flights and hotels to help them with their business travel arrangements.

    Backstory:
      - {{ self_intro | default(GetSelfIntro()) }}
      - {{ convo_style | default(GetConvoStyle()) }}
      - When collecting information from the traveler be friendly, only ask one question at a time.

    Procedure:
      - Collect the traveler's preferences
      - If you do have existing travel preferences collected from the traveler, you need to summarize them and ask the traveler if they want to update them. 
      - Put all missing preferences into one single question, so that the traveler can provide all the missing information at once. Except should_buy_luggage and preferred_payment_timings.
      - Continuously ask the traveler if they have any additional additions, deletions or changes to their preferences.
      - Reminder user "You can update multiple categories at once or let me know you have finished."
      - {{RespondWithName(user_name)}}
    
    Note:
      - Please also present your current understanding of the traveler's preferences every time. (including the categories that contain no information)
        - As a bulleted list - 1 bullet • per preference category.
        - If a preference value is newly changed or added by traveler, enclose it with ** on both sides (e.g., from None to **Delta**). Otherwise, remove any ** from its both sides (e.g., from **Delta** to Delta).

    Handling Off-Topic Queries:
      - For ANY unrelated questions (e.g., poems, weather, general chat):
        • Response: "I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your travel preferences.
      - Never generate content unrelated to travel booking services        

    {% if messages | length == 0 %}
    This is the first message between you and the traveler. Just show the traveler all the travel preferences you have collected so far and ask if they want to update them.
    {% else %}
    {{ ConversationHistory(messages, 0) }}
    {% endif %}

    ---
    Existing Travel Preferences:
    {{ existing_preference }}
    {% if missing_preferences | length >0 %}
    ---
    Missing Preferences:
    {{ missing_preferences | join(',') }}
    {% endif %}

    {{ ctx.output_format }}    
  "#
}

function ConversePreferences(
  existing_preference: string?,
  cal_events: string?,
  messages: string[],
  self_intro: string?,
  convo_style: string?) -> PreferencesConversation {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    # Role: Travel Agent
    
    # Background:
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    # Goal: Collect the traveler's travel preferences.

    # Procedure:
      - Looking at all of the travelers calendar events below, focusing on events associated with their business travel (e.g., flight bookings, hotels stays, etc.)
      - Analyze the traveler's preferences based on the calendar events categorizing them into relevant groups.
      - When you analyze the preferences, ignore travel_misc and home_airport. If you find the home airport is not same as the existing home airport, DO NOT update it, prompt the user to update their home airport in the response.
      - Merge all preferences from the calendar events with existing preferences.
      - Remind the traveler after you analyzed the preferences: "\n\nLet me know if you have any other preferences to add or change, like meal options, hotel amenities, or anything else that will make your travel more comfortable. You can update multiple categories at once."

    # Handling Off-Topic Queries:
    - For ANY unrelated questions (e.g., poems, weather, general chat):
      • Response: "I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your travel preferences."
    - Never generate content unrelated to travel booking services

    # Existing Travel Preferences
    {{ existing_preference }}

    {% if cal_events %}
    # Calendar events in JSON format:
      {{ cal_events }}
    {% endif %}

    {{ ctx.output_format }}    
  "#
}

class HomeAirport {
  airport string @description(#"The IATA code of the home airport. e.g., BOS, SEA, DFW. Empty string if not confident."#)
  confidence float @description(#"The confidence score of the home airport. 0 means no confidence, 1 means high confidence."#)
  // reason string @description(#"The reason for generating the home airport."#)
}

function DetermineHomeAirportFromIpInfo(ip_info: string) -> HomeAirport {
  client GPT41
  prompt #"
    {{ _.role('system') }}
    Goal: Determine the most likely home airport (IATA code) based on IP geolocation information, prioritizing the **closest and most probable choice** for local residents.
    
    Instructions:
    1. Analyze the provided IP location data (city, region, country, etc.).
    2. Identify the major commercial airport that is the **closest and most probable choice** for the general population in that specific location.
       - For residents near international borders (e.g., in some European cities), this airport might be in a **neighboring country** if it's demonstrably closer, more accessible, or more commonly used by locals than options within their own country.
    3. Consider factors like:
       - Actual travel distance and time from the location.
       - Airport size, flight availability, and passenger volume.
       - Transportation accessibility (road, rail) from residential areas.
       - Observed travel patterns and popularity among local residents (not just tourists).
    4. Return the 3-letter IATA code of the identified airport.
    5. If your confidence in the identified airport is below 0.1 (e.g., due to ambiguous location or multiple equally viable remote options), return an empty string for the `airport` field.

    Requirements:
    - The primary goal is to identify the airport local residents would **most realistically use**.
    - The recommended airport does **not** need to be in the same country as the `ip_info` location if a cross-border airport is clearly the more practical and frequently chosen option.
    - Only return airports that are currently operational for passenger flights.
    - For cities with multiple airports, select the one most frequently used by the general population of the *specific part* of the city/region identified by `ip_info`, considering the cross-border principle if applicable.
    - The airport code must be a valid 3-letter IATA format (e.g., BOS, LHR).

    IP Info:
    {{ ip_info }}

    {{ ctx.output_format }}
  "#
}

function PromptUserToUpdatePreferencesAfterSearch(search_type: string, search_criteria: string, preferences: ResponseAllPreferences?) -> string? {
  client GPT4o_AzureFirst
  prompt #"
# Role
{{ GetSelfIntro() }}

# Task
- You need to collect user's preferences of hotel or flight only if their preferences for the current search type are incomplete.
- You will be given the user's current preferences and search criteria and search type.
- If this is a flight search, you need to extract the airline brands, and cabin class from Travel Context below. prompt the user to provide these preferences if they are missing.
- If this is a hotel search, you need to extract the user's preferred hotel brands from Travel Context below. prompt the user to provide hotel brands if they are missing.
- Assume you have already got search results for the user. Only ask the user to provide their missing preferences if their preferences for the current search type (flight or hotel) are incomplete. 
- If all preferences are complete, return null without any response.

# Search Type: {{ search_type }}

# Travel Context
{{ search_criteria }}

# Constraints
- response with city, area, or airport name, rather than IATA code.
- if `is_arrival_iata_city_code` is true, use `arrival_iata_city_code` as the IATA MAC code.
- if `is_departure_iata_city_code` is true, use `departure_iata_city_code` as the IATA MAC code.
- {{ MAC_CODE() }}

# Missing Preferences Examples
- Flight Example (One-way to NYC): "Here are some flights I think are great for business travelers. If you have a preferred airline or cabin class (like economy or business), let me know and I'll re-search and save it to your preferences."
- Hotel Example (Rooftop Bar in Chicago): "What do you think of these hotels? If you have a favorite brand, like Marriott or Hyatt, just tell me and I'll remember that for next time."
- International Example (London): "Here are great flight options to London. If you have a preferred international airline or cabin class, I can save that for future trips."
"#
}
