test company_policy_check_test {
  functions [DetermineFlightPolicyCompliance]
  args {
    flight_options [
        {
            fare_option_name "Main Cabin"
            cabin "Main Cabin"
            airline_code "DL"
            departure_time "2025-05-19 08:00"
            arrival_time "2025-05-19 17:04"
            flight_number "506"
        }
        {
            fare_option_name "Main Cabin"
            cabin "Main Cabin"
            airline_code "DL"
            departure_time "2025-05-19 08:30"
            arrival_time "2025-05-19 17:20"
            flight_number "338"
        }
        {
            fare_option_name "Main Cabin"
            cabin "Main Cabin"
            airline_code "DL"
            departure_time "2025-05-19 09:50"
            arrival_time "2025-05-19 21:29"
            flight_number "2324"
        }
        {
            fare_option_name "Main Cabin"
            cabin "Main Cabin"
            airline_code "DL"
            departure_time "2025-05-19 09:50"
            arrival_time "2025-05-19 23:16"
            flight_number "2324"
        }
        {
            fare_option_name "Main Cabin"
            cabin "Main Cabin"
            airline_code "DL"
            departure_time "2025-05-19 11:40"
            arrival_time "2025-05-19 23:16"
            flight_number "1706"
        }
        {
            fare_option_name "Delta Comfort+"
            cabin "Delta Comfort Plus"
            airline_code "DL"
            departure_time "2025-05-19 19:35"
            arrival_time "2025-05-20 05:55"
            flight_number "1239"
        }

    ]
    
    company_policy #"
        {"default_class":"ECONOMY","exceptions":[{"duration_in_hours":3,"flight_class":"PREMIUM_ECONOMY"}]}
    "#
  }
}