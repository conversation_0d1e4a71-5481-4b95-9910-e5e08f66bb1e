class BaggageIncludedNotification {
  agent_response string @description("Response to the traveler about free checked baggage")
}

class BaggageUpsellOffer {
  agent_response string @description("Response to the traveler about paid baggage options")
}

function GenerateBaggageUpsellOffer(baggage_options: string, current_date: string, self_intro: string, convo_style: string) -> BaggageUpsellOffer {
  client GPT4o
  prompt #"
    {{ _.role("system")}}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    {{ self_intro }}
    - {{ convo_style }}

    You are a travel assistant helping a user with their flight booking. The user has selected a flight that does not include free checked baggage, but paid baggage options are available.

    Write a brief, friendly message informing the user about the available baggage options and their prices. Present this as an optional upsell opportunity, not as a requirement.

    Keep your response concise and conversational. Don't use bullet points or numbered lists unless absolutely necessary.

    Baggage options: {{ baggage_options }}

    If the baggage options are empty or not available, inform the user that traveler him/herself need to check with the airline for baggage options.

    ----
    Example response:
    Example 1: This fare doesn't include free checked bags. Since you always want to check bag, Would you like to prepay for a checked bag for $35?
    Example 2: We don't have baggage options information for this flight. You can check with the airline for baggage options. Sorry for the inconvenience.
    ----

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}
