test FrontOfHouseSupervisorCreateNewTripDestinationChange {
  functions [FrontOfHouseSupervisorDoConverse]
  args {
    messages [
      "user I want to plan a trip to New York from Seattle for next month",
      "assistant I'd be happy to help you plan your trip to New York! Let me search for flights from Seattle to New York for next month.",
      "user Actually, I want to go to Los Angeles instead, departing from Boston"
    ]
    current_date "2024-11-13"
    travel_context "{\"flight_search_core_criteria\": {\"departure_airport_code\": \"SEA\", \"arrival_airport_code\": \"JFK\", \"outbound_date\": \"2024-12-15\"}}"
    previous_destination "JFK"
    previous_trip_dates "2024-12-15-2024-12-20"
    has_concluded_trip false
  }
}

test FrontOfHouseSupervisorCreateNewTripTimeChange {
  functions [FrontOfHouseSupervisorDoConverse]
  args {
    messages [
      "user I want to plan a trip to Paris for December",
      "assistant Great! I'll help you plan your trip to Paris in December.",
      "user Actually, I want to go to Paris but in March next year"
    ]
    current_date "2024-11-13"
    travel_context "{\"flight_search_core_criteria\": {\"departure_airport_code\": \"SEA\", \"arrival_airport_code\": \"CDG\", \"outbound_date\": \"2024-12-15\"}}"
    previous_destination "CDG"
    previous_trip_dates "2024-12-15-2024-12-20"
    has_concluded_trip false
  }
}

test FrontOfHouseSupervisorCreateNewTripConcludedTrip {
  functions [FrontOfHouseSupervisorDoConverse]
  args {
    messages [
      "user I want to book a new trip to Miami",
      "assistant I'd be happy to help you plan a new trip to Miami!"
    ]
    current_date "2024-11-13"
    travel_context "{\"flight_search_core_criteria\": {\"departure_airport_code\": \"SEA\", \"arrival_airport_code\": \"LAX\", \"outbound_date\": \"2024-11-20\"}}"
    previous_destination "LAX"
    previous_trip_dates "2024-11-20-2024-11-25"
    has_concluded_trip true
  }
}

test FrontOfHouseSupervisorRoundTripNotCreateNewTrip {
  functions [FrontOfHouseSupervisorDoConverse]
  args {
    messages [
      "user I want to book my return flight from Los Angeles to Seattle",
      "assistant I'll help you book your return flight from Los Angeles back to Seattle."
    ]
    current_date "2024-11-13"
    travel_context "{\"flight_search_core_criteria\": {\"departure_airport_code\": \"SEA\", \"arrival_airport_code\": \"LAX\", \"outbound_date\": \"2024-11-20\"}}"
    previous_destination "LAX"
    previous_trip_dates "2024-11-20-2024-11-25"
    has_concluded_trip false
  }
}
