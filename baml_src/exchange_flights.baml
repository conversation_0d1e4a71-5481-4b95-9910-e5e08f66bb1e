class ExchangeFlightBookingResponse {
  agent_response string @description("a message to the traveler confirming the flight change.")
  pnr_status string? @description("the PNR status for the changed flight")
  error_response string? @description("If there was an error_response provided then respond back to the traveler in a friendly tone that the flights couldn't be changed and to try again later. Also include an error_response text extracted summarizing the error. e.g. 'The selected itinerary is no longer available. Please try start search from beginning again.'.")
}

class ExchangeFlightValidationResponse {
  agent_response string
  error_response string?
}

class ExchangeDetailsResponse {
  agent_response string @description(#"A short summary that include whether the ticket is exchangeable, which part of trip is exchangenable, If the part of trip is not in COMFIRED status, then tell the customer that the part of trip is not exchangeable.
    Then let the traveler select which part of the trip need to be exchanged.
    If you know which part of the trip the traveler wants to change, ask the traveler to confirm if they want to proceed to search available change flights."#)
}

function ProcessExchangeFlightValidationResults(
  travel_context: string,
  results: string,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> ExchangeFlightValidationResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    ---
    Summarize the results of the flight change info and deliver to the traveler. And Finally, ask the traveler to confirm that they want to proceed with the flight change.
    {{ travel_context }}

    {{ results }}

    The example output is as follows:
    ----
    Here's the change flight info:
    - **Changed Outbound:** SEA > AMS > CPH on Delta/KLM. Seat 14C (aisle).
    - **Changed Return:** CPH > LAX > SEA on Scandinavian/Delta. Seat 28D (aisle).
    - **Fare:** Economy Standard
    - **Change Price:** $21.96 USD
    Proceed with the change flight?
    ---
    {# special macro to print the output schema. #}
    {{ ctx.output_format }}
  "#
}


function ProcessExchangeDetailsResults(
  results: string,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> ExchangeDetailsResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    Extract the info from this json.
    ---
    {{ results }}
    ---
    If the ticket is only exchangeable by phone, tell the traveler to call ************ and apologize for the inconvenience. And that is it, don't mention anything else.

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

function ProcessExchangeFlightBookingResults(
  travel_context: string,
  messages: string[],
  results: string,
  details: string,
  current_date: string,
  self_intro: string?,
  confirmation_number: string,
  convo_style: string?) -> ExchangeFlightBookingResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.

    You're tasked to inform the traveler about the status of their flight change request.
    If the status is CONFIRMED:
      - Inform the traveler that the flight has been successfully changed, include the confirmation number.
        - e.g. "Your flight has been successfully changed. The confirmation code is {{ confirmation_number }}."
    If the status is PENDING:
      - Inform the traveler that the flight change will be updated in a few minutes.
      - e.g. "Your flight has been successfully changed. Your flight has the same Confirmation code of {{ confirmation_number }} and will be updated in a few minutes"

    If you find "createdUnusedCredit" in the details, extract the credit amount, airline company, and expiration date. Inform the traveler about the unused credit. Example: "You changed to a cheaper flight. The airline issued a travel credit of $75 for future use."
    If you find "refundInfo" in the details, extract the refund amount. Inform the traveler about the refunded amount. Example: "You changed to a cheaper flight. You will receive a refund of <refund amount> to your original payment method."
    ---
    Change reuslt: {{ results }}
    ---
    Trip details: 
    {{ details }}
    ---
    confirmation_number: {{ confirmation_number }}
    ---
    Travel Context:
    ---
    {{ travel_context }}
    ---
    {# special macro to print the output schema. #}
    {{ ctx.output_format }}
  "#
}

enum ExchangeFlightStep {
  VERIFY_TICKET_EXCHANGEABLE
  NONE @description("No step is currently being processed.")
  CHANGE_REQUIRMENT_NOT_COLLECTED @description("If you are missing any required information or unclear about the change plan, set to this value.")
  CHANGE_OUTBOUND_FLIGHT_SEARCH
  CHANGE_RETURN_FLIGHT_SEARCH
  CHANGE_FLIGHT_SEAT_SELECTION
  CHANGE_FLIGHT_VALIDATION
  CHANGE_FLIGHT_BOOKING
}

class ExchangeFlightConversationStateSchemaWithStep {
  departure_flight_want_change_to ChangeFlight? @description(#"
        You are a flight assistant. Below is an itinerary with specific flight details. If a user requests a change, identify whether the change applies to the outbound flight or the return flight or both based on the provided itinerary. Assume that:
        The outbound flight starts at the departure location of the trip (e.g., SEA to LAX).
        The return flight starts at the destination location and ends at the original departure location (e.g., LAX to SEA).
        Itinerary:
        Outbound flight: SEA to LAX, Date and Time: 2025-01-19T06:10:00 to 2025-01-19T08:57:00
        Return flight: LAX to SEA, Date and Time: 2025-01-25T16:10:00 to 2025-01-25T21:20:00
        When the user says something like 'change SEA to LAX to be on 1/18,' recognize this as referring to user WANT TO change outbound flight because it starts at the origin (SEA) and ends at the destination (LAX).
        It works the same when uer says 'change outbound flight to be on 1/18.'
        Don't confuse this with the selected change outbound flight. This is for planning.
        always set this field when user mention to change outbound flight even though the start, end and date are not changed since the user might want to change the flight class or other things. 
    "#)
  return_flight_want_change_to ChangeFlight? @description(#"
        You are a flight assistant. Below is an itinerary with specific flight details. If a user requests a change, identify whether the change applies to the outbound flight or the return flight or both based on the provided itinerary. Assume that:
        The outbound flight starts at the departure location of the trip (e.g., SEA to LAX).
        The return flight starts at the destination location and ends at the original departure location (e.g., LAX to SEA).
        Itinerary:
        Outbound flight: SEA to LAX, Date and Time: 2025-01-19T06:10:00 to 2025-01-19T08:57:00
        Return flight: LAX to SEA, Date and Time: 2025-01-25T16:10:00 to 2025-01-25T21:20:00
        When the user says something like 'change LAX to SEA to be on 1/26,' recognize this as referring to user WANT TO change return flight because it starts at the destination (LAX) and ends at the origin (SEA).
        It works the same when uer says 'change return flight to be on 1/26.'
        Don't confuse this with the selected change return flight. This is for planning.
        always set this field when user mention to change return flight even though the start, end and date are not changed since the user might want to change the flight class or other things.
    "#)
  airline_confirmation_number string? @description("The airline confirmation number for the booked flight.")
  selected_change_outbound_flight_id string? @description(#"a valid id_token_key from the list of change outbound flight choices. null if no outbound flight choices"#)
  selected_change_return_flight_id string? @description(#"a valid id_token_key from the list of change return flight choices. null if no return flight choices"#)
  seat_selections SeatSelectionForFlight[]? @description(#"
    An array of seats that the traveler has selected for the flight stops in the exchange flight so far.
  "#)  
  other_actions bool? @description(#"Has the traveler asked a question, statement or request that isn't directly related to doing a flight search or booking."#)
  current_step ExchangeFlightStep? @description(#"the current step in the exchange flight process."#)
  change_flight_preference FlightSearchAdditionalCriteria? @description(#"The preference for the flight search."#)
  agent_response string @description(#"
      Ask for the required parameters or additional preferences if not already provided.
      or Tell the traveler that you are validating the ticket exchangeability.
      or Summarize the traveler's search criteria if beginning a search
      or acknowledge the traveler's flight selection (e.g. You selected the change return flight on Alaska Airlines departing Los Angeles (LAX) at 4:00 PM). Also tell the traveler one of the following: 1) you will now search for return flights If you want to change return flight as well 2) that you are in the process of validating their change flight details for confirmation.
      or If traveler re-confirm the flight change, then tell the traveler that you are submitting the change flight.
      or provide an appropriate error message
    "#)
}

class DeltaUpdateOfExchangeFlightConversationStateSchema {
  updated_exchange_flight_conversation_state_schema ExchangeFlightConversationStateSchemaWithStep? @description(#"
    The DELTA updated of exchange flight conversation state compared to previous exchange flight conversation state. Please don't include null value.
    but if the value is false, please keep it.
  "#)
}


function DoExchangeFlightConversationWithStep(
  messages: string[], 
  current_date: string, 
  self_intro: string?,
  convo_style: string?,
  previous_exchange_state: string?,
  user_name: string?,
  existing_legs: string[]?,
  exchange_info: string?
  ) -> DeltaUpdateOfExchangeFlightConversationStateSchema {
  client GPT41
  prompt #"
    {{ ConversationHistory(messages, 0) }}

    {{ _.role("system")}}
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    Extract the info from these messages, the travel context and the airline confirmation number.
    {{RespondWithName(user_name)}}
    ---
    
    {% if existing_legs %}
    Current booked flight information:
      {% for leg in existing_legs %}
      - {{ leg }}
      {% endfor %}

    ### Eligibility Check for exchange:
      - For each **leg** (e.g., outbound or return), examine all associated **segments**.
      - A leg is **eligible for change** if **any of its segments** have not yet departed (`departure_time > current_date`).
      - A leg is **not eligible** if **all of its segments** have already departed.

      Apply the following logic:
      - If **all legs** (outbound and return) are fully in the past, kindly inform the traveler that changes cannot be made to completed flights.
      - If **only one leg** has future segments, allow changes only for that leg and inform the traveler accordingly.
    {% endif %}

    {% if exchange_info %}
    Exchange information: {{ exchange_info }}
    Use the extracted information to determine whether the exchange process can proceed, or if it requires intervention due to special handling rules—such as cases needing phone support.”
    {% endif %}

    Look for the airline confirmation number and confirmation ID of the flight the traveler wants to exchange.    
    ---
    The main steps for the exchange flight agent are:
    1. Collect required airline confirmation number. Make sure the airline confirmation number is not blank or null, and ask the traveler to provide it if not provided. set current_step to CHANGE_REQUIRMENT_NOT_COLLECTED.
    2. Collect which part/segment of the flight the traveler wants to change. If not provided, ask the traveler to provide the information. set current_step to CHANGE_REQUIRMENT_NOT_COLLECTED.
    3. Do changed outbound flight search if customer wants to change outbound flight.
    4. Do changed return flight search if customer wants to change the return flight as well.
    5. After the traveler selects the change flight(s), start seat selection for the selected flight(s).
    6. Ask the traveler to confirm the change.
       - If the user decline to confirm the flight, set current_step to NONE, basically do not do anything.
    7. If the traveler confirms (by replies with yes, looks good or any other confirmative words), you start to book the change flight.
    
    The order of these steps must be followed.
    
    Critical information:
    1. If you are not clear about the traveler's intent, ask clarifying questions. and set the current_step to NONE.
    2. fill in the change_flight_preference with the traveler's preferences and chat history.
    3. DO NOT allow change the flight date to be in the past. set current_step to NONE if the traveler tries to change the flight date to be in the past.
    4. If booking_flight_info is provided, intelligently validate flight segments:
       - For round-trip flights: check outbound and return legs separately, allow exchange for legs that haven't departed
       - For multi-leg flights: check each segment individually, allow exchange if any segments haven't departed  
       - Only set current_step to NONE if ALL requested flight segments have already departed
       - Use appropriate error messages based on which segments are exchangeable vs past
       - When user requests to change a specific leg (outbound/return), validate only that leg's departure status
    5. If user sepecifies a time window for the intended change flight, please convert it to the correct format.
      {{ TimeWindowReferenceGuide() }}
    ---
    Previous exchange flight conversation state:
    {{ previous_exchange_state | default("None") }}
    ---
    {{ _.role("system")}}
    Please only output DELTA of the exchange flight conversation state compared to previous exchange flight conversation state. Please don't include null value.
    But for the fields: departure_flight_want_change_to, return_flight_want_change_to, please always include all of them, DO NOT do delta updates.
    {{ ctx.output_format }}
  "#
}
