class AgentErrorMessage {
  agent_response string @description(#"
      Response to the traveler
    "#)
}


function GenerateErrorMessage(travel_context: string, messages: string[], error_message: string, current_date: string) -> AgentErrorMessage {
  client GPT4o
  prompt #"
    {{ _.role("system")}}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    Role: You're a senior tech consultant.

    Goal: According to the error message, provide an actionable response to the customer.
    Your customer doesn't know much about technical things. When an error occurs, inform the customer that the processing has failed.
    
    Important:
    - Be gentle but direct
    - Do not mention technical details
    - Be concise
    - Put the response in the agent_response field
    - Likely ask the customer to try asking you again

    Error Message: {{ error_message }}

    Examples:
    - "Apologies, something didn't go as planned. Could you try asking again with a bit more detail or perhaps in a different way?"
    - "Looks like that fare just expired — could you search for the flight again to get the latest options?"
    - "I'm sorry, I couldn't find that information. Could you try asking again in a different way?"
    - "I'm sorry, it looks like the server has a hiccup. Could you try asking again?"

    travel_context: {{ travel_context }}
    {# Only do last 5 messages for error message generation. #}
    {# {{ ConversationHistory(messages, -5) }} #}

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}