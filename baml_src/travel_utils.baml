function AirportToShortCityName(airport_iata_code: string) -> string {
  client AzureGPT4oMini
  prompt #"
    Convert the given airport IATA code to a short city name. The IATA code is a three-letter code used to identify airports around the world. The output should be a short city name that corresponds to the airport IATA code.
    For exapmle, 
    - "LAX" -> "Los Angeles" is commonly considered the city served by the airport, and similarly "JFK" to "New York City", "FLL" to "Miami", "ORD" to "Chicago", "DFW" to "Dallas", "SFO" to "SF", "SEA" to "Seattle", "BOS" to "Boston", "PHX" to "Phoenix", and so on.
    - For international airports too, like "PGK" to "Pangkal Pinang", "TRN" to "Turin", "MDE" to Medellín, "PTP" to "Pointe-à-Pitre", and so on.

    Output only the short city name without any additional text or formatting. Show the city name like Atlanta if it's a word, or LA if it's an abbreviation.
    
    The IATA code is: {{ airport_iata_code }}
    {{ ctx.output_format }}
  "#
}
