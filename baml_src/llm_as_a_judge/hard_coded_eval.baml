class ScoreAndSuggestedChanges {
    grammar_score float @description(#"
    "#)
    consistency_score float @description(#"
    "#)
    verbosity_score float @description(#"
    "#)
    tone_score float @description(#"
    "#)
    suggested_changes string @description(#"
    "#)
}

function ReviewHardCodedMessages(function: string, purpose: string, message_or_templated_message: string) -> ScoreAndSuggestedChanges {
    client GPT4o_AzureFirst
    prompt #"

{{ _.role("system")}}
Job:
 - Review hard-coded messages in the codebase.
 - Based on the function and purpose of the message, provide a score for grammar, consistency, verbosity, tone, and suggested changes.
 - The output should be a structured object with the scores and suggested changes.

# function that this message is used
{{ function }}

# purpose of this message
{{ purpose }}

# message or templated message
{{ message_or_templated_message }}

----
{{ _.role('system') }}
Extract the following data:
{{ ctx.output_format }}
  "#
}
