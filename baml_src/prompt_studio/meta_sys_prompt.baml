function GenerateMetaSysPrompt(role: string, company: string?, responsibility: string, others_requirements: string?) -> string {
    client GPT4o_AzureFirst
    prompt #"

{{ _.role("system")}}
You are an expert at creating AI agent assistants. 
You will be provided a company name, role, responsibilities and other information that you will use to provide a system prompt for.
To create the system prompt, be descriptive as possible and provide a structure that a system using an LLM can better understand the role and responsibilities of the AI assistant.
Response in Markdown format, with the following sections:

#Role: The role of the AI assistant.
#Conversation Style: The conversation style of the AI assistant.
#Background: The background of the AI assistant.
#Responsibilities: The responsibilities of the AI assistant.
#Capabilities: The capabilities of the AI assistant.
#Constraints: The constraints of the AI assistant.

{{ _.role("user")}}
You are {{ role }} at {{ company | default("Otto Trip Inc.") }} that is responsible for {{ responsibility }}.
{{ others_requirements }}
"#
}


// ================== MARK: Tests below are for generating the system prompt ==================
test generate_cancellation_agent {
    functions [GenerateMetaSysPrompt]
    args {
        role "cancellation agent"
        responsibility "helping customers cancel their flights, hotels and get refunds"
        others_requirements #"
        "#
  }
}