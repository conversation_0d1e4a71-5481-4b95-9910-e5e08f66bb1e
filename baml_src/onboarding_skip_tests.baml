

test skip_onboarding_low_probability_for_declined_calendar_access_test2 {
  functions [DetectSkipOnboarding]
  args {
    messages [
      "assistant <PERSON> <PERSON><PERSON>! I’m <PERSON>. <PERSON> to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user explain it",
      "assistant {\"agent_response\": \"Great! Here's a bit about how I can make your life easier:&NewLine; &NewLine;I\u2019m like your expert-level travel agent, here to assist with flights and hotels in the US while following your company\u2019s Travel Policy. Need the best seat on the plane? Got it. Plans change? I\u2019ll take care of rebooking or cancellations.&NewLine; &NewLine;When you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.&NewLine; &NewLine;That said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.&NewLine; &NewLine;Even if you\u2019re not booking a trip today, getting to know your travel preferences now will make things smoother when you do. I\u2019ll tailor recommendations to your style\u2014whether it\u2019s sticking to favorite airlines and hotels, staying near meeting locations, or ensuring your hotel has the right amenities. Plus, I\u2019ll always flag options that exceed your company\u2019s Travel Policy.&NewLine; &NewLine;Would you like to set up your travel preferences now, or do you have any questions before we get started?\", \"step\": \"CustomerWantKnowMore\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user Can we skip this?",
      "assistant {\"agent_response\": \"Totally understand. But I want to call out the importance of having your preferences setting to ensure a smooth booking process.\n\n \nThe best way for me to be helpful is if I get to know your preferences and travel habits by checking your calendar for past and upcoming travel.&NewLine;\nSound good to start with connecting your calendar?\n\", \"step\": \"AskForCalendarAccess\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false, \"onboarding_Skip_Attempted\": true}",
      "user No, I don't want to share",
    ]
  }
  @@assert(should_not_skip_onboarding, {{this.skipping_probability < 0.8}})
}

test skip_onboarding_high_probability_test1 {
  functions [DetectSkipOnboarding]
  args {
    messages [
      "assistant Hi Yuchen! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user book me a flight to SF next monday",
      "assistant {\"agent_response\": \"\nI understand you want to get right to it and do your first search. The best way for me to be helpful is if I get to know your preferences and travel habits by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. \n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n\nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"SkipOnboarding\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "user sure, why not",
      "assistant {\"agent_response\": \"Great! Let's get started with connecting your calendar so I can learn more about your travel habits.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}",
      "user Google calendar request error type: popup_closed",
      "assistant {\"agent_response\": \"It looks like that didn\u2019t work. Would you like to try again?\", \"step\": \"AskForCalendarAccess\", \"is_retry\": true, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "user retry connect it again",
      "assistant {\"agent_response\": \"Great! Let's try connecting your calendar again so I can learn more about your travel habits.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": true, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": false}",
      "assistant {\"action\": \"I'm starting to grant access to your calendar\", \"message_type\": \"google_login\"}",
      "user Calendar permission has been granted",
      "assistant {\"agent_response\": \"Thank you for granting calendar access! I'm starting to analyze your calendar to gather your travel preferences.\", \"step\": \"AnalyzeCalendar\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"has_calendar_access\": true}",
      "assistant {\"agent_response\":\"It seems we don't have any preferences gathered from your calendar events. I’ll just ask you a few quick questions so I can learn about your travel style and preferences. Let’s get started. First, do you have a preferred home airport that you usually fly out of?\",\"response_all_preferences\":{\"preferred_home_airport\":null,\"preferred_airline_brands\":null,\"preferred_cabin\":null,\"preferred_seats\":null,\"preferred_hotel_brands\":null,\"preferred_travel_misc\":null},\"is_conversation_finished\":false}",
      "user book me a flight to SF next monday",
    ]
  }
  @@assert(skip_onboarding, {{this.skipping_probability >= 0.8}})
}

test skip_onboarding_high_probability_test3 {
  functions [DetectSkipOnboarding]
  args {
    messages [
      "assistant Hi Yuchen! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user book me a flight to SF",
    ]
  }
  @@assert(skip_onboarding, {{this.skipping_probability >= 0.8}})
}

test skip_onboarding_low_probability_test1 {
  functions [DetectSkipOnboarding]
  args {
    messages [
      "assistant Hi Huibo! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user yes, please explain",
    ]
  }
  @@assert(should_not_skip_onboarding, {{this.skipping_probability < 0.8}})
}

test skip_onboarding_low_probability_test2 {
  functions [DetectSkipOnboarding]
  args {
    messages [
      "assistant Hi Huibo! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user Let's start.",
    ]
  }
  @@assert(should_not_skip_onboarding, {{this.skipping_probability < 0.8}})
}

test skip_onboarding_low_probability_test3 {
  functions [DetectSkipOnboarding]
  args {
    messages [
      "assistant Hi Huibo! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user jump straight in",
    ]
  }
  @@assert(should_not_skip_onboarding, {{this.skipping_probability < 0.8}})
}

// test skip_onboarding_medium_probability_test2 {
//   functions [DetectSkipOnboarding]
//   args {
//     messages [
//       "assistant Hi Huibo! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
//       "user explain it",
//       "assistant {\"agent_response\": \"Great! Here's a bit about how I can make your life easier:&NewLine; &NewLine;I\u2019m like your expert-level travel agent, here to assist with flights and hotels in the US while following your company\u2019s Travel Policy. Need the best seat on the plane? Got it. Plans change? I\u2019ll take care of rebooking or cancellations.&NewLine; &NewLine;When you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.&NewLine; &NewLine;That said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.&NewLine; &NewLine;Even if you\u2019re not booking a trip today, getting to know your travel preferences now will make things smoother when you do. I\u2019ll tailor recommendations to your style\u2014whether it\u2019s sticking to favorite airlines and hotels, staying near meeting locations, or ensuring your hotel has the right amenities. Plus, I\u2019ll always flag options that exceed your company\u2019s Travel Policy.&NewLine; &NewLine;Would you like to set up your travel preferences now, or do you have any questions before we get started?\", \"step\": \"CustomerWantKnowMore\", \"onboarding_skip_attempted\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
//       "user I am not ready for a trip. Can we do this later?"
//     ]
//   }
//   @@assert(skip_onboarding, {{this.skipping_probability >= 0.6 and this.skipping_probability <= 0.7}})
// }

test one_message_skip_onboarding_should_not_skip_onboarding {
  functions [DetectSkipOnboarding]
  args {
    messages [
      "assistant Hi Chundong! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user no need to intro. just dive in",
    ]
  }
  @@assert(skip_onboarding, {{this.skipping_probability < 0.6}})
}