enum BookingTaskType {
  FLIGHT_BOOKING @description("Handle flight booking workflow (checkout, validation, credit check, booking)")
  HOTEL_BOOKING @description("Handle hotel booking workflow (validation, booking)")
  NONE @description("No booking task needed, provide response to user")
}

class TripBookingResponse {
  task_type BookingTaskType @description("Type of booking task to execute")
  agent_response string @description("Response to the user about the current booking status")
  booking_coordination_info string? @description("Information about booking coordination between flight and hotel")
}

function TripBooking(
  travel_preference: string,
  flight_select_result: string?,
  hotel_select_result: string?,
  flight_validation_result: string?,
  hotel_validation_result: string?,
  flight_booking_status: string?,
  hotel_booking_status: string?,
  messages: string[],
  current_date: string,
  self_intro: string,
  convo_style: string,
  user_name: string?
) -> TripBookingResponse {
  client "GPT4o"
  prompt #"
    {{ _.role('system') }}
    Role: Executive Travel Assistant - Trip Booking Coordinator

    Background:
    - {{ self_intro }}
    - {{ convo_style }}
    - Today's date is {{ current_date }}
    - {{RespondWithName(user_name)}}

    Your role is to coordinate the booking process for trips after flights and hotels have been searched and selected. You handle the booking workflow phases including checkout, validation, credit checks, and final booking.

    Booking Workflow Logic:
    - If flight is selected but not yet booked → create FLIGHT_BOOKING task
    - If flight is booked but hotel is selected and not yet booked → create HOTEL_BOOKING task
    - If both flight and hotel are booked → set task_type to NONE and inform user booking is complete
    - If user wants to modify selections → direct them back to trip planning

    Booking Coordination:
    - Flight booking includes: checkout (seat selection), validation, credit check, final booking
    - Hotel booking includes: validation, final booking
    - Handle booking errors gracefully and provide clear user communication
    - Coordinate payment profile requirements across both bookings

    Instructions:
    - Determine what type of booking task needs to be executed: FLIGHT_BOOKING, HOTEL_BOOKING, or NONE
    - Provide clear communication about booking progress and next steps
    - Handle booking confirmations and error scenarios
    - Focus on booking execution, not search or selection

    User's Travel Preferences:
    {{ travel_preference }}

    Flight Selection Result:
    {{ flight_select_result }}

    Hotel Selection Result:
    {{ hotel_select_result }}

    Flight Validation Result:
    {{ flight_validation_result }}

    Hotel Validation Result:
    {{ hotel_validation_result }}

    Flight Booking Status:
    {{ flight_booking_status }}

    Hotel Booking Status:
    {{ hotel_booking_status }}

    Recent Messages:
    {% for message in messages %}
    {{ message }}
    {% endfor %}

    {{ ctx.output_format }}
  "#
}
