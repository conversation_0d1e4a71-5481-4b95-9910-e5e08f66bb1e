class AssertionResult {
  is_expected bool @description(#"Whether the test result is expected."#)
  reason string @description(#"The reason why the test result is expected or not expected. Be concise"#)
}

function Assert(test_output: string, expected_criteria: string) -> AssertionResult {

  client GeminiFlash
  prompt #"
    You are a test assertion evaluator. Your task is to determine if a test output meets the expected criteria.

    Test output to evaluate:
    ```
    {{ test_output }}
    ```

    Expected criteria:
    ```
    {{ expected_criteria }}
    ```

    Evaluate if the test output follows the general structure and format of the expected criteria. Focus on:
    1. Does it contain all required information?
    2. Does it follow the expected message structure/format?
    3. Does it include all required actions/confirmations?

    Important: Do NOT compare specific values like:
    - IDs/reference numbers
    - Exact locations/coordinates
    - Timestamps
    - Specific numeric values
    
    Instead, verify that these fields exist in the correct format, regardless of their exact values.

    Response format:
    {{ ctx.output_format }}
  "#
}

function AssertDynamicly(test_output:string) -> AssertionResult {

  client GeminiFlash
  prompt #"
    You are a test assertion evaluator. Your task is to determine if a test output meets the expected criteria.

    Test to evaluate:
    ```
    {{ test_output }}

    ```

    Evaluate if the output is True or False.

    Response format:
    {{ ctx.output_format }}
  "#
}
