from server.schemas.user_profile.personal_information import PersonalInformationExtendedRequest
from server.utils.mongo_connector import payment_profile_collection, user_profile_personal_information_collection


async def migrate():
    payment_profiles = await payment_profile_collection.find({}).to_list(None)

    for profile in payment_profiles:
        user_id = profile.get("users_id", None)

        try:
            assert user_id is not None

            personal_information = PersonalInformationExtendedRequest(**profile)

            await user_profile_personal_information_collection.update_one(
                {"users_id": user_id}, {"$set": {"users_id": user_id, **personal_information.model_dump()}}, upsert=True
            )
            print(f"Updated personal information profile for user {user_id}")
        except Exception:
            print(f"Failed to update personal information profile for user {user_id}")
            pass

    print("Done!")


if __name__ == "__main__":
    import asyncio

    asyncio.run(migrate())
