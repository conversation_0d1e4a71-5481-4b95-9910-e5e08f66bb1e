# """
# Example script demonstrating how to use the S3 utils.

# This is not meant to be executed directly but serves as a reference for other
# developers who need to use the S3 upload functionality.
# """

import asyncio

# from unittest.mock import DEFAULT
# from server.database.models.chat_thread import ChatThread
# from server.services.task_manager.task_manager import GlobalTaskManager
from server.utils.s3_utils import s3_utils

# async def upload_json_example(thread_id: int):
#     """Example of uploading a JSON file to S3."""
#     chat_thread = await ChatThread.from_id(thread_id)
#     if not chat_thread:
#         print(f"Chat thread with ID {thread_id} not found")
#         return

#     data = {
#         "user_id": chat_thread.users_id,
#         "trip_title": chat_thread.title,
#         "trip_dates": {
#             "start": chat_thread.date_start.isoformat() if chat_thread.date_start else None,
#             "end": chat_thread.date_end.isoformat() if chat_thread.date_end else None,
#         },
#     }

#     await s3_utils.log_trip_artifact_to_s3(
#         trip_id=chat_thread.id, file_name="trip_summary", content=data, file_type="json"
#     )
#     print("Queued uploading of trip summary to S3")


# async def upload_log_example(thread_id: int, log_content: str):
#     """Example of uploading a log file to S3."""
#     chat_thread = await ChatThread.from_id(thread_id)
#     if not chat_thread:
#         print(f"Chat thread with ID {thread_id} not found")
#         return

#     await s3_utils.log_trip_artifact_to_s3(
#         trip_id=chat_thread.id,
#         file_name=f"trip_log_{thread_id}",
#         content=log_content,
#         file_type="log",
#         extra_path="logs",  # Organize logs in a separate folder
#     )
#     print("Queued uploading of trip summary to S3")


# async def upload_csv_example(thread_id: int, csv_content: str):
#     """Example of uploading a CSV file to S3."""
#     chat_thread = await ChatThread.from_id(thread_id)
#     if not chat_thread:
#         print(f"Chat thread with ID {thread_id} not found")
#         return

#     await s3_utils.log_trip_artifact_to_s3(
#         trip_id=chat_thread.id,
#         file_name=f"trip_data_{thread_id}",
#         content=csv_content,
#         file_type="csv",
#         extra_path="data",  # Organize data files in a separate folder
#     )
#     print("Queued uploading of trip summary to S3")


# async def upload_batch_example(thread_id: int):
#     """Example of batch uploading multiple files to S3 asynchronously."""
#     chat_thread = await ChatThread.from_id(thread_id)
#     if not chat_thread:
#         print(f"Chat thread with ID {thread_id} not found")
#         return

#     json_data = {"data": [1, 2, 3], "metadata": {"source": "batch_example"}}
#     csv_data = "id,value\n1,first\n2,second\n3,third"
#     log_data = "INFO: Starting batch upload\nDEBUG: Processing data\nINFO: Completed batch upload"

#     await s3_utils.log_trip_artifact_to_s3(
#         trip_id=chat_thread.id,
#         file_name="batch_json",
#         content=json_data,
#         file_type="json",
#         queue_for_batch=True,
#     )

#     await s3_utils.log_trip_artifact_to_s3(
#         trip_id=chat_thread.id,
#         file_name="batch_csv",
#         content=csv_data,
#         file_type="csv",
#         extra_path="data",
#         queue_for_batch=True,
#     )

#     await s3_utils.log_trip_artifact_to_s3(
#         trip_id=chat_thread.id,
#         file_name="batch_log",
#         content=log_data,
#         file_type="log",
#         extra_path="logs",
#         queue_for_batch=True,
#     )

#     print("Queued JSON file for upload.")
#     print("Queued CSV file for upload.")
#     print("Queued log file for upload.")


# async def upload_immediate_async_example(bucket_name: str, thread_id: int):
#     """Example of immediately uploading a file to S3 asynchronously (not batched)."""
#     chat_thread = await ChatThread.from_id(thread_id)
#     if not chat_thread:
#         print(f"Chat thread with ID {thread_id} not found")
#         return

#     data = {"timestamp": "2023-01-01T12:00:00Z", "event": "user_action", "details": "Clicked on search button"}

#     await s3_utils.log_trip_artifact_to_s3(
#         bucket_name=bucket_name,
#         trip_id=chat_thread.id,
#         file_name="event_log",
#         content=data,
#         file_type="json",
#         queue_for_batch=False,  # Upload immediately in background
#     )
#     print("Started async upload now.")


async def download_from_s3_uri_example(s3_uri: str):
    content = await s3_utils.download_from_s3_uri(s3_uri)
    if content:
        print("Downloaded content:", content)
    else:
        print("Failed to download from S3")


async def main():
    await download_from_s3_uri_example(
        "s3://otto-flights/dev/data/serp/search-results-c450efb8-590f-451b-a610-5d90198690fd-serp.json"
    )


if __name__ == "__main__":
    asyncio.run(main())
