"""Github action: Calculate pass rate of agent scenarios from pytest report."""

import json
import re
from collections import defaultdict


# fmt: off
def calculate_pass_rate(report_path, output_path):
    with open(report_path, 'r') as file:
        report_data = json.load(file)

    results = defaultdict(lambda: defaultdict(lambda: {'total': 0, 'passed': 0}))

    for test in report_data.get('tests', []):
        if 'nodeid' in test:
            # Parse file and scenario from nodeid
            parts = test['nodeid'].split('::')
            file_name = parts[0]
            scenario_raw = parts[1] if len(parts) > 1 else 'Unknown'
            match = re.match(r".*\[(.*?)-", scenario_raw)
            scenario = match.group(1) if match else "Unknown"

            # Update results
            results[file_name][scenario]['total'] += 1
            if test.get('outcome') == 'passed':
                results[file_name][scenario]['passed'] += 1

    # Compute pass rates
    pass_rates = {}
    for file_name, scenarios in results.items():
        pass_rates[file_name] = {}
        for scenario, counts in scenarios.items():
            total = counts['total']
            passed = counts['passed']
            pass_rate = (passed / total * 100) if total > 0 else 0
            pass_rates[file_name][scenario] = {
                'total': total,
                'passed': passed,
                'pass_rate': round(pass_rate, 2)
            }

    # Save to output file
    with open(output_path, 'w') as output_file:
        json.dump(pass_rates, output_file, indent=4)
    print(f'Pass rate report saved to {output_path}')


report_file = 'pytest_report.json'
output_file = 'agent_scenario_pass_rate.json'
calculate_pass_rate(report_file, output_file)
