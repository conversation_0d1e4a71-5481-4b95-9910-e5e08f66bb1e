import json

import requests

from server.utils.settings import settings

prod_env = "https://demandapi.booking.com"
accomodations_details_cache_file_name = "./accomodations_details_cache.txt"


def collect_accomodation_details():
    # https://developers.booking.com/demand/docs/open-api/demand-api/accommodations/accommodations/details

    # self.booking_api_key = "otS61_xecSKVYHBOM39i3IZou5lhZjBf2jGHnXODyTx99u0V58Ne" # settings.BOOKING_DOT_COM_API_KEY
    # self.booking_affiliate_id = "2426026" #
    # settings.BOOKING_DOT_COM_AFFILIATE_ID

    booking_api_key = settings.BOOKING_DOT_COM_API_KEY
    booking_affiliate_id = settings.BOOKING_DOT_COM_AFFILIATE_ID

    headers = {
        "Content-Type": "application/json",
        "X-Affiliate-Id": f"{booking_affiliate_id}",
        "Authorization": f"Bearer {booking_api_key}",
    }

    url = f"{prod_env}/3.1/accommodations/details"

    payload = {
        "country": "us",
        "extras": [
            "description",
            "facilities",
            "payment",
            "photos",
            "policies",
            "rooms",
        ],
        "rows": 500,
        "payment": {"timing": "pay_at_the_property"},
        # "accommodation_types": [204],  # Just hotels
        "languages": [
            "en-us",
        ],
    }

    keep_paging = True
    property_counter = 0
    while keep_paging:
        hotels = []
        response = requests.post(url, json=payload, headers=headers)

        if response.status_code != 200:
            print(f"get_properties_details() - Error: Failed to retrieve hotel lists. {response}")
            return

        # Looks like we get 20 at a time, each time in a 'data' array
        obj = response.json()
        for hotel in obj["data"]:
            hotels.append(json.dumps(hotel))

        next_page = obj.get("next_page", None)
        if next_page is not None:
            payload = {"page": next_page}
        else:
            keep_paging = False

        with open(accomodations_details_cache_file_name, "a") as file:
            for hotel in hotels:
                property_counter += 1
                file.write(hotel + "\n")

        print(f"Fetched and wrote {property_counter} hotels")


if __name__ == "__main__":
    collect_accomodation_details()
