#!/usr/bin/env python3
"""
Test script for gmail_send_message function.
This script sends a test email with hardcoded HTML content for local testing purposes.

Usage: uv run scripts/test_gmail_send.py

Requirements:
- GMAIL_SENDER_SERVICE_ACCOUNT_JSON environment variable must be set
- Virtual environment must be activated
"""

import os
import sys

# Add the project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)

import markdown2
from jinja2 import Environment, FileSystemLoader

from server.utils.smtp import gmail_send_message


def main():
    """Send a test email with hardcoded HTML content."""

    to_email = "<EMAIL>"
    subject = "Test Email from Otto - Local Testing"
    from_name = "Otto Test"

    try:
        agent_response = """I provided a list of top business-friendly hotels in downtown San Diego's Gaslamp Quarter that comply with company policy and offer free breakfast. The options include: """

        agent_response_html = markdown2.markdown(agent_response, extras=["task_list", "tables"])
        env = Environment(loader=FileSystemLoader("email_templates"))

        cards_html = ""
        template_mapping = {
            "flights": "flight_oneway_card.html",
            "flight_combos": "flight_roundtrip_card.html",
            "accommodations": "hotel_card.html",
        }

        for card_type, template_name in template_mapping.items():
            if email_cards.get(card_type):
                try:
                    cards_template = env.get_template(template_name)
                    cards_html += cards_template.render(email_cards)
                except Exception as e:
                    print(f"[MAILGUN WEBHOOK] Error rendering {card_type} template: {e}")

        context = {
            "trip_title": "Trip to New York, June 14-16 2025",
            "trip_id": "12345",
            "base_url": "https://app.ottotheagent.com/",
            "summary_html": agent_response_html,
            "cards_html": cards_html,
            "eding_html": """Some other options like Park Terrace Hotel - Deluxe High Floor, Grayson Hotel - King Room with Empire View, and Archer Hotel New York - Classic King Guest Room are not picked. Let me know if you want to see more details about them.
Reply here to let me know which hotel option you'd like to book, or share any additional preferences (e.g., loyalty program, room type, amenities) so I can further tailor my recommendations.""",
        }

        template = env.get_template("response_base.html")
        response_body_as_html = template.render(context)

        result = gmail_send_message(
            to=to_email, subject=subject, content=response_body_as_html, mime_type="html", from_name=from_name
        )

        print("✅ Email sent successfully!")
        print(f"Result: {result}")

    except Exception as e:
        print(f"❌ Failed to send email: {str(e)}")
        print("Make sure GMAIL_SENDER_SERVICE_ACCOUNT_JSON environment variable is set.")
        sys.exit(1)


email_cards = {
    "flight_date": "June 14 - June 16",
    "hotel_date": "May 24 - June 16",
    "flights": [
        {
            "img": {"alt": "HA", "src": None},
            "highlight": None,
            "id": "CiAKHgoQNDU2OGI3NzIzMDNmNmU2YhIICM8CEgExGAEgAQ==",
            "action": "For my outbound I choose flight id: CiAKHgoQNDU2OGI3NzIzMDNmNmU2YhIICM8CEgExGAEgAQ==.",
            "cancelled": None,
            "price": {"amount": "153.30", "currency": "USD"},
            "credits": {"amount": "0.00", "currency": "USD"},
            "net_price": {"amount": "0.00", "currency": "USD"},
            "type": "ONE_WAY",
            "cabin": "ECONOMY",
            "booking_code": "L",
            "total_distance_miles": 953.0,
            "total_duration": None,
            "within_policy": None,
            "within_or_out_policy_reason": "reason for policy.",
            "exchange_policy": "Change allowed for free",
            "cancellation_policy": "Non-refundable",
            "fare_option_name": "Main Cabin",
            "flight_segments": [
                {
                    "origin_code": "SEA",
                    "origin_name": "Seattle–Tacoma International Airport",
                    "destination_code": "LAX",
                    "destination_name": "Los Angeles International Airport",
                    "flight_stops": [
                        {
                            "origin_code": "SEA",
                            "origin_name": "Seattle–Tacoma International Airport",
                            "destination_code": "LAX",
                            "destination_name": "Los Angeles International Airport",
                            "airline_code": "HA",
                            "airline_name": "Hawaiian Airlines",
                            "operating_airline_code": "AS",
                            "operating_flight_number": "1316",
                            "aircraft_iata_code": "737",
                            "aircraft_name": "",
                            "flight_number": "6774",
                            "duration": "PT2H44M",
                            "cabin": "ECONOMY",
                            "booking_code": "L",
                            "departure": "2025-07-04T06:00:00",
                            "departure_timezone": "America/Los_Angeles",
                            "arrival": "2025-07-04T08:44:00",
                            "arrival_timezone": "America/Los_Angeles",
                            "seat": None,
                            "confirmation": None,
                        }
                    ],
                }
            ],
            "recommendationReasons": [
                "This flight departs at 6:00 AM, is operated by Alaska Airlines, offers the lowest cost in economy, and is changeable without fee. Ideal for early travelers."
            ],
            "seat_selection_policy": "Select seat anytime",
            "boarding_policy": "Standard boarding",
            "operating_airline_code": "AS",
            "operating_flight_number": "1316",
            "keyword": "cheapest",
            "is_red_eye": False,
            "source": "SABRE",
        },
        {
            "img": {"alt": "HA", "src": None},
            "highlight": None,
            "id": "CiAKHgoQNDU2OGI3NzIzMDNmNmU2YhIICKcCEgExGAEgAQ==",
            "action": "For my outbound I choose flight id: CiAKHgoQNDU2OGI3NzIzMDNmNmU2YhIICKcCEgExGAEgAQ==.",
            "cancelled": None,
            "price": {"amount": "153.30", "currency": "USD"},
            "credits": {"amount": "0.00", "currency": "USD"},
            "net_price": {"amount": "0.00", "currency": "USD"},
            "type": "ONE_WAY",
            "cabin": "ECONOMY",
            "booking_code": "L",
            "total_distance_miles": 953.0,
            "total_duration": None,
            "within_policy": False,
            "within_or_out_policy_reason": "reason for out of policy.",
            "exchange_policy": "Change allowed for free",
            "cancellation_policy": "Non-refundable",
            "fare_option_name": "Main Cabin",
            "flight_segments": [
                {
                    "origin_code": "SEA",
                    "origin_name": "Seattle–Tacoma International Airport",
                    "destination_code": "LAX",
                    "destination_name": "Los Angeles International Airport",
                    "flight_stops": [
                        {
                            "origin_code": "SEA",
                            "origin_name": "Seattle–Tacoma International Airport",
                            "destination_code": "LAX",
                            "destination_name": "Los Angeles International Airport",
                            "airline_code": "HA",
                            "airline_name": "Hawaiian Airlines",
                            "operating_airline_code": "AS",
                            "operating_flight_number": "797",
                            "aircraft_iata_code": "737",
                            "aircraft_name": "",
                            "flight_number": "6763",
                            "duration": "PT2H44M",
                            "cabin": "ECONOMY",
                            "booking_code": "L",
                            "departure": "2025-07-04T07:00:00",
                            "departure_timezone": "America/Los_Angeles",
                            "arrival": "2025-07-04T09:44:00",
                            "arrival_timezone": "America/Los_Angeles",
                            "seat": None,
                            "confirmation": None,
                        }
                    ],
                }
            ],
            "recommendationReasons": [
                "Departs at 7:00 AM, operated by Alaska Airlines, economy fare, and changeable without fee. Convenient for a slightly later start while keeping costs low."
            ],
            "seat_selection_policy": "Select seat anytime",
            "boarding_policy": "Standard boarding",
            "operating_airline_code": "AS",
            "operating_flight_number": "797",
            "keyword": None,
            "is_red_eye": False,
            "source": "SABRE",
        },
    ],
    "flight_combos": {
        "first_round_trip_combo": {
            "alias": ["option 1", "first option", "1st option", "combo 1"],
            "outbound_flight": {
                "img": {"alt": "HA", "src": None},
                "highlight": None,
                "id": "CiAKHgoQM2NiYmZlYmY4YTM2NzRiMhIICMQSEgExGAEgAQ==",
                "action": "For my outbound I choose flight id: CiAKHgoQM2NiYmZlYmY4YTM2NzRiMhIICMQSEgExGAEgAQ==.",
                "cancelled": None,
                "price": {"amount": "406.59", "currency": "USD"},
                "credits": {"amount": "0.00", "currency": "USD"},
                "net_price": {"amount": "0.00", "currency": "USD"},
                "type": "ROUND_TRIP",
                "cabin": "ECONOMY",
                "booking_code": "L",
                "total_distance_miles": 953.0,
                "total_duration": None,
                "within_policy": False,
                "within_or_out_policy_reason": "reason for without policy.",
                "exchange_policy": "Change allowed for free",
                "cancellation_policy": "Refundable for free",
                "fare_option_name": "Main Cabin Refundable",
                "flight_segments": [
                    {
                        "origin_code": "SEA",
                        "origin_name": "Seattle–Tacoma International Airport",
                        "destination_code": "LAX",
                        "destination_name": "Los Angeles International Airport",
                        "flight_stops": [
                            {
                                "origin_code": "SEA",
                                "origin_name": "Seattle–Tacoma International Airport",
                                "destination_code": "LAX",
                                "destination_name": "Los Angeles International Airport",
                                "airline_code": "HA",
                                "airline_name": "Hawaiian Airlines",
                                "operating_airline_code": "AS",
                                "operating_flight_number": "505",
                                "aircraft_iata_code": "737",
                                "aircraft_name": "",
                                "flight_number": "6762",
                                "duration": "PT2H44M",
                                "cabin": "ECONOMY",
                                "booking_code": "L",
                                "departure": "2025-07-05T09:45:00",
                                "departure_timezone": "America/Los_Angeles",
                                "arrival": "2025-07-05T12:29:00",
                                "arrival_timezone": "America/Los_Angeles",
                                "seat": None,
                                "confirmation": None,
                            }
                        ],
                    }
                ],
                "recommendationReasons": ["HA 6762 offers changeable economy, midday, nonstop, lowest cost."],
                "seat_selection_policy": "Select seat anytime",
                "boarding_policy": "Standard boarding",
                "operating_airline_code": "AS",
                "operating_flight_number": "505",
                "keyword": "cheapest",
                "is_red_eye": False,
                "source": "SABRE",
            },
            "return_flight": {
                "img": {"alt": "HA", "src": None},
                "highlight": None,
                "id": "Ch0KGwoQM2NiYmZlYmY4YTM2NzRiMhIFCMQSGAEgAQofCh0KEDQ3MWI2ZDY1MjdkMGIxMzUSBwgQEgExGAEgAQ==",
                "action": "For my return I choose flight id: Ch0KGwoQM2NiYmZlYmY4YTM2NzRiMhIFCMQSGAEgAQofCh0KEDQ3MWI2ZDY1MjdkMGIxMzUSBwgQEgExGAEgAQ==.",
                "cancelled": None,
                "price": {"amount": "406.59", "currency": "USD"},
                "credits": {"amount": "0.00", "currency": "USD"},
                "net_price": {"amount": "0.00", "currency": "USD"},
                "type": "ROUND_TRIP",
                "cabin": "ECONOMY",
                "booking_code": "L",
                "total_distance_miles": 953.0,
                "total_duration": None,
                "within_policy": True,
                "within_or_out_policy_reason": "reason for within policy.",
                "exchange_policy": "Change allowed for free",
                "cancellation_policy": "Refundable for free",
                "fare_option_name": "Main Cabin Refundable",
                "flight_segments": [
                    {
                        "origin_code": "LAX",
                        "origin_name": "Los Angeles International Airport",
                        "destination_code": "SEA",
                        "destination_name": "Seattle–Tacoma International Airport",
                        "flight_stops": [
                            {
                                "origin_code": "LAX",
                                "origin_name": "Los Angeles International Airport",
                                "destination_code": "SEA",
                                "destination_name": "Seattle–Tacoma International Airport",
                                "airline_code": "HA",
                                "airline_name": "Hawaiian Airlines",
                                "operating_airline_code": "AS",
                                "operating_flight_number": "1293",
                                "aircraft_iata_code": "737",
                                "aircraft_name": "",
                                "flight_number": "6202",
                                "duration": "PT3H",
                                "cabin": "ECONOMY",
                                "booking_code": "L",
                                "departure": "2025-07-06T06:00:00",
                                "departure_timezone": "America/Los_Angeles",
                                "arrival": "2025-07-06T09:00:00",
                                "arrival_timezone": "America/Los_Angeles",
                                "seat": None,
                                "confirmation": None,
                            }
                        ],
                    }
                ],
                "recommendationReasons": ["HA 6202 is changeable, nonstop, economy, lowest cost, morning."],
                "seat_selection_policy": "Select seat anytime",
                "boarding_policy": "Standard boarding",
                "operating_airline_code": "AS",
                "operating_flight_number": "1293",
                "keyword": "cheapest",
                "is_red_eye": False,
                "source": "SABRE",
            },
        },
        "second_round_trip_combo": {
            "alias": ["option 2", "second option", "2nd option", "combo 2"],
            "outbound_flight": {
                "img": {"alt": "HA", "src": None},
                "highlight": None,
                "id": "CiAKHgoQM2NiYmZlYmY4YTM2NzRiMhIICJQREgExGAEgAQ==",
                "action": "For my outbound I choose flight id: CiAKHgoQM2NiYmZlYmY4YTM2NzRiMhIICJQREgExGAEgAQ==.",
                "cancelled": None,
                "price": {"amount": "406.59", "currency": "USD"},
                "credits": {"amount": "0.00", "currency": "USD"},
                "net_price": {"amount": "0.00", "currency": "USD"},
                "type": "ROUND_TRIP",
                "cabin": "ECONOMY",
                "booking_code": "L",
                "total_distance_miles": 953.0,
                "total_duration": None,
                "within_policy": True,
                "within_or_out_policy_reason": "reason for within policy.",
                "exchange_policy": "Change allowed for free",
                "cancellation_policy": "Refundable for free",
                "fare_option_name": "Main Cabin Refundable",
                "flight_segments": [
                    {
                        "origin_code": "SEA",
                        "origin_name": "Seattle–Tacoma International Airport",
                        "destination_code": "LAX",
                        "destination_name": "Los Angeles International Airport",
                        "flight_stops": [
                            {
                                "origin_code": "SEA",
                                "origin_name": "Seattle–Tacoma International Airport",
                                "destination_code": "LAX",
                                "destination_name": "Los Angeles International Airport",
                                "airline_code": "HA",
                                "airline_name": "Hawaiian Airlines",
                                "operating_airline_code": "AS",
                                "operating_flight_number": "1220",
                                "aircraft_iata_code": "737",
                                "aircraft_name": "",
                                "flight_number": "6769",
                                "duration": "PT2H42M",
                                "cabin": "ECONOMY",
                                "booking_code": "L",
                                "departure": "2025-07-05T12:50:00",
                                "departure_timezone": "America/Los_Angeles",
                                "arrival": "2025-07-05T15:32:00",
                                "arrival_timezone": "America/Los_Angeles",
                                "seat": None,
                                "confirmation": None,
                            }
                        ],
                    }
                ],
                "recommendationReasons": ["HA 6769 is changeable economy, early afternoon, nonstop, affordable."],
                "seat_selection_policy": "Select seat anytime",
                "boarding_policy": None,
                "operating_airline_code": "AS",
                "operating_flight_number": "1220",
                "keyword": "afternoon",
                "is_red_eye": False,
                "source": "SABRE",
            },
            "return_flight": {
                "img": {"alt": "HA", "src": None},
                "highlight": None,
                "id": "Ch0KGwoQM2NiYmZlYmY4YTM2NzRiMhIFCJQRGAEgAQofCh0KEDhlYjhjYjIxZjAxMzNlZDcSBwgnEgExGAEgAQ==",
                "action": "For my return I choose flight id: Ch0KGwoQM2NiYmZlYmY4YTM2NzRiMhIFCJQRGAEgAQofCh0KEDhlYjhjYjIxZjAxMzNlZDcSBwgnEgExGAEgAQ==.",
                "cancelled": None,
                "price": {"amount": "406.59", "currency": "USD"},
                "credits": {"amount": "0.00", "currency": "USD"},
                "net_price": {"amount": "0.00", "currency": "USD"},
                "type": "ROUND_TRIP",
                "cabin": "ECONOMY",
                "booking_code": "L",
                "total_distance_miles": 953.0,
                "total_duration": None,
                "within_policy": True,
                "within_or_out_policy_reason": "reason for within policy.",
                "exchange_policy": "Change allowed for free",
                "cancellation_policy": "Refundable for free",
                "fare_option_name": "Main Cabin Refundable",
                "flight_segments": [
                    {
                        "origin_code": "LAX",
                        "origin_name": "Los Angeles International Airport",
                        "destination_code": "SEA",
                        "destination_name": "Seattle–Tacoma International Airport",
                        "flight_stops": [
                            {
                                "origin_code": "LAX",
                                "origin_name": "Los Angeles International Airport",
                                "destination_code": "SEA",
                                "destination_name": "Seattle–Tacoma International Airport",
                                "airline_code": "HA",
                                "airline_name": "Hawaiian Airlines",
                                "operating_airline_code": "AS",
                                "operating_flight_number": "1293",
                                "aircraft_iata_code": "737",
                                "aircraft_name": "",
                                "flight_number": "6202",
                                "duration": "PT3H",
                                "cabin": "ECONOMY",
                                "booking_code": "L",
                                "departure": "2025-07-06T06:00:00",
                                "departure_timezone": "America/Los_Angeles",
                                "arrival": "2025-07-06T09:00:00",
                                "arrival_timezone": "America/Los_Angeles",
                                "seat": None,
                                "confirmation": None,
                            }
                        ],
                    }
                ],
                "recommendationReasons": ["HA 6202 is changeable, nonstop, economy, lowest cost, morning."],
                "seat_selection_policy": "Select seat anytime",
                "boarding_policy": None,
                "operating_airline_code": "AS",
                "operating_flight_number": "1293",
                "keyword": "cheapest",
                "is_red_eye": False,
                "source": "SABRE",
            },
        },
    },
    "accommodations": [
        {
            "highlight": "4.0",
            "hotel": "Capital Hilton",
            "id": "capital-hilton",
            "img": {
                "alt": "Capital Hilton",
                "src": "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=26106efc5ee51aeabec34417f8367f2ac06dfec52730c6cf636f497fdf806fd6&o=",
            },
            "mapMarker": {
                "coordinates": {"lat": "38.903182", "lng": "-77.036272"},
                "address": "1001 16th St NW, Washington, DC 20036, USA",
                "text": "Capital Hilton",
            },
            "photos": [
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=26106efc5ee51aeabec34417f8367f2ac06dfec52730c6cf636f497fdf806fd6&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/690718495.jpg?k=1a082f267d20f8d73c6f991a47eab4ff3f9a13b8f2b0ed9c7af5e7efd822da3d&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/666178297.jpg?k=d65a648fd9b25982940e026dd45aa630af983854293bbceb107c59d9136889d9&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/666178301.jpg?k=c34b97f3e34ef586addee26ea39d1af0d832cf1b81034718d9a64040395224cc&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/666178300.jpg?k=4b3dee2400993e6f77f607a1124d0f5ca68d0d0d3a23f4d2ddb42ffe9897c101&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/666178298.jpg?k=57395a8748f52342ba36e39be85658954844c1c70f5a0b29b23717d02da18a7a&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/666178302.jpg?k=64e248f301619651963532904e29c235d186f7ff390ed501da80280948ee9991&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=19d514fa03dfb2b5ebeeccd8517d697f21f7301e7770afeb234955b54dc9ddc4&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/666178305.jpg?k=cef356532f2ae4f529ac3d9275f511aa9e62226470b34dabad470bc4bd3e7484&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/666178303.jpg?k=594ea0c1740d358c08968e8cf8116e9c79c06b49342947aa28219403141582a6&o=",
            ],
            "price": None,
            "recommendationReasons": {
                "choice_characteristic": "Historic luxury",
                "preference_alignment_reason": "A Hilton in the heart of downtown, steps from the White House and Metro, with a 24-hour front desk and fitness center.",
                "distinction_reason_one": "On-site dining",
                "distinction_reason_two": "Historic property",
            },
            "hotel_class": "4.0",
            "amenities": [
                "Express check-in/out",
                "Fax/Photocopying",
                "Hypoallergenic room available",
                "Car rental",
                "Fitness center",
                "Concierge",
                "Accessible parking",
                "Meeting/Banquet facilities",
                "Dry cleaning",
                "Valet parking",
                "Private Parking",
                "Parking garage",
                "Restaurant",
                "Contactless check-in/out",
                "Parking",
                "ATM on site",
            ],
            "check_in_time": "16:00:00",
            "check_out_time": "11:00:00",
            "within_policy": False,
            "within_or_out_policy_reason": "reason why.",
            "rooms": [
                {
                    "id": 2971342,
                    "image": {
                        "alt": "Queen Room",
                        "src": "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=26106efc5ee51aeabec34417f8367f2ac06dfec52730c6cf636f497fdf806fd6&o=",
                    },
                    "room_photo": "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=19d514fa03dfb2b5ebeeccd8517d697f21f7301e7770afeb234955b54dc9ddc4&o=",
                    "price": 1105.32,
                    "pricePerNight": 160.65,
                    "taxAndFees": 302.07,
                    "priceExcludingFees": 803.25,
                    "no_nights": 5,
                    "option_title": "Queen Room",
                    "options": ["Free cancellation until 08/06"],
                    "cancellation_policy": {
                        "type": "free_cancellation",
                        "display_policy": "Free cancellation until August 06, 03:59",
                    },
                    "payment_policy": {
                        "supported_timings": ["pay_at_the_property", "pay_online_later", "pay_online_now"],
                        "display_label": "",
                        "policy": "",
                    },
                    "within_policy": False,
                    "within_or_out_policy_reason": "reason for out of policy.",
                    "amenities": [
                        "Air conditioning",
                        "TV",
                        "Desk",
                        "Ironing facilities",
                        "Hairdryer",
                        "Telephone",
                        "Refrigerator",
                        "Interconnecting room(s) available",
                        "Pay-per-view channels",
                        "Privacy curtain",
                        "Alarm clock",
                        "Radio",
                        "Cable channels",
                        "Tea/Coffee maker",
                        "Flat-screen TV",
                        "Video",
                        "Heating",
                        "Free toiletries",
                        "Sitting area",
                    ],
                    "action": "I choose hotel Capital Hilton, room id 2971342.",
                    "recommendation_reason": "Queen Room offers the lowest total price, pay-at-hotel, free cancellation, and all business essentials. Ideal for solo business travelers prioritizing value and flexibility.",
                }
            ],
            "rating": "8.4",
            "rating_description": "very good",
        },
        {
            "highlight": "5.0",
            "hotel": "The St. Regis Washington, D.C.",
            "id": "the-st.-regis-washington,-d.c.",
            "img": {
                "alt": "The St. Regis Washington, D.C.",
                "src": "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=493bb73ca890f15632c6231d9a79e992d69eb8b9ba4754ccdabc8283120cdbaa&o=",
            },
            "mapMarker": {
                "coordinates": {"lat": "38.902084", "lng": "-77.036243"},
                "address": "923 16th St NW, Washington, DC 20006, USA",
                "text": "The St. Regis Washington, D.C.",
            },
            "photos": [
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=68a3b65334fe21c1beaf9ee91f429503eb1f185d575d4c66e6b8a5243600f224&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/435688509.jpg?k=930d4de592398c6050927b642a1c5e0a6a68d669a8e649d9825c988f8f76cebc&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/435688507.jpg?k=1871c518c9fd6aa488ba147be652cfb6a9f2c66fdaa6c237eea93f0fe5286cc8&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/435688506.jpg?k=c39f85a59715087da9df595bf29000e124fef93321358f4f23e6cd6bc6a5c758&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/435560653.jpg?k=dca13e718eb1ce61678b9959de94b1f62b083fdf542cb746efc9c2d0562d9c90&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=983ee73280bc4ae8291504b8f0c411892dc6d9c54219f626b3030f46e1469d3a&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/673776504.jpg?k=5b26d7deaa46e6017f0a57d87df37c62114d918e633e1f3560a2dd22a3f2a533&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=493bb73ca890f15632c6231d9a79e992d69eb8b9ba4754ccdabc8283120cdbaa&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/449945775.jpg?k=7602344c12511dd10afcd036c9a93132e2438ad8c4dbb6d0c8a9a85e8e887b6b&o=",
                "https://q-xx.bstatic.com/xdata/images/hotel/max500/449945774.jpg?k=0ef544ed103acc68cf842482455cad00f39c822a28b660b0530e18009490ee83&o=",
            ],
            "price": None,
            "recommendationReasons": {
                "choice_characteristic": "Presidential prestige",
                "preference_alignment_reason": "A five-star property two blocks from the White House, offering luxury amenities and 24-hour business services.",
                "distinction_reason_one": "Butler service",
                "distinction_reason_two": "Historic presidential favorite",
            },
            "hotel_class": "5.0",
            "amenities": [
                "Terrace",
                "Express check-in/out",
                "Ironing service",
                "Suit press",
                "Dry cleaning",
                "Bar",
                "Electric vehicle charging station",
                "Breakfast in the room",
                "Accessible parking",
                "Free Wifi",
                "Parking on site",
                "Currency exchange",
                "Parking",
                "Fax/Photocopying",
                "Hypoallergenic room available",
                "Car rental",
                "Concierge",
                "Meeting/Banquet facilities",
                "Valet parking",
                "Restaurant",
                "Business center",
                "Special diet meals (on request)",
                "Private check-in/out",
                "Fitness center",
                "Contactless check-in/out",
            ],
            "check_in_time": "16:00:00",
            "check_out_time": "11:00:00",
            "within_policy": True,
            "within_or_out_policy_reason": "reason why.",
            "rooms": [
                {
                    "id": 4595236,
                    "image": {
                        "alt": "Superior, Guest room, 1 King",
                        "src": "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=493bb73ca890f15632c6231d9a79e992d69eb8b9ba4754ccdabc8283120cdbaa&o=",
                    },
                    "room_photo": "https://q-xx.bstatic.com/xdata/images/hotel/max500/*********.jpg?k=983ee73280bc4ae8291504b8f0c411892dc6d9c54219f626b3030f46e1469d3a&o=",
                    "price": 2053.47,
                    "pricePerNight": 354.2,
                    "taxAndFees": 282.47,
                    "priceExcludingFees": 1771.0,
                    "no_nights": 5,
                    "option_title": "Superior, Guest room, 1 King",
                    "options": [],
                    "cancellation_policy": {"type": "non_refundable", "display_policy": "Non-refundable"},
                    "payment_policy": {
                        "supported_timings": ["pay_online_now"],
                        "display_label": "Pay now",
                        "policy": "",
                    },
                    "within_policy": True,
                    "within_or_out_policy_reason": "reason for within policy.",
                    "amenities": [
                        "Air conditioning",
                        "TV",
                        "CD player",
                        "Desk",
                        "Private bathroom",
                        "Telephone",
                        "Pay-per-view channels",
                        "High chair",
                        "Hand sanitizer",
                        "Alarm clock",
                        "Radio",
                        "Tea/Coffee maker",
                        "Bathtub or shower",
                        "Slippers",
                        "Coffee machine",
                        "Wake-up service/Alarm clock",
                        "Bathrobe",
                        "Free toiletries",
                        "Free gym access",
                        "Iron",
                        "Concierge services",
                    ],
                    "action": "I choose hotel The St. Regis Washington, D.C., room id 4595236. Pay online now.",
                    "recommendation_reason": "Superior King offers the lowest total price, king bed, full amenities, and is ideal for business travelers seeking value and comfort in downtown DC.",
                }
            ],
            "rating": "8.0",
            "rating_description": "very good",
        },
    ],
}


if __name__ == "__main__":
    main()
