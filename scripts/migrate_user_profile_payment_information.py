from server.schemas.user_profile.payment_information import PaymentInformationRequest
from server.utils.mongo_connector import payment_profile_collection, user_profile_payment_information_collection


async def migrate():
    payment_profiles = await payment_profile_collection.find({}).to_list(None)

    for profile in payment_profiles:
        user_id = profile.get("users_id", None)

        try:
            assert user_id is not None

            payment_information = PaymentInformationRequest(**profile)

            await user_profile_payment_information_collection.update_one(
                {"users_id": user_id}, {"$set": {"users_id": user_id, **payment_information.model_dump()}}, upsert=True
            )
            print(f"Updated payment information profile for user {user_id}")
        except Exception:
            print(f"Failed to update payment information profile for user {user_id}")
            pass

    print("Done!")


if __name__ == "__main__":
    import asyncio

    asyncio.run(migrate())
