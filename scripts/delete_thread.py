#!/usr/bin/env python3
"""
Script to delete a chat thread and its associated checkpoints.
Requires user confirmation before deletion.
"""

import asyncio
import logging
import os
import sys
from typing import Optional

from server.utils.settings import settings

# Add the project root to Python path before imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Import after path setup
# ruff: noqa: E402
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.database.models.user import User

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


async def get_thread_info(thread_id: int) -> Optional[tuple[ChatThread, User]]:
    """
    Get thread and associated user information.
    Returns None if either thread or user is not found.
    """
    thread = await ChatThread.from_id(thread_id, include_deleted=True)
    if not thread:
        logger.error(f"No chat thread found with ID {thread_id}")
        return None

    user = await User.from_id(thread.users_id)
    if not user:
        logger.error(f"No user found for thread {thread_id} (user_id: {thread.users_id})")
        return None

    return thread, user


async def delete_thread(thread_id: int) -> bool:
    """
    Delete a thread and its checkpoints.
    Returns True if deletion was successful, False otherwise.
    """
    try:
        # This method handles both checkpoint and thread deletion
        await Checkpoint.delete_threads([thread_id])
        logger.info(f"Successfully deleted thread {thread_id} and its checkpoints")
        return True
    except Exception as e:
        logger.error(f"Failed to delete thread {thread_id}: {str(e)}")
        return False


async def main():
    """
    Main function that handles the thread deletion workflow.
    """
    try:
        print(f"Delete Chat Thread Tool for {settings.OTTO_ENV} Environment\n\n")
        # Get thread ID from user
        thread_id_str = input("Enter thread ID to delete: ")
        try:
            thread_id = int(thread_id_str)
        except ValueError:
            logger.error(f"Invalid thread ID format: {thread_id_str}")
            return

        # Get thread and user info
        result = await get_thread_info(thread_id)
        if not result:
            return
        thread, user = result

        # Confirm deletion
        print("\nThread details:")
        print(f"Title: {thread.title}")
        print(f"User email: {user.email}")
        print(f"Created date: {thread.created_date}")
        if thread.is_deleted:
            print("Note: This thread is already marked as deleted")

        confirm = input("\nAre you sure you want to delete this thread? (y/N) ")
        if confirm.lower() != "y":
            print("Aborting deletion.")
            return

        # Perform deletion
        if await delete_thread(thread_id):
            print(f"\nSuccessfully deleted thread {thread_id}")
        else:
            print(f"\nFailed to delete thread {thread_id}")

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
