"""Github action: Annotate the total pass rate of the agent tests."""

import json

report_file = "pytest_report.json"
try:
    with open(report_file) as f:
        data = json.load(f)
        total_tests = data["summary"]["total"]
        passed_tests = data["summary"]["passed"]
        pass_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"::notice title=Agent Testing::Pass Rate: {pass_rate:.2f}%, {total_tests} : {passed_tests}")
except FileNotFoundError:
    print(f"Error: {report_file} not found.")
    exit(1)
except json.JSONDecodeError:
    print(f"Error: Failed to parse {report_file}.")
    exit(1)
