# import asyncio
# import json
# from copy import deepcopy

# from dotenv import load_dotenv

# from server.database.models.user import User

# # from server.utils.spotnana_api import spotnana_api
# from virtual_travel_agent.flights_tools import FlightSearchTools

# load_dotenv()


# async def inbound_search(
#     outbound_flight_params: str,
#     search_id: str,
#     selected_outbound_flight_id: str,
#     flight_option: dict,
# ):
#     flight_id = f"{flight_option['airline_code']}{flight_option['flight_number']}"

#     inbound_flight_params = deepcopy(outbound_flight_params)
#     inbound_flight_params["initiate_search"] = False
#     inbound_flight_params["do_outbound_flight_search"] = False
#     inbound_flight_params["do_return_flight_search"] = True
#     inbound_flight_params["search_id"] = search_id
#     inbound_flight_params["selected_outbound_flight_id"] = selected_outbound_flight_id

#     print(f"inbound search for {flight_id}/{selected_outbound_flight_id} started!")
#     flight_options_str = FlightSearchTools.search_flights_spotnana(flight_params=json.dumps(inbound_flight_params))
#     with open(f"./scripts/{flight_id}-{selected_outbound_flight_id}.json", "+wt") as fp:
#         json.dump(json.loads(flight_options_str), fp)


# async def main():
#     print("Hey ...")
#     user: User = await User.from_email("<EMAIL>")
#     print(f"{user.first_name}!")
#     _ = FlightSearchTools(user=user)  # required to initialize the auth token

#     outbound_flight_params = {
#         "last_message_topic": "Flight Booking",
#         "message_explanation": "The traveler wants to book a flight to Phoenix from November 18 to November 22.",
#         "agent_response": "Searching for outbound flights from Seattle to Phoenix on November 18 and return flights on November 22.",
#         "departure_city": "Seattle",
#         "departure_airport_code": "SEA",
#         "is_departure_iata_city_code": False,
#         "arrival_city": "Phoenix",
#         "arrival_airport_code": "PHX",
#         "is_arrival_iata_city_code": False,
#         "outbound_date": "2024-11-18",
#         "return_date": "2024-11-22",
#         "preferred_airline_codes": ["DL"],
#         "preferred_outbound_times": None,
#         "preferred_return_times": None,
#         "prefers_non_stop": True,
#         "do_flight_search": True,
#         "selected_outbound_flight_id": None,
#         "selected_return_flight_id": None,
#         "selected_fare_option_id": None,
#         "flight_parameters_provided": True,
#         "other_actions": False,
#         "do_outbound_flight_search": True,
#         "do_return_flight_search": False,
#         "get_fare_options": False,
#         "do_flight_booking": False,
#         "flights_validated": False,
#         "initiate_search": True,
#         "search_id": None,
#     }
#     # 1. Outbound search
#     print("outbound search started!")
#     flight_options_str = FlightSearchTools.search_flights_spotnana(flight_params=json.dumps(outbound_flight_params))
#     flight_options = json.loads(flight_options_str)
#     search_id = flight_options["search_id"]
#     flight_to_option_dict = {}
#     flight_token_dict = {}
#     for flight_option in flight_options["flight_choices"]:
#         flight_id = f"{flight_option['airline_code']}{flight_option['flight_number']}"
#         options: list = flight_to_option_dict.get(flight_id, [])
#         options.append(flight_option["id_token_key"])
#         flight_to_option_dict[flight_id] = options
#         flight_token_dict[flight_option["id_token_key"]] = flight_option

#     # 2. pick 2 fare options per flight, use same search_id
#     inbound_search_tasks = []
#     for flight_id in flight_to_option_dict.keys():
#         for flight_token in flight_to_option_dict[flight_id][:2]:
#             flight_option = flight_token_dict[flight_token]
#             inbound_search_tasks.append(inbound_search(outbound_flight_params, search_id, flight_token, flight_option))
#     await asyncio.gather(*inbound_search_tasks)
#     print("all done!")


# asyncio.run(main())
