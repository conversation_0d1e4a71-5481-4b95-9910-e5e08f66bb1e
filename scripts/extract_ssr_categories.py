#!/usr/bin/env python3
import asyncio
import json
from typing import Any, Dict, List

from server.utils.logger import logger
from server.utils.settings import settings
from server.utils.spotnana_api import spotnana_api


async def get_all_ssr_categories() -> List[Dict[str, Any]]:
    """
    Get all Special Service Request categories from the Spotnana API.
    """
    try:
        response = await spotnana_api.get(f"{settings.SPOTNANA_HOST}/v2/air/special-service-requests")
        return response.get("specialServiceRequests", [])
    except Exception as e:
        logger.error(f"Error getting SSR categories: {str(e)}")
        return []


async def get_category_details(category_type: str) -> List[Dict[str, Any]]:
    """
    Get detailed information for a specific SSR category.
    """
    try:
        response = await spotnana_api.get(
            f"{settings.SPOTNANA_HOST}/v2/air/special-service-requests/categories", params={"category": category_type}
        )
        return response.get("specialServiceRequests", [])
    except Exception as e:
        logger.error(f"Error getting details for category {category_type}: {str(e)}")
        return []


async def main():
    output_data = {"categories": {}, "metadata": {"generated_at": "", "api_host": settings.SPOTNANA_HOST}}

    all_ssrs = await get_all_ssr_categories()
    if not all_ssrs:
        logger.error("Failed to retrieve any SSR categories")
        return

    category_types = set()
    for ssr in all_ssrs:
        if "category" in ssr and "type" in ssr["category"]:
            category_types.add(ssr["category"]["type"])

    logger.info(f"Found {len(category_types)} unique category types")

    for category_type in category_types:
        category_details = await get_category_details(category_type)
        if category_details:
            category_description = (
                category_details[0]["category"]["description"] if category_details else "No description"
            )

            output_data["categories"][category_type] = {"description": category_description, "subcategories": []}

            for detail in category_details:
                if "subCategory" in detail:
                    subcategory_info = {
                        "code": detail.get("code", ""),
                        "subcategory": detail.get("subCategory", ""),
                        "isFreeTextMandatory": detail.get("isFreeTextMandatory", False),
                        "isFreeTextAllowed": detail.get("isFreeTextAllowed", False),
                    }
                    output_data["categories"][category_type]["subcategories"].append(subcategory_info)

    from datetime import datetime

    output_data["metadata"]["generated_at"] = datetime.now().isoformat()

    output_filename = "./data/special_services.json"
    with open(output_filename, "w") as f:
        json.dump(output_data, f, indent=2)

    logger.info(f"SSR categories data saved to {output_filename}")
    print(f"SSR categories data saved to {output_filename}")


if __name__ == "__main__":
    asyncio.run(main())
