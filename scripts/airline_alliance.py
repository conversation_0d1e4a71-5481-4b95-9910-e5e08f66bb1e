"""Generated by GPT4o"""

import json

# Load the airline_alliance.json and airlines_program.json files
with open("scripts/airline_alliance.json", "r") as alliance_file:
    alliances_data = json.load(alliance_file)

with open("scripts/airlines_program.json", "r") as program_file:
    programs_data = json.load(program_file)

# Create a mapping of airline codes to their alliance and member details
alliance_mapping = {}
for alliance in alliances_data:
    alliance_name = alliance.get("Alliance")
    members = alliance.get("Members", [])
    for member in members:
        iata_code = member.get("IATACode")
        if iata_code:
            alliance_mapping[iata_code] = {
                "allianceName": alliance_name if alliance_name else None,
                "allies": [m.get("IATACode") for m in members if m.get("IATACode") != iata_code],
            }

# Generate the final JSON structure
output_data = {}
for program in programs_data:
    airline_code = program.get("airlineCode")
    if airline_code:
        output_data[airline_code] = {
            "airline_code": airline_code,
            "airline_name": program.get("airlineName"),
            "frequent_flyer_program": program.get("programName"),
            "alliance_name": alliance_mapping.get(airline_code, {}).get("allianceName"),
            "allies": alliance_mapping.get(airline_code, {}).get("allies", []),
        }

# Save the output to a JSON file
output_file_path = "data/airline_alliance_map.json"
with open(output_file_path, "w") as output_file:
    json.dump(output_data, output_file, indent=4)

output_file_path
