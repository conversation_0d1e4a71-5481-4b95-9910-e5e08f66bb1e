"""Read "busiest_airports_top1000_full.csv" and generate a dictionary from IATA to city name using AirportToShortCityName function."""

import csv

from baml_client import b
from server.utils.logger import logger
from server.utils.settings import settings


async def airport_to_city():
    """
    Reads the "busiest_airports_top1000_full.csv" file and generates a dictionary
    mapping IATA codes to city names using the AirportToShortCityName function.
    And output the dictionary to the console.
    """
    with open("data/busiest_airports_top1000_full.csv", "r") as file:
        reader = csv.DictReader(file)
        with open("data/airport_to_city.csv", "w") as target_file:
            writer = csv.writer(target_file)
            writer.writerow(["IATA Code", "City Name"])
            for row in reader:
                iata_code = row["Code (IATA)"]
                city_name = await b.AirportToShortCityName(iata_code)
                writer.writerow([iata_code, city_name])
                logger.info(f"Processed {iata_code} -> {city_name}")
    logger.info("Finished processing all airports.")


if __name__ == "__main__":
    import asyncio

    print(f"Generating airport to city name with {settings.OTTO_ENV} Environment\n\n")
    asyncio.run(airport_to_city())
