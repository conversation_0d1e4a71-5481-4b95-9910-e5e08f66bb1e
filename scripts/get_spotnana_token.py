#!/usr/bin/env python3
import asyncio
import json
from datetime import datetime

from server.utils.spotnana_api import spotnana_api


async def main():
    # Force token refresh by clearing existing token
    spotnana_api.token_info = {}
    await spotnana_api.authorize()

    # Convert epoch to human readable format
    expires_at_epoch = spotnana_api.token_info["expire"]
    expires_at_readable = datetime.fromtimestamp(expires_at_epoch).isoformat()

    # Print token info as JSON
    print(
        json.dumps(
            {
                "token": spotnana_api.token_info["token"],
                "expires_at": expires_at_readable,
                "expires_at_epoch": expires_at_epoch,
            },
            indent=2,
        )
    )


if __name__ == "__main__":
    asyncio.run(main())
