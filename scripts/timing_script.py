import os
import re
import subprocess
import time

import matplotlib.pyplot as plt

# Directory containing the scripts
scripts_dir = "./scripts"

# Find all scripts matching the pattern "timing_<title>.sh"
scripts = {}
pattern = re.compile(r"timing_target_(.+)\.sh")

for filename in os.listdir(scripts_dir):
    match = pattern.match(filename)
    if match:
        title = match.group(1)  # Extract title from the filename
        scripts[title] = os.path.join(scripts_dir, filename)

if not scripts:
    print("No scripts found. \nMake sure they are named as 'timing_target_<title>.sh'")
    exit()

print("Found the following scripts:")
for title, path in scripts.items():
    print(f"\t{title}: {path}")

# Number of runs per script
num_runs = 12

# Collect execution times
execution_times = {name: [] for name in scripts}

print(f"Interlaced execution: Running each script {num_runs} times alternately...")

# Interlace runs (alternating between different scripts)
for i in range(num_runs):
    for name, script in scripts.items():
        start_time = time.time()
        subprocess.run(["bash", script], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, check=True)
        end_time = time.time()
        execution_times[name].append(end_time - start_time)
    print(f"Run {i + 1}/{num_runs} completed.")

# Create a side-by-side boxplot with custom styling
plt.figure(figsize=(8, 5))

boxprops = dict(linestyle="-", linewidth=1.5, color="blue")
medianprops = dict(linestyle="-", linewidth=2, color="red")
whiskerprops = dict(linestyle="--", linewidth=1.2, color="black")
capprops = dict(linewidth=1.5, color="black")
flierprops = dict(marker="o", color="green", alpha=0.5)

# Create boxplot
bp = plt.boxplot(
    list(execution_times.values()),
    label=list(execution_times.keys()),
    patch_artist=True,
    boxprops=boxprops,
    medianprops=medianprops,
    whiskerprops=whiskerprops,
    capprops=capprops,
    flierprops=flierprops,
)

plt.ylabel("Execution Time (seconds)")
plt.title("Execution Time Comparison (Flight Validation Stage)")

# Save the output in ./scripts/
output_file = os.path.join(scripts_dir, "execution_time_comparison.png")
plt.savefig(output_file)
print(f"\nBoxplot saved as {output_file}")

# Show the plot (optional)
plt.show()
