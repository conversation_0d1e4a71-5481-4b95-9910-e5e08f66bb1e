import argparse
import async<PERSON>
import json
import os
import subprocess
import sys
from typing import Any, Dict, cast

import requests
import websockets

from server.database.models.user import User
from server.dependencies import manager
from server.utils.logger import logger

ca_root = subprocess.check_output(["mkcert", "-CAROOT"]).decode("utf-8").strip()
mkcert_ca_bundle_path = os.path.join(ca_root, "rootCA.pem")


async def get_user_data(user_id: int) -> tuple[Dict[str, str], str, str]:
    """
    Retrieves user data and creates a JWT token for authentication.

    Args:
        user_id: The ID of the user to create a token for

    Returns:
        A tuple containing (user data, access token, refresh token)
    """
    user = await User.from_id(user_id)
    if not user:
        raise ValueError(f"No user found with ID: {user_id}")

    payload = {
        "sub": str(user_id),
        "first_name": user.first_name,
        "last_name": user.last_name,
    }

    access_token, refresh_token = manager.create_custom_jwt(payload)

    user_data = {"id": user_id, "first_name": user.first_name, "last_name": user.last_name, "email": user.email}

    return user_data, access_token, refresh_token


async def create_new_trip(access_token: str, refresh_token: str) -> Dict[str, Any]:
    """
    Creates a new trip using the API endpoint.

    Args:
        access_token: JWT token for authentication
        refresh_token: JWT refresh token

    Returns:
        Response from the API containing trip details
    """
    url = "https://api.local.dev.otto-demo.com:8000/api/trips/new"

    response = requests.post(
        url,
        headers={"Content-Type": "application/json"},
        cookies={
            "access_token": access_token,
            "refresh_token": refresh_token,
        },
        verify=mkcert_ca_bundle_path,
    )
    if response.status_code != 200:
        raise Exception(f"Failed to create trip: {response.text}")

    logger.info(f"Trip created successfully: {response.json()}")

    return response.json()


async def post_trip_message(trip_id: int, access_token: str, refresh_token: str, text: str) -> Dict[str, Any]:
    """
    Posts a message to the trip using the HTTP endpoint.

    Args:
        trip_id: The trip ID
        access_token: JWT token for authentication
        text: Message text to send

    Returns:
        Response from the API
    """
    url = f"https://api.local.dev.otto-demo.com:8000/api/trips/{trip_id}/messages"
    payload = {
        "type": "prompt",
        "text": text,
        "extra": {},
    }

    response = requests.post(
        url,
        headers={
            "Content-Type": "application/json",
        },
        cookies={
            "access_token": access_token,
            "refresh_token": refresh_token,
        },
        json=payload,
        verify=mkcert_ca_bundle_path,
    )

    if response.status_code != 200:
        raise Exception(f"Failed to post message: {response.text}")

    logger.info(f"Message posted successfully: {response.json()}")
    return response.json()


async def chat_via_websocket(access_token: str, user_id: int, trip_id: int) -> None:
    """
    Connects to the websocket, sends trip initialization and a message.

    Args:
        access_token: JWT token for authentication
        user_id: The user ID
        trip_id: The trip ID to initialize
    """
    uri = "wss://api.local.dev.otto-demo.com:8000/api/ws"

    async with websockets.connect(uri, subprotocols=["Authorization", access_token]) as websocket:  # type: ignore
        logger.info(f"Connected to websocket for user {user_id}")

        trip_init_message = {"type": "trip_init", "tripId": trip_id}
        await websocket.send(json.dumps(trip_init_message))
        logger.info(f"Sent trip_init message for trip {trip_id}")

        response = await websocket.recv()
        logger.info(f"Received response: {response}")

        prompt_message = {
            "type": "prompt",
            "tripId": trip_id,
            "text": "need one way flight to SF first monday next month",
        }
        await websocket.send(json.dumps(prompt_message))
        logger.info("Sent prompt message")

        await asyncio.sleep(5)


async def main():
    parser = argparse.ArgumentParser(description="Create JWT, trip, and send websocket messages")
    parser.add_argument("user_id", type=int, help="User ID to create JWT for")
    args = parser.parse_args()

    try:
        user_data, access_token, refresh_token = await get_user_data(args.user_id)
        logger.info(f"Created JWT token for user: {user_data['first_name']} {user_data['last_name']}")

        trip_data = await create_new_trip(access_token, refresh_token)
        trip_id = trip_data["id"]
        logger.info(f"Created new trip with ID: {trip_id}")

        # await chat_via_websocket(access_token, args.user_id, trip_id)

        await post_trip_message(
            trip_id, access_token, refresh_token, "need one way flight to SF first monday next month"
        )

        logger.info("Script executed successfully")

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
