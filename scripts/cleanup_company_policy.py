#!/usr/bin/env python3
import asyncio
import sys
from pathlib import Path

from sqlalchemy import delete

root_dir = str(Path(__file__).resolve().parent.parent)
sys.path.append(root_dir)

from server.database.models.chat_thread import ChatThread  # noqa: E402
from server.database.models.checkpoint import Checkpoint  # noqa: E402
from server.database.models.user import User, UserRole  # noqa: E402
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy  # noqa: E402
from server.utils.pg_connector import async_session  # noqa: E402
from server.utils.settings import settings  # noqa: E402


async def main():
    while True:
        try:
            organization_id = int(input("Enter the organization_id to reset travel policy for: ").strip())
            break
        except ValueError:
            print(f"Invalid organization_id. Please enter a valid organization ID (int) in {settings.OTTO_ENV}.")

    try:
        company_admin = await User.from_organization_id_and_role(organization_id, UserRole.company_admin)
        if not company_admin:
            print(f"No company_admin found for organization_id: {organization_id}")
            return False

        print("\nFound company admin:")
        print(f"Name:            {company_admin.first_name} {company_admin.last_name}")
        print(f"Email:           {company_admin.email}")
        print(f"User ID:         {company_admin.id}")
        print(f"Organization ID: {company_admin.organization_id}")

        travel_policy_threads = await ChatThread.from_user_id_and_title(
            company_admin.id, settings.TRAVEL_POLICY_THREAD_TITLE
        )

        if not travel_policy_threads:
            print(f"No travel policy thread found for company admin {company_admin.email}")
            travel_policy_thread = None
        else:
            travel_policy_thread = travel_policy_threads[0][0]
            print(f"Found travel policy thread ID: {travel_policy_thread.id}")

        travel_policy_record = await UserCompanyTravelPolicy.from_user_id(company_admin.id)
        if travel_policy_record:
            print(f"Found travel policy record of company admin {company_admin.email}")
        else:
            print("No travel policy record found for company admin")

        checkpoints_to_delete = []
        if travel_policy_thread:
            checkpoints = await Checkpoint.from_thread_id(travel_policy_thread.id)
            if checkpoints and len(checkpoints) > 1:
                checkpoints_to_delete = [checkpoint[0].id for checkpoint in checkpoints[1:]]
                print(f"Found {len(checkpoints)} messages in travel policy thread")
                print(f"Will delete {len(checkpoints_to_delete)} messages (keeping the earliest)")
            elif checkpoints and len(checkpoints) == 1:
                print("Found only 1 message in travel policy thread - will keep it")
            else:
                print("No messages found in travel policy thread")

        print("\n" + "=" * 60)
        print("CLEANUP SUMMARY:")
        print("=" * 60)
        print(f"Organization ID: {organization_id}")
        print(f"Company Admin:   {company_admin.email} (ID: {company_admin.id})")
        if travel_policy_thread:
            print(f"Travel Policy Thread: {travel_policy_thread.id}")
            print(f"Messages to delete: {len(checkpoints_to_delete)}")
        else:
            print("Travel Policy Thread: None found")
        print(f"Travel Policy Record: {'Will delete' if travel_policy_record else 'None found'}")
        print("=" * 60)

        if not checkpoints_to_delete and not travel_policy_record:
            print("\nNothing to reset for this organization's travel policy.")
            return True

        print("\nWARNING: This action cannot be undone!")
        print(
            f"You are about to reset company travel policy for organization {organization_id} in the {settings.OTTO_ENV} environment."
        )
        confirm = input("\nType 'YES' to confirm reset: ")
        if confirm != "YES":
            print("Aborting reset.")
            return False

        async with async_session() as session:
            async with session.begin():
                if checkpoints_to_delete:
                    query = delete(Checkpoint).where(Checkpoint.id.in_(checkpoints_to_delete))
                    await session.execute(query)
                    print(f"Successfully deleted {len(checkpoints_to_delete)} messages from travel policy thread")

                if travel_policy_record:
                    await travel_policy_record.delete()
                    print(f"Successfully deleted travel policy record for company admin {company_admin.email}")

        print(f"Successfully completed reset for organization {organization_id}")
        return True

    except Exception as e:
        print(f"Error during reset: {str(e)}")
        return False


if __name__ == "__main__":
    asyncio.run(main())
