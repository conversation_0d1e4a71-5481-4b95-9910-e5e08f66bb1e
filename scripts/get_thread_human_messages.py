#!/usr/bin/env python
"""
Script to retrieve human messages and user preferences for a given thread_id.

Usage:
    python -m scripts.get_thread_human_messages <thread_id>
"""

import asyncio
import json
import sys
from typing import Any, Dict, List

from langchain_core.messages import messages_from_dict

from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.database.models.user import User
from server.services.user.user_preferences import get_user_preferences


async def get_human_messages_by_thread_id(thread_id: int) -> List[Dict[str, Any]]:
    """
    Retrieve human messages for a given thread_id.

    Args:
        thread_id: The ID of the thread to retrieve messages from

    Returns:
        A list of human messages
    """
    # Get the thread
    thread = await ChatThread.from_id(thread_id)
    if not thread:
        print(f"Thread with ID {thread_id} not found.")
        return []

    # Get the user
    user = await User.from_id(thread.users_id)
    if not user:
        print(f"User with ID {thread.users_id} not found.")
        return []

    print(f"Thread: {thread.title} ({thread_id})")
    print(f"User: {user.first_name} {user.last_name} ({user.email}) ({user.id})")

    # Get user preferences
    preferences = await get_user_preferences(thread.users_id)
    if preferences:
        print("\nUser Preferences:")
        print(json.dumps(preferences.model_dump(), indent=2))
    else:
        print("\nNo user preferences found.")

    # Get messages
    items = await Checkpoint.from_thread_id(thread_id=thread_id)
    if not items:
        print("\nNo messages found for this thread.")
        return []

    # Parse messages and filter for human messages only
    human_messages = []
    print("\nHuman Messages:")
    for item in items:
        # Handle both string and dictionary data types for backward compatibility
        message_data = item.Checkpoint.data
        if isinstance(message_data, str):
            message_data = json.loads(message_data)
        if message_data.get("type") == "human":
            # Create a message object
            messages = messages_from_dict([message_data])
            if messages:
                message = messages[0]
                human_messages.append(
                    {
                        "id": item.Checkpoint.id,
                        "created_date": item.Checkpoint.created_date.isoformat(),
                        "content": message.content,
                        "additional_kwargs": message.additional_kwargs,
                    }
                )
                print(f"\n[{item.Checkpoint.created_date.isoformat()}]")
                print(f"Content: {message.content}")
                if message.additional_kwargs:
                    print(f"Additional data: {json.dumps(message.additional_kwargs, indent=2)}")

    return human_messages


async def main():
    if len(sys.argv) != 2:
        print("Usage: python -m scripts.get_thread_human_messages <thread_id>")
        sys.exit(1)

    try:
        thread_id = int(sys.argv[1])
    except ValueError:
        print("Error: thread_id must be an integer")
        sys.exit(1)

    await get_human_messages_by_thread_id(thread_id)


if __name__ == "__main__":
    asyncio.run(main())
