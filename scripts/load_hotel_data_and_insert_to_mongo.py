import asyncio
import json

from bson.objectid import ObjectId
from motor.motor_asyncio import (
    AsyncIOMotorClient,
    AsyncIOMotorCollection,
    AsyncIOMotorDatabase,
)
from pymongo import UpdateOne


def convert_objectid(obj):
    if isinstance(obj, ObjectId):
        return str(obj)
    return obj


async def update_hotel_data(collection: AsyncIOMotorCollection):
    # Counters for tracking updates
    updated_count = 0
    not_found_count = 0

    # Batch operations containers
    batch_updates = []
    batch_inserts = []
    BATCH_SIZE = 1000  # Adjust this value based on your needs

    # First, load all existing MongoDB documents into memory
    # Only fetch the fields we need: _id, id, and geo_location
    existing_hotels = {}
    cursor = collection.find({}, {"_id": 1, "id": 1, "geo_location": 1})
    async for doc in cursor:
        existing_hotels[doc["id"]] = {"_id": doc["_id"], "geo_location": doc.get("geo_location")}

    # Read and process the JSONL file
    with open("./accomodations_details_cache.txt", "r") as file:
        for line in file:
            try:
                # Parse JSON from each line
                hotel_data = json.loads(line.strip())
                hotel_id = hotel_data["id"]

                if hotel_id in existing_hotels:
                    # Get the stored MongoDB data
                    mongo_data = existing_hotels[hotel_id]

                    # Update the document while keeping _id and geo_location
                    hotel_data["_id"] = mongo_data["_id"]
                    if mongo_data.get("geo_location"):
                        hotel_data["geo_location"] = mongo_data["geo_location"]

                    # Add to batch updates
                    batch_updates.append(UpdateOne({"_id": mongo_data["_id"]}, {"$set": hotel_data}, upsert=False))

                else:
                    # Create geo_location for new hotels
                    if "location" in hotel_data and "coordinates" in hotel_data["location"]:
                        coords = hotel_data["location"]["coordinates"]
                        hotel_data["geo_location"] = {
                            "type": "Point",
                            "coordinates": [coords["longitude"], coords["latitude"]],
                        }
                    # Add to batch inserts
                    batch_inserts.append(hotel_data)
                    not_found_count += 1

                # Process batches when they reach the size limit
                if len(batch_updates) >= BATCH_SIZE:
                    if batch_updates:
                        result = await collection.bulk_write(batch_updates)
                        updated_count += result.modified_count
                        print(f"Batch updated {result.modified_count} documents, total updated: {updated_count}")
                    batch_updates = []

                if len(batch_inserts) >= BATCH_SIZE:
                    if batch_inserts:
                        result = await collection.insert_many(batch_inserts)
                        print(f"Batch inserted {len(result.inserted_ids)} documents, total inserted: {not_found_count}")
                    batch_inserts = []

            except json.JSONDecodeError as e:
                print(f"Error parsing JSON: {e}")
            except Exception as e:
                print(f"Error processing hotel: {e}")

    # Process remaining batches
    try:
        if batch_updates:
            result = await collection.bulk_write(batch_updates)
            updated_count += result.modified_count
            print(f"Final batch updated {result.modified_count} documents, total updated: {updated_count}")

        if batch_inserts:
            result = await collection.insert_many(batch_inserts)
            print(f"Final batch inserted {len(result.inserted_ids)} documents, total inserted: {not_found_count}")

    except Exception as e:
        print(f"Error in final batch processing: {e}")

    # Print summary
    print("\nUpdate Summary:")
    print(f"Total documents updated: {updated_count}")
    print(f"Documents not found (inserted): {not_found_count}")


async def main():
    try:
        MONGO_USER = "otto"
        MONGO_PASSWORD = "xxxx"
        MONGO_HOST_DEV = "***********"
        # MONGO_HOST_STG = "ec2-3-136-62-171.us-east-2.compute.amazonaws.com"
        # MONGO_HOST_PROD = "ec2-3-137-39-181.us-east-2.compute.amazonaws.com"
        MONGO_HOST = MONGO_HOST_DEV
        MONGO_PORT = "27017"
        MONGO_DATABASE = "otto_data"
        MONGO_DETAILS = f"mongodb://{MONGO_USER}:{MONGO_PASSWORD}@{MONGO_HOST}:{MONGO_PORT}/"

        mongo_client: AsyncIOMotorClient = AsyncIOMotorClient(MONGO_DETAILS)

        mongo_database: AsyncIOMotorDatabase = mongo_client.get_database(MONGO_DATABASE)
        hotels_data_cache_collection: AsyncIOMotorCollection = mongo_database.get_collection("hotels_data_cache")
        count = await hotels_data_cache_collection.count_documents({})
        print(count)
        await update_hotel_data(hotels_data_cache_collection)
    except Exception as e:
        print(f"Script error: {e}")
    finally:
        mongo_client.close()


if __name__ == "__main__":
    asyncio.run(main())
