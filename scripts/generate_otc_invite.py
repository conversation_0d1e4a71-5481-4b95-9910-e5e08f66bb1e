"""
Script to generate One-Time Code (OTC) invite codes.
"""

import argparse
import asyncio
import sys
from typing import List

from server.services.one_time_codes.manage import generate_otcs


async def generate_otc_invite_codes(group_name: str, count: int) -> List[str]:
    """Generate OTC invite codes and save them to the database."""
    return await generate_otcs(group_name, count)


def main():
    parser = argparse.ArgumentParser(description="Generate OTC invite codes")
    parser.add_argument("-g", "--group", type=str, required=True, help="Group name for the invite codes")
    parser.add_argument("-c", "--count", type=int, default=1, help="Number of codes to generate (default: 1)")

    args = parser.parse_args()

    if args.count < 1:
        print("Error: Count must be at least 1")
        sys.exit(1)

    codes = asyncio.run(generate_otc_invite_codes(args.group, args.count))

    print(f"Generated {len(codes)} OTC invite code(s) for group '{args.group}':")
    for code in codes:
        print(code)

    return 0


if __name__ == "__main__":
    sys.exit(main())
