from typing import Any

from baml_client.async_client import b


async def review_hardcoded_message(function: str, purpose: str, message_or_templated_message: str):
    response = await b.ReviewHardCodedMessages(
        function=function, purpose=purpose, message_or_templated_message=message_or_templated_message
    )
    return response


async def main():
    with open("/Users/<USER>/Downloads/messages_to_review.csv", "r") as csv_file:
        import csv

        csv_reader = csv.reader(csv_file)
        _ = next(csv_reader, None)  # Skip the header row if it exists
        result: list[list[Any]] = [
            [
                "Function",
                "Purpose",
                "Message or Templated Message",
                "Grammar Score",
                "Consistency Score",
                "Verbosity Score",
                "Tone Score",
                "Suggested Changes",
            ]
        ]
        # enumerate the rows in the CSV file, with index so can print progress
        for index, row in enumerate(csv_reader):
            if len(row) < 3:
                print(f"Skipping row with insufficient data: {row}")
                continue

            function, purpose, message_or_templated_message = row[0], row[1], row[2]
            response = await review_hardcoded_message(function, purpose, message_or_templated_message)
            # print progresss so far
            print(f"Processed {index + 1} rows so far...")

            result.append(
                [
                    function,
                    purpose,
                    message_or_templated_message,
                    response.grammar_score,
                    response.consistency_score,
                    response.verbosity_score,
                    response.tone_score,
                    response.suggested_changes,
                ]
            )
        # Write results to a new CSV file
        output_file = "/Users/<USER>/Downloads/reviewed_messages.csv"
        with open(output_file, "w", newline="") as output_csv_file:
            csv_writer = csv.writer(output_csv_file)
            csv_writer.writerows(result)


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
