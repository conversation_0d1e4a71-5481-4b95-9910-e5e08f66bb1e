import asyncio

from dotenv import load_dotenv

from flight_agent.flights_tools import FlightSearchTools
from server.utils.mongo_connector import bookings_collection

load_dotenv()


async def main():
    bookings = await bookings_collection.find({"type": "flight"}).to_list(length=None)

    for booking in bookings:
        booking_content = booking["content"]
        if "airline_confirmation_number" not in booking_content:
            if "confirmation_id" in booking_content and "trip_id" in booking_content:
                trip_id = booking_content["trip_id"]
                pnr_id = booking_content["confirmation_id"]
                print("existing_confirmation_id", pnr_id)
                print("existing_trip_id", trip_id)
                trip_details = await FlightSearchTools.get_trip_details_spotnana(trip_id)
                confirmation_number = FlightSearchTools.get_vendor_confirmation_number(trip_details)
                pnrs = trip_details.get("pnrs", [])
                if not pnrs:
                    return None

                first_pnr = pnrs[0]
                data = first_pnr.get("data", {})
                airpnr = data.get("airPnr", {})
                print("airPnr", airpnr)
                print("new_confirmation_id", confirmation_number)
                await bookings_collection.update_one(
                    {"thread_id": booking["thread_id"], "type": "flight"},
                    {"$set": {"content.airline_confirmation_number": confirmation_number}},
                )


asyncio.run(main())
