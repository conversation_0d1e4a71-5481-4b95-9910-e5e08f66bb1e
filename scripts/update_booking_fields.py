import argparse
import async<PERSON>
from typing import Any, Dict

from server.database.models.bookings import Booking
from server.utils.booking_utils import extract_booking_dates_and_status
from server.utils.logger import logger


async def update_booking_fields(dry_run: bool = False):
    """
    Update all bookings in the database to populate start_date, end_date, and status fields
    from the JSON content field.

    Args:
        dry_run: If True, only print what would be updated without actually updating the database
    """
    bookings = await Booking.from_query_batch({})
    logger.info(f"Found {len(bookings)} bookings to process")

    updated_count = 0
    for booking in bookings:
        if not booking.content:
            logger.warning(f"Booking ID {booking.id} has no content, skipping")
            continue

        logger.info(f"Processing booking ID {booking.id} of thread ID {booking.thread_id}")
        start_date, end_date, status = extract_booking_dates_and_status(booking.type, booking.content)

        update_dict: Dict[str, Any] = {}
        if start_date:
            update_dict["start_date"] = start_date

        if end_date:
            update_dict["end_date"] = end_date

        if not booking.status and status:
            update_dict["status"] = status.name

        if update_dict:
            if dry_run:
                logger.info(f"Would update booking ID {booking.id} with {update_dict}")
            else:
                await Booking.update_fields({"id": booking.id}, update_dict)
                logger.info(f"Updated booking ID {booking.id} with {update_dict}")
                updated_count += 1

    logger.info(f"Updated {updated_count} bookings")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Update booking fields with extracted dates and status")
    parser.add_argument("--dry-run", action="store_true", help="Only show what would be updated without making changes")
    args = parser.parse_args()

    asyncio.run(update_booking_fields(dry_run=args.dry_run))
