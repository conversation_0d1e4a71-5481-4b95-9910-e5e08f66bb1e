#!/usr/bin/env python3
import asyncio
import logging
import re
from typing import Any, Dict

from server.api.v1.endpoints.admin.organization import create_organization
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User
from server.schemas.user.organization import OrganizationCreate
from server.utils.logger import logger
from server.utils.settings import settings

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


def validate_email(email: str) -> bool:
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(pattern, email) is not None


def get_user_input() -> Dict[str, Any]:
    print("=== Organization Setup Script ===")
    print("This script will create a new organization and admin user.")
    print()

    company_name = input("Enter company name: ").strip()
    if not company_name:
        raise ValueError("Company name cannot be empty")

    domain_name = input("Enter domain name (e.g., example.com): ").strip().lower()
    if not domain_name:
        raise ValueError("Domain name cannot be empty")

    admin_preferred_name = input("Enter admin preferred name: ").strip()
    if not admin_preferred_name:
        raise ValueError("Admin preferred name cannot be empty")

    admin_email = input("Enter admin email: ").strip().lower()
    if not admin_email:
        raise ValueError("Admin email cannot be empty")
    if not validate_email(admin_email):
        raise ValueError("Invalid email format")

    return {
        "name": company_name,
        "domain": domain_name,
        "auto_approve_domain_users": True,
        "require_otp_login": True,
        "travel_policy": {},
        "additional_domains": [],
        "admin_email": admin_email,
        "admin_preferred_name": admin_preferred_name,
    }


async def create_user(admin_email: str, admin_preferred_name: str) -> User:
    existing_user = await User.from_email(admin_email)
    if existing_user:
        logging.info(f"User with email {admin_email} already exists (ID: {existing_user.id})")
        return existing_user

    default_avatar = f"{settings.SERVER_DNS}/static/default-avatar-icon.jpg"

    name_parts = admin_preferred_name.split(" ", 1)
    first_name = name_parts[0]
    last_name = name_parts[1] if len(name_parts) > 1 else ""

    new_user = User(email=admin_email, first_name=first_name, last_name=last_name, profile_picture=default_avatar)
    new_user.preferred_name = admin_preferred_name

    await User.new_user(new_user)
    logging.info(f"Created user: {admin_email} with preferred name: {admin_preferred_name}")

    return new_user


# def create_organization(user_input: Dict[str, str]) -> None:
#     if not settings.SERVER_DNS:
#         raise ValueError("SERVER_DNS setting is not configured")

#     url = f"{settings.SERVER_DNS}/api/admin/organizations"

#     request_body = {
#         "name": user_input["company_name"],
#         "domain": user_input["domain_name"],
#         "auto_approve_domain_users": True,
#         "require_otp_login": True,
#         "travel_policy": {},
#         "additional_domains": [],
#         "admin_email": user_input["admin_email"],
#     }

#     try:
#         response = requests.post(url, json=request_body, headers={"Content-Type": "application/json"}, timeout=30)

#         if response.status_code == 201:
#             org_data = response.json()
#             logging.info(f"Successfully created organization: {org_data['name']} (ID: {org_data['id']})")
#             logging.info(f"Domain: {org_data['domain']}")
#             logging.info("Admin user updated to company_admin role")
#         else:
#             logging.error(f"Failed to create organization. Status: {response.status_code}")
#             logging.error(f"Response: {response.text}")
#             raise Exception(f"Organization creation failed with status {response.status_code}")

#     except requests.exceptions.RequestException as e:
#         logging.error(f"HTTP request failed: {str(e)}")
#         raise


async def main():
    try:
        user_input = get_user_input()

        logger.info("\nReview the information:")
        logger.info(f"Company: {user_input['name']}")
        logger.info(f"Domain: {user_input['domain']}")
        logger.info(f"Admin Email: {user_input['admin_email']}")
        logger.info(f"Admin Name: {user_input['admin_preferred_name']}")
        logger.info(f"Environment: {settings.OTTO_ENV}")

        confirm = input("\nProceed with organization creation? (y/N): ").strip().lower()
        if confirm != "y":
            logger.info("Organization creation cancelled.")
            return

        admin_user = await create_user(user_input["admin_email"], user_input["admin_preferred_name"])

        await create_organization(OrganizationCreate.model_validate(user_input))

        # Create initial chat threads for the admin user
        await ChatThread.create_initial_chat_threads(admin_user.id)

        logger.info("\n✅ Organization setup completed successfully!")
        logger.info(f"Organization: {user_input['name']}")
        logger.info(f"Admin: {user_input['admin_preferred_name']} ({user_input['admin_email']})")

    except Exception as e:
        logging.error(f"Organization setup failed: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
