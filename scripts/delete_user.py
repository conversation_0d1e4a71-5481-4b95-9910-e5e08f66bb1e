import asyncio
import os
import sys
from pathlib import Path
from typing import Sequence, <PERSON><PERSON>

from sqlalchemy import Row

# Add project root to Python path
root_dir = str(Path(__file__).resolve().parent.parent)
sys.path.append(root_dir)

# Import server modules after path setup
from server.database.models.chat_thread import ChatThread  # noqa: E402
from server.database.models.user import User  # noqa: E402
from server.services.user.user_account_delete import delete_user_account  # noqa: E402


async def get_user_threads(user_id: int) -> Sequence[Row[Tuple[ChatThread]]]:
    try:
        return await ChatThread.from_user_id(user_id)
    except Exception as e:
        print(f"Error retrieving threads: {str(e)}")
        return []


async def main():
    # Get and validate environment
    while True:
        env = input("Enter environment [DEV/STG/LIVE]: ").upper()
        if env in ["DEV", "STG", "LIVE"]:
            break
        print("Invalid environment. Must be DEV, STG, or LIVE")

    os.environ["OTTO_ENV"] = env

    # Get and validate email
    while True:
        target_email = input("Enter the email of the user to delete: ").strip()
        if "@" in target_email and "." in target_email:
            break
        print("Invalid email format. Please enter a valid email address.")

    try:
        user = await User.from_email(target_email)
        if not user:
            print(f"No user found with email: {target_email}")
            return

        # Get threads
        user.threads = await get_user_threads(user.id)
    except Exception as e:
        print(f"Error finding user: {str(e)}")
        return False

    # Display user details for confirmation
    print("\nUser Details:")
    print("-" * 40)
    print(f"Name:            {user.first_name} {user.last_name}")
    print(f"Email:           {user.email}")
    print(f"Role:            {user.role}")
    print(f"Number of threads: {len(user.threads)}")
    print("-" * 40)

    # Get confirmation
    print("\nWARNING: This action cannot be undone!")
    print(f"You are about to delete user {user.email} in the {env} environment.")
    confirm = input("\nType 'YES' to confirm deletion: ")
    if confirm != "YES":
        print("Aborting deletion.")
        return False

    try:
        # Delete user and all associated data including Spotnana profile
        await delete_user_account(user.id, user.email)
        print(f"Successfully deleted user {user.email} and all associated data")
        return True
    except Exception as e:
        print(f"Error during deletion: {str(e)}")
        return False


if __name__ == "__main__":
    asyncio.run(main())
