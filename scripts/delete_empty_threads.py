"""
Script to:
1. Delete empty threads (threads without checkpoints) older than N days
2. Reset tutorial_completed flag for users with 1 or fewer non-special threads
   (excluding onboarding/preference/policy/future-trip threads)
"""

import argparse
import asyncio
import logging
import sys
from datetime import datetime, timedelta
from typing import List, Tuple

from sqlalchemy import select

from server.database.models import user
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.database.models.user import User, get_all_users
from server.utils.pg_connector import async_session
from server.utils.settings import settings

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


async def get_new_trip_threads_older_than(days: int) -> List[ChatThread]:
    """
    Get all new trip threads that are older than the specified number of days.

    Args:
        days: Number of days to look back

    Returns:
        List of ChatThread objects that is a new trip and older than the specified days
    """
    cutoff_date = datetime.now() - timedelta(days=days)

    async with async_session() as session:
        async with session.begin():
            query = select(ChatThread).where(
                (ChatThread.created_date < cutoff_date)
                & ((ChatThread.is_deleted == False) | (ChatThread.is_deleted == None))  # noqa
                & (ChatThread.title.in_([settings.NEW_TRIP_TITLE, settings.GETTING_STARTED_TITLE]))
            )
            results = await session.execute(query)
            return [row[0] for row in results.fetchall()]


async def is_thread_empty(thread_id: int) -> bool:
    """
    Check if a thread is empty (has no checkpoints).

    Args:
        thread_id: ID of the thread to check

    Returns:
        True if the thread has no checkpoints, False otherwise
    """
    checkpoints = await Checkpoint.from_thread_id(thread_id)
    return len(checkpoints) == 0 if checkpoints else True


async def delete_thread(thread_id: int) -> bool:
    """
    Delete a thread and its checkpoints.

    Args:
        thread_id: ID of the thread to delete

    Returns:
        True if deletion was successful, False otherwise
    """
    try:
        await Checkpoint.delete_threads([thread_id])
        logger.info(f"Successfully deleted thread {thread_id}")
        return True
    except Exception as e:
        logger.error(f"Failed to delete thread {thread_id}: {str(e)}")
        return False


async def reset_user_tutorial(user_id: int) -> bool:
    """
    Reset the tutorial_completed flag for a user.

    Args:
        user_id: ID of the user to reset

    Returns:
        True if reset was successful, False otherwise
    """
    try:
        user = await User.from_id(user_id)
        if user:
            await user.reset_tutorial()
            logger.info(f"Reset tutorial flag for user {user_id} ({user.email})")
            return True
        else:
            logger.warning(f"User {user_id} not found")
            return False
    except Exception as e:
        logger.error(f"Failed to reset tutorial flag for user {user_id}: {str(e)}")
        return False


async def is_special_thread(thread_title: str) -> bool:
    """
    Check if a thread is a special thread (onboarding/preference/policy/future-trip).

    Args:
        thread_title: Title of the thread to check

    Returns:
        True if the thread is a special thread, False otherwise
    """
    special_titles = [
        settings.ONBOARDING_THREAD_TITLE,
        settings.PREFERENCES_THREAD_TITLE,
        settings.TRAVEL_POLICY_THREAD_TITLE,
        settings.FUTURE_TRIPS_THREAD_TITLE,
        settings.ONBOARDING_PAGE_TITLE,
    ]
    return thread_title in special_titles


async def count_non_special_threads(user_id: int) -> int:
    """
    Count the number of non-special threads for a user.

    Args:
        user_id: ID of the user to count threads for

    Returns:
        Number of non-special threads for the user
    """
    threads = await ChatThread.from_user_id(user_id)
    non_special_count = 0

    for thread_row in threads:
        thread = thread_row[0]
        if not await is_special_thread(thread.title):
            non_special_count += 1

    return non_special_count


async def process_users_tutorial_flags(dry_run: bool = False) -> int:
    """
    Process all users and reset tutorial_completed flag for users with 1 or fewer
    non-special threads.

    Args:
        dry_run: If True, only log actions without actually resetting flags

    Returns:
        Number of users whose tutorial flags were reset
    """
    users = await get_all_users()
    logger.info(f"Found {len(users)} users to process")

    reset_count = 0

    for user in users:
        non_special_thread_count = await count_non_special_threads(user.id)

        if non_special_thread_count <= 1:
            logger.info(
                f"User {user.id} ({user.email}) has {non_special_thread_count} non-special threads, "
                f"resetting tutorial flag"
            )

            if not dry_run:
                if await reset_user_tutorial(user.id):
                    reset_count += 1
            else:
                reset_count += 1

    return reset_count


async def delete_empty_threads(days: int, dry_run: bool = False) -> Tuple[int, int]:
    """
    Delete empty threads older than the specified number of days.

    Args:
        days: Number of days to look back
        dry_run: If True, only log actions without actually deleting threads

    Returns:
        Tuple of (number of threads deleted, number of users affected)
    """
    threads = await get_new_trip_threads_older_than(days)
    logger.info(f"Found {len(threads)} new trip threads older than {days} days")

    deleted_count = 0
    affected_users = set()
    new_trip_titles = set([settings.NEW_TRIP_TITLE, settings.GETTING_STARTED_TITLE])

    for thread in threads:
        if thread.title in new_trip_titles and await is_thread_empty(thread.id):
            user = await User.from_id(thread.users_id)
            assert user is not None, f"User {thread.users_id} not found for thread {thread.id}"
            logger.info(
                f"Thread {thread.id} ('{thread.title}') of user {user.id} ({user.email}) is empty new trip and will be deleted"
            )
            affected_users.add(thread.users_id)

            if not dry_run:
                if await delete_thread(thread.id):
                    deleted_count += 1
            else:
                deleted_count += 1

    return deleted_count, len(affected_users)


async def main():
    """
    Main function that handles both thread deletion and tutorial flag reset workflows.
    """
    parser = argparse.ArgumentParser(
        description="Delete empty threads older than N days and reset tutorial flags for users with 1 or fewer threads"
    )
    parser.add_argument(
        "-d", "--days", type=int, default=7, help="Delete threads older than this many days (default: 7)"
    )
    parser.add_argument("--dry-run", action="store_true", help="Only log actions without actually making changes")
    parser.add_argument("--skip-thread-deletion", action="store_true", help="Skip empty thread deletion")
    parser.add_argument("--skip-tutorial-reset", action="store_true", help="Skip tutorial flag reset")

    args = parser.parse_args()

    if args.days < 1:
        logger.error("Days must be at least 1")
        return 1

    try:
        print(f"Thread Maintenance Tool for {settings.OTTO_ENV} Environment\n")

        if args.dry_run:
            print("DRY RUN MODE: No actual changes will be performed\n")

        deleted_count = 0
        affected_users = 0
        reset_count = 0

        # Process 1: Delete empty threads older than N days
        if not args.skip_thread_deletion:
            print(f"Looking for empty threads older than {args.days} days...")
            deleted_count, affected_users = await delete_empty_threads(args.days, args.dry_run)

        # Process 2: Reset tutorial flags for users with 1 or fewer non-special threads
        if not args.skip_tutorial_reset:
            print("\nProcessing users to reset tutorial flags...")
            reset_count = await process_users_tutorial_flags(args.dry_run)

        print("\nSummary:")
        if not args.skip_thread_deletion:
            print(f"- Deleted {deleted_count} empty threads")
            print(f"- Affected {affected_users} users by thread deletion")

        if not args.skip_tutorial_reset:
            print(f"- Reset tutorial flag for {reset_count} users with 1 or fewer non-special threads")

        if args.dry_run:
            print("\nThis was a dry run. No actual changes were made.")

        return 0

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 130
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
