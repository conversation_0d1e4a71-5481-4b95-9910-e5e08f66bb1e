import asyncio

from server.database.models.user import get_all_users
from server.services.payment_profile.spotnana_profile import update_spotnana_payment_profile
from server.services.payment_profile.user_payment_profile import get_user_payment_profile
from server.utils.spotnana_api import spotnana_api


async def update_payment_profile(user_id, user_email):
    mongo_payment_info = await get_user_payment_profile(user_id)

    if mongo_payment_info is None:
        print(f"no payment info found in mongo for user {user_email}")
        return

    traveler_info = await spotnana_api.get_traveler_by_email(user_email)

    if not traveler_info or len(traveler_info["results"]) == 0:
        print(f"user missing on spotnana {user_email}")
        return

    user_org_id = traveler_info["results"][0].get("userOrgId")

    traveler = await spotnana_api.traveler_read(user_org_id)

    if len(traveler.get("traveler", {}).get("user").get("paymentInfos", [])) == 0:
        print(f"payment profile missing on spotnana {user_email}")
        return

    payment_info = traveler.get("traveler", {}).get("user").get("paymentInfos")[0]

    if 2 in payment_info.get("applicableTo"):
        print(f"Payment method is already applicable to hotels for user {user_email}")
        return

    await update_spotnana_payment_profile(mongo_payment_info, user_email)
    print(f"Payment profile successfully updated for user {user_email}")


async def main():
    users = await get_all_users()

    for user in users:
        try:
            await update_payment_profile(user.id, user.email)
        except Exception as e:
            print(f"Could not update payment profile for user {user.email}, error {e}")


if __name__ == "__main__":
    asyncio.run(main())
