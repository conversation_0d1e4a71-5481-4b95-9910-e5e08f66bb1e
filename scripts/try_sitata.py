import asyncio
from pprint import pprint

import httpx


async def call_sitata(citizenship, destination):
    url = "https://staging.sitata.com/api/v2/entry_requirements"
    headers = {
        "Authorization": "TKN xxxx",
        "Organization": "4f7650ce-cab8-445c-b78a-d56220676f4b",
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                url,
                params={
                    "nationality": citizenship,
                    "destination": destination,
                    "types": "11,12",
                },
                headers=headers,
            )
            response.raise_for_status()
            return response.json()

    except Exception as e:
        print(f"Unexpected error occurred while fetching entry requirements: {e}")
        return None


async def get_entry_requirements(nationality: str, destination_country: str | None):
    json_response_list = await call_sitata(nationality, destination_country)
    if json_response_list:
        visa_comment = None
        passport_comment = None
        for entry in json_response_list:
            if affected_countries := entry.get("affected_countries"):
                for country in affected_countries:
                    if country.get("country_code") == nationality:
                        if entry.get("type") == 11:
                            visa_comment = entry.get("comment")
                            break
                        elif entry.get("type") == 12:
                            passport_comment = entry.get("comment")
                            break

        return {"visa": visa_comment, "passport": passport_comment}


schengen_countries = [
    "AT",
    "BE",
    "CZ",
    "DK",
    "EE",
    "FI",
    "FR",
    "DE",
    "GR",
    "HU",
    "IS",
    "IT",
    "LV",
    "LI",
    "LT",
    "LU",
    "MT",
    "NL",
    "NO",
    "PL",
    "PT",
    "SK",
    "SI",
    "ES",
    "SE",
    "CH",
]


async def run():
    for country in schengen_countries:
        result = await get_entry_requirements("US", country)
        pprint(f"Entry requirements for US to {country}")
        pprint(result)
        print()

    for country in schengen_countries:
        result = await get_entry_requirements("DE", country)
        pprint(f"Entry requirements for DE to {country}")
        pprint(result)
        print()


if __name__ == "__main__":
    asyncio.run(run())
