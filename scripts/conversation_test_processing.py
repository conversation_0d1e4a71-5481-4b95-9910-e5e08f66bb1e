import json


def simplify_jsonl(input_file, output_file):
    results = []

    with open(input_file, "r", encoding="utf-8") as f:
        for line in f:
            try:
                data = json.loads(line.strip())

                # Case 1: Extract test_user_reply and explanation
                if "test_user_reply" in data:
                    result = {"test_user_reply": data["test_user_reply"], "explanation": data["explanation"]}
                    results.append(result)

                # Case 2: Extract bot messages
                elif data.get("isBotMessage") is True and "text" in data:
                    result = {"otto_message": data["text"]}
                    results.append(result)

                # Case 3: Extract error messages
                elif data.get("status") == "error" and "message" in data:
                    result = {"error": data["message"]}
                    results.append(result)

                # Case 4: Extract test_plan
                elif "test_plan" in data:
                    result = {"test_plan": data["test_plan"]}
                    results.append(result)

            except json.JSONDecodeError:
                print(f"Skipping invalid JSON line: {line.strip()}")

    # Write results to output file
    with open(output_file, "w", encoding="utf-8") as f:
        for result in results:
            f.write(json.dumps(result, ensure_ascii=False, indent=2) + "\n")


# Usage
input_file = "e2e_conversation_test.jsonl"
output_file = "e2e_conversation_test_simplified.jsonl"
simplify_jsonl(input_file, output_file)
