import argparse
import asyncio
from typing import Literal

from server.database.models.user import User
from server.services.feature_flags.feature_flag import (
    FeatureFlags,
    create_feature_flag,
    remove_feature_flag,
)


async def manage_feature_flags(op: Literal["add", "remove"], flag: FeatureFlags, email: str):
    if user := await User.from_email(email):
        if op == "add":
            await create_feature_flag(user.id, user.email, flag)

        if op == "remove":
            await remove_feature_flag(user.id, user.email, flag)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Manage user feature flags")
    parser.add_argument("op", type=str, choices=["add", "remove"], help="Specify the operation to apply")
    parser.add_argument(
        "--flag",
        type=FeatureFlags,
        choices=[choice.value for choice in FeatureFlags],
        required=True,
        help="The feature flag involved in the operation",
    )
    parser.add_argument("--email", type=str, required=True, help="The user email involved in the operation")
    args = parser.parse_args()

    asyncio.run(manage_feature_flags(args.op, args.flag, args.email))

# Example of script runs:
# python -m scripts.feature_flag add --flag enable_spotnana_hotels_api --email <EMAIL>
# python -m scripts.feature_flag remove --flag enable_spotnana_hotels_api --email <EMAIL>
