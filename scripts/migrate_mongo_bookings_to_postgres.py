import asyncio

from sqlalchemy import select

from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.utils.mongo_connector import bookings_collection
from server.utils.pg_connector import async_session


async def get_all_thread_ids():
    async with async_session() as session:
        async with session.begin():
            query = select(ChatThread.id)
            result = (await session.execute(query)).fetchall()

            return [row[0] for row in result]


async def migrate():
    bookings = await bookings_collection.find({}, {"_id": 0}).to_list(length=None)

    pg_thread_ids = await get_all_thread_ids()

    pg_bookings: list[Booking] = []
    for booking in bookings:
        if booking.get("thread_id") not in pg_thread_ids:
            continue

        # Only use keys that match column names
        fields = {c.name for c in Booking.__table__.columns}
        filtered_data = {k: v for k, v in booking.items() if k in fields}

        pg_bookings.append(Booking(**filtered_data))

    await Booking.new_booking_batch(pg_bookings)


if __name__ == "__main__":
    asyncio.run(migrate())
