import asyncio

from dotenv import load_dotenv
from sqlalchemy import select, update

from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.utils.pg_connector import async_session

load_dotenv()


async def main():
    async with async_session() as session:
        async with session.begin():
            query = (
                select(Booking, ChatThread.users_id)
                .join(ChatThread, Booking.thread_id == ChatThread.id)
                .where(Booking.user_id.is_(None))
            )

            results = (await session.execute(query)).fetchall()

            print(f"Found {len(results)} bookings without user_id")

            for booking, user_id in results:
                print(f"Updating booking {booking.id} with user_id {user_id}")
                await session.execute(update(Booking).where(Booking.id == booking.id).values(user_id=user_id))

            print(f"Successfully updated {len(results)} bookings")


if __name__ == "__main__":
    asyncio.run(main())
