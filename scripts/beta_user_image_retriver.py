"""Reverse lookup personal information from email addresses using the ProxyCurl API."""

import csv
import time

import requests

# API Key and Endpoint
api_key = "CnljYnH_ggNB3Maf36_PPg"  # Replace with your actual API key
headers = {"Authorization": "Bearer " + api_key}
api_endpoint = "https://nubela.co/proxycurl/api/linkedin/profile/resolve/email"

# List of emails to look up
emails = ["<EMAIL>"]
# Output CSV file
output_file = "scripts/email_lookup_results.csv"

# Field names for the CSV
fieldnames = [
    "Email",
    "LinkedIn Profile",
    "Twitter Profile",
    "Facebook Profile",
    "Similarity Score",
    "Last Updated",
]


def perform_request_with_retries(url, params, headers, retries=3):
    """Perform a request with retries on HTTP 429."""
    attempt = 0
    while attempt < retries:
        response = requests.get(url, params=params, headers=headers)
        if response.status_code == 429:
            attempt += 1
            print(f"Received 429 Too Many Requests. Retrying in 30 seconds... (Attempt {attempt}/{retries})")
            time.sleep(30)  # Wait for 30 seconds before retrying
        else:
            return response
    print("Max retries reached. Moving to next email.")
    return None  # Return None if all retries fail


# Open the CSV file for writing
with open(output_file, mode="w", newline="", encoding="utf-8") as csvfile:
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    writer.writeheader()  # Write the header row

    # Perform the lookups and write results to the CSV
    for email in emails:
        params = {
            "email": email,
            "lookup_depth": "superficial",  # Initial superficial lookup
            "enrich_profile": "skip",  # Enrichment option
        }
        try:
            print(f"Performing superficial lookup for {email}...")
            response = perform_request_with_retries(api_endpoint, params, headers)

            if response and response.status_code == 200:
                data = response.json()
                linkedin_profile = data.get("linkedin_profile_url", None)

                # # If a LinkedIn profile is found, perform a deep lookup
                # if linkedin_profile:
                #     print(f'Deep lookup triggered for {email}...')
                #     params['lookup_depth'] = 'deep'
                #     deep_response = perform_request_with_retries(api_endpoint, params, headers)

                #     if deep_response and deep_response.status_code == 200:
                #         data = deep_response.json()

                # Write results to CSV after deep lookup (if applicable)
                writer.writerow(
                    {
                        "Email": email,
                        "LinkedIn Profile": data.get("linkedin_profile_url", "Not Found"),
                        "Twitter Profile": data.get("twitter_profile_url", "Not Found"),
                        "Facebook Profile": data.get("facebook_profile_url", "Not Found"),
                        "Similarity Score": data.get("similarity_score", "Not Found"),
                        "Last Updated": data.get("last_updated", "Not Found"),
                    }
                )
            else:
                # Handle failed responses
                print(f"Superficial lookup failed for {email}: {response.status_code if response else 'No Response'}")
                writer.writerow(
                    {
                        "Email": email,
                        "LinkedIn Profile": "Failed",
                        "Twitter Profile": "Failed",
                        "Facebook Profile": "Failed",
                        "Similarity Score": "Failed",
                        "Last Updated": f"Error {response.status_code if response else 'No Response'}",
                    }
                )

        except Exception as e:
            # Handle exceptions
            print(f"Error during lookup for {email}: {e}")
            writer.writerow(
                {
                    "Email": email,
                    "LinkedIn Profile": "Error",
                    "Twitter Profile": "Error",
                    "Facebook Profile": "Error",
                    "Similarity Score": "Error",
                    "Last Updated": str(e),
                }
            )

print(f"Results have been saved to {output_file}")
