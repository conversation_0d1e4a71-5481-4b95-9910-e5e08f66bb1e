from unittest.mock import AsyncMock, patch

import pytest

from server.database.models.user import User
from server.services.user.user_account_delete import delete_user_account
from server.utils.spotnana_api import spotnana_api


@pytest.fixture
def mock_user():
    return User(
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        profile_picture=None,
    )


@pytest.mark.asyncio
async def test_delete_user_account_with_spotnana(mock_user):
    # Mock all the necessary database operations
    with (
        patch("server.database.models.chat_thread.ChatThread.from_user_id", return_value=[]),
        patch("server.database.models.checkpoint.Checkpoint.delete_threads"),
        patch("server.database.models.user_profile.UserProfile.from_user_id", return_value=None),
        patch("server.database.models.user.User.delete"),
        patch("server.utils.mongo_connector.user_preferences_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.bookings_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.trip_travel_context_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.google_calendar_events_collection.delete_many", new_callable=AsyncMock),
        # Mock Spotnana API calls
        patch.object(
            spotnana_api,
            "get_user_by_email",
            new_callable=AsyncMock,
            return_value={"data": [{"id": "spotnana-user-123"}]},
        ),
        patch.object(spotnana_api, "delete_user", new_callable=AsyncMock),
    ):
        await delete_user_account(mock_user.id, mock_user.email)

        # Verify Spotnana API calls
        spotnana_api.get_user_by_email.assert_called_once_with(mock_user.email)  # type: ignore
        spotnana_api.delete_user.assert_called_once_with("spotnana-user-123")  # type: ignore


@pytest.mark.asyncio
async def test_delete_user_account_no_spotnana_user(mock_user):
    # Mock all the necessary database operations
    with (
        patch("server.database.models.chat_thread.ChatThread.from_user_id", return_value=[]),
        patch("server.database.models.checkpoint.Checkpoint.delete_threads"),
        patch("server.database.models.user_profile.UserProfile.from_user_id", return_value=None),
        patch("server.database.models.user.User.delete"),
        patch(
            "server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id",
            new_callable=AsyncMock,
            return_value=None,
        ),
        patch("server.utils.mongo_connector.user_preferences_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.bookings_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.trip_travel_context_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.google_calendar_events_collection.delete_many", new_callable=AsyncMock),
        # Mock Spotnana API calls - return empty data
        patch.object(
            spotnana_api,
            "get_user_by_email",
            new_callable=AsyncMock,
            return_value={"data": []},
        ),
        patch.object(spotnana_api, "delete_user", new_callable=AsyncMock),
    ):
        await delete_user_account(mock_user.id, mock_user.email)

        # Verify Spotnana API calls
        spotnana_api.get_user_by_email.assert_called_once_with(mock_user.email)  # type: ignore
        spotnana_api.delete_user.assert_not_called()  # type: ignore


@pytest.mark.asyncio
async def test_delete_user_account_spotnana_error(mock_user):
    # Mock all the necessary database operations
    with (
        patch("server.database.models.chat_thread.ChatThread.from_user_id", return_value=[]),
        patch("server.database.models.checkpoint.Checkpoint.delete_threads"),
        patch("server.database.models.user_profile.UserProfile.from_user_id", return_value=None),
        patch("server.database.models.user.User.delete"),
        patch(
            "server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id",
            new_callable=AsyncMock,
            return_value=None,
        ),
        patch("server.utils.mongo_connector.user_preferences_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.bookings_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.trip_travel_context_collection.delete_many", new_callable=AsyncMock),
        patch("server.utils.mongo_connector.google_calendar_events_collection.delete_many", new_callable=AsyncMock),
        # Mock Spotnana API calls to raise an exception
        patch.object(
            spotnana_api,
            "get_user_by_email",
            new_callable=AsyncMock,
            side_effect=Exception("Spotnana API error"),
        ),
        patch.object(spotnana_api, "delete_user", new_callable=AsyncMock),
    ):
        # Should not raise an exception
        await delete_user_account(mock_user.id, mock_user.email)

        # Verify Spotnana API calls
        spotnana_api.get_user_by_email.assert_called_once_with(mock_user.email)  # type: ignore
        spotnana_api.delete_user.assert_not_called()  # type: ignore
