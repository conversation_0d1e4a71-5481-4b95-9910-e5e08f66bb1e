from llm_utils.llm_utils import reconcile_llm_inferred_airline_iata_code


def test_reconcile_llm_inferred_airline_iata_code():
    assert reconcile_llm_inferred_airline_iata_code("AA") == "AA"
    assert reconcile_llm_inferred_airline_iata_code("WN") == "WN"

    assert reconcile_llm_inferred_airline_iata_code("Frontier") == "F9"
    assert reconcile_llm_inferred_airline_iata_code("frontier") == "F9"
    assert reconcile_llm_inferred_airline_iata_code("Delta Air lines") == "DL"
    assert reconcile_llm_inferred_airline_iata_code("South west") == "WN"
    assert reconcile_llm_inferred_airline_iata_code("Southwest") == "WN"
    assert reconcile_llm_inferred_airline_iata_code("Southwest airline") == "WN"
