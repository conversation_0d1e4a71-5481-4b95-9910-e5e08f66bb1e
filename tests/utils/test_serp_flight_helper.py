import json

import pytest

from front_of_house_agent.serp_common_models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Stop
from front_of_house_agent.serp_flight_helper import SerpFlightSearchHelper


@pytest.mark.asyncio
async def test_process_flight_search_results():
    json_response = {}
    # Read JSON response from file
    with open("./tests/data/serp_test_booking_response.json", "r") as file:
        json_response = file.read()

    json_response_list = json.loads(json_response)
    result = SerpFlightParser.parse_booking_option_response(json_response_list[0])

    # TODO: consider to remove this line or fix it
    flightSerpHelper = SerpFlightSearchHelper(None, None, None, None)  # type: ignore
    result = flightSerpHelper.process_flight_search_results([result])
    assert result is not None
    assert len(result[2]) > 0
    for res in result[2]:
        assert res.model_dump_json(indent=4) is not None


def test_parse_booking_option_response():
    json_response = {}
    # Read JSON response from file
    with open("./tests/data/serp_test_booking_response.json", "r") as file:
        json_response = file.read()

    json_response_list = json.loads(json_response)
    result = SerpFlightParser.parse_booking_option_response(json_response_list[1])
    assert result.booking_options
    assert result.booking_options[0].option_title is None
    assert result.booking_options[0].book_with == "Alaska"


def test_parse_flight_search_response():
    json_response = {}
    # Read JSON response from file
    with open("./tests/data/serp_test_departure_response.json", "r") as file:
        json_response = file.read()

    result = SerpFlightParser.parse_flight_search_response(json.loads(json_response)[0])
    assert result is not None
    assert len(result.flight_details) == 14
    flight = result.flight_details[0]
    assert flight.total_duration == 199
    assert flight.price == 90.0
    assert flight.booking_token is not None
    assert isinstance(flight.flights[0], Stop)


def test_parse_best_flights_search_response():
    json_response = {}
    # Read JSON response from file
    with open("./tests/data/serp_test_departure_response.json", "r") as file:
        json_response = file.read()

    result = SerpFlightParser.parse_flight_search_response(json.loads(json_response)[1])
    assert result is not None
    assert len(result.flight_details) == 6
    flight = result.flight_details[0]
    assert flight.total_duration == 136
    assert flight.price == 234
