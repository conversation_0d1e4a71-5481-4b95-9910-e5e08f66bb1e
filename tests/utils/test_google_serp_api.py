import json
from unittest.mock import AsyncMock, patch

import pytest

from server.utils.google_serp_api import google_serp_api


@pytest.fixture
def mock_flight_search_params():
    return {
        "departure_id": "JFK",  # New York JFK Airport
        "arrival_id": "LAX",  # Los Angeles Airport
        "outbound_date": "2025-04-05",
        "return_date": "2025-04-12",
        "currency": "USD",
        "hl": "en",
        "gl": "us",
        "type": "1",  # Round trip
    }


@pytest.fixture
def mock_flight_search_response():
    return {
        "search_metadata": {
            "id": "63f85c2b8e4e7e9d5a8b4567",
            "status": "Success",
            "json_endpoint": "https://serpapi.com/searches/63f85c2b8e4e7e9d5a8b4567/json",
            "created_at": "2025-03-05 04:15:23 UTC",
            "processed_at": "2025-03-05 04:15:25 UTC",
            "google_flights_url": "https://www.google.com/travel/flights/search?tfs=...",
            "raw_html_file": "https://serpapi.com/searches/63f85c2b8e4e7e9d5a8b4567/raw_html",
            "total_time_taken": 1.83,
        },
        "search_parameters": {
            "engine": "google_flights",
            "departure_id": "JFK",
            "arrival_id": "LAX",
            "outbound_date": "2025-04-05",
            "return_date": "2025-04-12",
            "currency": "USD",
            "hl": "en",
            "gl": "us",
            "type": "1",
        },
        "other_flights": [
            {
                "flights": [
                    {
                        "departure_airport": {"name": "John F. Kennedy International Airport", "code": "JFK"},
                        "arrival_airport": {"name": "Los Angeles International Airport", "code": "LAX"},
                        "departure_date": "2025-04-05",
                        "arrival_date": "2025-04-05",
                        "departure_time": "08:00",
                        "arrival_time": "11:15",
                        "airline": "Delta",
                        "flight_number": "DL123",
                        "duration": "6h 15m",
                    },
                    {
                        "departure_airport": {"name": "Los Angeles International Airport", "code": "LAX"},
                        "arrival_airport": {"name": "John F. Kennedy International Airport", "code": "JFK"},
                        "departure_date": "2025-04-12",
                        "arrival_date": "2025-04-12",
                        "departure_time": "13:30",
                        "arrival_time": "22:00",
                        "airline": "Delta",
                        "flight_number": "DL456",
                        "duration": "5h 30m",
                    },
                ],
                "price": "$450",
            }
        ],
    }


@pytest.mark.asyncio
async def test_search_flights_success(mock_flight_search_params, mock_flight_search_response):
    """Test successful flight search with SerpAPI."""
    # Use patch.object to mock the make_get_request method directly on the module
    with patch("server.utils.google_serp_api.make_get_request", new_callable=AsyncMock) as mock_get:
        # Configure the mock to return a successful response
        mock_get.return_value = mock_flight_search_response

        # Call the method under test
        result = await google_serp_api.search_flights(mock_flight_search_params)

        # Verify the result
        assert result == mock_flight_search_response

        # Verify the API was called with the correct parameters
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        assert args[0] == "https://serpapi.com/search"
        assert kwargs["headers"] == {"Content-Type": "application/json"}

        # Verify the parameters sent to the API
        params = kwargs["params"]
        print(json.dumps(params))
        assert params["engine"] == "google_flights"
        assert params["departure_id"] == mock_flight_search_params["departure_id"]
        assert params["arrival_id"] == mock_flight_search_params["arrival_id"]
        assert params["api_key"] == google_serp_api.api_key


@pytest.mark.asyncio
async def test_search_flights_error():
    """Test error handling during flight search."""
    with patch("server.utils.google_serp_api.make_get_request", new_callable=AsyncMock) as mock_get:
        # Configure the mock to raise an exception
        mock_get.side_effect = Exception("API error")

        # Call the method under test and expect an exception
        with pytest.raises(Exception) as excinfo:
            await google_serp_api.search_flights({"departure_id": "JFK", "arrival_id": "LAX"})

        # Verify the exception
        assert "API error" in str(excinfo.value)


@pytest.mark.asyncio
async def test_search_method(mock_flight_search_params, mock_flight_search_response):
    """Test the generic post method."""
    with patch.object(google_serp_api, "search_flights", new_callable=AsyncMock) as mock_search:
        # Configure the mock to return a successful response
        mock_search.return_value = mock_flight_search_response

        # Call the method under test
        result = await google_serp_api.search_flights(mock_flight_search_params)

        # Verify the result
        assert result == mock_flight_search_response

        # Verify search_flights was called with the correct parameters
        mock_search.assert_called_once_with(mock_flight_search_params)
