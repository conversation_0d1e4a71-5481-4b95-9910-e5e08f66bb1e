import yaml


def test_convert_flat_json_with_single_quotes():
    """Test that convert_flat_json_like can handle input with single-quoted values."""
    # Test input with single-quoted values
    input_str = """{"trip_outbound_arrival_time": '2025-05-12T08:28:00'}"""

    json_dict = yaml.load(input_str, yaml.SafeLoader)

    # Verify the result
    assert json_dict is not None
    assert "trip_outbound_arrival_time" in json_dict
    assert json_dict["trip_outbound_arrival_time"] == "2025-05-12T08:28:00"


def test_convert_flat_json_with_double_quotes():
    """Test that convert_flat_json_like can handle input with double-quoted values."""
    # Test input with double-quoted values
    input_str = """{"trip_outbound_arrival_time": "2025-05-12T08:28:00", "seat": ["Aisle", "Window"]}"""

    json_dict = yaml.load(input_str, yaml.SafeLoader)

    # Verify the result
    assert json_dict is not None
    assert "trip_outbound_arrival_time" in json_dict
    assert json_dict["trip_outbound_arrival_time"] == "2025-05-12T08:28:00"
    assert json_dict["seat"] == ["Aisle", "Window"]


def test_convert_flat_json():
    input_str = """{"trip_destination":"San Francisco","trip_start_date":"2024-12-04","trip_end_date":"2024-12-07","trip_origin_airport":"SEA","trip_destination_airport":"SFO","trip_airline_brands":["Delta Air Lines","Alaska Airlines"],"trip_cabin":["Premium Economy"],"trip_outbound_departure_time":"morning","trip_flight_type":"RoundTrip"}"""
    json_dict = yaml.load(input_str, yaml.SafeLoader)
    assert json_dict is not None
    assert "trip_destination" in json_dict
    assert json_dict["trip_destination"] == "San Francisco"
