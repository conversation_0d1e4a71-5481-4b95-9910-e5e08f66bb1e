import sys
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from server.utils.s3_utils import S3Utils


class TestS3Utils:
    @pytest.fixture
    def s3_utils(self):
        with patch("server.utils.s3_utils.boto3.client"):
            s3_utils = S3Utils()
            s3_utils.s3_client = MagicMock()
            yield s3_utils

    def test_singleton_pattern(self):
        """Test that S3Utils implements the singleton pattern correctly."""
        with patch("server.utils.s3_utils.boto3.client"):
            s3_utils_1 = S3Utils()
            s3_utils_2 = S3Utils()
            assert s3_utils_1 is s3_utils_2

    @pytest.mark.asyncio
    async def test_log_trip_artifact_to_s3_json(self, s3_utils):
        """Test logging JSON data to S3."""
        test_data = {"key": "value", "nested": {"data": True}}

        with patch.object(
            s3_utils,
            "_upload_single_file",
            return_value={"success": True, "uri": "s3://test-bucket/dev/12345/test_file.json"},
        ):
            with patch("server.utils.s3_utils.settings") as mock_settings:
                mock_settings.OTTO_ENV = "dev"

                await s3_utils.log_trip_artifact_to_s3(
                    bucket_name="test-bucket",
                    trip_id=12345,
                    file_name="test_file",
                    content=test_data,
                    file_type="json",
                    queue_for_batch=False,
                )
                assert True

    @pytest.mark.asyncio
    async def test_log_trip_artifact_to_s3_csv(self, s3_utils):
        """Test logging CSV data to S3."""
        test_data = "header1,header2\nvalue1,value2\nvalue3,value4"

        with patch.object(
            s3_utils,
            "_upload_single_file",
            return_value={"success": True, "uri": "s3://test-bucket/dev/12345/test_file.csv"},
        ):
            with patch("server.utils.s3_utils.settings") as mock_settings:
                mock_settings.OTTO_ENV = "dev"

                await s3_utils.log_trip_artifact_to_s3(
                    bucket_name="test-bucket",
                    trip_id=12345,
                    file_name="test_file",
                    content=test_data,
                    file_type="csv",
                    queue_for_batch=False,
                )
                assert True

    @pytest.mark.asyncio
    async def test_log_trip_artifact_to_s3_log(self, s3_utils):
        """Test logging log data to S3."""
        test_data = "INFO: This is a log message\nERROR: Something went wrong"

        with patch.object(
            s3_utils,
            "_upload_single_file",
            return_value={"success": True, "uri": "s3://test-bucket/dev/12345/test_file.log"},
        ):
            with patch("server.utils.s3_utils.settings") as mock_settings:
                mock_settings.OTTO_ENV = "dev"

                await s3_utils.log_trip_artifact_to_s3(
                    bucket_name="test-bucket",
                    trip_id=12345,
                    file_name="test_file",
                    content=test_data,
                    file_type="log",
                    queue_for_batch=False,
                )
                assert True

    @pytest.mark.asyncio
    async def test_log_trip_artifact_to_s3_with_extra_path(self, s3_utils):
        """Test logging data to S3 with an extra path."""
        test_data = {"data": "value"}

        with patch.object(
            s3_utils,
            "_upload_single_file",
            return_value={"success": True, "uri": "s3://test-bucket/dev/12345/logs/test_file.json"},
        ):
            with patch("server.utils.s3_utils.settings") as mock_settings:
                mock_settings.OTTO_ENV = "dev"

                await s3_utils.log_trip_artifact_to_s3(
                    bucket_name="test-bucket",
                    trip_id=12345,
                    file_name="test_file",
                    content=test_data,
                    file_type="json",
                    extra_path="logs",
                    queue_for_batch=False,
                )
                assert True

    @pytest.mark.asyncio
    async def test_error_handling(self, s3_utils):
        """Test error handling in S3 upload function."""
        with patch.object(s3_utils, "log_trip_artifact_to_s3", side_effect=Exception("Test error")):
            try:
                await s3_utils.log_trip_artifact_to_s3(
                    bucket_name="test-bucket",
                    trip_id=12345,
                    file_name="test_file",
                    content={"data": "value"},
                    file_type="json",
                    queue_for_batch=False,
                )
                assert False, "Exception was not raised"
            except Exception:
                assert True

    @pytest.mark.asyncio
    async def test_log_trip_artifact_to_s3_immediate(self, s3_utils):
        """Test immediately uploading a file to S3 asynchronously."""
        with patch("server.utils.s3_utils.GlobalTaskManager.register_task") as mock_register:
            with patch("asyncio.create_task") as mock_create_task:
                with patch("server.utils.s3_utils.settings") as mock_settings:
                    mock_settings.OTTO_ENV = "dev"
                    test_data = {"key": "value"}

                    await s3_utils.log_trip_artifact_to_s3(
                        bucket_name="test-bucket",
                        trip_id=12345,
                        file_name="test_async",
                        content=test_data,
                        file_type="json",
                        queue_for_batch=False,
                    )

                    assert mock_create_task.called
                    assert mock_register.called

    @pytest.mark.asyncio
    async def test_log_trip_artifact_to_s3_batch(self, s3_utils):
        """Test batch uploading a file to S3 asynchronously."""
        with patch("server.utils.s3_utils.GlobalTaskManager.register_task") as mock_register:
            with patch("server.utils.s3_utils.settings") as mock_settings:
                mock_settings.OTTO_ENV = "dev"
                test_data = {"key": "value"}

                await s3_utils.log_trip_artifact_to_s3(
                    bucket_name="test-bucket",
                    trip_id=12345,
                    file_name="test_batch",
                    content=test_data,
                    file_type="json",
                    queue_for_batch=True,
                )

                assert len(s3_utils._upload_queue["test-bucket"]) == 1
                assert s3_utils._is_worker_running
                assert mock_register.called

    @pytest.mark.asyncio
    async def test_batch_upload_worker(self, s3_utils):
        """Test the batch upload worker."""
        test_files = [
            {"key": "test1", "content": b"test1 content"},
            {"key": "test2", "content": b"test2 content"},
            {"key": "test3", "content": b"test3 content"},
        ]

        bucket = "test-bucket"
        for i, file_info in enumerate(test_files):
            async with s3_utils._queue_lock:
                s3_utils._upload_queue[bucket].append(
                    {"key": file_info["key"], "body": file_info["content"], "uri": f"s3://{bucket}/{file_info['key']}"}
                )

        s3_utils._upload_single_file = MagicMock(return_value={"success": True, "uri": "test-uri"})

        await s3_utils._batch_upload_worker()

        assert s3_utils._upload_single_file.call_count == 3
