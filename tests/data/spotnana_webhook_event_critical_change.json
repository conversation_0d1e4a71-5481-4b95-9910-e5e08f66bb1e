{"_id": {"$oid": "68532b625d4816160ac4bf4b"}, "timestamp": "2025-06-18T21:10:58.617059357Z", "event_type": "PNR_V3", "operation": "TICKET_VOIDED", "payload": {"version": 3, "createdVia": "OBT", "initialVersionCreatedVia": "OBT", "sourceInfo": {"sourcePnrId": "1T2Z34", "bookingSource": "FARELOGIX_NDC", "thirdParty": "FARELOGIX_NDC", "bookingDateTime": {"iso8601": "2025-06-05T19:11:02Z"}, "posDescriptor": "A4L0", "iataNumber": ""}, "invoiceDelayedBooking": false, "travelers": [{"user": {"dob": {"iso8601": "2000-04-04"}, "email": "<EMAIL>", "gender": "FEMALE", "name": {"family1": "Li", "family2": "", "given": "<PERSON>", "middle": "", "preferred": ""}, "paymentInfos": [{"applicableTo": [], "card": {"id": "b5e8b290-70ea-4668-b2e8-da0252c5c8ae", "type": "CREDIT", "company": "AMEX", "name": "Huibo Li", "address": {"addressLines": ["1818 Westlake Ave N"], "administrativeArea": "WA", "locality": "Bellevue", "postalCode": "98007", "regionCode": "US"}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2030, "cvv": "", "label": "OTTO credit card", "expiry": {"expiry": {"expiryMonth": 1, "expiryYear": 2030}}}, "accessType": "PERSONAL", "access": {"accessType": "PERSONAL", "entityIds": ["61c69a83-1c10-436b-bd69-f743812d761f"], "entities": [{"entityId": "61c69a83-1c10-436b-bd69-f743812d761f", "centralCardAccessLevel": "UNKNOWN"}]}}], "phoneNumbers": [{"countryCode": 0, "countryCodeSource": "UNSPECIFIED", "extension": "", "isoCountryCode": "", "italianLeadingZero": false, "nationalNumber": 0, "numberOfLeadingZeros": 0, "preferredDomesticCarrierCode": "", "rawInput": "***********", "type": "UNKNOWN_TYPE"}]}, "userBusinessInfo": {"designation": "", "email": "<EMAIL>", "employeeId": "", "legalEntityId": {"id": "96dd735a-d576-4e68-ad91-5e06da377e2e"}, "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}}, "userOrgId": {"organizationAgencyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "userId": {"id": "61c69a83-1c10-436b-bd69-f743812d761f"}, "tmcInfo": {"id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}, "primaryServiceProviderTmc": {"tmcId": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}}, "secondaryServiceProviderTmcs": [], "partnerTmcId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}}, "tmcBasicInfo": {"contractingTmc": {"id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "name": "<PERSON>", "logo": {"data": "", "dimensions": {"height": 0, "width": 0}, "url": ""}}, "bookingTmc": {"id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}}}}, "persona": "EMPLOYEE", "isActive": true, "tier": "BASIC"}], "pnrTravelers": [{"userId": {"id": "61c69a83-1c10-436b-bd69-f743812d761f"}, "travelerInfo": {"userId": {"id": "61c69a83-1c10-436b-bd69-f743812d761f"}, "adhocTravelerInfo": {}}, "personalInfo": {"dob": {"iso8601": "2000-04-04"}, "email": "<EMAIL>", "gender": "FEMALE", "name": {"family1": "Li", "family2": "", "given": "<PERSON>", "middle": "", "preferred": ""}, "phoneNumbers": [{"countryCode": 0, "countryCodeSource": "UNSPECIFIED", "extension": "", "isoCountryCode": "", "italianLeadingZero": false, "nationalNumber": 0, "numberOfLeadingZeros": 0, "preferredDomesticCarrierCode": "", "rawInput": "***********", "type": "UNKNOWN_TYPE"}]}, "loyalties": [], "persona": "EMPLOYEE", "businessInfo": {"legalEntity": {"id": "96dd735a-d576-4e68-ad91-5e06da377e2e", "name": "Otto Trip, Inc.", "ein": "", "externalId": "", "companySpecifiedAttributes": []}, "companyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "companyInfo": {"id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "name": "<PERSON>", "externalId": ""}}, "tier": "BASIC"}], "costOfGoodsSold": {"payments": [{"travelerIndices": [0], "userIds": [{"id": "61c69a83-1c10-436b-bd69-f743812d761f"}], "fop": {"type": "CARD", "card": {"id": "b5e8b290-70ea-4668-b2e8-da0252c5c8ae", "type": "CREDIT", "company": "AMEX", "name": "Huibo Li", "address": {"addressLines": ["1818 Westlake Ave N"], "administrativeArea": "WA", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "Bellevue", "locationCode": "", "organization": "", "postalCode": "98007", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2030, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": "", "vaultId": "00000000-0000-0000-0000-000000000000", "expiry": {}}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["61c69a83-1c10-436b-bd69-f743812d761f"], "entities": [{"entityId": "61c69a83-1c10-436b-bd69-f743812d761f", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD", "paymentMetadata": {"cardMetadata": {"card": {"id": "b5e8b290-70ea-4668-b2e8-da0252c5c8ae", "type": "CREDIT", "company": "AMEX", "name": "Huibo Li", "address": {"addressLines": ["1818 Westlake Ave N"], "administrativeArea": "WA", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "Bellevue", "locationCode": "", "organization": "", "postalCode": "98007", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2030, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": "", "vaultId": "00000000-0000-0000-0000-000000000000", "expiry": {}}, "accessType": {"accessType": "PERSONAL", "entityIds": ["61c69a83-1c10-436b-bd69-f743812d761f"], "entities": [{"entityId": "61c69a83-1c10-436b-bd69-f743812d761f", "centralCardAccessLevel": "UNKNOWN"}]}, "isLodgeCard": false}}, "paymentSourceType": "CARD"}, "paymentReference": "", "paymentType": "FLIGHTS", "paymentThirdParty": "UNKNOWN_PARTY", "paymentId": "", "paymentGateway": "PAYMENT_GATEWAY_UNKNOWN", "isRefunded": false, "networkTransactionId": ""}]}, "isFinalized": true, "policyInfo": {"outOfPolicy": false, "reasonCode": "UNKNOWN_CHECKOUT_ANSWER_TYPE", "reason": "", "appliedPolicyInfo": {"policies": [{"id": "366011aa-9ad6-4cab-bb6b-add9ef060c33", "version": "0"}], "ruleResultInfos": []}}, "airPnr": {"legs": [{"flights": [{"departureDateTime": {"iso8601": "2025-07-01T05:10:00"}, "arrivalDateTime": {"iso8601": "2025-07-01T07:22:00"}, "duration": {"iso8601": "PT2H12M"}, "flightId": "CgNTRUESA1NGTxoVChMyMDI1LTA3LTAxVDA1OjEwOjAwIhUKEzIwMjUtMDctMDFUMDc6MjI6MDA=", "origin": "SEA", "destination": "SFO", "departureGate": {"gate": "", "terminal": ""}, "arrivalGate": {"gate": "", "terminal": "3"}, "marketing": {"num": "706", "airlineCode": "UA"}, "operating": {"num": "706", "airlineCode": "UA"}, "operatingAirlineName": "", "hiddenStops": [], "vendorConfirmationNumber": "MK07CE", "cabin": "ECONOMY", "bookingCode": "K", "flightStatus": "CANCELLED", "otherStatuses": [], "co2EmissionDetail": {"emissionValue": 0.119768, "averageEmissionValue": 0.1361, "flightDistanceKm": 1190, "isApproximate": false}, "restrictions": [], "sourceStatus": "None", "equipment": {"code": "7M9", "type": "", "name": "Boeing 737 MAX 9"}, "distance": {"length": 678, "unit": "MILE"}, "flightWaiverCodes": [], "amenities": [{}, {}, {}, {}, {}, {}, {}, {}], "flightIndex": 0}], "brandName": "Economy", "validatingAirlineCode": "UA", "legStatus": "CANCELLED_STATUS", "sortingPriority": 0, "travelerRestrictions": [{"userId": {"id": "61c69a83-1c10-436b-bd69-f743812d761f"}, "restrictions": ["SEAT_EDIT_NOT_ALLOWED", "LOYALTY_EDIT_NOT_ALLOWED", "KTN_EDIT_NOT_ALLOWED", "REDRESS_EDIT_NOT_ALLOWED", "SSR_EDIT_NOT_ALLOWED", "OSI_EDIT_NOT_ALLOWED"]}], "fareOffers": [{"userId": {"id": "61c69a83-1c10-436b-bd69-f743812d761f"}, "baggagePolicy": {"checkedIn": [], "carryOn": []}}], "legId": "CgNTRUESA1NGTxoKNjMxODI4NzExNQ==", "rateType": "PUBLISHED", "preferredTypes": [], "preferences": [], "legIndex": 0}, {"flights": [{"departureDateTime": {"iso8601": "2025-07-02T06:30:00"}, "arrivalDateTime": {"iso8601": "2025-07-02T08:48:00"}, "duration": {"iso8601": "PT2H18M"}, "flightId": "CgNTRk8SA1NFQRoVChMyMDI1LTA3LTAyVDA2OjMwOjAwIhUKEzIwMjUtMDctMDJUMDg6NDg6MDA=", "origin": "SFO", "destination": "SEA", "departureGate": {"gate": "", "terminal": "3"}, "arrivalGate": {"gate": "", "terminal": ""}, "marketing": {"num": "1969", "airlineCode": "UA"}, "operating": {"num": "1969", "airlineCode": "UA"}, "operatingAirlineName": "", "hiddenStops": [], "vendorConfirmationNumber": "MK07CE", "cabin": "ECONOMY", "bookingCode": "K", "flightStatus": "CANCELLED", "otherStatuses": [], "co2EmissionDetail": {"emissionValue": 0.136048, "averageEmissionValue": 0.1546, "flightDistanceKm": 1190, "isApproximate": false}, "restrictions": [], "sourceStatus": "None", "equipment": {"code": "7M8", "type": "", "name": "Boeing 737 MAX 8"}, "distance": {"length": 678, "unit": "MILE"}, "flightWaiverCodes": [], "amenities": [{}, {}, {}, {}, {}, {}, {}, {}], "flightIndex": 0}], "brandName": "Economy", "validatingAirlineCode": "UA", "legStatus": "CANCELLED_STATUS", "sortingPriority": 0, "travelerRestrictions": [{"userId": {"id": "61c69a83-1c10-436b-bd69-f743812d761f"}, "restrictions": ["SEAT_EDIT_NOT_ALLOWED", "LOYALTY_EDIT_NOT_ALLOWED", "KTN_EDIT_NOT_ALLOWED", "REDRESS_EDIT_NOT_ALLOWED", "SSR_EDIT_NOT_ALLOWED", "OSI_EDIT_NOT_ALLOWED"]}], "fareOffers": [{"userId": {"id": "61c69a83-1c10-436b-bd69-f743812d761f"}, "baggagePolicy": {"checkedIn": [], "carryOn": []}}], "legId": "CgNTRk8SA1NFQRoKNjMxODI4NzExNQ==", "rateType": "PUBLISHED", "preferredTypes": [], "preferences": [], "legIndex": 1}], "airPnrRemarks": [], "travelerInfos": [{"airVendorCancellationInfo": {"airVendorCancellationObjects": []}, "createdMcos": [], "travelerIdx": 0, "userId": {"id": "61c69a83-1c10-436b-bd69-f743812d761f"}, "paxType": "ADULT", "tickets": [{"ticketNumber": "0164300345275", "ticketType": "ANCILLARY", "issuedDateTime": {"iso8601": "2025-06-05T14:11:00"}, "status": "VOIDED", "amount": {"base": {"amount": 38.05, "currencyCode": "USD", "convertedAmount": 38.05, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}}, "flightCoupons": [], "ancillaries": [], "validatingAirlineCode": "UA", "iataNumber": "45526666", "fareCalculation": "", "updateDateTime": {"iso8601": "2025-06-18T00:00:00"}, "paymentDetails": [], "ticketSettlement": "NON_ARC_BSP_TICKET", "vendorCancellationId": "", "conjunctionTicketSuffix": [], "pcc": "A4L0", "ticketIncompleteReasons": ["SYS_TICKET"]}], "boardingPass": [], "booking": {"seats": [], "luggageDetails": [], "otherAncillaries": [], "itinerary": {"totalFare": {"base": {"amount": 238.48, "currencyCode": "USD", "convertedAmount": 238.48, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 48.49, "currencyCode": "USD", "convertedAmount": 48.49, "convertedCurrency": "USD", "otherCoinage": []}}, "totalFlightsFare": {"base": {"amount": 238.48, "currencyCode": "USD", "convertedAmount": 238.48, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 48.49, "currencyCode": "USD", "convertedAmount": 48.49, "convertedCurrency": "USD", "otherCoinage": []}}, "flightFareBreakup": [{"legIndices": [0, 1], "flightsFare": {"base": {"amount": 238.48, "currencyCode": "USD", "convertedAmount": 238.48, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 48.49, "currencyCode": "USD", "convertedAmount": 48.49, "convertedCurrency": "USD", "otherCoinage": []}}}], "fareComponents": [{"fareBasisCode": "KAA2AQDN", "tourCode": "", "ticketDesignator": "", "baseFare": {"amount": 119.24, "currencyCode": "USD", "convertedAmount": 119.24, "convertedCurrency": "USD", "otherCoinage": []}, "flightIds": [{"legIdx": 0, "flightIdx": 0}], "farePaxType": "UNKNOWN_PASSENGER_TYPE"}, {"fareBasisCode": "KAA2AQDN", "tourCode": "", "ticketDesignator": "", "baseFare": {"amount": 119.24, "currencyCode": "USD", "convertedAmount": 119.24, "convertedCurrency": "USD", "otherCoinage": []}, "flightIds": [{"legIdx": 1, "flightIdx": 0}], "farePaxType": "UNKNOWN_PASSENGER_TYPE"}], "otherAncillaryFares": []}, "otherCharges": []}, "appliedCredits": [], "specialServiceRequestInfos": []}], "bookingMetadata": {"fareStatistics": {"statisticsItems": [{"statisticType": "MINIMUM", "totalFare": {"base": {"amount": 238.14, "currencyCode": "USD", "convertedAmount": 238.14, "convertedCurrency": "USD"}, "tax": {"amount": 48.46, "currencyCode": "USD", "convertedAmount": 48.46, "convertedCurrency": "USD"}}}, {"statisticType": "MEDIAN", "totalFare": {"base": {"amount": 294.29, "currencyCode": "USD", "convertedAmount": 294.29, "convertedCurrency": "USD"}, "tax": {"amount": 72.77, "currencyCode": "USD", "convertedAmount": 72.77, "convertedCurrency": "USD"}}}, {"statisticType": "MAXIMUM", "totalFare": {"base": {"amount": 2806.09, "currencyCode": "USD", "convertedAmount": 2806.09, "convertedCurrency": "USD"}, "tax": {"amount": 250.76, "currencyCode": "USD", "convertedAmount": 250.76, "convertedCurrency": "USD"}}}]}}, "otherServiceInfos": []}, "additionalMetadata": {"airportInfo": [{"airportCode": "SFO", "airportName": "San Francisco International Airport", "cityName": "San Francisco", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles", "stateCode": "CA"}, {"airportCode": "SEA", "airportName": "Seattle–Tacoma International Airport", "cityName": "Seattle", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles", "stateCode": "WA"}], "airlineInfo": [{"airlineCode": "UA", "airlineName": "United Airlines"}]}, "preBookAnswers": {"answers": []}, "customFields": [], "customFieldsV3Responses": [], "bookingHistory": [{"bookerInfo": {"name": "OttoTest ApiUser", "email": "<EMAIL>", "role": "AGENT", "tmcName": ""}, "bookingInfo": {"updatedDateTime": {"iso8601": "2025-06-05T19:11:02Z"}, "status": "BOOKED", "bookingSourceClient": "WEB"}}], "serviceFees": [], "paymentInfo": [{"fop": {"type": "CARD", "card": {"id": "b5e8b290-70ea-4668-b2e8-da0252c5c8ae", "type": "CREDIT", "company": "AMEX", "name": "Huibo Li", "address": {"addressLines": ["1818 Westlake Ave N"], "administrativeArea": "WA", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "Bellevue", "locationCode": "", "organization": "", "postalCode": "98007", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2030, "cvv": "", "label": "OTTO credit card", "currency": "", "externalId": "", "vaultId": "00000000-0000-0000-0000-000000000000", "expiry": {}}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": [], "entities": []}, "paymentMethod": "CREDIT_CARD"}, "totalCharge": {"amount": 325.02, "currencyCode": "USD", "convertedAmount": 325.02, "convertedCurrency": "USD", "otherCoinage": []}, "totalRefund": {"amount": 325.02, "currencyCode": "USD", "convertedAmount": 325.02, "convertedCurrency": "USD", "otherCoinage": []}, "netCharge": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}}], "bookingStatus": "CANCELLED_STATUS", "contactSupport": false, "travelerPnrVisibilityStatus": "VISIBLE", "pnrCreationDetails": {}, "tripId": "6558262539", "documents": [], "pnrId": "6318287115", "totalFareAmount": {"base": {"amount": 276.53, "currencyCode": "USD", "convertedAmount": 276.53, "convertedCurrency": "USD"}, "tax": {"amount": 48.49, "currencyCode": "USD", "convertedAmount": 48.49, "convertedCurrency": "USD"}}, "savingsFare": {"fareAmount": {"base": {"amount": 238.48, "currencyCode": "USD", "convertedAmount": 238.48, "convertedCurrency": "USD"}, "tax": {"amount": 48.49, "currencyCode": "USD", "convertedAmount": 48.49, "convertedCurrency": "USD"}}, "isTaxIncluded": true}, "tripUsageMetadata": {"tripUsageType": "STANDARD"}, "owningPccInfo": {"zoneId": "America/Chicago"}}, "operationSummary": {"pnrUpdateSummary": {"ticketsVoided": ["0164300345275"]}}}