from flight_agent.flights_tools import FlightSearchTools


def test_seat_map_conversion():
    test_input = {
        "seatMapId": "seatMapId-0",
        "wingRows": {"min": 0, "max": 0},
        "cabinSections": [
            {
                "cabin": "BUSINESS",
                "bookingCode": "",
                "locations": ["MAIN_DECK"],
                "facilitySections": [],
                "columnSections": [
                    {"columns": [{"columnNumber": "A", "position": "WINDOW"}]},
                    {"columns": [{"columnNumber": "C", "position": "AISLE"}]},
                    {"columns": [{"columnNumber": "D", "position": "WINDOW"}]},
                ],
                "rowSections": [
                    {
                        "rowNumbers": {"min": 1, "max": 1},
                        "availableSeats": [{"columnNumber": ["C", "D"]}],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "C", "D"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": ["BULKHEAD_ROW"],
                                "commercialName": "",
                                "price": {},
                            }
                        ],
                    },
                    {
                        "rowNumbers": {"min": 2, "max": 4},
                        "availableSeats": [
                            {"columnNumber": ["A", "D"]},
                            {"columnNumber": ["A", "D"]},
                            {"columnNumber": ["A", "C", "D"]},
                        ],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "C", "D"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            }
                        ],
                    },
                ],
            }
        ],
    }

    # ROW	COLUMN	SEAT_TYPE	SEAT_LOCATION	IS_WING
    # 1	C	AISLE	{'MAIN_DECK', 'BULKHEAD_ROW'}	False
    # 1	D	WINDOW	{'MAIN_DECK', 'BULKHEAD_ROW'}	False
    # 2	A	WINDOW	{'MAIN_DECK'}	False
    # 2	D	WINDOW	{'MAIN_DECK'}	False
    # 3	A	WINDOW	{'MAIN_DECK'}	False
    # 3	D	WINDOW	{'MAIN_DECK'}	False
    # 4	A	WINDOW	{'MAIN_DECK'}	False
    # 4	C	AISLE	{'MAIN_DECK'}	False
    # 4	D	WINDOW	{'MAIN_DECK'}	False

    csv, _ = FlightSearchTools.convert_seat_map_to_csv(test_input)
    assert (
        csv
        == "ROW,COLUMN,SEAT_TYPE,SEAT_LOCATION,IS_WING,PRICE\r\n1,C,AISLE,\"['MAIN_DECK', 'BULKHEAD_ROW']\",False,0\r\n1,D,WINDOW,\"['MAIN_DECK', 'BULKHEAD_ROW']\",False,0\r\n2,A,WINDOW,['MAIN_DECK'],False,0\r\n2,D,WINDOW,['MAIN_DECK'],False,0\r\n3,A,WINDOW,['MAIN_DECK'],False,0\r\n3,D,WINDOW,['MAIN_DECK'],False,0\r\n4,A,WINDOW,['MAIN_DECK'],False,0\r\n4,C,AISLE,['MAIN_DECK'],False,0\r\n4,D,WINDOW,['MAIN_DECK'],False,0\r\n"
    )


def test_seat_map_conversion_2():
    test_input = {
        "seatMapId": "seatMapId-0",
        "wingRows": {"min": 0, "max": 0},
        "cabinSections": [
            {
                "cabin": "ECONOMY",
                "bookingCode": "",
                "locations": [],
                "facilitySections": [],
                "columnSections": [
                    {
                        "columns": [
                            {"columnNumber": "A", "position": "WINDOW"},
                            {"columnNumber": "B", "position": "CENTER"},
                            {"columnNumber": "C", "position": "AISLE"},
                        ]
                    },
                    {
                        "columns": [
                            {"columnNumber": "D", "position": "AISLE"},
                            {"columnNumber": "E", "position": "CENTER"},
                            {"columnNumber": "F", "position": "WINDOW"},
                        ]
                    },
                ],
                "rowSections": [
                    {
                        "rowNumbers": {"min": 11, "max": 11},
                        "availableSeats": [{"columnNumber": ["A", "B", "C", "D", "E", "F"]}],
                        "rowTypes": ["EXIT", "EXTRA_LEG_ROOM"],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "B", "C", "D", "E", "F"],
                                "type": "SEAT",
                                "limitations": ["NOT_ALLOWED_FOR_INFANT", "NOT_SUITABLE_FOR_CHILD"],
                                "facilities": ["LEG_SPACE", "PREFERRED_SEAT"],
                                "location": ["BULKHEAD_ROW", "EXIT_ROW"],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 98.45,
                                        "currencyCode": "USD",
                                        "convertedAmount": 98.45,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 6.87,
                                        "currencyCode": "USD",
                                        "convertedAmount": 6.87,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 91.58,
                                        "currencyCode": "USD",
                                        "convertedAmount": 91.58,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            }
                        ],
                    },
                    {
                        "rowNumbers": {"min": 12, "max": 16},
                        "availableSeats": [
                            {"columnNumber": ["A", "B", "C", "F"]},
                            {"columnNumber": ["A", "B", "C", "D", "E", "F"]},
                            {"columnNumber": ["A", "B", "C", "D", "E", "F"]},
                            {"columnNumber": ["A", "B", "C", "D", "E", "F"]},
                            {"columnNumber": ["A", "B", "C", "D", "E", "F"]},
                        ],
                        "rowTypes": ["EXTRA_LEG_ROOM"],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["B", "E"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": ["LEG_SPACE", "PREFERRED_SEAT"],
                                "location": [],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 85.61,
                                        "currencyCode": "USD",
                                        "convertedAmount": 85.61,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 5.97,
                                        "currencyCode": "USD",
                                        "convertedAmount": 5.97,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 79.64,
                                        "currencyCode": "USD",
                                        "convertedAmount": 79.64,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            },
                            {
                                "columnNumbers": ["A", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": ["LEG_SPACE", "PREFERRED_SEAT"],
                                "location": [],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 89.89,
                                        "currencyCode": "USD",
                                        "convertedAmount": 89.89,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 6.27,
                                        "currencyCode": "USD",
                                        "convertedAmount": 6.27,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 83.62,
                                        "currencyCode": "USD",
                                        "convertedAmount": 83.62,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            },
                            {
                                "columnNumbers": ["C", "D"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": ["LEG_SPACE", "PREFERRED_SEAT"],
                                "location": [],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 94.17,
                                        "currencyCode": "USD",
                                        "convertedAmount": 94.17,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 6.57,
                                        "currencyCode": "USD",
                                        "convertedAmount": 6.57,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 87.6,
                                        "currencyCode": "USD",
                                        "convertedAmount": 87.6,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            },
                        ],
                    },
                    {
                        "rowNumbers": {"min": 17, "max": 17},
                        "availableSeats": [{"columnNumber": ["B", "E"]}],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["B", "E"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            },
                            {
                                "columnNumbers": ["A", "C", "D", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": ["PREFERRED_SEAT"],
                                "location": [],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            },
                        ],
                    },
                    {
                        "rowNumbers": {"min": 18, "max": 18},
                        "availableSeats": [{"columnNumber": ["B", "D", "E", "F"]}],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["B", "D", "E", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            },
                            {
                                "columnNumbers": ["A", "C"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": ["PREFERRED_SEAT"],
                                "location": [],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            },
                        ],
                    },
                    {
                        "rowNumbers": {"min": 19, "max": 22},
                        "availableSeats": [
                            {"columnNumber": ["A", "B", "C", "D", "E", "F"]},
                            {"columnNumber": ["A", "B", "C", "D", "E", "F"]},
                            {"columnNumber": ["A", "B", "C", "D", "E", "F"]},
                            {"columnNumber": []},
                        ],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "B", "C", "D", "E", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {
                                    "totalAmount": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "tax": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                    "base": {
                                        "amount": 0.0,
                                        "currencyCode": "USD",
                                        "convertedAmount": 0.0,
                                        "convertedCurrency": "USD",
                                        "otherCoinage": [],
                                    },
                                },
                            }
                        ],
                    },
                ],
            }
        ],
    }

    csv, _ = FlightSearchTools.convert_seat_map_to_csv(test_input)
    assert (
        csv
        == "ROW,COLUMN,SEAT_TYPE,SEAT_LOCATION,IS_WING,PRICE\r\n11,A,WINDOW,\"['BULKHEAD_ROW', 'EXIT_ROW']\",False,98.45\r\n11,B,CENTER,\"['BULKHEAD_ROW', 'EXIT_ROW']\",False,98.45\r\n11,C,AISLE,\"['BULKHEAD_ROW', 'EXIT_ROW']\",False,98.45\r\n11,D,AISLE,\"['BULKHEAD_ROW', 'EXIT_ROW']\",False,98.45\r\n11,E,CENTER,\"['BULKHEAD_ROW', 'EXIT_ROW']\",False,98.45\r\n11,F,WINDOW,\"['BULKHEAD_ROW', 'EXIT_ROW']\",False,98.45\r\n12,A,WINDOW,[],False,89.89\r\n12,B,CENTER,[],False,85.61\r\n12,C,AISLE,[],False,94.17\r\n12,F,WINDOW,[],False,89.89\r\n13,A,WINDOW,[],False,89.89\r\n13,B,CENTER,[],False,85.61\r\n13,C,AISLE,[],False,94.17\r\n13,D,AISLE,[],False,94.17\r\n13,E,CENTER,[],False,85.61\r\n13,F,WINDOW,[],False,89.89\r\n14,A,WINDOW,[],False,89.89\r\n14,B,CENTER,[],False,85.61\r\n14,C,AISLE,[],False,94.17\r\n14,D,AISLE,[],False,94.17\r\n14,E,CENTER,[],False,85.61\r\n14,F,WINDOW,[],False,89.89\r\n15,A,WINDOW,[],False,89.89\r\n15,B,CENTER,[],False,85.61\r\n15,C,AISLE,[],False,94.17\r\n15,D,AISLE,[],False,94.17\r\n15,E,CENTER,[],False,85.61\r\n15,F,WINDOW,[],False,89.89\r\n16,A,WINDOW,[],False,89.89\r\n16,B,CENTER,[],False,85.61\r\n16,C,AISLE,[],False,94.17\r\n16,D,AISLE,[],False,94.17\r\n16,E,CENTER,[],False,85.61\r\n16,F,WINDOW,[],False,89.89\r\n17,B,CENTER,[],False,0\r\n17,E,CENTER,[],False,0\r\n18,B,CENTER,[],False,0\r\n18,D,AISLE,[],False,0\r\n18,E,CENTER,[],False,0\r\n18,F,WINDOW,[],False,0\r\n19,A,WINDOW,[],False,0\r\n19,B,CENTER,[],False,0\r\n19,C,AISLE,[],False,0\r\n19,D,AISLE,[],False,0\r\n19,E,CENTER,[],False,0\r\n19,F,WINDOW,[],False,0\r\n20,A,WINDOW,[],False,0\r\n20,B,CENTER,[],False,0\r\n20,C,AISLE,[],False,0\r\n20,D,AISLE,[],False,0\r\n20,E,CENTER,[],False,0\r\n20,F,WINDOW,[],False,0\r\n21,A,WINDOW,[],False,0\r\n21,B,CENTER,[],False,0\r\n21,C,AISLE,[],False,0\r\n21,D,AISLE,[],False,0\r\n21,E,CENTER,[],False,0\r\n21,F,WINDOW,[],False,0\r\n"
    )


def test_seat_map_conversion_more_limitation():
    test_input = {
        "seatMapId": "seatMapId-1",
        "wingRows": {"min": 0, "max": 0},
        "cabinSections": [
            {
                "cabin": "ECONOMY",
                "bookingCode": "",
                "locations": ["MAIN_DECK"],
                "facilitySections": [],
                "columnSections": [
                    {
                        "columns": [
                            {"columnNumber": "A", "position": "WINDOW"},
                            {"columnNumber": "B", "position": "CENTER"},
                            {"columnNumber": "C", "position": "AISLE"},
                        ]
                    },
                    {
                        "columns": [
                            {"columnNumber": "D", "position": "AISLE"},
                            {"columnNumber": "E", "position": "CENTER"},
                            {"columnNumber": "F", "position": "WINDOW"},
                        ]
                    },
                ],
                "rowSections": [
                    {
                        "rowNumbers": {"min": 6, "max": 11},
                        "availableSeats": [
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                        ],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "B", "C", "D", "E", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            }
                        ],
                    },
                    {
                        "rowNumbers": {"min": 12, "max": 12},
                        "availableSeats": [{"columnNumber": ["C"]}],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "B", "D", "E", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            },
                            {
                                "columnNumbers": ["C"],
                                "type": "SEAT",
                                "limitations": ["LOYALTY_LEVEL_REQUIRED", None],
                                "facilities": ["PREFERRED_SEAT"],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            },
                        ],
                    },
                    {
                        "rowNumbers": {"min": 13, "max": 13},
                        "availableSeats": [{"columnNumber": []}],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "B", "C", "D", "E", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            }
                        ],
                    },
                    {
                        "rowNumbers": {"min": 14, "max": 14},
                        "availableSeats": [{"columnNumber": []}],
                        "rowTypes": ["NO_ROW"],
                        "facilitySections": [],
                        "seatSections": [],
                    },
                    {
                        "rowNumbers": {"min": 15, "max": 15},
                        "availableSeats": [{"columnNumber": ["E"]}],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "B", "C", "D", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            },
                            {
                                "columnNumbers": ["E"],
                                "type": "SEAT",
                                "limitations": ["RESTRICTED_RECLINE", "NOT_ALLOWED_FOR_INFANT"],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            },
                        ],
                    },
                    {
                        "rowNumbers": {"min": 16, "max": 16},
                        "availableSeats": [{"columnNumber": ["A", "B", "C", "D", "E"]}],
                        "rowTypes": ["EXIT"],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            },
                            {
                                "columnNumbers": ["A", "B", "C", "D", "E"],
                                "type": "SEAT",
                                "limitations": ["NOT_ALLOWED_FOR_INFANT"],
                                "facilities": [],
                                "location": ["EXIT_ROW"],
                                "commercialName": "",
                                "price": {},
                            },
                        ],
                    },
                    {
                        "rowNumbers": {"min": 17, "max": 17},
                        "availableSeats": [{"columnNumber": ["B", "E", "F"]}],
                        "rowTypes": ["EXIT"],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "C", "D"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            },
                            {
                                "columnNumbers": ["B", "E", "F"],
                                "type": "SEAT",
                                "limitations": ["NOT_ALLOWED_FOR_INFANT"],
                                "facilities": [],
                                "location": ["EXIT_ROW"],
                                "commercialName": "",
                                "price": {},
                            },
                        ],
                    },
                    {
                        "rowNumbers": {"min": 18, "max": 32},
                        "availableSeats": [
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                            {"columnNumber": []},
                        ],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["A", "B", "C", "D", "E", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            }
                        ],
                    },
                    {
                        "rowNumbers": {"min": 33, "max": 34},
                        "availableSeats": [{"columnNumber": []}, {"columnNumber": []}],
                        "rowTypes": [],
                        "facilitySections": [],
                        "seatSections": [
                            {
                                "columnNumbers": ["D", "E", "F"],
                                "type": "SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            },
                            {
                                "columnNumbers": ["A", "B", "C"],
                                "type": "NO_SEAT",
                                "limitations": [],
                                "facilities": [],
                                "location": [],
                                "commercialName": "",
                                "price": {},
                            },
                        ],
                    },
                ],
            }
        ],
    }

    # | ROW | COLUMN | SEAT_TYPE | SEAT_LOCATION               | IS_WING | PRICE |
    # | --- | ------ | ---------- | ---------------------------- | -------- | ----- |
    # | 15  | E      | CENTER     | \['MAIN\_DECK']              | False    | 0     |
    # | 16  | A      | WINDOW     | \['MAIN\_DECK', 'EXIT\_ROW'] | False    | 0     |
    # | 16  | B      | CENTER     | \['MAIN\_DECK', 'EXIT\_ROW'] | False    | 0     |
    # | 16  | C      | AISLE      | \['MAIN\_DECK', 'EXIT\_ROW'] | False    | 0     |
    # | 16  | D      | AISLE      | \['MAIN\_DECK', 'EXIT\_ROW'] | False    | 0     |
    # | 16  | E      | CENTER     | \['MAIN\_DECK', 'EXIT\_ROW'] | False    | 0     |
    # | 17  | B      | CENTER     | \['MAIN\_DECK', 'EXIT\_ROW'] | False    | 0     |
    # | 17  | E      | CENTER     | \['MAIN\_DECK', 'EXIT\_ROW'] | False    | 0     |
    # | 17  | F      | WINDOW     | \['MAIN\_DECK', 'EXIT\_ROW'] | False    | 0     |

    csv, _ = FlightSearchTools.convert_seat_map_to_csv(test_input)
    assert (
        csv
        == "ROW,COLUMN,SEAT_TYPE,SEAT_LOCATION,IS_WING,PRICE\r\n15,E,CENTER,['MAIN_DECK'],False,0\r\n16,A,WINDOW,\"['MAIN_DECK', 'EXIT_ROW']\",False,0\r\n16,B,CENTER,\"['MAIN_DECK', 'EXIT_ROW']\",False,0\r\n16,C,AISLE,\"['MAIN_DECK', 'EXIT_ROW']\",False,0\r\n16,D,AISLE,\"['MAIN_DECK', 'EXIT_ROW']\",False,0\r\n16,E,CENTER,\"['MAIN_DECK', 'EXIT_ROW']\",False,0\r\n17,B,CENTER,\"['MAIN_DECK', 'EXIT_ROW']\",False,0\r\n17,E,CENTER,\"['MAIN_DECK', 'EXIT_ROW']\",False,0\r\n17,F,WINDOW,\"['MAIN_DECK', 'EXIT_ROW']\",False,0\r\n"
    )
